﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local NodeBaseServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.NodeBaseServer")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")
local BattlePositionUtilsEnv = xrequire("Common.Battle.BattlePositionUtils")

---@class SelectNodeServer : NodeBaseServer
local SelectNodeServer = DefineClass("SelectNodeServer", NodeBaseServerEnv.NodeBaseServer)

function SelectNodeServer:Execute(graphData)
    local caster = graphData.caster
    local targetIds = {}
    local count = graphData:GetProperty(self, "count")

    if caster:CheckCrowdControl(TableConst.enums.CrowdControlType.Confusion) then -- 混乱
        local allHeroIds = graphData:GetFromBlackboard("HeroIds")
        local aliveHeroIds = {}
        for _, heroId in ipairs(allHeroIds) do
            local hero = graphData.caster.battleGame.heroUniqueIdMap[heroId]
            if hero:IsAlive() then
                table.insert(aliveHeroIds, heroId)
            end
        end
        targetIds = math.randomchoices(aliveHeroIds, count)
    else
        local rawTargetIds = graphData:GetProperty(self, "rawTargetIds")
        local exceptTargetIds = graphData:GetProperty(self, "exceptTargetIds")
        for i = #rawTargetIds, 1, -1 do
            local rawTargetId = rawTargetIds[i]
            if table.index(exceptTargetIds, rawTargetId) then
                table.remove(rawTargetIds, i)
            end
        end

        if graphData:GetProperty(self, "selectSelf") then
            table.insert(targetIds, caster.uniqueId)
        elseif graphData:GetProperty(self, "pursue") then
            for _, rawTargetId in ipairs(rawTargetIds) do
                if table.index(caster.plainAttackTargetIds, rawTargetId) then
                    local hero = graphData.caster.battleGame.heroUniqueIdMap[rawTargetId]
                    if hero:IsAlive() then
                        table.insert(targetIds, rawTargetId)
                    end
                end
            end
        elseif graphData:GetProperty(self, "selectCurTurnHero") then
            for _, rawTargetId in ipairs(rawTargetIds) do
                if rawTargetId == graphData.caster.battleGame.curActionHeroUid then
                    table.insert(targetIds, rawTargetId)
                    break
                end
            end
        else
            local selectFlags = {}
            local enemy = graphData:GetProperty(self, "enemy")
            local ally = graphData:GetProperty(self, "ally")
            local includeSelf = graphData:GetProperty(self, "includeSelf")
            local alive = graphData:GetProperty(self, "alive")
            local dead = graphData:GetProperty(self, "dead")
            local captain = graphData:GetProperty(self, "captain")
            local pioneer = graphData:GetProperty(self, "pioneer")
            local adviser = graphData:GetProperty(self, "adviser")
            local countryWei = graphData:GetProperty(self, "countryWei")
            local countryShu = graphData:GetProperty(self, "countryShu")
            local countryWu = graphData:GetProperty(self, "countryWu")
            local countryQun = graphData:GetProperty(self, "countryQun")
            local male = graphData:GetProperty(self, "male")
            local female = graphData:GetProperty(self, "female")
            local considerAdditionalPosition = graphData:GetProperty(self, "considerAdditionalPosition")
            for i, rawTargetId in ipairs(rawTargetIds) do
                local hero = graphData.caster.battleGame.heroUniqueIdMap[rawTargetId]
                local relationFlag = self:GetTotalConditionFlag(enemy, ally, includeSelf)
                local aliveFlag = self:GetTotalConditionFlag(alive, dead)
                local countryFlag = self:GetTotalConditionFlag(countryWei, countryShu, countryWu, countryQun)
                local posFlag = self:GetTotalConditionFlag(captain, pioneer, adviser)
                local genderFlag = self:GetTotalConditionFlag(male, female)
                -- 关系 --
                if enemy and hero.camp ~= caster.camp then
                    relationFlag = true
                end
                if ally and hero.camp == caster.camp and hero.uniqueId ~= caster.uniqueId then
                    relationFlag = true
                end
                if includeSelf and hero.uniqueId == caster.uniqueId then
                    relationFlag = true
                end
                -- 是否存活 --
                if alive and hero:IsAlive() then
                    aliveFlag = true
                end
                if dead and not hero:IsAlive() then
                    aliveFlag = true
                end
                -- 军职 --
                if not posFlag then
                    if (captain and BattlePositionUtilsEnv.IsCaptain(hero))
                        or (pioneer and BattlePositionUtilsEnv.IsPioneer(hero))
                        or (adviser and BattlePositionUtilsEnv.IsAdviser(hero)) then
                        posFlag = true
                    end
                    if not posFlag and considerAdditionalPosition then --考虑额外军职
                        if (captain and hero:CheckCrowdControl(TableConst.enums.CrowdControlType.AdditionalCaptain))
                            or (pioneer and hero:CheckCrowdControl(TableConst.enums.CrowdControlType.AdditionalPioneer))
                            or (adviser and hero:CheckCrowdControl(TableConst.enums.CrowdControlType.AdditionalAdviser)) then
                            posFlag = true
                        end
                    end
                end
                -- 国家 --
                if not countryFlag then
                    if (countryWei and hero.dynasty == TableConst.enums.GeneralDynasty.Wei)
                        or (countryShu and hero.dynasty == TableConst.enums.GeneralDynasty.Shu)
                        or (countryWu and hero.dynasty == TableConst.enums.GeneralDynasty.Wu)
                        or (countryQun and hero.dynasty == TableConst.enums.GeneralDynasty.Qun) then
                        countryFlag = true
                    end
                end
                -- 性别 --
                local isFemale = GameCommon.TableDataManager:GetTemplateHeroData(hero.heroId).is_female
                if male and not isFemale then
                    genderFlag = true
                end
                if female and isFemale then
                    genderFlag = true
                end

                selectFlags[i] = relationFlag and aliveFlag and countryFlag and posFlag and genderFlag
            end
            for i, flag in ipairs(selectFlags) do
                if flag then
                    table.insert(targetIds, rawTargetIds[i])
                end
            end
            -- Buff --
            if graphData:GetProperty(self, "checkBuff") then
                local buffIds = graphData:GetProperty(self, "buffIds")
                if not table.isnilorempty(buffIds) then
                    local tempTargetIds = {}
                    for _, targetId in ipairs(targetIds) do
                        local hero = graphData.caster.battleGame.heroUniqueIdMap[targetId]
                        for _, buffId in ipairs(buffIds) do
                            if hero:GetBuff(buffId) then
                                table.insert(tempTargetIds, targetId)
                                break
                            end
                        end
                    end
                    targetIds = tempTargetIds
                end
            end
            -- 属性 --
            if graphData:GetProperty(self, "checkAttribute") then
                local chosenTarget, extremum
                local selectType = graphData:GetProperty(self, "selectType")
                local attributeType = graphData:GetProperty(self, "attributeType")
                for _, targetId in ipairs(targetIds) do
                    local hero = graphData.caster.battleGame.heroUniqueIdMap[targetId]
                    local attributeValue = hero:GetAttribute(attributeType)
                    if selectType == BattleConstEnv.SelectAttributeType.Min then
                        if not extremum or attributeValue < extremum then
                            extremum = attributeValue
                            chosenTarget = targetId
                        end
                    elseif selectType == BattleConstEnv.SelectAttributeType.Max then
                        if not extremum or attributeValue > extremum then
                            extremum = attributeValue
                            chosenTarget = targetId
                        end
                    end
                end
                targetIds = {chosenTarget}
            end
            -- 其他 --
            local random = graphData:GetProperty(self, "random")
            if random and #targetIds > count then
                targetIds = math.randomchoices(targetIds, count)
            end
        end

        local priorSelectTargetId = graphData:GetFromBlackboard("PriorSelectTargetId")
        if priorSelectTargetId then
            local priorSelectTarget = graphData.caster.battleGame.heroUniqueIdMap[priorSelectTargetId]
            local allyIndices = {}
            for i, targetId in ipairs(targetIds) do
                local hero = graphData.caster.battleGame.heroUniqueIdMap[targetId]
                if hero.camp == priorSelectTarget.camp then -- 必须是同阵营
                    table.insert(allyIndices, i)
                end
            end
            if not table.isnilorempty(allyIndices) then
                local PriorSelectTargetKey = graphData:GetFromBlackboard("PriorSelectTargetKey")
                local decreased = caster:DecreasePriorSelectTargetCount(PriorSelectTargetKey)
                -- 减层成功再生效
                if decreased then
                    local i = math.randomchoice(allyIndices)
                    targetIds[i] = priorSelectTargetId
                end
            end
        end
    end
    graphData:SetProperty(self, "targetIds", targetIds)

    if next(targetIds) then
        graphData:SetProperty(self, "outputName", "next")
        -- 特殊处理，记录普攻目标
        local tacticId = graphData:GetProperty(self, "tacticId") --支持传入id
        if string.isnilorempty(tacticId) then
            tacticId = graphData.tacticId
        end
        if BattleUtilsEnv.IsPlainAttack(tacticId) then
            graphData.caster.battleGame.recorder:DetailPlainAttackTarget(targetIds[1])
        end
    else
        graphData:SetProperty(self, "outputName", "elseNode")
        --if not graphData.start then
        --    graphData.reason = {BattleConstEnv.DetailTriggerBuffReason.NoTarget}
        --end
    end
end

function SelectNodeServer:GetTotalConditionFlag(...)
    local conditions = { ... }  -- 收集所有传入的子条件
    local allTrue = true
    local allFalse = true

    for _, cond in ipairs(conditions) do
        if cond then
            allFalse = false
        else
            allTrue = false
        end
    end

    -- 如果全为 true 或全为 false，则返回 true；否则返回 false
    return allTrue or allFalse
end
