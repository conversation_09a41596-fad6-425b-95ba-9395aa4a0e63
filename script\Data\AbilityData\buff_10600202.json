{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"consts": {"Num": {"VariableRangeType": 1, "ResetOnExecute": false, "Type": "number", "Value": 0}}, "event1": {"Type": "number", "Value": "106"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}, "event2": {"Type": "number", "Value": "5"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "False"}, "event3": {"Type": "number", "Value": "9"}, "eventPriority3": {"Type": "number", "Value": "0"}, "onlySelf3": {"Type": "boolean", "Value": "False"}}}, "1": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "True"}, "tacticId": {"Type": "number", "BlackboardValue": "InputTacticId"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [{"Type": "number", "Value": "1"}, {"Type": "number", "Value": "3"}], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}, "3": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "1"}, "key": {"Type": "string", "Value": "<PERSON><PERSON>"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "6": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "0"}, "key": {"Type": "string", "Value": "<PERSON><PERSON>"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "9": {"Type": "CompareNode", "Field": {"number2": {"Type": "number", "Value": "0"}, "number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "5"}}}, "10": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "<PERSON><PERSON>"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "12": {"Type": "HealNode", "Field": {"targetIds": [], "healFactor": {"Type": "string", "Value": ""}, "healFactorNew": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedHeal": {"Type": "boolean", "Value": "False"}, "fixedHealValue": {"Type": "number", "Value": "0"}, "healHeroIds": [], "healPackageIds": [], "missHeroIds": []}}, "13": {"Type": "SelectNode", "Field": {"rawTargetIds": [], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "True"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "14": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "heal1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "胭脂阵治疗率"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"AfterUseTacticNode": ["1.prev"], "RoundPrepareNode": ["6.prev"], "RoundEndNode": ["10.prev"]}, "1": {"next": ["3.prev"]}, "9": {"next": ["13.prev"]}, "10": {"next": ["9.prev"]}, "13": {"next": ["14.prev"]}, "14": {"next": ["12.prev"]}}, "DataFlows": {"10": {"output": ["9.number1"]}, "13": {"targetIds": ["12.targetIds"]}, "14": {"result": ["12.<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}