local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")

---@class (partial) Avatar
---@class TaskComponent: ComponentBase
TaskComponent = DefineClass("TaskComponent", ComponentBaseEnv.ComponentBase)

---@diagnostic disable-next-line: duplicate-set-field
function TaskComponent:OnAvatarFirstCreate()
    local taskId = GameCommon.TableDataManager:GetTaskConstData("InitTask")
    self:acceptTask(taskId)
end

---@diagnostic disable-next-line: duplicate-set-field
function TaskComponent:OnAvatarInitLoad()
    for taskId, progress in pairs(self.props.mainTasks) do
        self:onActiveTask(taskId)
    end
    for taskId, progress in pairs(self.props.cycleTasks) do
        self:onActiveTask(taskId)
    end
end

---@return boolean
function TaskComponent:isMainTask(taskId)
    return true  -- TODO(qun): 暂时所有任务都是主线任务，记得处理一下 _InitTaskChapterMap
end

function TaskComponent:isChapterTask(taskId)
    if not self:isMainTask(taskId) then
        return false
    end
    local config = GameCommon.TableDataManager:GetTaskData(taskId)
    return config.chapters == 0
end

function TaskComponent:acceptTask(taskId)
    if self:hasTask(taskId) or self:isTaskCompleteOrSatisfied(taskId) then
        return
    end
    self:logInfo("TaskComponent:acceptTask taskId: %s", taskId)
    if self:isMainTask(taskId) then
        self.props.mainTasks[taskId] = {}
        self:onActiveTask(taskId)
        if self:isChapterTask(taskId) then
            for subTaskId, _ in pairs(ProcessedTableEnv.TASK_CHAPTER_MAP[taskId]) do
                self:acceptTask(subTaskId)
            end
        end
    else
        self.props.cycleTasks[taskId] = {}
        self:onActiveTask(taskId)
    end
end

function TaskComponent:hasTask(taskId)
    return self.props.mainTasks[taskId] ~= nil or self.props.cycleTasks[taskId] ~= nil
end

function TaskComponent:isTaskCompleteOrSatisfied(taskId)
    if self.props.completeMainTasks[taskId] then
        return true
    end
    if self.props.mainTasks[taskId] and self.props.mainTasks[taskId].satisfied then
        return true
    end
    return false
end

function TaskComponent:onActiveTask(taskId)
    local config = GameCommon.TableDataManager:GetTaskData(taskId)
    -- 激活任务，判断当前是否已经满足条件
    self:taskJudgeSatisfy(taskId)
    -- 激活任务，需要触发对应的监听
    if self:isTaskCompleteOrSatisfied(taskId) then
        return
    end
    for _, event in ipairs(config.triggerEvent) do
        local listenAll = false
        if table.isempty(config.condition) then
            if config.collectFunc._type_ == "_taskGetChapterComplete" then
                self:registerEvent(event, "taskEventCB", "task_" .. taskId, taskId)
                return
            end
            self:logError("TaskComponent:onActiveTask condition is empty taskId: %s", taskId)
            return
        end
        for _, con in ipairs(config.condition) do
            if con.id then
                self:registerEventWithId(event, con.id, "taskEventCB", "task_" .. taskId, taskId)
            elseif not listenAll then
                listenAll = true
                self:registerEvent(event, "taskEventCB", "task_" .. taskId, taskId)
            end
        end
    end
end

function TaskComponent:taskEventCB(taskId, id, ...)
    --- 任务事件回调，触发检查任务完成
    self:taskJudgeSatisfy(taskId)
end

function TaskComponent:taskJudgeSatisfy(taskId)
    if self:isTaskCompleteOrSatisfied(taskId) then
        return
    end
    local config = GameCommon.TableDataManager:GetTaskData(taskId)
    if config.collectFunc._type_ == "_taskGetChapterComplete" then
        self:_judgeChapterSatisfied(taskId, config)
        return
    end
    for _, cond in ipairs(config.condition) do
        local args = nil
        if self[config.collectFunc._type_] then
            args = self[config.collectFunc._type_](self, cond)
        elseif self.worldAvatar[config.collectFunc._type_] then
            args = self.worldAvatar[config.collectFunc._type_](self.worldAvatar, cond)
        else
            self:logError("TaskComponent:taskJudgeSatisfy collectFunc not found taskId: %s", taskId)
            return
        end
        assert(args, "TaskComponent:taskJudgeSatisfy args is nil taskId: %s", taskId)
        local complete = false
        --判断优先级: id > level > num > value
        if cond.id and cond.level then
            complete = (cond.id == args.id and cond.level <= args.level)
        elseif cond.id and cond.value then
            complete = (cond.id == args.id and cond.value <= args.value)
        elseif cond.level and cond.num then
            complete = (cond.level <= args.level and cond.num <= args.num)
        elseif cond.value then
            complete = (cond.value <= args.value)
        elseif cond.num then
            complete = (cond.num <= args.num)
        else
            self:logError("TaskComponent:taskJudgeSatisfy condition not paired taskId: %s", taskId)
        end
        if not complete then
            return
        end
    end
    --所有条件都通过了判断
    self:taskSatisfied(taskId, config)
end

function TaskComponent:_judgeChapterSatisfied(taskId, config)
    if self:isTaskCompleteOrSatisfied(taskId) then
        return
    end
    for subTaskId in pairs(ProcessedTableEnv.TASK_CHAPTER_MAP[taskId]) do
        if not self:isTaskCompleteOrSatisfied(subTaskId) then
            return
        end
    end
    self:taskSatisfied(taskId, config)
end

function TaskComponent:taskSatisfied(taskId, config)
    if self:isTaskCompleteOrSatisfied(taskId) then
        return
    end
    if not self:isMainTask(taskId) then
        -- 暂不处理周期任务
        return
    end
    self:logInfo("TaskComponent:taskSatisfied taskId: %s", taskId)
    self.props.mainTasks[taskId].satisfied = true
    self:unregisterTaskEvent(taskId, config)
    self:safeClientRpc("onTaskSatisfy", taskId)
end

function TaskComponent:claimTaskReward(taskId)
    -- 手动领取奖励的情况，判断任务条件是否满足
    if not self.props.mainTasks[taskId] or not self.props.mainTasks[taskId].satisfied then
        return
    end
    -- 任务完成，领取奖励
    local config = GameCommon.TableDataManager:GetTaskData(taskId)
    self:_taskComplete(taskId, config)
    self:clientRpc("onClaimTaskReward", taskId)
end

function TaskComponent:_taskComplete(taskId, config)
    -- 判断任务条件是否满足
    if not self.props.mainTasks[taskId] or not self.props.mainTasks[taskId].satisfied then
        return
    end
    self:logInfo("TaskComponent:_taskComplete taskId: %s", taskId)
    self.props.mainTasks[taskId] = nil
    self.props.completeMainTasks[taskId] = true
    -- 发放奖励
    self:AddReward(config.rewards, TableConst.enums.Reason.TASK_REWARD)
    self:fireAvatarEvent(TableConst.enums.SAvatarEvent.TASK_COMPLETE, taskId)
    -- 检查下一个章节任务
    if self:isChapterTask(taskId) then
        local nextTaskId = ProcessedTableEnv.GetNextChapterTaskId(taskId)
        if nextTaskId then
            self:acceptTask(nextTaskId)
        end
    end
end

function TaskComponent:unregisterTaskEvent(taskId, config)
    for _, event in ipairs(config.triggerEvent) do
        if table.isempty(config.condition) then
            if config.collectFunc._type_ == "_taskGetChapterComplete" then
                self:unregisterEvent(event, "taskEventCB", "task_" .. taskId, taskId)
                return
            end
            self:logError("TaskComponent:unregisterTaskEvent condition is empty taskId: %s", taskId)
            return
        end
        local listenAll = false
        for _, con in ipairs(config.condition) do
            if con.id then
                self:unregisterEventWithId(event, con.id, "taskEventCB", "task_" .. taskId)
            elseif not listenAll then
                listenAll = true
                self:unregisterEvent(event, "taskEventCB", "task_" .. taskId)
            end
        end
    end
end

function TaskComponent:gmFinishTaskChapter(chapterId)
    for i = 1, 100 do
        local hasNew = false
        for taskId, _ in pairs(self.props.mainTasks) do
            if not self:isMainTask(taskId) then
                goto continue
            end
            local config = GameCommon.TableDataManager:GetTaskData(taskId)
            assert(config)
            if not self:isChapterTask(taskId) then
                if config.chapters > chapterId then
                    goto continue
                end
                self:taskSatisfied(taskId, config)
            else
                if config.id > chapterId then
                    goto continue
                end
                assert(self.props.mainTasks[taskId])
                if not self.props.mainTasks[taskId].satisfied then
                    goto continue
                end
            end
            self:claimTaskReward(taskId)
            hasNew = true
            ::continue::
        end
        if not hasNew then
            break
        end
    end
    return Config.Ret.SUCCESS
end
