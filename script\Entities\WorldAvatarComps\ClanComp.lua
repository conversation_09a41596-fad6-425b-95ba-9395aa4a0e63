local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local GlobalDataMgrEnv = xrequire("Framework.Utils.GlobalDataMgr")
local ClanConstEnv = xrequire("Common.Const.ClanConst")
local StringUtilsEnv = xrequire("Common.Utils.StringUtils")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local AvatarConst = xrequire("Common.Const.AvatarConst")

---@class (partial) WorldAvatar
---@class ClanComp: ComponentBase
ClanComp = DefineClass("ClanComp", ComponentBaseEnv.ComponentBase)

function ClanComp:ctor()
    self.lastQuerySortedClanListStamp = 0
    self.sortedClanList = {}
    self.isCurrCommSortedClanList = false

    self.lastQuerySortedClanStFreeAvatarListStamp = 0
    self.lastQuerySortedClanStFreeAvatarListField = nil
    self.SortedClanStFreeAvatarList = {}

    self.updateClanAvatarInfoCache = {}
    self.updateClanAvatarInfoTimer = nil

    if self.props.clanId == 0 then
        self:addTimer(math.random(10), 0, function() self:serviceRpc("ClanInviteService", "RegisterAvatar", self.props.gid, self:getClanInviteAvatarSortInfo()) end)
    end
end

function ClanComp:getClanInviteAvatarSortInfo()
    return {
        birthCommandery = self.props.birthCommandery,
        prosperity = self:GetProsperity(),
        stamp = self.props.enterLeaveStamp
    }
end

function ClanComp:TryConnectClan()
    if self.props.clanId == 0 then
        return
    end

    self:WaitClanData(function()
        self:callClan(
                self.props.clanId, function() self:logError("TryConnectClan Fail. Cannot Find Clan Mailbox. clanId: %s", self.props.clanId) end,
                "MemberObserve", self.props.gid, self:getClient())
    end)
end

function ClanComp:TryDisconnectClan(clientSessionId)
    if self.props.clanId == 0 then
        return
    end

    clientSessionId = clientSessionId or self:getClientSession()
    self:WaitClanData(function()
        self:callClan(
                self.props.clanId, function() self:logError("TryDisconnectClan Fail. Cannot Find Clan Mailbox. clanId: %s", self.props.clanId) end,
                "MemberUnobserve", self.props.gid, clientSessionId)
    end)
end

function ClanComp:RpcCheckClanNameRedundant(clanName)
    self:serviceRpc("ClanService", "CheckClanNameRedundant", clanName, self:makeCallback("OnRpcCheckClanNameRedundant", clanName))
end

function ClanComp:OnRpcCheckClanNameRedundant(clanName, isRedundant)
    self:logInfo("OnRpcCheckClanNameRedundant. clanName: %s, isRedundant: %s", clanName, isRedundant)
    self:clientRpc("OnRpcCheckClanNameRedundant", clanName, isRedundant)
end

function ClanComp:CreateClan(clanName, clanBadge, clanMotto, applyVerify)
    local code = self:checkCreateClan(clanName, clanBadge, clanMotto)
    if code ~= Config.Ret.SUCCESS then
        self:logInfo("CreateClan Fail. code: %s, clanName: %s, clanBadge: %v, clanMotto: %s, applyVerify: %v", code, clanName, clanBadge, clanMotto, applyVerify)
        self:clientRpc("OnCreateClan", code)
        return
    end

    self.avatar:AsyncCheckSensitiveWord(clanName .. "_" .. clanBadge.patternWord .. "_" .. clanMotto, function(succ, result, pass)
        if not succ or not pass then
            self:logInfo("CreateClan Fail. Name Sensitive. clanName: %s, clanBadge: %v, clanMotto: %s", clanName, clanBadge, clanMotto)
            self.avatar:AddToBag(GameCommon.TableDataManager:GetClanConst("CLAN_CREATE_COST"), TableConst.enums.Reason.CLAN_CREATE)
            self:clientRpc("OnCreateClan", Config.Ret.SENSITIVE_WORD_CHECK_FAIL)
            return
        end

        self:logInfo("CreateClan. clanName: %s, clanBadge: %v, clanMotto: %s", clanName, clanBadge, clanMotto)
        self:serviceRpc("ClanService", "CreateClan", self.props.gid, clanName, self:getClanMemberBriefInfo(), clanBadge, clanMotto, applyVerify, self:makeCallback("OnCreateClan"))
    end)
end

function ClanComp:getClanMemberBriefInfo()
    return {
        gid = self.props.gid,
        name = self.props.name,
        birthCommandery = self.props.birthCommandery,
        birthplace = self.props.birthplace,
        headpic = self.props.headpic,
        career = self.props.career,
        merit = self.props.meritSummary,
        prosperity = self:GetProsperity(),
        mainCityView = self.props.mainCityView,
        rebornFlag = self.props.rebornFlag
    }
end

function ClanComp:checkCreateClan(clanName, clanBadge, clanMotto)
    if self.props.clanId > 0 then
        return Config.Ret.CLAN_AVATAR_STATUS_ERROR
    end

    if not self.props.mainCityView then
        return Config.Ret.CLAN_SERVER_ERROR
    end

    local currStamp = ServerTimeEnv.GetServerNowInt()
    local processId = GlobalDataMgrEnv.instance:get("ambitions").processId > 0 and GlobalDataMgrEnv.instance:get("ambitions").processId or 1
    if currStamp - self.props.enterLeaveStamp < GameCommon.TableDataManager:GetClanConst("CLAN_ENTER_CD")[processId] then
        return Config.Ret.CLAN_ENTER_CD
    end

    local clanNameLength = StringUtilsEnv.getCharCount(clanName)
    if clanNameLength < 2 or clanNameLength > GameCommon.TableDataManager:GetClanConst("CLAN_NAME_LENGTH") then
        return Config.Ret.CLAN_STRING_LENGTH_EXCEED
    end

    if not StringUtilsEnv.checkStringOnlyChinese(clanName) then
        return Config.Ret.CLAN_STR_NOT_ONLY_CHINESE
    end

    if StringUtilsEnv.getCharCount(clanBadge.patternWord) > 2 then
        return Config.Ret.CLAN_STRING_LENGTH_EXCEED
    end
    if not StringUtilsEnv.checkStringOnlyChinese(clanBadge.patternWord) then
        return Config.Ret.CLAN_STR_NOT_ONLY_CHINESE
    end

    if StringUtilsEnv.getCharCount(clanMotto) > GameCommon.TableDataManager:GetClanConst("CLAN_MOTTO_LENGTH") then
        return Config.Ret.CLAN_STRING_LENGTH_EXCEED
    end

    if not self.avatar or not self.avatar:ConsumeFromBag(GameCommon.TableDataManager:GetClanConst("CLAN_CREATE_COST"), TableConst.enums.Reason.CLAN_CREATE) then
        return Config.Ret.CONSUME_CURRENCY_FAIL
    end

    return Config.Ret.SUCCESS
end

function ClanComp:OnCreateClan(code, clanId, clanMb, clanName)
    self:logInfo("OnCreateClan. code: %s, clanId: %s, clanMb: %s, clanName: %s", code, clanId, clanMb, clanName)
    if code == Config.Ret.SUCCESS then
        self:enterClan(clanId, LuaMailbox.createFromString(clanMb), clanName)
    else
        self.avatar:AddToBag(GameCommon.TableDataManager:GetClanConst("CLAN_CREATE_COST"), TableConst.enums.Reason.CLAN_CREATE)
    end

    self:clientRpc("OnCreateClan", code)
end

function ClanComp:enterClan(clanId, clanMb, clanName)
    if self.props.clanId > 0 then
        self:doLeaveClan()
    end

    self.props.clanId = clanId
    self.props.clanName = clanName
    self.props.enterLeaveStamp = ServerTimeEnv.GetServerNowInt()
    self.clanMb = clanMb
    if self:hasClient() then
        self:serverRpc(clanMb, "MemberObserve", self.props.gid, self:getClient())
    end
    self:clearClanApplyInvite()
    self.props.appliedClanList = {}
    self.props.invitedClanList = {}
    self:effectClanBuff()
    self:broadcastClan()
    self:serviceRpc("ClanInviteService", "NotifyEnterClan", self.props.gid)

    -- todo lzyClanAlly
    self:OnSyncAlly(clanId)

    self:save()
end

function ClanComp:doLeaveClan()
    if self:hasClient() then
        self:TryDisconnectClan()
    end
    local oldClanId = self.props.clanId
    self.props.clanId = 0
    self.props.clanName = ""
    self.props.enterLeaveStamp = ServerTimeEnv.GetServerNowInt()
    self.clanMb = nil
    self:unEffectClanBuff()
    self:broadcastClan()
    self:serviceRpc("ClanInviteService", "NotifyLeaveClan", self.props.gid, self:getClanInviteAvatarSortInfo())

    -- todo lzyClanAlly
    self:OnLeaveAlly(oldClanId)

    self:save()
end

function ClanComp:clearClanApplyInvite()
    for _, clanList in ipairs({ self.props.appliedClanList, self.props.invitedClanList }) do
        for _, clanId in ipairs(clanList) do
            local clanMb = self:findClanMbByClanId(clanId)
            if clanMb then
                self:logInfo("clearClanApplyInvite. Cancel Apply. clanId: %s", clanId)
                self:serverRpc(clanMb, "CancelApplyInvite", self.props.gid)
            end
        end
    end
    self.props.invitedClanCnt = 0
end

function ClanComp:effectClanBuff()
    local addition = {}
    for res, produce in pairs(GameCommon.TableDataManager:GetClanConst("CLAN_RESOURCE_BUFF")) do
        addition[res] = produce
    end
    self:AddResProduceAddition(TableConst.enums.ProduceSrcType.ClanBuff, addition)
end

function ClanComp:unEffectClanBuff()
    local addition = {}
    for res, produce in pairs(GameCommon.TableDataManager:GetClanConst("CLAN_RESOURCE_BUFF")) do
        addition[res] = produce
    end
    self:DelResProduceAddition(TableConst.enums.ProduceSrcType.ClanBuff, addition)
end

function ClanComp:broadcastClan()
    self:broadcastOwning("OnSyncClan", self.props.clanId)
    -- 地块信息上暂时不需要显示氏族，先不触发同步
    -- self:callAvatarChunks("OnSyncRelationProps", self:GetRelationProps())
    self:serviceRpc("AvatarInfoService", "UpdateDiffAvatarInfo", self.props.gid, AvatarConst.AvatarInfoField.clanId, self.props.clanId)
    self:serviceRpc("AvatarInfoService", "UpdateDiffAvatarInfo", self.props.gid, AvatarConst.AvatarInfoField.clanName, self.props.clanName)
    EZGlobal.logicProxy:UpdateImSdkUserInfo({
        userID = tostring(self.props.gid + clusterId * 100000),
        ex = GameCommon.json.encode({ clanName = self.props.clanName }),
    })
end

function ClanComp:ModifyClanInfo(info)
    if self.props.clanId == 0 then
        self:logError("ModifyClanInfo Fail. Not In Clan. info: %v", info)
        self:clientRpc("OnModifyClanInfo", Config.Ret.NOT_IN_CLAN)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("ModifyClanInfo Fail. Cannot Find Clan Mailbox. clanId: %s", self.props.clanId)
        self:clientRpc("OnModifyClanInfo", Config.Ret.CANNOT_FIND_CLAN)
        return
    end

    if info.clanMotto then
        if StringUtilsEnv.getCharCount(info.clanMotto) > GameCommon.TableDataManager:GetClanConst("CLAN_MOTTO_LENGTH") then
            self:logError("ModifyClanInfo Fail. Motto Exceed Length. clanId: %s", self.props.clanId)
            self:clientRpc("OnModifyClanInfo", Config.Ret.CLAN_STRING_LENGTH_EXCEED)
        end

        self.avatar:AsyncCheckSensitiveWord(info.clanMotto, function(succ, result, pass)
            if not succ or not pass then
                self:logInfo("ModifyClanInfo Fail. Clan Motto Sensitive. info: %v", info)
                self:clientRpc("OnModifyClanInfo", Config.Ret.SENSITIVE_WORD_CHECK_FAIL)
                return
            end

            self:logInfo("ModifyClanInfo. clanId: %s, info: %v", self.props.clanId, info)
            self:serverRpc(clanMb, "ModifyInfo", self.props.gid, info, self:makeCallback("OnModifyClanInfo"))
        end)
    else
        self:logInfo("ModifyClanInfo. clanId: %s, info: %v", self.props.clanId, info)
        self:serverRpc(clanMb, "ModifyInfo", self.props.gid, info, self:makeCallback("OnModifyClanInfo"))
    end
end

function ClanComp:OnModifyClanInfo(code)
    self:clientRpc("OnModifyClanInfo", code)
end

function ClanComp:QueryInvitedClanList()
    if table.isempty(self.props.invitedClanList) then
        self:clientRpc("OnQueryInvitedClanList", {})
        return
    end

    self:QueryMultiClan(self.props.invitedClanList, function(clanInfoList)
        self:clientRpc("OnQueryInvitedClanList", clanInfoList)
    end)
end

function ClanComp:checkApplyClan(clanId, description)
    if self.props.clanId > 0 then
        return Config.Ret.CLAN_AVATAR_STATUS_ERROR
    end

    if not self.props.mainCityView then
        return Config.Ret.CLAN_PARAM_ERROR
    end

    if table.contains(self.props.appliedClanList, clanId) then
        return Config.Ret.CLAN_REDUNDANT_OP
    end

    if StringUtilsEnv.getCharCount(description) > 20 then
        return Config.Ret.CLAN_STRING_LENGTH_EXCEED
    end

    return Config.Ret.SUCCESS
end

function ClanComp:ApplyClan(clanId, description)
    local code = self:checkApplyClan(clanId, description)
    if code ~= Config.Ret.SUCCESS then
        self:logError("ApplyClan Fail. code: %s, clanId: %s", code, clanId)
        self:OnApplyClanEnd(code, clanId)
        return
    end

    self:logInfo("ApplyClan. clanId: %s, description: %s", clanId, description)
    self:callClan(
            clanId, function() self:OnApplyClanEnd(Config.Ret.CANNOT_FIND_CLAN, clanId) end,
            "Apply", self:getClanMemberBriefInfo(), description, self:makeCallback("OnApplyClan", clanId))
end

function ClanComp:OnApplyClan(clanId, code, isDirectEnter)
    self:logInfo("OnApplyClan. code: %s, clanId: %s, isDirectEnter: %s", code, clanId, isDirectEnter)
    if code == Config.Ret.SUCCESS and not isDirectEnter then
        if not table.contains(self.props.appliedClanList, clanId) then
            self.props.appliedClanList:insert(clanId)
        end
    end

    self:OnApplyClanEnd(code, clanId)
end

function ClanComp:OnApplyClanEnd(code, clanId)
    self:clientRpc("OnApplyClan", code, clanId)
    self:NotifyGmEventEnd(TableConst.enums.GMEvent.ApplyClan, code)
end

function ClanComp:CancelApplyClan(clanId)
    local clanMb = self:findClanMbByClanId(clanId)
    if not clanMb then
        self:logError("CancelApplyClan Fail. Cannot Find Clan Mailbox. clanId: %s", clanId)
        self:clientRpc("OnCancelApplyClan", Config.Ret.CANNOT_FIND_CLAN, clanId)
        return
    end

    self:logInfo("CancelApplyClan. clanId: %s", clanId)
    self:serverRpc(clanMb, "CancelApply", self.props.gid, self:makeCallback("OnCancelApplyClan", clanId))
end

function ClanComp:OnCancelApplyClan(clanId)
    self:logInfo("OnCancelApplyClan. clanId: %s", clanId)
    if table.contains(self.props.appliedClanList, clanId) then
        self.props.appliedClanList:remove(table.index(self.props.appliedClanList, clanId))
    end
    self:clientRpc("OnCancelApplyClan", Config.Ret.SUCCESS, clanId)
end

function ClanComp:QuerySortedClanStFreeAvatars(field, from, count)
    if self.props.clanId == 0 then
        self:logError("QuerySortedClanStFreeAvatars Fail. Not In Clan.")
        self:clientRpc("OnQuerySortedClanStFreeAvatars", Config.Ret.CLAN_AVATAR_STATUS_ERROR, field, from, count, {})
        return
    end

    if field ~= self.lastQuerySortedClanStFreeAvatarListField or
            (from == 1 and ServerTimeEnv.GetServerNowInt() - self.lastQuerySortedClanStFreeAvatarListStamp >= 30) then
        self:QueryMultiClan({ self.props.clanId }, function(clanInfoList)
            if table.isempty(clanInfoList) then
                self:logError("QuerySortedClanStFreeAvatars Fail. Cannot Find Clan Info. clanId: %s", self.props.clanId)
                self:clientRpc("OnQuerySortedClanStFreeAvatars", Config.Ret.CANNOT_FIND_CLAN, field, from, count, {})
                return
            end

            local clanInfo = clanInfoList[1]
            self:serviceRpc(
                    "ClanInviteService", "QuerySortedAvatars", clanInfo.birthCommandery, field, clanInfo.applyVerify.influenceRequire,
                    self:makeCallback("OnQuerySortedClanStFreeAvatars", field, from, count))
        end)
    else
        self:doQuerySortedClanStFreeAvatars(field, from, count)
    end
end

function ClanComp:OnQuerySortedClanStFreeAvatars(field, from, count, avatarList)
    self:logInfo("OnQuerySortedClanStFreeAvatars. field: %s, from: %s, count: %s, avatarList: %v", field, from, count, avatarList)
    self.SortedClanStFreeAvatarList = avatarList
    self.lastQuerySortedClanStFreeAvatarListStamp = ServerTimeEnv.GetServerNowInt()
    self.lastQuerySortedClanStFreeAvatarListField = field
    self:doQuerySortedClanStFreeAvatars(field, from, count)
end

function ClanComp:doQuerySortedClanStFreeAvatars(field, from, count)
    local ret = {}
    local AvatarList = {}
    if from <= #self.SortedClanStFreeAvatarList then
        for i = 1, math.min(count, #self.SortedClanStFreeAvatarList - from + 1) do
            ret[i] = self.SortedClanStFreeAvatarList[from + i - 1]
            AvatarList[i] = self.SortedClanStFreeAvatarList[from + i - 1].gid
        end
    end

    local fieldList = {
        AvatarConst.AvatarInfoField.name,
        AvatarConst.AvatarInfoField.headpic,
        AvatarConst.AvatarInfoField.career,
        AvatarConst.AvatarInfoField.kingBuildingLv
    }
    self.avatar:ServerQueryAvatarInfo(AvatarList, fieldList, function(avatarDetailInfoDict)
        for _, avatarBriefInfo in ipairs(ret) do
            local avatarGid = avatarBriefInfo.gid
            local avatarDetailInfo = avatarDetailInfoDict[avatarGid]
            if avatarDetailInfo then
                for _, fieldId in ipairs(fieldList) do
                    local fieldName = AvatarConst.AvatarInfoFiledName[fieldId]
                    if not avatarBriefInfo[fieldName] then
                        avatarBriefInfo[fieldName] = avatarDetailInfo[fieldId]
                    end
                end
            end
        end

        self:logInfo("QuerySortedClanStFreeAvatars End. ret: %v", ret)
        self:clientRpc("OnQuerySortedClanStFreeAvatars", Config.Ret.SUCCESS, field, from, count, ret)
    end)
end

function ClanComp:InviteClan(inviteeGid)
    if self.props.clanId == 0 then
        self:logError("InviteClan Fail. Not In Clan. inviteeGid: %s", inviteeGid)
        self:clientRpc("OnInviteClan", Config.Ret.CLAN_AVATAR_STATUS_ERROR, inviteeGid)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("InviteClan Fail. Cannot Find Clan Mailbox. clanId: %s, inviteeGid: %s", self.props.clanId, inviteeGid)
        self:clientRpc("OnInviteClan", Config.Ret.CANNOT_FIND_CLAN, self.props.clanId)
        return
    end

    self:logInfo("InviteClan. clanId: %s, inviteeGid: %s", self.props.clanId, inviteeGid)
    self:serverRpc(clanMb, "Invite", self.props.gid, inviteeGid, self:makeCallback("OnInviteClan", inviteeGid))
end

function ClanComp:OnInviteClan(inviteeGid, code, isDirectEnter)
    self:logInfo("OnInviteClan. code: %s, inviteeGid: %s, isDirectEnter: %s", code, inviteeGid, isDirectEnter)
    self:clientRpc("OnInviteClan", code, inviteeGid)
end

function ClanComp:InvitedToClan(invitorGid, clanId, callback)
    if self.props.clanId > 0 then
        self:logInfo("InvitedToClan Fail. Already In A Clan. invitorGid: %s, clanId: %s", invitorGid, clanId)
        self:callbackRpc(callback, false)
        return
    end

    self:logInfo("InvitedToClan. invitorGid: %s, clanId: %s", invitorGid, clanId)
    if not table.contains(self.props.invitedClanList, clanId) then
        self.props.invitedClanList:insert(clanId)
        self.props.invitedClanCnt = self.props.invitedClanCnt + 1
        self:clientRpc("OnInvitedToClan", invitorGid, clanId)
    end
    self:callbackRpc(callback, true)
end

function ClanComp:AcceptClanInvite(clanId)
    self:logInfo("AcceptClanInvite. clanId: %s", clanId)
    if self.props.clanId > 0 then
        self:logError("AcceptClanInvite Fail. Already In Clan. clanId: %s, myClanId: %s", clanId, self.props.clanId)
        self:clientRpc("OnAcceptClanInvite", Config.Ret.CLAN_AVATAR_STATUS_ERROR, clanId)
        return
    end

    if not self.props.mainCityView then
        self:logError("AcceptClanInvite Fail. Cannot Find mainCityView. clanId: %s", clanId)
        self:clientRpc("OnAcceptClanInvite", Config.Ret.CLAN_SERVER_ERROR, clanId)
        return
    end

    if not table.contains(self.props.invitedClanList, clanId) then
        self:logError("AcceptClanInvite Fail. Not Been Invited By This Clan. clanId: %s", clanId)
        self:clientRpc("OnAcceptClanInvite", Config.Ret.CANNOT_FIND_CLAN, clanId)
        return
    end

    local clanMb = self:findClanMbByClanId(clanId)
    if not clanMb then
        self:logError("AcceptClanInvite Fail. Cannot Find Clan Mailbox. clanId: %s", clanId)
        self:clientRpc("OnAcceptClanInvite", Config.Ret.CANNOT_FIND_CLAN, clanId)
        return
    end

    self:logInfo("AcceptClanInvite. clanId: %s", clanId)
    self:serverRpc(clanMb, "AcceptInvite", self.props.gid, self:getClanMemberBriefInfo(), self:makeCallback("OnAcceptClanInvite", clanId))
end

function ClanComp:OnAcceptClanInvite(clanId, code)
    self:logInfo("OnAcceptClanInvite. code: %s, clanId: %s", code, clanId)
    if code == Config.Ret.SUCCESS then
        if table.contains(self.props.invitedClanList, clanId) then
            self.props.invitedClanList:remove(table.index(self.props.invitedClanList, clanId))
            self.props.invitedClanCnt = self.props.invitedClanCnt - 1
        end
    end
    self:clientRpc("OnAcceptClanInvite", code, clanId)
end

function ClanComp:RejectClanInvite(clanId)
    local clanMb = self:findClanMbByClanId(clanId)
    if not clanMb then
        self:logError("RejectClanInvite Fail. Cannot Find Clan Mailbox. clanId: %s", clanId)
        self:clientRpc("OnRejectClanInvite", Config.Ret.CANNOT_FIND_CLAN, clanId)
        return
    end

    self:logInfo("RejectClanInvite. clanId: %s", clanId)
    self:serverRpc(clanMb, "RejectInvite", self.props.gid, self:makeCallback("OnRejectClanInvite", clanId))
end

function ClanComp:OnRejectClanInvite(clanId)
    self:logInfo("OnRejectClanInvite. clanId: %s", clanId)
    if table.contains(self.props.invitedClanList, clanId) then
        self.props.invitedClanList:remove(table.index(self.props.invitedClanList, clanId))
        self.props.invitedClanCnt = self.props.invitedClanCnt - 1
    end
    self:clientRpc("OnRejectClanInvite", Config.Ret.SUCCESS, clanId)
end

function ClanComp:ApproveClan(applicantGid)
    if self.props.clanId == 0 then
        self:logError("ApproveClan Fail. Not In Clan. applicantGid: %s", applicantGid)
        self:clientRpc("OnApproveClan", Config.Ret.NOT_IN_CLAN, applicantGid)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("ApproveClan Fail. Cannot Find Clan Mailbox. clanId: %s, applicantGid: %s", self.props.clanId, applicantGid)
        self:clientRpc("OnApproveClan", Config.Ret.CANNOT_FIND_CLAN, applicantGid)
    end

    self:logInfo("ApproveClan. applicantGid: %s", applicantGid)
    self:serverRpc(clanMb, "Approve", self.props.gid, applicantGid, self:makeCallback("OnApproveClan", applicantGid))
end

function ClanComp:OnApproveClan(applicantGid, code)
    self:logInfo("OnApproveClan. code: %s, applicantGid: %s", code, applicantGid)
    self:clientRpc("OnApproveClan", code, applicantGid)
end

function ClanComp:ApprovedEnterClan(clanId, clanMb, clanName)
    self:logInfo("ApprovedEnterClan. clanId: %s, clanMb: %s, clanName: %s", clanId, clanMb, clanName)
    self:enterClan(clanId, LuaMailbox.createFromString(clanMb), clanName)
    if self:hasClient() then
        self:clientRpc("OnApprovedEnterClan", clanId)
    end
end

function ClanComp:ApproveClanAll()
    if self.props.clanId == 0 then
        self:logError("ApproveClanAll Fail. Not In Clan.")
        self:clientRpc("OnApproveClanAll", Config.Ret.NOT_IN_CLAN)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("ApproveClanAll Fail. Cannot Find Clan Mailbox. clanId: %s", self.props.clanId)
        self:clientRpc("OnApproveClanAll", Config.Ret.CANNOT_FIND_CLAN)
    end

    self:logInfo("ApproveClanAll.")
    self:serverRpc(clanMb, "ApproveAll", self.props.gid, self:makeCallback("OnApproveClanAll"))
end

function ClanComp:OnApproveClanAll(code)
    self:logInfo("OnApproveClanAll. code: %s", code)
    self:clientRpc("OnApproveClanAll", code)
end

function ClanComp:RejectClanApply(applicantGid)
    if self.props.clanId == 0 then
        self:logError("RejectClanApply Fail. Not In Clan. applicantGid: %s", applicantGid)
        self:clientRpc("OnRejectClanApply", Config.Ret.NOT_IN_CLAN, applicantGid)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("RejectClanApply Fail. Cannot Find Clan Mailbox. clanId: %s, applicantGid: %s", self.props.clanId, applicantGid)
        self:clientRpc("OnRejectClanApply", Config.Ret.CANNOT_FIND_CLAN, applicantGid)
    end

    self:logInfo("RejectClanApply. applicantGid: %s", applicantGid)
    self:serverRpc(clanMb, "RejectApply", self.props.gid, applicantGid, self:makeCallback("OnRejectClanApply", applicantGid))
end

function ClanComp:OnRejectClanApply(applicantGid, code)
    self:logInfo("OnRejectClanApply. code: %s, applicantGid: %s", code, applicantGid)
    self:clientRpc("OnRejectClanApply", code, applicantGid)
end

function ClanComp:OnClanApplyRejected(clanId)
    self:logInfo("OnClanApplyRejected. clanId: %s", clanId)
    if table.contains(self.props.appliedClanList, clanId) then
        self.props.appliedClanList:remove(table.index(self.props.appliedClanList, clanId))
    end
end

function ClanComp:RejectClanApplyAll()
    if self.props.clanId == 0 then
        self:logError("RejectClanApplyAll Fail. Not In Clan.")
        self:clientRpc("OnRejectClanApplyAll", Config.Ret.NOT_IN_CLAN)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("RejectClanApplyAll Fail. Cannot Find Clan Mailbox. clanId: %s", self.props.clanId)
        self:clientRpc("OnRejectClanApplyAll", Config.Ret.CANNOT_FIND_CLAN)
    end

    self:logInfo("RejectClanApplyAll.")
    self:serverRpc(clanMb, "RejectApplyAll", self.props.gid, self:makeCallback("OnRejectClanApplyAll"))
end

function ClanComp:OnRejectClanApplyAll(code)
    self:logInfo("OnRejectClanApplyAll. code: %s", code)
    self:clientRpc("OnRejectClanApplyAll", code)
end

function ClanComp:checkLeaveClan()
    if self.props.clanId == 0 then
        return Config.Ret.NOT_IN_CLAN
    end

    return Config.Ret.SUCCESS
end

function ClanComp:LeaveClan()
    local code = self:checkLeaveClan()
    if code ~= Config.Ret.SUCCESS then
        self:logError("LeaveClan Fail. code: %s", code)
        self:OnLeaveClanEnd(code)
        return
    end

    self:callClan(
            self.props.clanId, function() self:OnLeaveClanEnd(Config.Ret.CANNOT_FIND_CLAN) end,
            "Leave", self.props.gid, self:makeCallback("OnLeaveClan", self.props.clanId))
end

function ClanComp:OnLeaveClan(clanId, code)
    self:logInfo("OnLeaveClan. code: %s, clanId: %s", code, clanId)
    if code == Config.Ret.SUCCESS then
        if self.props.clanId == clanId then
            self:doLeaveClan()
        end
    end

    self:OnLeaveClanEnd(code)
end

function ClanComp:OnLeaveClanEnd(code)
    self:clientRpc("OnLeaveClan", code)
    self:NotifyGmEventEnd(TableConst.enums.GMEvent.LeaveClan, code)
end

function ClanComp:KickClanMember(memberGid)
    if self.props.clanId == 0 then
        self:logError("KickClanMember Fail. Not In Clan. memberGid: %s", memberGid)
        self:clientRpc("OnKickClanMember", Config.Ret.NOT_IN_CLAN, memberGid)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("KickClanMember Fail. Cannot Find Clan Mailbox. memberGid: %s", memberGid)
        self:clientRpc("OnKickClanMember", Config.Ret.CANNOT_FIND_CLAN, memberGid)
    end

    if self.props.gid == memberGid then
        self:logError("KickClanMember Fail. Cannot Kick Self. memberGid: %s", memberGid)
        self:clientRpc("OnKickClanMember", Config.Ret.CLAN_PARAM_ERROR, memberGid)
    end

    self:logInfo("KickClanMember. memberGid: %s", memberGid)
    self:serverRpc(clanMb, "Kick", self.props.gid, memberGid, self:makeCallback("OnKickClanMember", memberGid))
end

function ClanComp:OnKickClanMember(memberGid, code)
    self:logInfo("OnKickClanMember. code: %s, memberGid: %s", code, memberGid)
    self:clientRpc("OnKickClanMember", code, memberGid)
end

function ClanComp:KickedOutClan(clanId, clanMb, managerGid)
    self:logInfo("KickedOutClan. clanId: %s, clanMb: %s, managerGid: %s", clanId, clanMb, managerGid)
    if self.props.clanId == clanId then
        self:doLeaveClan()
        self:clientRpc("KickedOutClan", clanId)
    end
end

function ClanComp:DismissClan()
    if self.props.clanId == 0 then
        self:logError("DismissClan Fail. Not In Clan.")
        self:clientRpc("OnDismissClan", Config.Ret.NOT_IN_CLAN)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("DismissClan Fail. Cannot Find Clan Mailbox. clanId: %s", self.props.clanId)
        self:clientRpc("OnDismissClan", Config.Ret.CANNOT_FIND_CLAN)
    end

    self:logInfo("DismissClan. clanId: %s", self.props.clanId)
    self:serverRpc(clanMb, "Dismiss", self.props.gid, self:makeCallback("OnDismissClan", self.props.clanId))
end

function ClanComp:OnDismissClan(clanId, code)
    self:logInfo("OnClanDismiss. code: %s, clanId: %s", code, clanId)
    if code == Config.Ret.SUCCESS then
        if self.props.clanId == clanId then
            self:doLeaveClan()
        end
    end

    self:clientRpc("OnDismissClan", code)
end

function ClanComp:OnClanDismissed(clanId)
    self:logInfo("OnClanDismiss. clanId: %s", clanId)
    if self.props.clanId == clanId then
        self:doLeaveClan()
    end

    self:clientRpc("OnClanDismissed")
end

function ClanComp:AppointClan(memberId, grade)
    if self.props.clanId == 0 then
        self:logError("AppointClan Fail. Not In Clan. memberId: %s, grade: %s", memberId, grade)
        self:clientRpc("OnAppointClan", Config.Ret.NOT_IN_CLAN, memberId, grade)
        return
    end

    if grade > ClanConstEnv.ClanMemberGrade.VICE_LEADER or grade < ClanConstEnv.ClanMemberGrade.NORMAL then
        self:logError("AppointClan Fail. Grade Param Error. memberId: %s, grade: %s", memberId, grade)
        self:clientRpc("OnAppointClan", Config.Ret.CLAN_PARAM_ERROR, memberId, grade)
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("AppointClan Fail. Cannot Find Clan Mailbox. clanId: %s, memberId: %s, grade: %s", self.props.clanId, memberId, grade)
        self:clientRpc("OnAppointClan", Config.Ret.CANNOT_FIND_CLAN, memberId, grade)
    end

    self:logInfo("AppointClan. clanId: %s, memberId: %s, grade: %s", self.props.clanId, memberId, grade)
    self:serverRpc(clanMb, "Appoint", self.props.gid, memberId, grade, self:makeCallback("OnAppointClan", self.props.clanId, memberId, grade))
end

function ClanComp:OnAppointClan(clanId, memberId, grade, code)
    self:logInfo("OnClanDismiss. code: %s, clanId: %s, memberId: %s, grade: %s", code, clanId, memberId, grade)
    self:clientRpc("OnAppointClan", code, memberId, grade)
end

function ClanComp:QueryMultiClan(clanList, callback)
    self:logInfo("QueryMultiClan. clanList: %v", clanList)
    if #clanList == 0 then
        callback({})
        return
    end

    local no = EZGlobal.logicProxy:GenBatchOp(#clanList, function(ret)
        self:logInfo("queryMultiClanEnd. clanInfoList: %v", ret)
        callback(ret)
    end)
    for _, clanId in ipairs(clanList) do
        self:callClan(clanId, function() EZGlobal.logicProxy:SingleRecord(no) end, "Query", self:makeCallback("OnQueryClanInfo", no))
    end
end

function ClanComp:OnQueryClanInfo(no, clanBriefInfo)
    EZGlobal.logicProxy:SingleRecord(no, nil, clanBriefInfo)
end

function ClanComp:AcquireCurPrefClanList(isCurrComm, from, count)
    local clanData = GlobalDataMgrEnv.instance:get("ClanData") or { clans = {}, clanName2clanId = {}, clanPref2clanIdList = {}, clanComm2clanIdList = {} }
    local clanIdList = isCurrComm and (clanData.clanComm2clanIdList[self.props.birthCommandery] or {}) or (clanData.clanPref2clanIdList[self.props.birthplace] or {})
    if from > #clanIdList or from < 1 then
        self:clientRpc("OnAcquireCurPrefClanList", isCurrComm, from, count, {})
        return
    end

    local targetList = {}
    for i = 1, math.min(count, #clanIdList - from + 1) do
        table.insert(targetList, clanIdList[from + i - 1])
    end
    self:QueryMultiClan(targetList, function(clanInfoList)
        if isCurrComm then
            self:clientRpc("OnAcquireCurPrefClanList", isCurrComm, from, count, clanInfoList)
        else
            local nearCommClanInfoList = {}
            for _, clanInfo in ipairs(clanInfoList) do
                if self.props.birthCommandery ~= clanInfo.birthCommandery then
                    table.insert(nearCommClanInfoList, clanInfo)
                end
            end

            self:clientRpc("OnAcquireCurPrefClanList", isCurrComm, from, count, nearCommClanInfoList)
        end
    end)
end

function ClanComp:AcquireCurPrefSortedClanList(isCurrComm, from, count)
    self:logInfo("AcquireCurPrefSortedClanList. isCurrComm: %s, from: %s, count: %s", isCurrComm, from, count)
    if isCurrComm ~= self.isCurrCommSortedClanList or (from == 1 and ServerTimeEnv.GetServerNowInt() - self.lastQuerySortedClanListStamp >= 30) then
        self:serviceRpc(
                "ClanService", "QuerySortedClanList", isCurrComm, isCurrComm and self.props.birthCommandery or self.props.birthplace,
                self:makeCallback("OnQuerySortedClanList", isCurrComm, from, count))
    else
        self:doAcquireCurPrefSortedClanList(isCurrComm, from, count)
    end
end

function ClanComp:OnQuerySortedClanList(isCurrComm, from, count, sortedClanList)
    self:logInfo("OnQuerySortedClanList. isCurrComm: %s, from: %s, count: %s, sortedClanList: %v", isCurrComm, from, count, sortedClanList)
    if isCurrComm then
        self.sortedClanList = sortedClanList
    else
        local filter = {}
        for _, clanInfo in ipairs(sortedClanList) do
            if clanInfo.birthCommandery ~= self.props.birthCommandery then
                table.insert(filter, clanInfo)
            end
        end
        self.sortedClanList = filter
    end
    self.isCurrCommSortedClanList = isCurrComm
    self.lastQuerySortedClanListStamp = ServerTimeEnv.GetServerNowInt()
    self:doAcquireCurPrefSortedClanList(isCurrComm, from, count)
end

function ClanComp:doAcquireCurPrefSortedClanList(isCurrComm, from, count)
    local ret = {}
    if from <= #self.sortedClanList then
        for i = 1, math.min(count, #self.sortedClanList - from + 1) do
            ret[i] = self.sortedClanList[from + i - 1]
        end
    end

    self:logInfo("doAcquireCurPrefSortedClanList. isCurrComm: %s, from: %s, count: %s, ret: %v", isCurrComm, from, count, ret)
    self:clientRpc("OnAcquireCurPrefSortedClanList", isCurrComm, from, count, ret)
end

function ClanComp:SearchClan(clanId, clanName)
    self:logInfo("SearchClan. clanId: %s, clanName: %s", clanId, clanName)
    local clanMb
    if clanId > 0 then
        clanMb = self:findClanMbByClanId(clanId)
    else
        clanMb = self:findClanMbByClanName(clanName)
    end
    if not clanMb then
        self:logInfo("SearchClan Fail. Cannot Find Clan. clanId: %s, clanName: %s", clanId, clanName)
        self:clientRpc("OnSearchClan", clanId, {})
        return
    end

    self:serverRpc(clanMb, "QueryDetail", self:makeCallback("OnQueryClanDetailInfo", clanId))
end

function ClanComp:OnQueryClanDetailInfo(clanId, clanDetailInfo)
    self:clientRpc("OnSearchClan", clanId, clanDetailInfo)
end

---@param clanList table 世族ID列表, default: {}
---@param clanName table 世族名称(支持模糊搜索), default: { name = "" }
function ClanComp:SearchMultiClan(clanList, clanNameSearch)
    if clanNameSearch.name ~= "" then
        clanList = {}
        local clanRange = self:filterCurrPrefClans(clanNameSearch.isCurrComm)
        local clanData = GlobalDataMgrEnv.instance:get("ClanData") or { clans = {}, clanName2clanId = {}, clanPref2clanIdList = {}, clanComm2clanIdList = {} }
        for clanName, clanId in pairs(clanData.clanName2clanId) do
            if string.find(clanName, clanNameSearch.name) and table.contains(clanRange, clanId) then
                table.insert(clanList, clanId)
            end
        end
    end
    self:logInfo("SearchMultiClan. clanList: %v, clanNameSearch: %v", clanList, clanNameSearch)
    self:QueryMultiClan(clanList, function(clanInfoList) self:clientRpc("OnSearchMultiClan", clanInfoList) end)
end

function ClanComp:filterCurrPrefClans(isCurrComm)
    local clanData = GlobalDataMgrEnv.instance:get("ClanData") or { clans = {}, clanName2clanId = {}, clanPref2clanIdList = {}, clanComm2clanIdList = {} }
    local currCommClanList = clanData.clanComm2clanIdList[self.props.birthCommandery] or {}
    local currPrefClanList = clanData.clanPref2clanIdList[self.props.birthplace] or {}
    if isCurrComm then
        return currCommClanList
    else
        local clanList = {}
        for _, clanId in ipairs(currPrefClanList) do
            if not table.contains(currCommClanList, clanId) then
                table.insert(clanList, clanId)
            end
        end
        return clanList
    end
end

function ClanComp:DonateClan(donateCnt)
    if self.props.clanId == 0 then
        self:logError("DonateClan Fail. Not In Clan. donateCnt: %s", donateCnt)
        self:clientRpc("OnDonateClan", false)
        return
    end

    if donateCnt <= 0 then
        self:logError("DonateClan Fail. DonateCnt Error. donateCnt: %s", donateCnt)
        self:clientRpc("OnDonateClan", false)
        return
    end

    local currency = GameCommon.TableDataManager:GetClanConst("CLAN_DONATE_CURRENCY")
    if not self.avatar or not self.avatar:ConsumeFromBag({ [currency] = donateCnt }, TableConst.enums.Reason.CLAN_DONATE) then
        self:logError("DonateClan Fail. Consume Currency Fail. donateCnt: %s", donateCnt)
        self:clientRpc("OnDonateClan", false)
        return
    end

    self:logInfo("DonateClan. donateCnt: %s", donateCnt)
    self:AddClanContribution(donateCnt, donateCnt)
    self:clientRpc("OnDonateClan", true)
end

function ClanComp:OccupyAddClanContribution(level)
    if self.props.clanId == 0 then
        return
    end

    if not level or level <= 0 then
        return
    end
    self:logInfo("OccupyAddClanContribution. level: %s", level)
    self:AddClanContribution(math.floor(level * GameCommon.TableDataManager:GetClanConst("CLAN_OCCUPY_LAND_CONTRIBUTION_RATE")), 0)
end

function ClanComp:BattleAddClanContribution(addMerit)
    if self.props.clanId == 0 then
        return
    end

    if addMerit <= 0 then
        return
    end
    self:logInfo("BattleAddClanContribution. addMerit: %s", addMerit)
    self:AddClanContribution(math.floor(addMerit * GameCommon.TableDataManager:GetClanConst("CLAN_BATTLE_CONTRIBUTION_RATE")), 0)
end

function ClanComp:AddClanContribution(cnt, donateCnt)
    if cnt <= 0 then
        return
    end

    local clanMb = self:findClanMbByClanId(self.props.clanId)
    if not clanMb then
        self:logError("AddClanContribution Fail. Cannot Find Clan Mailbox. clanId: %s, cnt: %s, donateCnt: %s", self.props.clanId, cnt, donateCnt)
        return
    end

    self:logInfo("AddClanContribution. clanId: %s, cnt: %s, donateCnt: %s", self.props.clanId, cnt, donateCnt)
    self:serverRpc(clanMb, "AddContribution", self.props.gid, cnt, donateCnt)
end

function ClanComp:OnSyncClanCityLandProduce(adds)
    self:SetResProduceAddition(TableConst.enums.ProduceSrcType.CityLand, adds)
end

function ClanComp:UpdateClanAvatarInfo(updateInfo, flush)
    for k, v in pairs(updateInfo) do
        self.updateClanAvatarInfoCache[k] = v
    end

    if flush then
        self:doUpdateClanAvatarInfo()
    else
        if not self.updateClanAvatarInfoTimer then
            self.updateClanAvatarInfoTimer = self:addTimer(30, 0, function() self:doUpdateClanAvatarInfo() end)
        end
    end
end

function ClanComp:doUpdateClanAvatarInfo()
    if self.updateClanAvatarInfoTimer then
        self:delTimer(self.updateClanAvatarInfoTimer)
        self.updateClanAvatarInfoTimer = nil
    end

    if table.isempty(self.updateClanAvatarInfoCache) then
        return
    end

    if self.props.clanId > 0 then
        self:doUpdateOneClanAvatarInfo(self.props.clanId)
    else
        for _, clanId in ipairs(self.props.appliedClanList) do
            self:doUpdateOneClanAvatarInfo(clanId)
        end
        self:doUpdateInviteServiceAvatarInfo()
    end

    self.updateClanAvatarInfoCache = {}
end

function ClanComp:doUpdateOneClanAvatarInfo(clanId)
    local clanMb = self:findClanMbByClanId(clanId)
    if not clanMb then
        self:logError("doUpdateOneClanAvatarInfo Fail. Cannot Find Clan Mailbox. clanId: %s", clanId)
        return
    end

    self:logInfo("doUpdateOneClanAvatarInfo. clanId: %s, info: %v", clanId, self.updateClanAvatarInfoCache)
    self:serverRpc(clanMb, "UpdateAvatarInfo", self.props.gid, self.updateClanAvatarInfoCache)
end

function ClanComp:doUpdateInviteServiceAvatarInfo()
    local updateInfo = {}
    for _, field in ipairs({ "birthCommandery", "prosperity" }) do
        if self.updateClanAvatarInfoCache[field] then
            updateInfo[field] = self.updateClanAvatarInfoCache[field]
        end
    end

    if not table.isempty(updateInfo) then
        self:logInfo("doUpdateInviteServiceAvatarInfo. updateInfo: %v", updateInfo)
        self:serviceRpc("ClanInviteService", "UpdateAvatarInfo", self.props.gid, updateInfo)
    end
end

function ClanComp:OnModifyBirthPlaceNotifyClan(oriBirthCommandery, oriBirthplace, oriBelongState)
    if oriBirthCommandery ~= self.props.birthCommandery then
        self:UpdateClanAvatarInfo({ birthCommandery = self.props.birthCommandery }, true)
    end
    if oriBirthplace ~= self.props.birthplace and self.props.clanId == 0 then
        self:clearClanApplyInvite()
    end
end

-- 能否操作攻城相关，仅盟主和副盟主可以
function ClanComp:ValidModifySiege()
    return true
end

--region gm指令

function ClanComp:gmJoinClan(clanId)
    if self.props.clanId == clanId then
        return Config.Ret.SUCCESS
    end

    if self.props.clanId > 0 then
        self:NotifyGmListenEvent(TableConst.enums.GMEvent.LeaveClan)
        local ret = coroutine.yield(self:LeaveClan())
        if ret ~= Config.Ret.SUCCESS then
            return ret
        end
    end

    self:NotifyGmListenEvent(TableConst.enums.GMEvent.ApplyClan)
    self.props.enterLeaveStamp = 0
    return coroutine.yield(self:ApplyClan(clanId, "GM"))
end

function ClanComp:gmRegisterMultiVirtualAvatar()
    for i = 1, 100 do
        local baseInfo = self:getClanInviteAvatarSortInfo()
        baseInfo.prosperity = baseInfo.prosperity + i
        baseInfo.stamp = 0
        self:serviceRpc("ClanInviteService", "RegisterAvatar", 200 + i, baseInfo)
    end
end

--endregion gm指令