﻿local EntityEnv = xrequire("Framework.Entities.Entity")
local ActorOnCallCompEnv = xrequire("WorldActors.CommonComps.ActorOnCallComp")
local AnyEntityCallerCompEnv = xrequire("Entities.CommonComps.AnyEntityCallerComp")
local WorldActorUtilsEnv = xrequire("Common.Actor.WorldActorUtils")

local ServerTimeEnv = xrequire("Utils.ServerTime")
local CoordUtils = xrequire("Common.Utils.CoordUtils")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")

---@class (partial) WorldActor: SEntity, LuaEntityClass, CommonWorldActor, ActorOn<PERSON>allComp, AnyEntityCallerComp
---@field space WorldSpace?
---@field props WorldActorProps
---@field _actorType int
WorldActor = DefineEntity("WorldActor", {EntityEnv.Entity}, {
    WorldActorUtilsEnv.CommonWorldActor,  ---@type CommonWorldActor
    ActorOnCallCompEnv.ActorOnCallComp,  ---@type ActorOnCallComp
    AnyEntityCallerCompEnv.AnyEntityCallerComp,  ---@type AnyEntityCallerComp
})
WorldActor._persistent = false  ---@type boolean

---@diagnostic disable-next-line: duplicate-set-field
function WorldActor:ctor()
    ---@diagnostic disable-next-line
    assert(self._actorType, "actorType is nil")
    ---@diagnostic disable-next-line
    self:createSpaceActor()
    self.isInEngineSpace = false  ---@type boolean
    self.isInSpaceGrid = false  ---@type boolean
    self.destroyMark = false  ---@type boolean
    -- TODO(qun): 如果想等到world初始化完成后开启actor存盘，可以考虑在这里先删除autoSaveTimer
    self:actorSave()
end

function WorldActor:dftStartSave()
    return false
end

---@diagnostic disable-next-line: duplicate-set-field
function WorldActor:onDestroy()
    -- 需要走DestroyBy销毁actor，否则会导致时序异常，此时space引用未解除
    if not EZGlobal.IsClusterClosing then
        assert(not self.space)
    end
end

function WorldActor:SetDoubledPos2D(col, row)
    ---@cast self AvatarOwningComp | AllyOwningViewComp
    local oldCol, oldRow = self:GetDoubledPos2D()
    local x, y = CoordUtils.Doubled2Offset(col, row)
    self:setPos2D(x, y)
    self.props.pos = {  -- 仅存盘用于加载Actor
        x = col,
        y = row,
    }
    if self.HasAvatarOwningComp then
        self:syncActorView({
            pos = self:GetDoubledPos2DV(),
        })
    end
    if self.HasAllyOwningViewComp then
        self:DelaySyncAllyView()
    end
    return oldCol, oldRow
end

function WorldActor:OnPosChanged(oldX, oldY)
    ---@cast self MoveComp | FollowMoveComp
    -- SetPos不会直接调用 OnPosChanged的原因是要精准控制回调时机
    -- 举例：移动结束后应该先回调OnMoveEnd，然后回调OnPosChanged触发战斗
    if self.isInSpaceGrid then
        assert(self.space)
        self.space:OnActorPosChanged(self, oldX, oldY)
    end
    if self.HasMoveComp or self.HasFollowMoveComp then
        self:SetPosChangeTime()
    end
end

function WorldActor:SetStatueEndLeftTime(duration)
    ---@cast self AvatarOwningComp
    -- TODO(qun): 这个状态持续时间，要和状态绑定，一起设置和清理
    self.props.statusEndTime = ServerTimeEnv.GetServerNow() + duration
    if self.HasAvatarOwningComp then
        self:syncActorView({
            statusEndTime = self.props.statusEndTime,
        })
    end
end

function WorldActor:actorSave(callback)
    if self._persistent then
        if self:getDBID() == "" then
            assert(self.props.uid and self.props.uid ~= "")
            self:saveWithDBID(self.props.uid, callback)
        end
    end
end

function WorldActor:DestroyBy(srcEntId, reason, removed, callback)
    ---@cast self ActorContentComp
    self:logInfo("destroy by entity(%s) with reason(%s)", srcEntId, reason)
    if callback and callback.func ~= "" then
        self:callbackRpc(callback)
    end
    self.destroyMark = true  -- 标记一下即将销毁，让其他逻辑能感知到
    if self.space then
        --[[Actor销毁流程实现说明
        actor的多数业务逻辑依赖space，在销毁清理的过程中，各个组件也需要能拿到space引用
        而space的引用解除需要保证在底层销毁前调用，避免相关逻辑异常
        因此时许为 调用销毁-> 触发组件的销毁-> 解除actor和space的引用-> 调用底层销毁
        目前只能通过新增Event来确保该时序
        --]]
        self:fireEntityEvent("OnActorDestroy")
        if self.HasActorContentComp then
            self:OnActorDestroyLeaveContainer()
        end
        self.space:onActorLeave(self)
    end
    self:destroy(removed)
end

function WorldActor:DelayDestroyBy(srcEntId, reason, removed, callback)
    self:logInfo("delay destroy by entity(%s) with reason(%s)", srcEntId, reason)
    self:addTimer(0, 0, function()
        self:DestroyBy(srcEntId, reason, removed, callback)
    end)
end

function WorldActor:GetRelationProps()
    return {
        gid = self.props.gid,
        allyId = self.props.allyId,
        clanId = self.props.clanId,
    }
end

function WorldActor:HasBelonging()
    return self.props.gid ~= 0 or self.props.allyId ~= 0
end

--region 进入和离开space，与space互相绑定或解绑属性引用

function WorldActor:onEnterSpace() end

function WorldActor:onLeaveSpace() end

--endregion

--region 进出引擎space，导致aoi可见性变化

function WorldActor:isVisible()
    -- actor是否进入aoi可被玩家看见
    return true
end

function WorldActor:CheckVisible()
    if not self.space then
        return
    end
    if not self.destroyMark and self:isVisible() then
        if self.isInEngineSpace then
            return
        end
        self:enterSpace(self.space)
        self.isInEngineSpace = true
    else
        if not self.isInEngineSpace then
            return
        end
        self:leaveSpace()
        self.isInEngineSpace = false
    end
end

--endregion

--region 进出格子，导致与其他actor可交互性变化

---@return boolean
function WorldActor:isInteractable()
    -- actor是否进入格子可被其他actor交互
    return false
end

function WorldActor:CheckInteractable()
    if not self.space then
        return
    end
    if self:isInteractable() then
        if self.isInSpaceGrid then
            return
        end
        self.space:RegisterGridActor(self)
        self.isInSpaceGrid = true
    else
        if not self.isInSpaceGrid then
            return
        end
        self.space:UnregisterGridActor(self)
        self.isInSpaceGrid = false
    end
end

--endregion

--region MapReady

function WorldActor:waitMapReady(cb)
    assert(self.space)
    self.space:waitMapReady(self, cb)
end

function WorldActor:tryHoldLoadFinishComfirm()
    assert(self.space)
    local holdId = self.space:tryHoldLoadFinishComfirm()
    if not holdId then
        return
    end
    return EntCb("confirmLoadFinishHolder", holdId)
end

function WorldActor:confirmLoadFinishHolder(holdId)
    assert(self.space)
    self.space:confirmLoadFinishHolder(holdId)
end

--endregion

---@return WorldActor|any|nil
function WorldActor:getActor(uid)
    return self.space and self.space:getActor(uid)
end

---@return WorldActor|any|nil
function WorldActor:getActorByEid(eid)
    return self.space and self.space:getActorByEid(eid)
end

function WorldActor:OnSyncAlly(allyId)
    if self.props.allyId == allyId then
        return
    end
    local oriAllyId = self.props.allyId
    self.props.allyId = allyId
    self:fireEntityEvent("OnAllyChange", oriAllyId)
end

function WorldActor:OnSyncClan(clanId)
    if self.props.clanId == clanId then
        return
    end
    local oriClanId = self.props.clanId
    self.props.clanId = clanId
    self:fireEntityEvent("OnClanChange", oriClanId)
end

function WorldActor:OnSyncName(name)
    self:logInfo("OnSyncName. name: %s", name)
    self.props.name = name
    self:fireEntityEvent("OnNameChange")
end

---@return boolean
function WorldActor:ValidBehaviorToActor(acotr, interactiveBehavior)
    return BehaviorJudgeEnv.ValidBehaviorTo(self.props, acotr.props, self._actorType, acotr._actorType, interactiveBehavior)
end

function WorldActor:MonitorGetProps(callback)
    local packedProps = EZE.msgpackPack({
        id = self.id,
        class = self.__cname,
        actorType = self._actorType,
        props = self.props,
    })
    self:callbackRpc(callback, packedProps)
end

-- for emmylua
return {WorldActor=WorldActor}