{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"consts": {"times": {"VariableRangeType": 1, "ResetOnExecute": false, "Type": "number", "Value": 0}}, "event1": {"Type": "number", "Value": "107"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}, "event2": {"Type": "number", "Value": "106"}, "eventPriority2": {"Type": "number", "Value": "200"}, "onlySelf2": {"Type": "boolean", "Value": "True"}, "event3": {"Type": "number", "Value": "5"}, "eventPriority3": {"Type": "number", "Value": "0"}, "onlySelf3": {"Type": "boolean", "Value": "False"}}}, "1": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "司马懿重判概率，a=智力"}, "result": {"Type": "number", "Value": "0"}}}, "4": {"Type": "UseTacticNode", "Field": {"casterIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "tacticId": {"Type": "number", "BlackboardValue": "InputTacticId"}, "needProb": {"Type": "boolean", "Value": "True"}, "extraProb": {"Type": "number", "Value": "0"}, "notNeedCarried": {"Type": "boolean", "Value": "False"}, "argNames": [], "argsCount": {"Type": "number", "Value": "0"}, "argName0": {"Type": "string", "Value": ""}, "argValue0": {"Type": "string", "Value": ""}, "argName1": {"Type": "string", "Value": ""}, "argValue1": {"Type": "string", "Value": ""}, "argName2": {"Type": "string", "Value": ""}, "argValue2": {"Type": "string", "Value": ""}, "argName3": {"Type": "string", "Value": ""}, "argValue3": {"Type": "string", "Value": ""}, "argName4": {"Type": "string", "Value": ""}, "argValue4": {"Type": "string", "Value": ""}, "argName5": {"Type": "string", "Value": ""}, "argValue5": {"Type": "string", "Value": ""}, "argName6": {"Type": "string", "Value": ""}, "argValue6": {"Type": "string", "Value": ""}, "argName7": {"Type": "string", "Value": ""}, "argValue7": {"Type": "string", "Value": ""}, "argName8": {"Type": "string", "Value": ""}, "argValue8": {"Type": "string", "Value": ""}, "argName9": {"Type": "string", "Value": ""}, "argValue9": {"Type": "string", "Value": ""}}}, "6": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "buffId": {"Type": "number", "Value": "11100402"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "8": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "SourceId"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "2"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "10": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "11": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "5"}, "number2": {"Type": "number", "Value": "0"}}}, "12": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "1"}, "key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "14": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "0"}, "key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "17": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "True"}, "tacticId": {"Type": "number", "BlackboardValue": "InputTacticId"}, "isBornWith": {"Type": "boolean", "Value": "True"}, "validTypes": [{"Type": "number", "Value": "1"}, {"Type": "number", "Value": "3"}], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}, "18": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "SourceId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "True"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "True"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "2"}, "selectType": {"Type": "number", "Value": "2"}, "random": {"Type": "boolean", "Value": "True"}}}, "20": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "buffId": {"Type": "number", "Value": "11100403"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "22": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "True"}, "tacticId": {"Type": "number", "BlackboardValue": "InputTacticId"}, "isBornWith": {"Type": "boolean", "Value": "True"}, "validTypes": [{"Type": "number", "Value": "1"}, {"Type": "number", "Value": "3"}], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}}, "Links": {"0": {"TacticProbFailedNode": ["17.prev"], "AfterUseTacticNode": ["22.prev"], "RoundPrepareNode": ["14.prev"]}, "1": {"next": ["12.prev"]}, "8": {"next": ["1.prev"]}, "10": {"next": ["11.prev"]}, "11": {"next": ["8.prev"]}, "12": {"next": ["4.prev"]}, "17": {"next": ["10.prev"]}, "18": {"elseNode": ["20.prev"], "next": ["6.prev"]}, "22": {"next": ["18.prev"]}}, "DataFlows": {"1": {"result": ["4.extra<PERSON>rob"]}, "8": {"attributeValues": ["1.a"]}, "10": {"output": ["11.number1"]}}}