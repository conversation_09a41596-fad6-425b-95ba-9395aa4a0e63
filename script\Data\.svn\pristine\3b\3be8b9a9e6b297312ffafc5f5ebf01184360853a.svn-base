{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "3": {"Type": "PlainAttackNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetId": {"Type": "string", "Value": ""}, "ignoreCrowdControl": {"Type": "boolean", "Value": "True"}}}, "5": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "True"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "2"}, "random": {"Type": "boolean", "Value": "True"}}}, "6": {"Type": "PlainAttackNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetId": {"Type": "string", "Value": ""}, "ignoreCrowdControl": {"Type": "boolean", "Value": "True"}}}, "7": {"Type": "PlainAttackNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetId": {"Type": "string", "Value": ""}, "ignoreCrowdControl": {"Type": "boolean", "Value": "True"}}}, "8": {"Type": "PlainAttackNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetId": {"Type": "string", "Value": ""}, "ignoreCrowdControl": {"Type": "boolean", "Value": "True"}}}, "9": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [], "buffId": {"Type": "number", "Value": "11200501"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}}, "Links": {"0": {"next": ["1.prev"]}, "1": {"next": ["5.prev"]}, "3": {"next": ["6.prev"]}, "5": {"next": ["9.prev"]}, "7": {"next": ["8.prev"]}, "9": {"next": ["3.prev"]}}, "DataFlows": {"1": {"targetIds": ["3.targetId", "6.sourceId", "7.targetId", "8.sourceId"]}, "5": {"targetIds": ["3.sourceId", "6.targetId", "7.sourceId", "8.targetId", "9.targetIds"]}}}