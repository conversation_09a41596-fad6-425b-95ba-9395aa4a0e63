﻿local RootNodeServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.RootNodeServer")
local BattleConstEnv = xrequire("Common.Battle.BattleConst")

---@class ChangeSelectTargetNodeServer : NodeBase
local ChangeSelectTargetNodeServer = DefineClass("ChangeSelectTargetNodeServer", RootNodeServerEnv.RootNodeServer)

function ChangeSelectTargetNodeServer:Execute(graphData)
    local heroIds = graphData:GetProperty(self, "heroIds")
    local changeTargetRangeType = graphData:GetProperty(self, "changeTargetRangeType")
    local targetId = graphData:GetProperty(self, "targetId")
    local probability = graphData:GetProperty(self, "probability")
    local selectPriority = graphData:GetProperty(self, "selectPriority")
    local count = graphData:GetProperty(self, "count")
    if not count then
        count = -1
    end
    local buffId = graphData.battleBuff.buffId
    assert(buffId ~= nil)

    for _, heroId in ipairs(heroIds) do
        local hero = graphData.caster.battleGame.heroUniqueIdMap[heroId]
        local data = {
            targetId = targetId,
            changeTargetRangeType = changeTargetRangeType,
            probability = probability,
            selectPriority = selectPriority,
            useCount = count > 0,
            count = count,
            buffId = buffId
        }
        if changeTargetRangeType == BattleConstEnv.ChangeTargetRangeType.ByTacticType then
            local tacticTypes = graphData:GetProperty(self, "tacticTypes")
            data.tacticTypes = {}
            for _, tacticType in ipairs(tacticTypes) do
                data.tacticTypes[tacticType] = true
            end
        elseif changeTargetRangeType == BattleConstEnv.ChangeTargetRangeType.ByTacticId then
            local tacticIds = graphData:GetProperty(self, "tacticIds")
            data.tacticIds = {}
            for _, tacticId in ipairs(tacticIds) do
                data.tacticIds[tacticId] = true
            end
        end
        local key = hero:AddPriorSelectTarget(data)
        if graphData.buffLayer then
            local buffLayer = graphData.buffLayer
            table.insert(buffLayer.priorSelectTargetKeys, key)
        end
    end

end


