[{"name": "EntityId", "type": "int"}, {"name": "<PERSON><PERSON>", "type": "string"}, {"name": "Mailbox", "type": "string"}, {"name": "HttpSessionId", "type": "int"}, {"name": "EntityCallback", "type": "any"}, {"name": "AvatarGid", "type": "int"}, {"name": "AllyId", "type": "int"}, {"name": "ClanId", "type": "int"}, {"name": "WorldId", "type": "int"}, {"name": "TemplateHeroId", "type": "int"}, {"name": "CompleteHeroUid", "type": "string"}, {"name": "Health", "type": "int"}, {"name": "BattleCamp", "type": "int"}, {"name": "NpcTeamId", "type": "int"}, {"name": "ActorTypeEnum", "type": "int"}, {"name": "ArmySlotIdx", "type": "int"}, {"name": "UnitIdx", "type": "int"}, {"name": "ArmyType", "type": "int"}, {"name": "HomeBuildingId", "type": "int"}, {"name": "TaskId", "type": "int"}, {"name": "StrategyId", "type": "int"}, {"name": "PackedPos", "type": "int"}, {"name": "PackedPosList", "type": "list", "element": "PackedPos"}, {"name": "ItemId", "type": "int"}, {"name": "TokenId", "type": "int"}, {"name": "HeroId", "type": "int"}, {"name": "EquipUid", "type": "<PERSON><PERSON>"}, {"name": "HorseUid", "type": "<PERSON><PERSON>"}, {"name": "ReasonEnum", "type": "int"}, {"name": "Prefecture", "type": "int"}, {"name": "Commandery", "type": "int"}, {"name": "County", "type": "int"}, {"name": "CityId", "type": "int"}, {"name": "Career", "type": "int"}, {"name": "HeadPic", "type": "int"}, {"name": "Head<PERSON><PERSON><PERSON>", "type": "int"}, {"name": "InstanceId", "type": "int"}, {"name": "SiegeGroupIdx", "type": "int"}, {"name": "PeriodicData", "type": "struct", "elements": [{"name": "sum", "type": "int"}, {"name": "period", "type": "int"}]}, {"name": "PeriodicDataSummary", "type": "dict", "key": "int", "value": "PeriodicData"}, {"name": "Merit", "type": "int"}, {"name": "MeritSummary", "type": "PeriodicDataSummary"}, {"name": "ItemToNum", "type": "dict", "key": "ItemId", "value": "int"}, {"name": "TypeToItemDict", "type": "dict", "key": "int", "value": "ItemToNum"}, {"name": "ProduceToFloatNum", "type": "dict", "key": "int", "value": "float"}, {"name": "Switches", "type": "dict", "key": "string", "value": "bool"}, {"name": "AABB", "type": "struct", "elements": [{"name": "minX", "type": "float"}, {"name": "minZ", "type": "float"}, {"name": "maxX", "type": "float"}, {"name": "maxZ", "type": "float"}]}, {"name": "Position", "type": "struct", "class": "Vector2", "elements": [{"name": "x", "type": "float"}, {"name": "y", "type": "float"}]}, {"name": "DoubledPosition", "type": "Position"}, {"name": "OffsetPosition", "type": "Position"}, {"name": "PackedPosition", "type": "int"}, {"name": "PositionList", "type": "list", "element": "Position"}, {"name": "MainCityView", "type": "struct", "elements": [{"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "id", "type": "EntityId"}, {"name": "pos", "type": "Position"}, {"name": "level", "type": "int"}]}, {"name": "Relation", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "allyId", "type": "AllyId"}, {"name": "clanId", "type": "ClanId"}]}, {"name": "SlotSoldierNum", "type": "dict", "key": "UnitIdx", "value": "int"}, {"name": "GridType", "type": "int"}, {"name": "OneLandProduceAdd", "type": "struct", "elements": [{"name": "buffId", "type": "int"}, {"name": "percent", "type": "float"}, {"name": "timeout", "type": "float"}]}, {"name": "OneLandProduceAddList", "type": "list", "element": "OneLandProduceAdd"}, {"name": "BaseOccupation", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "allyId", "type": "AllyId"}, {"name": "name", "type": "string"}]}, {"name": "GridOccupation", "type": "struct", "inherit": "BaseOccupation", "elements": [{"name": "occupyTime", "type": "float", "aoi_level_flag": "0b01"}, {"name": "levelAddTo", "type": "int", "aoi_level_flag": "0b01"}, {"name": "produceBuffs", "type": "OneLandProduceAddList", "aoi_level_flag": "0b01"}]}, {"name": "GridBuilding", "type": "struct", "inherit": "BaseOccupation", "elements": [{"name": "buildingUid", "type": "<PERSON><PERSON>", "flag": "SERVER_ONLY"}, {"name": "tp", "type": "GridType"}, {"name": "isCenter", "type": "bool"}]}, {"name": "InGridOrderDetail", "type": "struct", "elements": [{"name": "matchGroupType", "type": "int"}, {"name": "order", "type": "int"}, {"name": "isSurrounding", "type": "bool", "default": null}]}, {"name": "InGridOrder", "type": "dict", "key": "PackedPos", "value": "InGridOrderDetail"}, {"name": "IntList", "type": "list", "element": "int"}, {"name": "StringList", "type": "list", "element": "string"}, {"name": "StrStrDict", "type": "dict", "key": "string", "value": "string"}, {"name": "StrBoolDict", "type": "dict", "key": "string", "value": "bool"}, {"name": "IntIntDict", "type": "dict", "key": "int", "value": "int"}, {"name": "IntFloatDict", "type": "dict", "key": "int", "value": "float"}, {"name": "IntBoolDict", "type": "dict", "key": "int", "value": "bool"}, {"name": "UidIntDict", "type": "dict", "key": "<PERSON><PERSON>", "value": "int"}, {"name": "UidStrDict", "type": "dict", "key": "<PERSON><PERSON>", "value": "string"}, {"name": "UidList", "type": "list", "element": "<PERSON><PERSON>"}, {"name": "HeroList", "type": "list", "element": "HeroId"}, {"name": "AvatarGidList", "type": "list", "element": "AvatarGid"}, {"name": "AvatarFieldList", "type": "IntList"}, {"name": "ArmyTypeList", "type": "list", "element": "ArmyType"}, {"name": "TacticId", "type": "int"}, {"name": "TacticData", "type": "struct", "elements": [{"name": "id", "type": "TacticId"}, {"name": "level", "type": "int"}, {"name": "stars", "type": "int"}]}, {"name": "Tactic", "type": "struct", "elements": [{"name": "id", "type": "TacticId"}, {"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "level", "type": "int"}, {"name": "stars", "type": "int"}]}, {"name": "EquipEntry", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "value", "type": "float"}]}, {"name": "EquipEffect", "type": "struct", "elements": [{"name": "id", "type": "int"}]}, {"name": "EquipEntriesList", "type": "list", "element": "EquipEntry"}, {"name": "EquipForge", "type": "struct", "elements": [{"name": "uid", "type": "EquipUid", "default": null}, {"name": "waitChooseEntries", "type": "EquipEntriesList"}]}, {"name": "EquipEffectsList", "type": "list", "element": "EquipEffect"}, {"name": "EquipRebuild", "type": "struct", "elements": [{"name": "uid", "type": "EquipUid", "default": null}, {"name": "waitChooseEffect", "type": "EquipEffectsList"}]}, {"name": "ForgeHistory", "type": "struct", "elements": [{"name": "continuousDecCnt", "type": "int"}, {"name": "historyBest", "type": "int"}]}, {"name": "Equip", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "entries", "type": "EquipEntriesList"}, {"name": "effects", "type": "EquipEffectsList"}, {"name": "forgeHistory", "type": "ForgeHistory"}, {"name": "heroId", "type": "HeroId"}]}, {"name": "EquipBag", "type": "dict", "key": "EquipUid", "value": "Equip"}, {"name": "EquipUidList", "type": "list", "element": "EquipUid"}, {"name": "MailAddEquip", "type": "dict", "key": "EquipUid", "value": "Equip"}, {"name": "Horse", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "entries", "type": "EquipEntriesList"}, {"name": "effects", "type": "EquipEffectsList"}, {"name": "heroId", "type": "HeroId"}]}, {"name": "HorseBag", "type": "dict", "key": "HorseUid", "value": "Horse"}, {"name": "HorseUidList", "type": "list", "element": "HorseUid"}, {"name": "MailAddHorse", "type": "dict", "key": "HorseUid", "value": "Horse"}, {"name": "HorseTraining", "type": "struct", "elements": [{"name": "horse", "type": "Horse"}, {"name": "trainingEndTime", "type": "float"}]}, {"name": "ArmyUsageDetail", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "tactics", "type": {"type": "dict", "key": "int", "value": "TacticId"}}]}, {"name": "ArmyUsage", "type": "dict", "key": "int", "value": "ArmyUsageDetail"}, {"name": "HeroStaminaDetail", "type": "struct", "elements": [{"name": "val", "type": "int", "default": 200}, {"name": "lastRecover", "type": "float"}]}, {"name": "ArmyStaminaDetail", "type": "struct", "inherit": "HeroStaminaDetail", "elements": [{"name": "max", "type": "int", "default": 200}]}, {"name": "HeroAddPointsDict", "type": "dict", "key": "int", "value": "int"}, {"name": "HeroEquipAddData", "type": "struct", "elements": [{"name": "points", "type": "HeroAddPointsDict"}, {"name": "expAddPercent", "type": "int", "default": 0}]}, {"name": "HeroTacticData", "type": "dict", "key": "int", "value": "TacticData"}, {"name": "IdentitySlots", "type": "dict", "key": "int", "value": "int"}, {"name": "HeroData", "type": "struct", "elements": [{"name": "id", "type": "HeroId"}, {"name": "level", "type": "int"}, {"name": "exp", "type": "int"}, {"name": "staminaDetail", "type": "HeroStaminaDetail"}, {"name": "injured<PERSON><PERSON>", "type": "float"}, {"name": "stars", "type": "int"}, {"name": "tactics", "type": "HeroTacticData"}, {"name": "<PERSON><PERSON><PERSON>", "type": "int", "default": 1}, {"name": "soldierMax", "type": "int"}, {"name": "recoverableSoldierNum", "type": "int"}, {"name": "addPoints", "type": "HeroAddPointsDict"}, {"name": "equips", "type": {"type": "list", "element": "Equip"}}, {"name": "horses", "type": {"type": "list", "element": "Horse"}}, {"name": "identitySlots", "type": "IdentitySlots"}]}, {"name": "HeroDataDict", "type": "dict", "key": "int", "value": "HeroData"}, {"name": "Hero", "type": "struct", "elements": [{"name": "id", "type": "HeroId"}, {"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "level", "type": "int"}, {"name": "stars", "type": "int"}, {"name": "tactics", "type": {"type": "dict", "key": "int", "value": "TacticId"}}]}, {"name": "BattleAttritubes", "type": "dict", "key": "int", "value": "any"}, {"name": "BattleHero", "type": "struct", "elements": [{"name": "heroId", "type": "HeroId"}, {"name": "level", "type": "int"}, {"name": "exp", "type": "int"}, {"name": "stars", "type": "int"}, {"name": "dynasty", "type": "int"}, {"name": "selfTactics", "type": "any"}, {"name": "carryingTactics", "type": "any"}, {"name": "attributes", "type": "BattleAttritubes"}, {"name": "equips", "type": {"type": "list", "element": "Equip"}}, {"name": "horses", "type": {"type": "list", "element": "Horse"}}, {"name": "identitySlots", "type": "IdentitySlots"}]}, {"name": "BattleArmyData", "type": "struct", "elements": [{"name": "heroes", "type": {"type": "dict", "key": "int", "value": "BattleHero"}}, {"name": "armyType", "type": "ArmyType"}, {"name": "isDirty", "type": "bool", "default": true}]}, {"name": "ArmyPreorderSiege", "type": "struct", "elements": [{"name": "siegeCamp", "type": "EntityId"}, {"name": "isMainForce", "type": "bool"}, {"name": "timeout", "type": "float"}]}, {"name": "ArmyInAutoPath", "type": "struct", "elements": [{"name": "index", "type": "int"}, {"name": "path", "type": "PositionList"}]}, {"name": "MainCityData", "type": "struct", "elements": [{"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "position", "type": "Position"}, {"name": "entityId", "type": "EntityId"}, {"name": "mailbox", "type": "any", "flag": "SERVER_ONLY"}]}, {"name": "ArmyHomeData", "type": "struct", "elements": [{"name": "baseId", "type": "<PERSON><PERSON>"}, {"name": "pos", "type": "Position"}]}, {"name": "ArmyDeployHomeData", "type": "struct", "inherit": "ArmyHomeData", "elements": [{"name": "arrived", "type": "bool", "default": false, "flag": "OWN_CLIENT"}]}, {"name": "ArmySiegeHomeData", "type": "struct", "inherit": "ArmyDeployHomeData", "elements": [{"name": "isMainForce", "type": "bool"}, {"name": "assaultRecord", "type": "string", "default": null, "persistent": false}]}, {"name": "ArmyBuff", "type": "struct", "elements": [{"name": "value", "type": "float"}, {"name": "buffId", "type": "int"}, {"name": "level", "type": "int"}, {"name": "isPercentage", "type": "bool"}, {"name": "effect_cnt", "type": "int"}, {"name": "heroOnly", "type": "HeroId", "default": null}]}, {"name": "ArmyBuffDict", "type": "dict", "key": "string", "value": "ArmyBuff"}, {"name": "ArmyBuffDictBySrc", "type": "dict", "key": "int", "value": "ArmyBuffDict"}, {"name": "BattleTeam", "type": "struct", "elements": [{"name": "eid", "type": "EntityId"}, {"name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"name": "gid", "type": "AvatarGid"}, {"name": "allyId", "type": "AllyId"}, {"name": "clanId", "type": "ClanId"}, {"name": "camp", "type": "int"}, {"name": "armySlotIdx", "type": "int"}, {"name": "buffs", "type": "ArmyBuffDictBySrc"}, {"name": "supply", "type": "int"}, {"name": "representHeroId", "type": "HeroId", "default": null}, {"name": "npcDataId", "type": "int", "default": null}, {"name": "battleArmyData", "type": "BattleArmyData", "default": null}]}, {"name": "GuidDict", "type": "dict", "key": "BattleCamp", "value": "<PERSON><PERSON>"}, {"name": "BattleInfo", "type": "struct", "elements": [{"name": "position", "type": "PackedPosition"}, {"name": "gridName", "type": "string"}, {"name": "battleType", "type": "int"}, {"name": "guid", "type": "GuidDict"}, {"name": "fastBattle", "type": "bool"}, {"name": "landLevel", "type": "int", "default": null}, {"name": "npcPoolId", "type": "int", "default": null}, {"name": "maxStaminaCost", "type": "int", "default": null}, {"name": "hasStaminaCost", "type": "bool", "default": null}, {"name": "drillLevel", "type": "int", "default": null}]}, {"name": "TeamHeroesHealth", "type": "dict", "key": "int", "value": "Health"}, {"name": "BattleResult", "type": "struct", "elements": [{"name": "success", "type": "bool"}, {"name": "winCamp", "type": "int"}, {"name": "heroesHealth", "type": {"type": "dict", "key": "BattleCamp", "value": "TeamHeroesHealth"}}, {"name": "teamConsumeHealth", "type": {"type": "dict", "key": "BattleCamp", "value": "Health"}}, {"name": "heroesRecoverableHealth", "type": {"type": "dict", "key": "BattleCamp", "value": "TeamHeroesHealth"}}, {"name": "addExp", "type": {"type": "dict", "key": "BattleCamp", "value": "IntIntDict"}}, {"name": "addMerit", "type": {"type": "dict", "key": "BattleCamp", "value": "int"}}]}, {"name": "ArmyFormDetails", "type": "struct", "elements": [{"name": "injured<PERSON><PERSON>", "type": "float", "default": null}, {"name": "staminaDetail", "type": "ArmyStaminaDetail", "default": null}, {"name": "armyMinLevel", "type": "int", "default": 5}, {"name": "pvpStaminaCost", "type": "int", "default": null}, {"name": "supplyMax", "type": "int", "default": null}]}, {"name": "CampFormInfo", "type": "dict", "key": "int", "value": "ArmyFormDetails"}, {"name": "AttributeData", "type": "struct", "elements": [{"name": "attack", "type": "int"}, {"name": "defense", "type": "int"}, {"name": "health", "type": "int"}, {"name": "intelligence", "type": "int"}, {"name": "speed", "type": "int"}]}, {"name": "InputsData", "type": "struct", "elements": [{"name": "event", "type": "int"}, {"name": "step", "type": "int"}, {"name": "args", "type": "any"}]}, {"name": "Vector3Int", "type": "struct", "elements": [{"name": "x", "type": "int"}, {"name": "y", "type": "int"}, {"name": "z", "type": "int"}]}, {"name": "Vector3Float", "type": "struct", "elements": [{"name": "x", "type": "float"}, {"name": "y", "type": "float"}, {"name": "z", "type": "float"}]}, {"name": "Tips", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "args", "type": "any"}]}, {"name": "Callback", "type": "struct", "elements": [{"name": "mailbox", "type": "string"}, {"name": "func", "type": "string"}, {"name": "args", "type": "any"}]}, {"name": "CurrencyDetail", "type": "struct", "elements": [{"name": "num", "type": "int"}, {"name": "limit", "type": "int"}]}, {"name": "CurrencyDict", "type": "dict", "key": "int", "value": "CurrencyDetail"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "dict", "key": "int", "value": "int"}, {"name": "HomeAddition", "type": "struct", "elements": [{"name": "armySoldierAdd", "type": "int"}, {"name": "armyBuffTemp", "type": "ArmyBuffDict", "flag": "SERVER_ONLY"}, {"name": "armyFateBuff", "type": "ArmyBuffDict", "flag": "SERVER_ONLY"}, {"name": "armySupplyAdd", "type": "int", "flag": "SERVER_ONLY"}]}, {"name": "WorldBuff", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "timeout", "type": "float"}]}, {"name": "WorldBuffDict", "type": "dict", "key": "string", "value": "WorldBuff"}, {"name": "HeroTacticDict", "type": "dict", "key": "int", "value": "TacticId"}, {"name": "EquipSlots", "type": "dict", "key": "int", "value": "<PERSON><PERSON>"}, {"name": "HeroPackDetail", "type": "struct", "elements": [{"name": "level", "type": "int", "default": 5}, {"name": "exp", "type": "int"}, {"name": "staminaDetail", "type": "HeroStaminaDetail"}, {"name": "injured<PERSON><PERSON>", "type": "float"}, {"name": "stars", "type": "int"}, {"name": "addPoints", "type": "HeroAddPointsDict"}, {"name": "bornWithTacticLevel", "type": "int", "default": 1}, {"name": "tactics", "type": "HeroTacticDict"}, {"name": "equipSlots", "type": "EquipSlots"}, {"name": "mainIdentity", "type": "int"}, {"name": "identitySlots", "type": "IdentitySlots"}, {"name": "inArmy", "type": "<PERSON><PERSON>", "persistent": false}]}, {"name": "HeroPack", "type": "dict", "key": "HeroId", "value": "HeroPackDetail"}, {"name": "TacticPackDetail", "type": "struct", "elements": [{"name": "level", "type": "int", "default": 1}, {"name": "stars", "type": "int"}, {"name": "equipBy", "type": "HeroId", "persistent": false}]}, {"name": "TacticPack", "type": "dict", "key": "TacticId", "value": "TacticPackDetail"}, {"name": "TacticConfig", "type": "struct", "elements": [{"name": "validResetCnt", "type": "int"}]}, {"name": "HomeBuildingDetail", "type": "struct", "elements": [{"name": "level", "type": "int"}]}, {"name": "HomeBuildingDict", "type": "dict", "key": "HomeBuildingId", "value": "HomeBuildingDetail"}, {"name": "ResBuffByRatio", "type": "dict", "key": "int", "value": "float"}, {"name": "ResProduceAddType", "type": "dict", "key": "int", "value": "int"}, {"name": "ResProduceAddSrc", "type": "dict", "key": "int", "value": "ResProduceAddType"}, {"name": "ResProduceBuff", "type": "struct", "elements": [{"name": "addByRatio", "type": "ResBuffByRatio"}]}, {"name": "ResProduceBuffs", "type": "dict", "key": "string", "value": "ResProduceBuff"}, {"name": "ResProduce", "type": "struct", "elements": [{"name": "addition", "type": "ResProduceAddSrc"}, {"name": "lastTime", "type": "float"}, {"name": "produceBuffs", "type": "ResProduceBuffs"}, {"name": "partProduceFloat", "type": "ProduceToFloatNum"}]}, {"name": "SoldierProduce", "type": "struct", "elements": [{"name": "speed", "type": "int"}, {"name": "fastCost", "type": "int"}, {"name": "fast", "type": "bool", "default": false}, {"name": "lastTime", "type": "float"}, {"name": "partProduceFloat", "type": "float"}]}, {"name": "HomeTechs", "type": "dict", "key": "int", "value": "int"}, {"name": "MainTaskDetail", "type": "struct", "elements": [{"name": "satisfied", "type": "bool", "default": false}, {"name": "processForShow", "type": "int"}]}, {"name": "MainTaskDict", "type": "dict", "key": "int", "value": "MainTaskDetail"}, {"name": "CompleteMainTaskDict", "type": "dict", "key": "int", "value": "bool"}, {"name": "CycleTaskDetail", "type": "struct", "elements": [{"name": "isComplete", "type": "bool"}]}, {"name": "CycleTaskDict", "type": "dict", "key": "int", "value": "CycleTaskDetail"}, {"name": "ActorCmd", "type": "struct", "elements": [{"name": "processingCmd", "type": "string"}, {"name": "uid", "type": "<PERSON><PERSON>"}]}, {"name": "ActorActionElement", "type": "struct", "elements": [{"name": "action", "type": "string"}, {"name": "params", "type": "any"}, {"name": "status", "type": "int", "default": 0}]}, {"name": "ActorActionElementList", "type": "list", "element": "ActorActionElement"}, {"name": "ActorActions", "type": "struct", "elements": [{"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "stack", "type": "ActorActionElementList"}, {"name": "failedActions", "type": "ActorActionElementList"}]}, {"name": "AvatarOneLand", "type": "struct", "elements": [{"name": "tp", "type": "GridType"}, {"name": "occupyTime", "type": "float"}, {"name": "giveUpTime", "type": "float", "default": null}, {"name": "locked", "type": "bool"}, {"name": "levelAddTo", "type": "int"}, {"name": "produceBuffs", "type": "OneLandProduceAddList"}]}, {"name": "AvatarLands", "type": "dict", "key": "int", "value": "AvatarOneLand"}, {"name": "AvatarOneRoad", "type": "struct", "elements": [{"name": "tp", "type": "GridType"}, {"name": "occupyTime", "type": "float"}, {"name": "giveUpTime", "type": "float", "default": null}, {"name": "locked", "type": "bool"}]}, {"name": "AvatarRoads", "type": "dict", "key": "PackedPosition", "value": "AvatarOneRoad"}, {"name": "AvatarOneFavorite", "type": "struct", "elements": [{"name": "name", "type": "string"}, {"name": "type", "type": "int"}]}, {"name": "AvatarFavorite", "type": "dict", "key": "PackedPosition", "value": "AvatarOneFavorite"}, {"name": "LandLimitAdd", "type": "struct", "elements": [{"name": "resLand", "type": "int"}, {"name": "roadLand", "type": "int"}, {"name": "homeTechResLand", "type": "int"}, {"name": "homeTechRoadLand", "type": "int"}]}, {"name": "CareerTree", "type": "dict", "key": "int", "value": "bool"}, {"name": "AllyMemberDetail", "type": "struct", "elements": [{"name": "online", "type": "bool"}]}, {"name": "AllyMembers", "type": "dict", "key": "AvatarGid", "value": "AllyMemberDetail"}, {"name": "ClanList", "type": "list", "element": "ClanId"}, {"name": "ClanMemberBriefInfo", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "name", "type": "string"}, {"name": "birthCommandery", "type": "Commandery"}, {"name": "birthplace", "type": "Prefecture"}, {"name": "headpic", "type": "int"}, {"name": "career", "type": "Career"}, {"name": "merit", "type": "MeritSummary"}, {"name": "prosperity", "type": "int"}, {"name": "mainCityView", "type": "MainCityView"}, {"name": "rebornFlag", "type": "bool"}]}, {"name": "ClanMemberDetail", "inherit": "ClanMemberBriefInfo", "type": "struct", "elements": [{"name": "grade", "type": "int", "default": 1}, {"name": "enterTimeStamp", "type": "int"}, {"name": "online", "type": "bool"}, {"name": "onOfflineStamp", "type": "int"}]}, {"name": "ClanMembers", "type": "dict", "key": "AvatarGid", "value": "ClanMemberDetail"}, {"name": "ClanApplicantDetail", "inherit": "ClanMemberBriefInfo", "type": "struct", "elements": [{"name": "description", "type": "string"}, {"name": "timestamp", "type": "int"}]}, {"name": "ClanApplicants", "type": "dict", "key": "AvatarGid", "value": "ClanApplicantDetail"}, {"name": "ClanViceLeaderList", "type": "list", "element": "AvatarGid"}, {"name": "ClanBadge", "type": "struct", "elements": [{"name": "pattern", "type": "int"}, {"name": "patternWord", "type": "string", "default": ""}, {"name": "patternWordVertical", "type": "bool", "default": true}, {"name": "scale", "type": "int"}, {"name": "backgroundColor", "type": "int"}, {"name": "patternColor", "type": "int"}, {"name": "rotate", "type": "int"}]}, {"name": "ClanApplyVerify", "type": "struct", "elements": [{"name": "needVerify", "type": "bool", "default": false}, {"name": "influenceRequire", "type": "int", "default": 0}, {"name": "isApplyOpen", "type": "bool", "default": true}]}, {"name": "ContributionDetail", "type": "dict", "key": "AvatarGid", "value": "PeriodicDataSummary"}, {"name": "CreateClanInfo", "type": "struct", "elements": [{"name": "clanId", "type": "int"}, {"name": "name", "type": "string"}, {"name": "birthCommandery", "type": "Commandery"}, {"name": "birthplace", "type": "Prefecture"}, {"name": "clanBadge", "type": "ClanBadge"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "applyVerify", "type": "ClanApplyVerify"}, {"name": "leader<PERSON><PERSON>", "type": "AvatarGid"}, {"name": "members", "type": "ClanMembers"}, {"name": "membersCnt", "type": "int"}, {"name": "contributionDetail", "type": "ContributionDetail"}]}, {"name": "ClanBriefInfo", "type": "struct", "elements": [{"name": "clanId", "type": "int"}, {"name": "name", "type": "string"}, {"name": "birthCommandery", "type": "Commandery"}, {"name": "birthplace", "type": "Prefecture"}, {"name": "clanBadge", "type": "ClanBadge"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "leader<PERSON><PERSON>", "type": "AvatarGid"}, {"name": "leader<PERSON><PERSON>", "type": "string"}, {"name": "leaderHeadpic", "type": "int"}, {"name": "membersCnt", "type": "int"}, {"name": "level", "type": "int"}, {"name": "influence", "type": "int"}, {"name": "totalMerit", "type": "Merit"}, {"name": "weekMerit", "type": "Merit"}, {"name": "applyVerify", "type": "ClanApplyVerify"}]}, {"name": "ClanBriefInfoList", "type": "list", "element": "ClanBriefInfo"}, {"name": "ClanSyncPrefectureInfo", "type": "struct", "elements": [{"name": "membersCnt", "type": "int"}]}, {"name": "ClanSyncPrefectureInfoDict", "type": "dict", "key": "ClanId", "value": "ClanSyncPrefectureInfo"}, {"name": "ClanSortInfo", "type": "struct", "elements": [{"name": "clanId", "type": "ClanId"}, {"name": "birthCommandery", "type": "Commandery"}, {"name": "birthplace", "type": "Prefecture"}, {"name": "influence", "type": "int"}]}, {"name": "ClanSortInfoList", "type": "list", "element": "ClanSortInfo"}, {"name": "ClanManagerBriefInfo", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "name", "type": "string"}, {"name": "headpic", "type": "int"}]}, {"name": "ClanManagerBriefInfoList", "type": "list", "element": "ClanManagerBriefInfo"}, {"name": "ClanDetailInfo", "inherit": "ClanBriefInfo", "type": "struct", "elements": [{"name": "commRank", "type": "int"}, {"name": "viceLeaderInfoList", "type": "ClanManagerBriefInfoList"}]}, {"name": "ClanNameSearch", "type": "struct", "elements": [{"name": "name", "type": "string"}, {"name": "isCurrComm", "type": "bool", "default": null}]}, {"name": "ClanInvitePlayerBriefInfo", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "name", "type": "string"}, {"name": "birthCommandery", "type": "Commandery"}, {"name": "headpic", "type": "int"}, {"name": "career", "type": "Career"}, {"name": "prosperity", "type": "int"}, {"name": "kingBuildingLv", "type": "int"}]}, {"name": "ClanInvitePlayerBriefInfoList", "type": "list", "element": "ClanInvitePlayerBriefInfo"}, {"name": "StrategyPackDetail", "type": "struct", "elements": [{"name": "useLimitAdd", "type": "int"}, {"name": "useCnt", "type": "int"}, {"name": "hasCnt", "type": "int"}, {"name": "storageLimitAdd", "type": "int"}]}, {"name": "StrategyPack", "type": "dict", "key": "StrategyId", "value": "StrategyPackDetail"}, {"name": "StrategyAddition", "type": "struct", "elements": [{"name": "wuZhongShengYouAddResRatio", "type": "int"}, {"name": "tuoJiangRadiusAdd", "type": "int"}, {"name": "tuoJiangDailyCardAdd", "type": "int"}, {"name": "poXianLandLevelLimitTo", "type": "int"}, {"name": "woYeProduceExRatio", "type": "int"}, {"name": "woYeDuration", "type": "int"}, {"name": "jieCeSaveStrategy", "type": "int"}, {"name": "xuShiStrategyRecoverAdd", "type": "int"}, {"name": "xuShiYingStrategyLimitAdd", "type": "int"}, {"name": "jiKuiResAdd", "type": "int"}, {"name": "resAddRatio4WuZhongShengYou", "type": "IntIntDict", "flag": "SERVER_ONLY"}]}, {"name": "CityDevGrid", "type": "struct", "elements": [{"name": "landType", "type": "int"}]}, {"name": "CityDevGrids", "type": "dict", "key": "int", "value": "CityDevGrid"}, {"name": "ItemDetail", "type": "struct", "elements": [{"name": "num", "type": "int"}, {"name": "expireTime", "type": "float", "default": 0}]}, {"name": "ItemDetailList", "type": "list", "element": "ItemDetail"}, {"name": "<PERSON><PERSON>", "type": "struct", "elements": [{"name": "num", "type": "int"}, {"name": "special", "type": "ItemDetailList"}]}, {"name": "ItemBag", "type": "dict", "key": "ItemId", "value": "<PERSON><PERSON>"}, {"name": "UseItemArg", "type": "struct", "elements": [{"name": "id", "type": "ItemId"}, {"name": "num", "type": "int"}, {"name": "choice", "type": "int"}]}, {"name": "UseItemArgList", "type": "list", "element": "UseItemArg"}, {"name": "ExpiredItems", "type": "dict", "key": "ItemId", "value": "int"}, {"name": "TokenIdList", "type": "list", "element": "TokenId"}, {"name": "AmbitionsRewardsRecord", "type": "dict", "key": "int", "value": "bool"}, {"name": "AmbitionOccupyLand", "type": "dict", "key": "int", "value": "int"}, {"name": "AmbitionTarget", "type": "struct", "elements": [{"name": "occupyLand", "type": "AmbitionOccupyLand"}]}, {"name": "AmbitionTargetView", "type": "dict", "key": "int", "value": "int"}, {"name": "OccupyBattleRecord", "type": "struct", "elements": [{"name": "recordUid", "type": "<PERSON><PERSON>", "default": null}, {"name": "recordGuid", "type": "<PERSON><PERSON>", "default": null}, {"name": "totalLosses", "type": "int"}, {"name": "teamAbstractDict", "type": "IntIntDict"}]}, {"name": "BaseRankElement", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "value", "type": "int"}, {"name": "recordUid", "type": "<PERSON><PERSON>", "default": null}, {"name": "recordGuid", "type": "<PERSON><PERSON>", "default": null}, {"name": "totalLosses", "type": "int", "default": null}, {"name": "teamAbstractDict", "type": "IntIntDict", "default": null}]}, {"name": "RankElement", "inherit": "BaseRankElement", "type": "struct", "elements": [{"name": "seq", "type": "int"}]}, {"name": "RankElementMap", "type": "dict", "key": "int", "value": "RankElement"}, {"name": "RankList", "type": "struct", "elements": [{"name": "lastSeq", "type": "int", "default": 0}, {"name": "elements", "type": "RankElementMap"}]}, {"name": "Rank", "type": "struct", "elements": [{"name": "cycle", "type": "int", "default": null}, {"name": "all", "type": "RankList", "default": null}, {"name": "by<PERSON><PERSON>er", "type": {"type": "dict", "key": "int", "value": "RankList"}, "default": null}, {"name": "byLevel", "type": {"type": "dict", "key": "int", "value": "RankList"}, "default": null}]}, {"name": "TopNElement", "inherit": "BaseRankElement", "type": "struct", "elements": [{"name": "detail", "type": "any", "default": null}]}, {"name": "RankTopN", "type": "list", "element": "TopNElement"}, {"name": "RankResult", "type": "struct", "elements": [{"name": "rank", "type": "int"}, {"name": "allyRank", "type": "int"}, {"name": "allyId", "type": "AllyId"}]}, {"name": "PersonalRankResult", "inherit": "RankResult", "type": "struct", "elements": [{"name": "valid", "type": "bool", "default": false}]}, {"name": "RankResultDict", "type": "dict", "key": "AvatarGid", "value": "RankResult"}, {"name": "RankSettleElement", "type": "struct", "elements": [{"name": "rank", "type": "int"}, {"name": "allyRank", "type": "int"}]}, {"name": "RankSettleList", "type": "list", "element": "RankSettleElement"}, {"name": "RankSettleResult", "type": "struct", "elements": [{"name": "valid", "type": "bool", "default": false}, {"name": "rank", "type": "RankResultDict"}]}, {"name": "RankFilter", "type": "struct", "elements": [{"name": "career", "type": "int", "default": null}, {"name": "level", "type": "int", "default": null}]}, {"name": "CityOccupationDetail", "type": "struct", "elements": [{"name": "allyId", "type": "AllyId"}, {"name": "occupyTime", "type": "float"}]}, {"name": "CityOccupation", "type": "dict", "key": "CityId", "value": "CityOccupationDetail"}, {"name": "RoleMap", "type": "dict", "key": "Prefecture", "value": "int"}, {"name": "NationBriefInfo", "type": "struct", "elements": [{"name": "foundTime", "type": "float"}]}, {"name": "AllyBriefInfo", "type": "struct", "elements": [{"name": "name", "type": "string"}, {"name": "tmpClanId", "type": "ClanId"}, {"name": "birthplace", "type": "Prefecture"}, {"name": "nation", "type": "NationBriefInfo", "default": null}, {"name": "ambitionsScore", "type": "int"}]}, {"name": "AllyBrief", "type": "dict", "key": "AllyId", "value": "AllyBriefInfo"}, {"name": "S1SuggestChooseRole", "type": "struct", "elements": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": {"type": "dict", "key": "AvatarGid", "value": "int"}, "flag": "SERVER_ONLY"}, {"name": "summary", "type": {"type": "dict", "key": "int", "value": "int"}}]}, {"name": "S1ChoosingRoleSequence", "type": "struct", "elements": [{"name": "birthplace", "type": "Prefecture"}, {"name": "allyId", "type": "AllyId"}]}, {"name": "S1ChoosingRoleSequenceList", "type": "list", "element": "S1ChoosingRoleSequence"}, {"name": "S1WaitChooseCard", "type": "struct", "elements": [{"name": "role", "type": "int"}, {"name": "selectBy", "type": "Prefecture"}]}, {"name": "S1ChoosingRole", "type": "struct", "elements": [{"name": "sequence", "type": "S1ChoosingRoleSequenceList"}, {"name": "selectingIdx", "type": "int"}, {"name": "curTimeout", "type": "float"}, {"name": "cards", "type": {"type": "dict", "key": "int", "value": "S1WaitChooseCard"}}]}, {"name": "DrillRoundBattle", "type": "struct", "elements": [{"name": "winCamp", "type": "int"}, {"name": "selfOrderIdx", "type": "int"}, {"name": "enemyOrderIdx", "type": "int"}, {"name": "selfSoldierLeft", "type": "int"}, {"name": "enemySoldierLeft", "type": "int"}, {"name": "selfRecoverable", "type": "int"}, {"name": "enemyRecoverable", "type": "int"}]}, {"name": "DrillRound", "type": "list", "element": "DrillRoundBattle"}, {"name": "DrillResult", "type": "list", "element": "DrillRound"}, {"name": "DrillChapterBox", "type": "struct", "elements": [{"name": "chapterId", "type": "int"}, {"name": "boxIdx", "type": "int"}]}, {"name": "DrillProduce", "type": "dict", "key": "int", "value": "int"}, {"name": "DrillFixedRewardProcess", "type": "dict", "key": "ItemId", "value": "float"}, {"name": "DrillHangingBox", "type": "struct", "elements": [{"name": "lastPickTime", "type": "float"}, {"name": "lastSettleTime", "type": "float"}, {"name": "fixedRewardProcess", "type": "DrillFixedRewardProcess"}, {"name": "partProduceFloat", "type": "ProduceToFloatNum"}, {"name": "speed", "type": "DrillProduce"}]}, {"name": "DrillDailyChance", "type": "struct", "elements": [{"name": "cycleId", "type": "int"}, {"name": "count", "type": "int", "default": 15}]}, {"name": "SupplyRecover", "type": "struct", "elements": [{"name": "last", "type": "float", "default": null, "persistent": true}, {"name": "timer", "type": "int", "default": null, "persistent": false}]}, {"name": "Prosperity", "type": "struct", "elements": [{"name": "sum", "type": "int"}, {"name": "src", "type": {"type": "dict", "key": "int", "value": "int"}}]}, {"name": "TentView", "type": "struct", "elements": [{"name": "id", "type": "EntityId"}, {"name": "pos", "type": "Position"}, {"name": "buildStatus", "type": "int"}, {"name": "processTimeout", "type": "float", "default": null}, {"name": "alias", "type": "string", "default": null}]}, {"name": "TentViewDict", "type": "dict", "key": "<PERSON><PERSON>", "value": "TentView"}, {"name": "StoneVehicle", "type": "struct", "elements": [{"name": "id", "type": "EntityId"}, {"name": "pos", "type": "Position"}, {"name": "buildStatus", "type": "int"}, {"name": "processTimeout", "type": "float", "default": null}]}, {"name": "StoneVehicleDict", "type": "dict", "key": "<PERSON><PERSON>", "value": "StoneVehicle"}, {"name": "MailType", "type": "int"}, {"name": "MailUid", "type": "int"}, {"name": "MailUidList", "type": "list", "element": "MailUid"}, {"name": "MailArgs", "type": "list", "element": "string"}, {"name": "Mail", "type": "struct", "elements": [{"name": "uid", "type": "MailUid"}, {"name": "time", "type": "int"}, {"name": "templateId", "type": "int", "default": null}, {"name": "args", "type": "MailArgs", "default": null}, {"name": "title", "type": "string", "default": null}, {"name": "content", "type": "string", "default": null}, {"name": "type", "type": "MailType", "default": null}, {"name": "sender", "type": "AvatarGid", "default": null}, {"name": "attachment", "type": {"type": "dict", "key": "ItemId", "value": "int"}, "default": null}, {"name": "gift", "type": "bool", "default": false}, {"name": "read", "type": "bool", "default": false}]}, {"name": "MailList", "type": "list", "element": "Mail"}, {"name": "Shop", "type": "struct", "elements": [{"name": "cycle", "type": "int"}, {"name": "refreshTime", "type": "float"}, {"name": "shelf", "type": "IntIntDict"}]}, {"name": "AvatarShop", "type": "dict", "key": "int", "value": "Shop"}, {"name": "AllySiegeCampView", "type": "struct", "elements": [{"name": "city", "type": "CityId"}, {"name": "cityPos", "type": "Position"}, {"name": "pos", "type": "Position"}, {"name": "buildStatus", "type": "int"}, {"name": "processTimeout", "type": "float", "default": null}, {"name": "siegeUid", "type": "<PERSON><PERSON>", "default": null}, {"name": "announceTimeout", "type": "float", "default": null}, {"name": "siegeTimeout", "type": "float", "default": null}, {"name": "mainForceNum", "type": "int"}, {"name": "duraForceNum", "type": "int"}]}, {"name": "AllySiegeCampViewDict", "type": "dict", "key": "EntityId", "value": "AllySiegeCampView"}, {"name": "AllyCityView", "type": "struct", "elements": [{"name": "instanceId", "type": "InstanceId"}, {"name": "pos", "type": "Position"}]}, {"name": "AllyCityViewDict", "type": "dict", "key": "EntityId", "value": "AllyCityView"}, {"name": "AllyVehicleView", "type": "struct", "elements": [{"name": "pos", "type": "Position"}, {"name": "operator", "type": "EntityId", "default": null}, {"name": "buildStatus", "type": "int"}, {"name": "processTimeout", "type": "float", "default": null}]}, {"name": "AllyVehicleViewDict", "type": "dict", "key": "EntityId", "value": "AllyVehicleView"}, {"name": "SysDefenderElement", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "team", "type": "any"}, {"name": "defenderInCombat", "type": "bool", "default": false, "persistent": false}]}, {"name": "SysDefender", "type": "dict", "key": "<PERSON><PERSON>", "value": "SysDefenderElement"}, {"name": "SiegeLastHit", "type": "dict", "key": "int", "value": "any"}, {"name": "SiegeRankElement", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "value", "type": "int"}]}, {"name": "SiegeRank", "type": "list", "element": "SiegeRankElement"}, {"name": "SiegeRankDetailElement", "type": "struct", "inherit": "SiegeRankElement", "elements": [{"name": "detail", "type": "any"}]}, {"name": "SiegeDetailRank", "type": "list", "element": "SiegeRankDetailElement"}, {"name": "CityFirstOccupy", "type": "struct", "elements": [{"name": "allyId", "type": "AllyId"}, {"name": "dura", "type": "SiegeRank"}, {"name": "kill", "type": "SiegeRank"}]}, {"name": "AllyInWarDetail", "type": "struct", "elements": [{"name": "siegeGroupIdx", "type": "int"}, {"name": "startAssault", "type": "bool"}]}, {"name": "AllyInWar", "type": "dict", "key": "AllyId", "value": "AllyInWarDetail"}, {"name": "AutoRoadPassGrids", "type": "dict", "key": "int", "value": "bool"}, {"name": "AutoRoad", "type": "struct", "elements": [{"name": "queryId", "type": "int"}, {"name": "path", "type": "PositionList"}, {"name": "serverQueryId", "type": "int", "flag": "SERVER_ONLY", "default": 0}, {"name": "passGrids", "type": "AutoRoadPassGrids", "flag": "SERVER_ONLY"}]}, {"name": "GachaInfo", "type": "struct", "elements": [{"name": "drawCnt", "type": "int"}, {"name": "dailyDrawCnt", "type": "int"}, {"name": "dailyPeriod", "type": "int"}, {"name": "lastFreeDrawStamp", "type": "int"}, {"name": "lastHalfDrawStamp", "type": "int"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "dict", "key": "int", "value": "GachaInfo"}, {"name": "RandomItemInfo", "type": "struct", "elements": [{"name": "guaranteeProg", "type": "IntIntDict"}]}, {"name": "RandomItemManager", "type": "dict", "key": "int", "value": "RandomItemInfo"}, {"name": "RewardDisplayDetail", "type": "struct", "elements": [{"name": "gain", "type": "int"}, {"name": "transfer", "type": "int"}]}, {"name": "RewardDisplayData", "type": "dict", "key": "ItemId", "value": "RewardDisplayDetail"}, {"name": "RewardDisplayDataList", "type": "list", "element": "RewardDisplayData"}, {"name": "ImpasseDetail", "type": "struct", "elements": [{"name": "endTime", "type": "float"}, {"name": "timer", "type": "int", "flag": "SERVER_ONLY"}, {"name": "packedPos", "type": "PackedPos", "flag": "SERVER_ONLY"}]}, {"name": "Impasse", "type": "dict", "key": "int", "value": "ImpasseDetail"}, {"name": "ContinousBattle", "type": "struct", "elements": [{"name": "merit", "type": "Merit"}, {"name": "winCount", "type": "int"}, {"name": "staminaCost", "type": "int"}]}, {"name": "DurabilityRecover", "type": "struct", "elements": [{"name": "enabled", "type": "bool"}, {"name": "fullStop", "type": "bool"}, {"name": "last", "type": "float", "default": null}]}, {"name": "ArmyView", "type": "struct", "elements": [{"name": "mainStatus", "type": "int", "default": 101}, {"name": "combatStatus", "type": "int", "default": 201}, {"name": "deployStatus", "type": "int", "default": 301}, {"name": "vehicleStatus", "type": "int", "default": 401}, {"name": "statusEndTime", "type": "float"}, {"name": "moveStartPos", "type": "Position"}, {"name": "moveStartTime", "type": "float"}, {"name": "moveTargetPos", "type": "Position"}, {"name": "movePath", "type": {"type": "list", "element": "Position"}}, {"name": "moveSpeed", "type": "float"}, {"name": "pos", "type": "Position"}, {"name": "supply", "type": "int"}, {"name": "supplyMax", "type": "int"}, {"name": "preorderSiege", "type": "ArmyPreorderSiege"}, {"name": "inAutoPath", "type": "ArmyInAutoPath"}, {"name": "home", "type": "ArmyHomeData"}, {"name": "deployHome", "type": "ArmyDeployHomeData", "default": null}, {"name": "siegeHome", "type": "ArmySiegeHomeData", "default": null}]}, {"name": "ArmyData", "type": "struct", "elements": [{"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "armyType", "type": "ArmyType", "default": 1}, {"name": "heroes", "type": "HeroDataDict"}, {"name": "autoFullSoldier", "type": "bool"}, {"name": "representHeroId", "type": "HeroId"}, {"name": "armyInjuredTime", "type": "float"}, {"name": "fateDynasty", "type": "int", "default": null}, {"name": "battleArmyData", "type": "BattleArmyData", "persistent": false}, {"name": "gmBattleArmyData", "type": "BattleArmyData", "persistent": false}, {"name": "entityId", "type": "EntityId", "persistent": false}, {"name": "view", "type": "ArmyView", "persistent": false}]}, {"name": "CardData", "type": "struct", "elements": [{"name": "id", "type": "int"}, {"name": "level", "type": "int"}]}, {"name": "CompletedHero", "type": "struct", "elements": [{"name": "attack", "type": "int"}, {"name": "attack_limit", "type": "int"}, {"name": "defense", "type": "int"}, {"name": "defense_limit", "type": "int"}, {"name": "health", "type": "int"}, {"name": "health_limit", "type": "int"}, {"name": "intelligence", "type": "int"}, {"name": "intelligence_limit", "type": "int"}, {"name": "speed", "type": "int"}, {"name": "speed_limit", "type": "int"}, {"name": "monopolyHeroId", "type": "string", "default": ""}, {"name": "templateHeroId", "type": "int"}, {"name": "cards", "type": {"type": "dict", "key": "int", "value": "CardData"}}]}, {"name": "CompletedHeroes", "type": "dict", "key": "string", "value": "CompletedHero"}, {"name": "BattleHeadInfo", "type": "struct", "elements": [{"name": "battleType", "type": "int"}, {"name": "modeType", "type": "int"}, {"name": "battleStageId", "type": "int"}, {"name": "selfUid", "type": "string"}, {"name": "enemyUid", "type": "string"}]}, {"name": "BattleFormationData", "type": "struct", "elements": [{"name": "displayName", "type": "string"}, {"name": "lastModified", "type": "float"}, {"name": "formationIndex", "type": "int"}, {"name": "heroes", "type": {"type": "dict", "key": "int", "value": "string"}}]}, {"name": "BattleStageHistoryData", "type": "struct", "elements": [{"name": "formationIndex", "type": "int"}]}, {"name": "BattleStageHistory", "type": "list", "element": "BattleStageHistoryData"}, {"name": "FocusActorView", "type": "struct", "elements": [{"name": "moveStartPos", "type": "Position"}, {"name": "moveStartTime", "type": "float"}, {"name": "moveTargetPos", "type": "Position"}, {"name": "movePath", "type": {"type": "list", "element": "Position"}}, {"name": "moveSpeed", "type": "float"}, {"name": "relation", "type": "Relation"}]}, {"name": "CompletedHeroUid", "type": "string"}, {"name": "FormationElement", "type": "struct", "elements": [{"name": "id", "type": "TemplateHeroId"}, {"name": "uid", "type": "CompletedHeroUid"}]}, {"name": "Formation", "type": "dict", "key": "int", "value": "FormationElement"}, {"name": "Formations", "type": "list", "element": "Formation"}, {"name": "HeroAbstract", "type": "struct", "elements": [{"name": "id", "type": "HeroId"}, {"name": "level", "type": "int"}, {"name": "stars", "type": "int"}, {"name": "startAlive", "type": "bool"}, {"name": "endAlive", "type": "bool"}]}, {"name": "BattleTeamAbstract", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"name": "allyId", "type": "AllyId"}, {"name": "clanId", "type": "ClanId"}, {"name": "health", "type": "int"}, {"name": "maxHealth", "type": "int"}, {"name": "recoverableHealth", "type": "int"}, {"name": "supply", "type": "int"}, {"name": "heroAbstractDict", "type": {"type": "dict", "key": "int", "value": "HeroAbstract"}}]}, {"name": "BattleDurabilityInfo", "type": "struct", "elements": [{"name": "consumeDurability", "type": "int"}, {"name": "buildingOwner", "type": "string"}, {"name": "buildingName", "type": "string"}, {"name": "zeroed", "type": "bool"}]}, {"name": "BattleAbstract", "type": "struct", "elements": [{"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "guid", "type": "GuidDict", "flag": "SERVER_ONLY", "default": null}, {"name": "battleType", "type": "int"}, {"name": "position", "type": "PackedPosition"}, {"name": "gridName", "type": "string"}, {"name": "timestamp", "type": "float"}, {"name": "winCamp", "type": "BattleCamp"}, {"name": "energyCost", "type": "int"}, {"name": "durabilityInfo", "type": "BattleDurabilityInfo", "default": null}, {"name": "teamAbstractDict", "type": {"type": "list", "element": "BattleTeamAbstract"}}, {"name": "drillLevel", "type": "int", "default": null}]}, {"name": "BattleAbstractGroup", "inherit": "BattleAbstract", "type": "struct", "elements": [{"name": "groupedAbstracts", "type": {"type": "list", "element": "BattleAbstract"}, "default": null}, {"name": "groupedAbstractUids", "type": {"type": "list", "element": "<PERSON><PERSON>"}, "default": null}]}, {"name": "BattleAbstracts", "type": "list", "element": "BattleAbstractGroup"}, {"name": "BattleAbstractsRedDot", "type": "struct", "elements": [{"name": "globalStamp", "type": "int"}, {"name": "guid2Stamp", "type": "UidIntDict"}, {"name": "globalOuterStamp", "type": "int", "flag": "SERVER_ONLY"}, {"name": "outerCount", "type": "int", "persistent": false}, {"name": "innerCount", "type": "int", "persistent": false}]}, {"name": "PackedBattleAbstractGroup", "type": "struct", "elements": [{"name": "firstAbstract", "type": "string"}, {"name": "groupedAbstractUids", "type": "UidList", "default": null}]}, {"name": "PackedBattleAbstractGroups", "type": "list", "element": "PackedBattleAbstractGroup"}, {"name": "PackedDrillBattleAbstractGroup", "inherit": "PackedBattleAbstractGroup", "type": "struct", "elements": [{"name": "firstAbstractUid", "type": "<PERSON><PERSON>"}, {"name": "guid", "type": "GuidDict"}]}, {"name": "PackedDrillBattleAbstractGroups", "type": "list", "element": "PackedDrillBattleAbstractGroup"}, {"name": "BattleAbstractFilter", "type": "struct", "elements": [{"name": "battleType", "type": "int", "default": null}, {"name": "position", "type": "PackedPosition", "default": null}, {"name": "allyId", "type": "AllyId", "default": null}, {"name": "clanId", "type": "ClanId", "default": null}, {"name": "playerGid", "type": "AvatarGid", "default": null}, {"name": "heroList", "type": "HeroList", "default": null}, {"name": "needHeroWin", "type": "bool", "default": null}]}, {"name": "BattleTacticStatistic", "type": "struct", "elements": [{"name": "tacticData", "type": "TacticData"}, {"name": "useCount", "type": "int"}, {"name": "damage", "type": "int"}, {"name": "heal", "type": "int"}]}, {"name": "BattleHeroStatistic", "type": "struct", "elements": [{"name": "armyType", "type": "ArmyType"}, {"name": "armyTypeQualification", "type": "int"}, {"name": "equips", "type": {"type": "list", "element": "Equip"}}, {"name": "horses", "type": {"type": "list", "element": "Horse"}}, {"name": "identitySlots", "type": "IdentitySlots"}, {"name": "attributes", "type": "BattleAttritubes"}, {"name": "health", "type": "int"}, {"name": "maxHealth", "type": "int"}, {"name": "recoverableHealth", "type": "int"}, {"name": "tacticStatisticDict", "type": {"type": "list", "element": "BattleTacticStatistic"}}, {"name": "gainExp", "type": "int"}, {"name": "toLevel", "type": "int"}]}, {"name": "BattleTeamStatistic", "type": "struct", "elements": [{"name": "merit", "type": "int"}, {"name": "totalKill", "type": "int"}, {"name": "heroStatisticDict", "type": {"type": "dict", "key": "int", "value": "BattleHeroStatistic"}}]}, {"name": "BattleStatistic", "type": "struct", "elements": [{"name": "position", "type": "Position"}, {"name": "winCamp", "type": "BattleCamp"}, {"name": "teamStatisticDict", "type": {"type": "dict", "key": "BattleCamp", "value": "BattleTeamStatistic"}}, {"name": "extraDebugInfo", "type": "any"}]}, {"name": "ImTokenInfo", "type": "struct", "elements": [{"name": "token", "type": "string"}, {"name": "expireStamp", "type": "int"}]}, {"name": "ReportPlayerInfo", "type": "struct", "elements": [{"name": "avatar<PERSON><PERSON>", "type": "AvatarGid"}, {"name": "reason", "type": "IntList"}, {"name": "desc", "type": "string"}]}, {"name": "ChannelAvatarInfo", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "name", "type": "string"}, {"name": "headpic", "type": "HeadPic"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Head<PERSON><PERSON><PERSON>"}, {"name": "career", "type": "Career"}, {"name": "ally<PERSON>ame", "type": "string"}]}, {"name": "ChannelMsgInfo", "type": "struct", "elements": [{"name": "uid", "type": "<PERSON><PERSON>"}, {"name": "content", "type": "string"}]}, {"name": "RequestChannelMsgInfo", "type": "struct", "elements": [{"name": "content", "type": "string"}, {"name": "refer", "type": "ChannelMsgInfo", "default": null}]}, {"name": "ChannelReferMsgInfo", "inherit": "ChannelMsgInfo", "type": "struct", "elements": [{"name": "refer", "type": "ChannelMsgInfo", "default": null}]}, {"name": "ChannelMsg", "type": "struct", "elements": [{"name": "avatarInfo", "type": "ChannelAvatarInfo"}, {"name": "msg", "type": "ChannelReferMsgInfo"}, {"name": "stamp", "type": "int"}]}, {"name": "ChannelMsgList", "type": {"type": "list", "element": "ChannelMsg"}}, {"name": "PayInfoRequest", "type": "struct", "elements": [{"name": "goodsId", "type": "int"}, {"name": "tb", "type": "string"}]}, {"name": "PayInfoResponse", "inherit": "PayInfoRequest", "type": "struct", "elements": [{"name": "orderId", "type": "<PERSON><PERSON>", "default": null}, {"name": "shipUrl", "type": "string", "default": null}]}, {"name": "PayInfoCache", "type": "dict", "key": "<PERSON><PERSON>", "value": "PayInfoResponse"}, {"name": "AutoPathAgent", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "allyId", "type": "AllyId"}, {"name": "clanId", "type": "ClanId"}, {"name": "level", "type": "int"}, {"name": "startPos", "type": "Position", "default": null}, {"name": "passGrids", "type": "AutoRoadPassGrids"}, {"name": "step<PERSON><PERSON><PERSON>", "type": "int"}]}, {"name": "DeployArgs", "type": "struct", "elements": [{"name": "deployType", "type": "int"}, {"name": "isMainForce", "type": "bool", "default": false}]}, {"name": "HostPort", "type": "struct", "elements": [{"name": "host", "type": "string"}, {"name": "port", "type": "int"}, {"name": "ssl", "type": "bool"}]}, {"name": "GmMirrorMemberDetail", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "confirmed", "type": "bool"}]}, {"name": "GmMirrorMembers", "type": "dict", "key": "string", "value": "GmMirrorMemberDetail"}, {"name": "GmMirror", "type": "struct", "elements": [{"name": "index", "type": "int", "default": null}, {"name": "members", "type": "GmMirrorMembers"}]}, {"name": "GmMirrorBy", "type": "struct", "elements": [{"name": "gid", "type": "AvatarGid"}, {"name": "index", "type": "int"}, {"name": "waitConfirm", "type": "Callback", "default": null}]}, {"name": "WaitBattleWithBase", "type": "struct", "elements": [{"name": "result", "type": "bool", "default": null}]}, {"name": "WaitBattleWithLandDefender", "type": "struct", "inherit": "WaitBattleWithBase", "elements": [{"name": "hasStaminaCost", "type": "bool"}, {"name": "occupyBattleRecord", "type": "any", "default": null}]}, {"name": "WaitBattleWithSysDefender", "type": "struct", "inherit": "WaitBattleWithBase", "elements": []}, {"name": "WaitBattleWithDuraDefender", "type": "struct", "inherit": "WaitBattleWithBase", "elements": [{"name": "recordUid", "type": "<PERSON><PERSON>", "default": null}]}, {"name": "WaitBattleArgs", "type": "struct", "elements": [{"name": "landDefender", "type": "WaitBattleWithLandDefender", "default": null}, {"name": "sysDefender", "type": "WaitBattleWithSysDefender", "default": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "WaitBattleWithDuraDefender", "default": null}]}, {"name": "WaitBattle", "type": "struct", "inherit": "WaitBattleArgs", "elements": [{"name": "cb", "type": "EntityCallback", "default": null}]}, {"name": "SlgCity", "type": "struct", "elements": [{"name": "level", "type": "int"}, {"name": "pos", "type": "Position"}, {"name": "actorType", "type": "ActorTypeEnum"}, {"name": "subId", "type": "int"}, {"name": "county", "type": "int", "default": null}]}, {"name": "SlgCityInfo", "type": "dict", "key": "CityId", "value": "SlgCity"}, {"name": "TmpSlgSummary", "type": "struct", "elements": [{"name": "city", "type": "SlgCityInfo"}]}]