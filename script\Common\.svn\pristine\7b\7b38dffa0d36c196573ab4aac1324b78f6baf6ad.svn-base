﻿local BattleFormulasEnv = xrequire("Common.Battle.BattleFormulas")
local NodeBaseServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.NodeBaseServer")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

---@class HealNodeServer : NodeBaseServer
local HealNodeServer = DefineClass("HealNodeServer", NodeBaseServerEnv.NodeBaseServer)

function HealNodeServer:ctor()
    self:MarkExecute()
end

function HealNodeServer:Execute(graphData)
    local caster = graphData.caster
    local tacticId = BattleUtilsEnv.GetGraphTacticId(self, graphData)
    local sourceTacticId, sourceTacticHero = BattleUtilsEnv.GetGraphGetSourceTacticIdAndHero(graphData)
    local battleGame = caster.battleGame
    local healTargetIds = {}
    local missHeroIds = {}
    local globalFactor = graphData:GetFromBlackboard("globalFactor") or 1
    local targetIds = graphData:GetProperty(self, "targetIds")
    local healFactorFactorInputString = graphData:GetProperty(self, "healFactor")
    local healFactor = graphData:GetProperty(self, "healFactorNew")
    local isFixedHeal = graphData:GetProperty(self, "isFixedHeal")
    local overwriteArgs = graphData:GetProperty(self, "overwriteArgs")
    for _, targetId in ipairs(targetIds) do
        local target = battleGame.heroUniqueIdMap[targetId]
        local factor = 1
        if isFixedHeal then
            factor = 1
        elseif string.len(healFactorFactorInputString) > 0 then
            factor = BattleUtilsEnv.ExecuteFormulaForTacticLevel(healFactorFactorInputString, graphData:GetFromBlackboard("Level"))
        end

        -- new
        if healFactor and healFactor ~= 0 then
            factor = healFactor
        end

        local healPackage = {
            heal = nil,
            healFactor = factor,
            globalFactor = globalFactor,
            casterId = caster.uniqueId,
            targetId = targetId,
            tacticId = tacticId,
            buffId = graphData.battleBuff and graphData.battleBuff.buffId,
            nestIds = {
                [TableConst.enums.BattleEvent.BeforeHeal] = battleGame.recorder:GetNestId(),
                [TableConst.enums.BattleEvent.AfterHeal] = battleGame.recorder:GetNestId(),
            },
            overwriteArgs = {overwriteArgs},
            accept = false
        }

        self:AttachAdditionalModification(graphData, healPackage)
        local result = battleGame:SubmitHeal(graphData, healPackage)

        if result.accept then
            table.insert(healTargetIds, target.uniqueId)
            battleGame.recorder:TacticStatistic(sourceTacticHero.camp, sourceTacticHero.uniqueId, sourceTacticId, 0, 0, result.heal, result.tacticId)
        else
            table.insert(missHeroIds, target.uniqueId)
        end
    end
    graphData:SetProperty(self, "healHeroIds", healTargetIds)
    graphData:SetProperty(self, "missHeroIds", missHeroIds)

    if next(healTargetIds) then
        graphData:SetProperty(self, "outputName", "next")
    else
        graphData:SetProperty(self, "outputName", "elseNode")
    end
end

function HealNodeServer:AttachAdditionalModification(graphData, healPackage)
    local enableAdditionalModification = graphData:GetProperty(self, "enableAdditionalAttributeModification")
    healPackage.additionalModification = {}
    local additionalModification = healPackage.additionalModification
    additionalModification.isFixedHeal = enableAdditionalModification and graphData:GetProperty(self, "isFixedHeal") or false
    additionalModification.fixedHealValue = graphData:GetProperty(self, "fixedHealValue")
end

