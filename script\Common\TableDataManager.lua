﻿local ManagerBaseEnv = xrequire("Common.ManagerBase")

---@class TableDataManager : ManagerBase
TableDataManager = DefineClass("TableDataManager", ManagerBaseEnv.ManagerBase)

function TableDataManager:ctor()
    self.__dataCache = {}
    self.__multiIndicesDataCache = {}
end

function TableDataManager:GetRow(tableName, row)
    local tbl = self.__dataCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.__dataCache[tableName] = tbl
    end
    return tbl.__xdata[row]
end

function TableDataManager:SetDynamicRow(tableName, baseRow, data)
    local tbl = self.__dataCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.__dataCache[tableName] = tbl
    end
    local i = 1
    while i < 100 do
        local row = string.format("%s_%02d", tostring(baseRow), i)
        if not tbl.__xdata[row] then
            tbl.__xdata[row] = data
            return row
        end
        i = i + 1
    end
end

function TableDataManager:CloneRow(tableName, row)
    local tbl = self.__dataCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.__dataCache[tableName] = tbl
    end
    return table.deepclone(tbl.__xdata[row])
end

function TableDataManager:GetRows(tableName, predicate)
    local tbl = self.__dataCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.__dataCache[tableName] = tbl
    end
    local records = {}
    for _, record in ipairs(tbl.__xdata) do
        if predicate(record) then
            records[#records + 1] = record
        end
    end
    return records
end

function TableDataManager:PreprocessMultiIndicesTables()
    for _, tableDef in ipairs(TableConst.tables) do
        if not string.isnilorempty(tableDef.index) then
            local indices = string.split(tableDef.index, "+")
            local indicesNum = #indices
            if indicesNum > 1 then
                local tbl = xrequire("Table.Data." .. tableDef.file)
                local dataCache = {}
                for _, data in ipairs(tbl.__xdata) do
                    local keyList = {}
                    for _, index in ipairs(indices) do
                        table.insert(keyList, data[index])
                    end
                    local combinedKey = string.concat(keyList, "+")
                    dataCache[combinedKey] = data
                end
                self.__multiIndicesDataCache[tableDef.file] = {data = dataCache, indexOrder = indices}
            end
        end
    end
end

function TableDataManager:GetRowByMultiIndices(tableName, indices)
    local dataCache = self.__multiIndicesDataCache[tableName]
    if not dataCache then
        return self:_GetRowByMultiIndices(tableName, indices)
    end
    local keyList = {}
    for _, index in ipairs(dataCache.indexOrder) do
        table.insert(keyList, indices[index])
    end
    local combinedKey = string.concat(keyList, "+")
    return dataCache.data[combinedKey]
end

function TableDataManager:_GetRowByMultiIndices(tableName, indices)
    local tbl = self.__dataCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.__dataCache[tableName] = tbl
    end
    for _, data in ipairs(tbl.__xdata) do
        local ret = data
        for k, v in pairs(indices) do
            if data[k] ~= v then
                ret = nil
                break
            end
        end
        if ret then
            return ret
        end
    end
    return nil
end

function TableDataManager:GetTable(tableName)
    local tbl = self.__dataCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.__dataCache[tableName] = tbl
    end
    return tbl.__xdata
end

function TableDataManager.FindTableByValueType(valueType)
    for _, tuple in ipairs(TableConst.tables) do
        if tuple.value_type == valueType then
            return tuple.file
        end
    end
end

function TableDataManager:LoadConfigs()
    local config = {
        Common = {},
        Battle = {},
        Slg = {}
    }

    for k, v in pairs(self:GetTable("common_tbcommonconfig")) do
        config.Common[k] = v
    end
    for k, v in pairs(self:GetTable("common_tbbattleconfig")) do
        config.Battle[k] = v
    end
    for k, v in pairs(self:GetTable("common_tbslgconfig")) do
        config.Slg[k] = v
    end

    setmetatable(config, {
        __index = config,
        __newindex = function (t,k,v)
            error("attempt to update a read-only table", 2)
        end
    })

    DeclareAndSetGlobal("Config", config)
end

TableDataManager.season_name = LuaNodeConfig and LuaNodeConfig.getConfig("fyc.season") or "s1"
assert(TableDataManager.season_name)
TableDataManager.season_tbname = "season_tb" .. TableDataManager.season_name  -- 赛季表名

----------------------------------------------------------------------------------------------------------------------------
--- 表格读取
----------------------------------------------------------------------------------------------------------------------------

function TableDataManager:GetEffectData(configId)
    return self:GetRow("common_tbeffect", configId)
end

function TableDataManager:GetTextData(configId)
    return self:GetRow("common_tbtext", configId)
end

function TableDataManager:GetGeneralAbilityData(configId)
    return self:GetRow("heros_tbgeneralability", configId)
end

function TableDataManager:GetAllTacticData()
    return self:GetTable("battle_tbbattletactic")
end

function TableDataManager:GetTacticData(configId)
    return self:GetRow("battle_tbbattletactic", configId)
end

function TableDataManager:GetTacticConstData(configId)
    if not configId then
        return self:GetTable("battle_tbtacticconst")
    end
    return self:GetRow("battle_tbtacticconst", configId)
end

function TableDataManager:GetTaskConstData(configId)
    if not configId then
        return self:GetTable("common_tbtaskconst")
    end
    return self:GetRow("common_tbtaskconst", configId)
end

function TableDataManager:GetTaskData(configId)
    return self:GetRow("common_tbtask", configId)
end

function TableDataManager:GetTaskChapterInterludeData(configId)
    return self:GetRow("common_tbtaskchapterinterlude", configId)
end

function TableDataManager:GetBuffData(configId)
    return self:GetRow("battle_tbbattlebuff", configId)
end

function TableDataManager:GetBuffGroupData(configId)
    return self:GetRow("battle_tbbattlebuffgroup", configId)
end

function TableDataManager:GetAttributeData(configId)
    return self:GetRow("battle_tbbattleattribute", configId)
end

function TableDataManager:GetBattleStageData(configId)
    return self:GetRow("battle_tbbattlestage", configId)
end

function TableDataManager:GetBattleStageNpcTeamData(configId)
    return self:GetRow("battle_tbbattlestagenpcteam", configId)
end

function TableDataManager:GetBattleStageNpcData(configId)
    return self:GetRow("battle_tbbattlestagenpc", configId)
end

function TableDataManager:GetAnimTransitionData(configId)
    return self:GetRow("battle_tbbattleanimationtransition", configId)
end


function TableDataManager:GetCurrencyData(configId)
    return self:GetRow("common_tbcurrency", configId)
end

function TableDataManager:GetHomeConstData(configId)
    if not configId then
        return self:GetTable("common_tbhomebuildingconst")
    end
    return self:GetRow("common_tbhomebuildingconst", configId)
end

function TableDataManager:GetHomeBuildingTypeData(config)
    return self:GetRow("common_tbhomebuildingtype", config)
end

function TableDataManager:GetAllHomeBuildingTypeData()
    return self:GetTable("common_tbhomebuildingtype")
end

function TableDataManager:GetHomeBuildingData(id, level)
    return self:GetRowByMultiIndices("common_tbhomebuilding", {id = id, level = level})
end

function TableDataManager:GetHomeTechData(id)
    if not id then
        return self:GetTable("common_tbhometech")
    end
    return self:GetRowByMultiIndices("common_tbhometech", {id = id})
end


function TableDataManager:GetLevelData(configId)
    return self:GetRow("levels_tblevel", configId)
end

function TableDataManager:GetLevelGoalData(configId)
    return self:GetRow("levels_tbgoal", configId)
end

function TableDataManager:GetCellElementData(configId)
    return self:GetRow("levels_tbcellelement", configId)
end

function TableDataManager:GetLevelElementData(configId)
    return self:GetRow("levels_tbelement", configId)
end

function TableDataManager:GetLevelElemtBonusData(configId)
    return self:GetRow("levels_tbelementbonus", configId)
end


function TableDataManager:GetHeroNastyData(configId)
    return self:GetRow("battle_tbherodynasty", configId)
end
function TableDataManager:GetAllHeroDynastyData()
    return self:GetTable("battle_tbherodynasty")
end

function TableDataManager:GetRarityRankData(configId)
    return self:GetRow("common_tbrarityrank", configId)
end

function TableDataManager:GetHeroSuitRankData(configId)
    return self:GetRow("common_tbherosuitrank", configId)
end

function TableDataManager:GetAllTemplateHeroData()
    return self:GetTable("battle_tbtemplatehero")
end

function TableDataManager:GetTemplateHeroData(configId)
    return self:GetRow("battle_tbtemplatehero", configId)
end

function TableDataManager:GetHeroConstData(key)
    return self:GetRow("battle_tbheroconst", key)
end

function TableDataManager:GetBattleNpcTeamData(configId)
    return self:GetRow("battle_tbbattlenpcteam", configId)
end

function TableDataManager:GetBattleNpcData(configId)
    return self:GetRow("battle_tbbattlenpc", configId)
end

function TableDataManager:GetHeroArmyTypeData(configId)
    return self:GetRow("battle_tbheroarmytype", configId)
end

function TableDataManager:GetStorySubTitleData(configId)
    return self:GetRow("StorySubTitleData", configId)
end

function TableDataManager:GetStoryItemRewardData(configId)
    return self:GetRow("StoryItemRewardData", configId)
end

function TableDataManager:GetHeroElementalData(elementalType)
    return self:GetRow("battle_tbheroelemental", elementalType)
end

function TableDataManager:GetBattleRecordText(configId)
    return self:GetRow("battle_tbbattlerecordtext", configId)
end

function TableDataManager:GetCurrencyDatas()
    return self:GetTable("common_tbcurrency")
end

function TableDataManager:GetCurrencyData(tp)
    return self:GetRow("common_tbcurrency", tp)
end

--
function TableDataManager:GetHeroAttrData(heroId)
    return self:GetRow("heros_tbhero", heroId)
end

function TableDataManager:GetHeroBattleAttrData(heroId)
    return self:GetRow("heros_tbbattleattr", heroId)
end

function TableDataManager:GetBattleFormulasParameterTable()
    return self:GetTable("battle_tbbattleformulas")
end

function TableDataManager:GetHeroAttrTypeData(configId)
    return self:GetRow("common_tbheroattrtype", configId)
end

function TableDataManager:GetHeroRoleTypeData(configId)
    return self:GetRow("common_tbheroroletype", configId)
end

function TableDataManager:GetMapElementData(id)
    local tblname = "slg_tbmapelement"
    local data = self:GetRow("slg_tbmapelement", id)
    if not data then
        WarnLog("cannot find %s row %s", tblname, id)
    end
    return data
end

function TableDataManager:GetTypeElementData(configId)
    return self:GetRow("slg_tbtypeelement", configId)
end

function TableDataManager:GetMapElementTable()
    return self:GetTable("slg_tbmapelement")
end

function TableDataManager:GetMapGISTable()
    return self:GetTable("slg_tbmapgis")
end

function TableDataManager:GetTermEntryData(configId)
    if not configId then
        return self:GetTable("common_tbtermentry")
    end
    return self:GetRow("common_tbtermentry", configId)
end

function TableDataManager:GetTermEntry1Data(configId)
    return self:GetRow("common_tbtermentry1", configId)
end

function TableDataManager:GetMainCityDistScoreTable()
    return self:GetTable("slg_tbmaincitydistscore")
end

function TableDataManager:GetMainCityResScoreTable()
    return self:GetTable("slg_tbmaincityresscore")
end

function TableDataManager:GetMainCityRandomConfigs()
    return self:GetTable("slg_tbmaincityrandomconfig")
end

function TableDataManager:GetArmyTypeQualificationModifyData(index)
    return self:GetRow("battle_tbarmytypequalification", index)
end

function TableDataManager:GetAllScheduleData()
    return self:GetTable("common_tbschedule")
end

function TableDataManager:GetScheduleData(configId)
    return self:GetRow("common_tbschedule", configId)
end

function TableDataManager:GetCareerConst(configId)
    return self:GetRow("common_tbcareerconst", configId)
end

function TableDataManager:GetCareerTreeData(configId)
    if not configId then
        return self:GetTable("common_tbcareertree")
    end
    return self:GetRow("common_tbcareertree", configId)
end

function TableDataManager:GetStrategyData(configId)
    return self:GetRow("common_tbstrategy", configId)
end

function TableDataManager:GetStrategyDatas()
    return self:GetTable("common_tbstrategy")
end

function TableDataManager:GetCareerTalentData(configId)
    return self:GetRow("common_tbcareertelent", configId)
end

function TableDataManager:GetServerListTable()
    return self:GetTable("common_tbserverlist")
end

function TableDataManager:GetWorldBuffData(configId)
    return self:GetRow("common_tbworldbuff", configId)
end

function TableDataManager:GetLandBuffData(configId)
    return self:GetRow("common_tblandbuff", configId)
end

function TableDataManager:GetItemData(configId)
    return self:GetRow("common_tbitem", configId)
end

function TableDataManager:GetAllItemData()
    return self:GetTable("common_tbitem")
end

function TableDataManager:GetItemConst(configId)
    return self:GetRow("common_tbitemconst", configId)
end

function TableDataManager:GetTokenData(configId)
    return self:GetRow("common_tbtoken", configId)
end

function TableDataManager:GetAllTokenData()
    return self:GetTable("common_tbtoken")
end

function TableDataManager:GetMaterialData(configId)
    return self:GetRow("common_tbmaterial", configId)
end

function TableDataManager:GetAllMaterialData()
    return self:GetTable("common_tbmaterial")
end

function TableDataManager:GetEquipConst(configId)
    return self:GetRow("common_tbequipconst", configId)
end

function TableDataManager:GetEquipData(configId)
    return self:GetRow("common_tbequip", configId)
end

function TableDataManager:GetHorseData(configId)
    return self:GetRow("common_tbhorse", configId)
end

function TableDataManager:GetEquipQualityRatio(configId)
    return self:GetRow("common_tbequipqualityratio", configId)
end

function TableDataManager:GetEquipEffectData(configId)
    return self:GetRow("common_tbequipeffect", configId)
end

function TableDataManager:GetAmbitionsData(configId)
    if not configId then
        return self:GetTable(self.season_tbname)
    end
    return self:GetRow(self.season_tbname, configId)
end


----------------------------------------------------------------------------------------------------------------------------
--- 表格读取(结束)
----------------------------------------------------------------------------------------------------------------------------

return TableDataManager