{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode"}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "8"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0.3"}, "value": {"Type": "number", "Value": "0.3"}}}, "2": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "14"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0.3"}, "value": {"Type": "number", "Value": "0.3"}}}, "3": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "10"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0.2"}, "value": {"Type": "number", "Value": "0.2"}}}, "4": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "16"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0.2"}, "value": {"Type": "number", "Value": "0.2"}}}, "5": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "虎翼阵造成伤害提升"}, "result": {"Type": "number", "Value": "0"}}}, "6": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr2"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "虎翼阵受到伤害提升"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"onLayerIncreased": ["5.prev"]}, "1": {"next": ["2.prev"]}, "2": {"next": ["6.prev"]}, "3": {"next": ["4.prev"]}, "5": {"next": ["1.prev"]}, "6": {"next": ["3.prev"]}}, "DataFlows": {"5": {"result": ["1.value", "2.value"]}, "6": {"result": ["3.value", "4.value"]}}}