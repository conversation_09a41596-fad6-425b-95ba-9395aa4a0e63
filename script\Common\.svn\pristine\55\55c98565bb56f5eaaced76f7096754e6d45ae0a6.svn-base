﻿function GetSelectWorldPosXY()
    local scene = Game.SceneManager.currentScene
    if scene.__cname == "SlgGameScene" then
        local pos = Game.ActorManager:GetClickObjPos()
        local CoordUtils = xrequire("Common.Utils.CoordUtils")
        local x, y = CoordUtils.World2Offset(pos.x, pos.z)
        return x, y, true
    end
    return 0, 0, false
end

function GetSelectBuildingActorData()
    local scene = Game.SceneManager.currentScene
    if scene.__cname == "SlgGameScene" then
        local pos = Game.ActorManager:GetClickObjPos()
        local CoordUtils = xrequire("Common.Utils.CoordUtils")
        local x, y = CoordUtils.World2Doubled(pos.x, pos.z)
        x, y = scene:SelectGridWithPriority(x, y)
        local buildingActorData = Game.ActorManager:GetBuildingData(x, y)
        if buildingActorData then
            return buildingActorData
        end
    end
    return nil
end

function GetSelectActor()
    return Game.ActorManager:GetChosenActor()
end

GMConfigs = {
    {
        tab = "战斗",
        func = "gmEnableBattleTitle",
        name = "开启战斗Title",
        params = {
            {
                name = "是否显示",
                type = "boolean",
                default = true,
            }
        }
    },
    {
        func = "gmAddCompletedHeroesFromTestBattleData",
        name = "读取Debug.TestBattleData添加记忆卡",
        params = {}
    },
    {
        tab = "玩家",
        func = "gmOfflineNow",
        isServerFunc = true,
        name = "立刻下线",
        params = {
        }
    },
    {
        func = "GmSetTime",
        isServerFunc = true,
        name = "设置时间",
        params = {
            {
                name = "年",
                type = "number",
                default = 2025,
            },
            {
                name = "月",
                type = "number",
                default = 1,
            },
            {
                name = "日",
                type = "number",
                default = 1,
            },
            {
                name = "时",
                type = "number",
                default = 0,
            },
            {
                name = "分",
                type = "number",
                default = 0,
            },
            {
                name = "秒",
                type = "number",
                default = 0,
            }
        }
    },
    {
        func = "gmOccupyLand",
        isServerFunc = true,
        name = "占领地块",
        params = {
            {
                name = "X坐标",
                type = "number",
                default = 0,
            },
            {
                name = "Y坐标",
                type = "number",
                default = 0,
            },
            {
                name = "半径",
                type = "number",
                default = 0,
            }
        },
        GetValueFunc = function()
            local x, y = GetSelectWorldPosXY()
            return {x, y}
        end
    },
    {
        func = "gmSendTemplateMail",
        isServerFunc = true,
        name = "发送模板邮件",
        params = {
            {
                name = "模板ID",
                type = "number",
                default = 1,
            },
            {
                name = "参数列表",
                type = "table",
                default = "{}"
            },
            {
                name = "用户列表",
                type = "table",
                default = "{}"
            },
            {
                name = "是否全服邮件",
                type = "boolean",
                default = 0
            },
            {
                name = "是否来自系统",
                type = "boolean",
                default = 1
            },
            {
                name = "附件",
                type = "table",
                default = "{100001}"
            }
        }
    },
    {
        func = "gmSendRawMail",
        isServerFunc = true,
        name = "发送基础邮件",
        params = {
            {
                name = "标题",
                type = "string",
                default = "邮件标题",
            },
            {
                name = "内容",
                type = "string",
                default = "邮件内容"
            },
            {
                name = "用户列表",
                type = "table",
                default = "{}"
            },
            {
                name = "是否全服邮件",
                type = "boolean",
                default = 0
            },
            {
                name = "邮件类型",
                type = "number",
                default = 4
            },
            {
                name = "是否来自系统",
                type = "boolean",
                default = 1
            },
            {
                name = "附件",
                type = "table",
                default = "{[100001] = 1}"
            }
        }
    },
    {
        func = "gmSetSlgSceneCamera",
        name = "大世界跳转坐标",
        params = {
            {
                name = "x",
                type = "number",
                default = 50,
            },
            {
                name = "z",
                type = "number",
                default = 50,
            },
            {
                name = "高度",
                type = "number",
                default = 3.5,
            }
        }
    },
    {
        func = "gmSetDurability",
        isServerFunc = true,
        name = "设置耐久度",
        params = {
            {
                name = "建筑id",
                type = "number",
                default = 0,
            },
            {
                name = "耐久度",
                type = "number",
                default = 100,
            }
        },
        GetValueFunc = function()
            local buildingActorData = GetSelectBuildingActorData()
            if buildingActorData then
                return { buildingActorData.entityId, 100 }
            end
            return { 0, 100 }
        end
    },
    {
        func = "SetUserId",
        name = "设置名称",
        params = {
            {
                name = "游戏名称",
                type = "string",
                default = "超级小帅",
            }
        }
    },
    {
        func = "EnableBugly",
        name = "启用Bugly",
        params = {
        }
    },
    {
        func = "BuglyLog",
        name = "Bugly信息上报",
        params = {
            {
                name = "日志类型",
                type = "number",
            },
            {
                name = "message",
                type = "string",
            }
        }
    },
    {
        func = "BuglyErrorLog",
        name = "Bugly错误信息上报",
        params = {
            {
                name = "message",
                type = "string",
            },
            {
                name = "stackTrace",
                type = "string",
            }
        }
    },
    {
        func = "gmOpenGuide",
        name = "开启新手引导",
        params = {
        }
    },
    {
        func = "gmCloseGuide",
        name = "关闭新手引导",
        params = {
        }
    },
    {
        func = "gmJumpToStep",
        name = "新手引导下一步",
        params = {
        }
    },
    {
        func = "gmMigrateMainCity",
        isServerFunc = true,
        name = "主堡迁城\n小问题很多，慎用",
        params = {
            {
                name = "X坐标",
                type = "number",
                default = 0,
            },
            {
                name = "Y坐标",
                type = "number",
                default = 0,
            }
        },
        GetValueFunc = function()
            local x, y = GetSelectWorldPosXY()
            return {x, y}
        end
    },
    {
        func = "gmDisconnect",
        isServerFunc = false,
        name = "断线",
        params = {
        }
    },
    {
        func = "gmReconnect",
        isServerFunc = false,
        name = "重连",
        params = {
        }
    },
    {
        func = "gmGetActorInfo",
        isServerFunc = true,
        name = "获取沙盘实体属性",
        params = {
            {
                name = "实体ID",
                type = "number",
                default = 0,
            }
        },
        GetValueFunc = function()
            local actor = GetSelectActor()
            if actor then
                return { actor.id }
            end
            local buildingActorData = GetSelectBuildingActorData()
            if buildingActorData then
                return { buildingActorData.entityId }
            end
            return { 0 }
        end
    },
    {
        func = "gmFinishTaskChapter",
        isServerFunc = true,
        name = "完成任务章节",
        params = {
            {
                name = "章节ID",
                type = "number",
                default = 1,
            }
        },
        GetValueFunc = function()
            for taskId, _ in pairs(Game.Avatar.props.mainTasks) do
                local taskConfig = GameCommon.TableDataManager:GetTaskData(taskId)
                if taskConfig and taskConfig.chapters ~= 0 then
                    return { taskConfig.chapters }
                end
            end
        end
    },
    {
        tab = "镜像",
        func = "gmBindMirrorAccount",
        isServerFunc = true,
        name = "绑定镜像账号",
        params = {
            {
                name = "镜像账号ID列表",
                type = "table",
                default = "{}",
            }
        },
    },
    {
        func = "gmUnbindMirrorAccount",
        isServerFunc = true,
        name = "解绑所有镜像账号",
        params = {}
    },
    {
        func = "gmBindMirrorBotAccount",
        name = "批量绑定机器人",
        params = {
            {
                name = "前缀",
                type = "string",
                default = "bot"
            },
            {
                name = "进程数",
                type = "number",
                default = 1,
            },
            {
                name = "容器数",
                type = "number",
                default = 1,
            },
            {
                name = "机器人数",
                type = "number",
                default = 1,
            }
        },
        GetValueFunc = function()
            local ret = { "bot", 1, 1, 1 }
            pcall(function()
                local config = GameCommon.JsonDataManager:loadJsonFileRaw("../Server/bot/Bot/Configs/bot.json")
                config = GameCommon.json.decode(config)
                local prefix = string.split(config.BOT_FORMAT, "_")[1]
                ret = { prefix, config.PROCESS_NUMS, config.CONTAINER_NUMS, config.BOT_NUMS_PER_CONTAINER }
            end)
            return ret
        end
    },
    {
        tab = "背包",
        func = "gmSetCurrency",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "设置货币数量",
        params = {
            {
                name = "货币ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "数量",
                type = "number",
                default = 100000000,
            }
        }
    },
    {
        func = "gmAddItem",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "添加道具",
        params = {
            {
                name = "道具ID",
                type = "number",
                default = -1,
            },
            {
                name = "数量",
                type = "number",
                default = 3,
            }
        }
    },
    {
        func = "gmConsumeItem",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "消耗道具",
        params = {
            {
                name = "道具ID",
                type = "number",
                default = -1,
            },
            {
                name = "数量",
                type = "number",
                default = 1,
            }
        }
    },
    {
        tab = "养成",
        func = "gmOneClickGrowth",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "一键养成",
        params = {}
    },
    {
        func = "gmSetHeroLevel",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "设置武将等级",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级",
                type = "number",
                default = 50,
            },
            {
                name = "经验(-1不修改)",
                type = "number",
                default = -1,
            }
        }
    },
    {
        func = "gmSetHeroStars",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "设置武将红度",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "红度",
                type = "number",
                default = 5,
            }
        }
    },
    {
        func = "gmSetHeroStamina",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "设置武将体力",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "体力",
                type = "number",
                default = 1,
            }
        }
    },
    {
        func = "gmSetTacticStars",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "设置战法红度",
        params = {
            {
                name = "战法ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "红度",
                type = "number",
                default = 5,
            }
        }
    },
    {
        func = "gmSetTacticLevel",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "设置战法等级",
        params = {
            {
                name = "战法ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级",
                type = "number",
                default = 10,
            }
        }
    },
    {
        func = "gmSetTacticResetCnt",
        isServerFunc = true,
        name = "设置战法可用重置次数",
        params = {
            {
                name = "次数",
                type = "number",
                default = 10,
            }
        }
    },
    {
        tab = "城建",
        func = "gmUpgradeHomeBuilding",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "升级家园建筑",
        params = {
            {
                name = "建筑ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级（-1为最大等级）",
                type = "number",
                default = -1,
            }
        }
    },
    {
        func = "gmUpgradeHomeTech",
        isServerFunc = true,
        isMirrorFunc = true,
        name = "升级家园科技",
        params = {
            {
                name = "科技ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级（-1为最大等级）",
                type = "number",
                default = -1,
            }
        }
    },
    {
        tab = "职业策牌",
        name = "添加职业策牌点数",
        func = "gmAddCareerTalentPoint",
        isServerFunc = true,
        params = {
            {
                name = "点数",
                type = "number",
                default = 13,
            }
        }
    },
    {
        name = "解锁所有天赋树节点",
        func = "gmUnlockAllStrategyTalent",
        isServerFunc = true,
        params = {}
    },
    {
        name = "清理策牌使用次数",
        func = "gmClearStrategyUseCnt",
        isServerFunc = true,
        params = {}
    },
    {
        name = "添加策牌",
        func = "gmAddStrategyCard",
        isServerFunc = true,
        params = {
            {
                name = "策牌ID",
                type = "number",
                default = -1,
            },
            {
                name = "数量",
                type = "number",
                default = 3,
            }
        }
    },
    {
        name = "添加策点",
        func = "gmAddStrategyPoint",
        isServerFunc = true,
        params = {
            {
                name = "策点数量",
                type = "number",
                default = 100,
            }
        }
    },
    -- 暂时屏蔽同盟的接口，不开放同盟
    -- {
    --     tab = "联盟",
    --     func = "gmCreateAlly",
    --     isServerFunc = true,
    --     name = "创建联盟",
    --     params = {
    --         {
    --             name = "联盟名称",
    --             type = "string",
    --             default = "allyName",
    --         }
    --     }
    -- },
    -- {
    --     func = "gmJoinAlly",
    --     isServerFunc = true,
    --     name = "加入联盟",
    --     params = {
    --         {
    --             name = "联盟名称",
    --             type = "string",
    --             default = "allyName",
    --         }
    --     }
    -- },
    -- {
    --     func = "gmLeaveAlly",
    --     isServerFunc = true,
    --     name = "离开联盟",
    --     params = {}
    -- },
    {
        tab = "攻城",
        name = "设置宣战倒计时",
        func = "gmSetAnnounceTimeout",
        isServerFunc = true,
        params = {
            {
                name = "倒计时秒数",
                type = "number",
                default = 10,
            }
        }
    },
    {
        name = "设置攻城倒计时",
        func = "gmSetSiegeTimeout",
        isServerFunc = true,
        params = {
            {
                name = "倒计时秒数",
                type = "number",
                default = 10,
            }
        }
    },
    {
        name = "占领城池",
        func = "gmOccupyCity",
        isServerFunc = true,
        params = {
            {
                name = "城池实例ID",
                type = "number",
                default = 1,
            }
        },
        GetValueFunc = function()
            local buildingActorData = GetSelectBuildingActorData()
            if buildingActorData then
                return { buildingActorData.props.instanceId }
            end
            return { 0 }
        end
    },
    {
        name = "占领建筑",
        func = "gmOccupyBuilding",
        isServerFunc = true,
        params = {
            {
                name = "建筑实体ID",
                type = "number",
                default = 1,
            }
        },
        GetValueFunc = function()
            local buildingActorData = GetSelectBuildingActorData()
            if buildingActorData then
                return { buildingActorData.entityId }
            end
            return { 0 }
        end
    },
    {
        name = "加入集结",
        func = "gmJoinSiegeGathering",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "部队索引(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "大营id",
                type = "number",
                default = 0,
            },
            {
                name = "主力",
                type = "boolean",
                default = 1,
            }
        },
        GetValueFunc = function()
            local buildingActorData = GetSelectBuildingActorData()
            if buildingActorData then
                return { 0, buildingActorData.entityId, 1 }
            end
            return { 0, 0, 1 }
        end
    },
    {
        tab = "霸业赛季",
        name = "联盟立国",
        func = "gmAllyFoundNation",
        isServerFunc = true,
        params = {}
    },
    {
        name = "设置联盟霸业积分",
        func = "gmSetAllyAmbitionsScore",
        isServerFunc = true,
        params = {
            {
                name = "积分",
                type = "number",
                default = 1000,
            }
        }
    },
    {
        name = "设置个人霸业积分",
        func = "gmSetPersonalAmbitionsScore",
        isServerFunc = true,
        params = {
            {
                name = "积分",
                type = "number",
                default = 1000,
            }
        }
    },
    {
        name = "设置个人繁荣度",
        func = "gmSetPersonalProsperity",
        isServerFunc = true,
        params = {
            {
                name = "繁荣度(小于0为取消GM设置)",
                type = "number",
                default = 1000,
            }
        }
    },
    {
        name = "增加个人武勋",
        func = "gmAddPersonalMerit",
        isServerFunc = true,
        params = {
            {
                name = "武勋",
                type = "number",
                default = 1000,
            }
        }
    },
    {
        name = "设置出生地",
        func = "gmSetBirthPlace",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "出生州",
                type = "number",
                default = 0,
            },
            {
                name = "出生郡",
                type = "number",
                default = 0,
            }
        },
        GetValueFunc = function()
            return {Game.WorldAvatar.props.birthCommandery, Game.WorldAvatar.props.birthplace}
        end
    },
    {
        name = "获取所选点信息",
        func = "gmPrintSelectedPlaceInfo",
        params = {
            {
                name = "州ID",
                type = "number",
                default = 0,
            },
            {
                name = "郡ID",
                type = "number",
                default = 0,
            },
            {
                name = "县ID",
                type = "number",
                default = 0,
            }
        },
        GetValueFunc = function()
            local CoordUtils = xrequire("Common.Utils.CoordUtils")
            local x, y, valid = GetSelectWorldPosXY()
            local col, row = CoordUtils.Offset2Doubled(x, y)
            if valid then
                local countyId = Game.SlgGameSComp:GetGridAdministrative(col, row)
                local commanderyId = GameCommon.TableDataManager:GetCommanderyConfig(countyId).id
                local prefectureId = GameCommon.TableDataManager:GetPrefectureConfig(commanderyId).id
                return { prefectureId, commanderyId, countyId }
            end
            return { 0, 0, 0 }
        end
    },
    {
        name = "设置霸业任务进度",
        func = "gmSetAmbitionsTaskProgress",
        isServerFunc = true,
        params = {
            {
                name = "霸业ID",
                type = "number",
                default = 1,
            },
            {
                name = "进度",
                type = "number",
                default = 666,
            }
        }
    },
    {
        name = "加入世族",
        func = "gmJoinClan",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "世族ID",
                type = "number",
                default = -1,
            }
        },
        GetValueFunc = function()
            return {Game.WorldAvatar.props.clanId}
        end
    },
    {
        tab = '部队基础',
        name = '设置部队移速buff',
        func = 'gmSetArmyMoveBuff',
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "ratio",
                type = "number",
                default = 1000,
            },
        }
    },
    {
        name = "设置部队行为等待时间",
        func = "GmSetWaitTime",
        isServerFunc = true,
        params = {
            {
                name = "等待时间",
                type = "number",
                default = 10,
            }
        }
    },
    {
        name = "解散编队",
        func = "gmDismissArmy",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "编队索引(0为全部)",
                type = "number",
                default = 0,
            }
        }
    },
    {
        name = "部队回城",
        func = "gmReturnBase",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "编队索引(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "基地类型(1主堡 2调动 3集结)",
                type = "number",
                default = 3,
            }
        }
    },
    {
        name = "传送部队",
        func = "gmTransferArmy",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "编队索引(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "目标坐标X",
                type = "number",
                default = 0,
            },
            {
                name = "目标坐标Y",
                type = "number",
                default = 0,
            }
        },
        GetValueFunc = function()
            local x, y = GetSelectWorldPosXY()
            return { 0, x, y }
        end
    },
    {
        name = "提升部队带兵上限",
        func = "gmAddSoldierMax",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "增加数量",
                type = "number",
                default = 20000,
            }
        }
    },
    {
        name = "自动上阵",
        func = "gmAutoFillArmy",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
            {
                name = "部队数量",
                type = "number",
                default = 3,
            }
        }
    },
    {
        name = "一键补兵",
        func = "gmFullArmySoldier",
        isServerFunc = true,
        isMirrorFunc = true,
        params = {
        }
    },
    {
        tab = '系统',
        name = 'ReloadLua',
        func = 'gmReloadLua',
        params = {
        }
    },
    {
        name = '显示帧率',
        func = 'gmShowFps',
        params = {
        }
    },
    {
        name = '低画质',
        func = 'gmSetLowGraphicsQuality',
        params = {
        }
    },
    {
        name = '中画质',
        func = 'gmSetMediumGraphicsQuality',
        params = {
        }
    },
    {
        name = '高画质',
        func = 'gmSetHighGraphicsQuality',
        params = {
        }
    },
    {
        name = '极致画质',
        func = 'gmSetUltraGraphicsQuality',
        params = {
        }
    },
    {
        name = '修改开关',
        func = 'gmSetSwitch',
        isServerFunc = true,
        params = {
            {
                name = "开关名",
                type = "string",
                default = "",
            },
            {
                name = "开关值",
                type = "boolean",
                default = true,
            }
        }
    },
    {
        name = '打开协议测试',
        func = 'gmEnableHookRpc',
        isServerFunc = false,
        params = {
            {
                name = "是否打开",
                type = "boolean",
                default = false,
            }
        }
    },
    {
        name = '打开RuntimeInspector',
        func = 'gmOpenRuntimeInspector',
        params = {
        }
    },
    {
        name = '关闭RuntimeInspector',
        func = 'gmCloseRuntimeInspector',
        params = {
        }
    },
    {
        name = '打印UI链',
        func = 'gmPrintUICallChain',
        params = {
        }
    },
    {
        name = '打印红点树',
        func = 'gmPrintRedpointTree',
        params = {
        }
    },
    {
        name = '打印地块状态',
        func = 'gmPrintChunkStates',
        params = {
        }
    },
    {
        name = '触发崩溃',
        func = 'gmCrashTest',
        params = {
            {
                name = "崩溃类型",
                type = "int",
                default = 1,
            },
        }
    },
    {
        name = '打印EventCenter',
        func = 'gmPrintEventCenter',
        params = {
        }
    },
    {
        tab = "玩法",
        name = "完成军演",
        func = "gmCompleteDrill",
        isServerFunc = true,
        params = {}
    },
    {
        name = "重置军演",
        func = "gmResetDrill",
        isServerFunc = true,
        params = {}
    },
}

MIRROR_FUNC_SUFFIX = "·镜"

GM_USE_TARGET = {
    Self = 0b01,
    Mirror = 0b10,
}
GmUseTargetList = {
    { name = "自己+镜像", value = GM_USE_TARGET.Self + GM_USE_TARGET.Mirror },
    { name = "自己", value = GM_USE_TARGET.Self },
    { name = "镜像", value = GM_USE_TARGET.Mirror },
}

DeclareAndSetGlobal("GMConfigs", GMConfigs)

return {
    MIRROR_FUNC_SUFFIX = MIRROR_FUNC_SUFFIX,
    GM_USE_TARGET = GM_USE_TARGET,
    GmUseTargetList = GmUseTargetList,
}