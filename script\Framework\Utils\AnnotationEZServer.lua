-- 统一定义注解

--region alias

---@alias DatabaseId string
---@alias NodeUid string
---@alias HttpSessionId int

--endregion

--region libs

--endregion

--region engine

--region EZE framework server

---从DB读取Entity数据，并创建Entity
---@param dbmgr EntityId @从该dbmgr读取Entity数据
---@param db string @Entity数据所在的mongo db名
---@param collection string @Entity数据所在的mongo collection名
---@param dbid DatabaseId @读取Entity的DatabaseId
---@param ename string @Entity类型名
---@param callback fun(errcode: integer, entity_id: EntityId): void @回调函数，callback(errcode, entity_id)
---@return void @⽆
function EZE.createEntityFromDB(dbmgr, db, collection, dbid, ename, callback) end

---根据Entity的Def定义，创建对应索引
---@param dbmgr EntityId @将创建索引请求发送到该dbmgr
---@param db string @mongo database
---@return void
function EZE.createEntitiesIndexes(dbmgr, db) end

---向集群注册global entity
---@param name string @唯一名
---@param eid EntityId @global entity的entity id
---@param ready bool @是否已经ready
---@param instanceData string @GlobalEntity实例附带的数据，如指定的分片等，这里已经打包好了
---@return void
function EZE.registerGlobalEntity(name, eid, ready, instanceData) end

---向集群注销global entity
---@param eid EntityId @global entity的entity id
---@return void
function EZE.unregisterGlobalEntity(eid) end

---通知集群global entity已经初始化完成了
---@param eid EntityId @global entity的entity id
---@return void
function EZE.setGlobalEntityReady(eid) end

---向集群发送关服请求
---@return boolean @请求是否发送成功
function EZE.startShutdown() end

---告知集群当前container可以进入下阶段了
---@return boolean @是否成功
function EZE.continueShutdown() end

---获得node的唯一id
---@return NodeUid
function EZE.nodeUid() end

---从ContainerId中获取NodeId
---@param cid ContainerId
---@return NodeId
function EZE.containerIdToNodeId(cid) end

---从EntityId中获取NodeId
---@param eid EntityId
---@return NodeId
function EZE.entityIdToNodeId(eid) end

---从EntityId中获取ContainerId
---@param eid EntityId
---@return ContainerId
function EZE.entityIdToContainerId(eid) end

---将一个lua基础类型推送到GlobalData
---@param key string @数据的key
---@param bin string @打包好的二进制数据
---@param cas? bool @compare and set
---@param comp? string @compare data
---@return ContainerId
function EZE.pushGlobalData(key, bin, cas, comp) end

---请求删除一个GlobalData数据
---@param key string @数据的key
---@return ContainerId
function EZE.popGlobalData(key) end

---获得watcher的值
---@param ... int | string @路径
---@return any
function EZE.getWatcher(...) end

---设置watcher的值
---@param val any @数据
---@param ... int | string @路径
---@return bool
function EZE.setWatcher(val, ...) end

--endregion

--region LuaGameEntityBase

---@class LuaGameEntityBase:LuaEntityBase
LuaGameEntityBase = {}

---绑定Entity到客户端连接，会⾃动在客户端创建该Entity。
---@param gate_id EntityId @客户端连接所属的gate entity id
---@param session_id? SessionId @客户端连接的session id
---@return boolean @lua中返回bool，表⽰操作是否成功
function LuaGameEntityBase:connectClient(gate_id, session_id) end

---Entity与客户端连接解除绑定，会⾃动在客户端销毁该Entity
---@return void
function LuaGameEntityBase:detachClient() end

---断开与Entity绑定的客户端的⽹络连接
---@return void
function LuaGameEntityBase:disconnectClient() end

---向该Entity的客户端发送rpc
---@param method_name string @rpc名，对应rpc处理函数名
---@vararg any @rpc参数
---@return boolean | nil @发送成功lua返回true，失败lua返回nil
function LuaGameEntityBase:clientRpc(method_name, ...) end

---向其他服务端Entity发送rpc.
---@param mailbox LuaMailbox @⽬标Entity的mailbox
---@param method_name string @rpc名，对应rpc处理函数名。
---@vararg any @rpc参数。
---@return boolean | nil @发送成功lua返回true，失败lua返回nil
function LuaGameEntityBase:serverRpc(mailbox, method_name, ...) end

---向其他服务端Entity发送rpc，并接收该rpc的回调
---@param cb EntityCallback @rpc回调，格式是callback(errcode, ...)
---@param timeout number @回调超时时间
---@param mailbox LuaMailbox @⽬标Entity的mailbox
---@param method_name string @rpc名，对应rpc处理函数名。
---@vararg any @rpc参数。
---@return boolean | nil @发送成功lua返回true，失败lua返回nil
function LuaGameEntityBase:serverRpcCallback(cb, timeout, mailbox, method_name, ...) end

---给可返回的rpc手动回包
---@param dst EntityId @⽬标EntityId
---@param msgtype int @rpc的msgtype
---@param cbid int @目标的cbid
---@param method_name string @rpc名
---@param err int @错误码
---@vararg any @返回的参数
---@return boolean | nil @发送成功lua返回true，失败lua返回nil
function LuaGameEntityBase:serverRpcReturn(dst, msgtype, cbid, method_name, err, ...) end

---获取Entity的mailbox
---@return LuaMailbox @该Entity的mailbox，LuaMailbox类型
function LuaGameEntityBase:getMailbox() end

---获取Entity是否已经与客户端连接绑定
---@return boolean
function LuaGameEntityBase:hasClient() end

---获取与Entity绑定的客户端连接信息
---@return EntityId, SessionId @gate_id, session_id. 两个返回值
function LuaGameEntityBase:getClient() end

---获取mongo collection代理，LuaMongoCollection。⽤来执⾏mongo操作。
---@param dbmgr EntityId @dbmgr entity id，后续mongo操作将在该dbmgr上执⾏。
---@param db string @mongodb db名
---@param collection string @mongodb collection名
---@return LuaMongoCollection
function LuaGameEntityBase:mongoCollection(dbmgr, db, collection) end

---Entity存盘
---@param dbmgr EntityId @dbmgr entity id，存盘操作将在该dbmgr上执⾏。
---@param db string @存储到的mongodb database名
---@param collection string @存储到的mongodb collection名
---@param callback? fun(succ: boolean): void @存盘的回调，callback(succ)。参数succ表⽰存盘是否成功。
---@return ⽆
function LuaGameEntityBase:saveTo(dbmgr, db, collection, callback) end

---获取Entity的DatabaseId，DatabaseId为Entity在数据库中的唯⼀ID，存储在mongodb中的_id字段。
---@return DatabaseId @DatabaseId
function LuaGameEntityBase:getDBID() end

---Entity term
---@return integer @term
function LuaGameEntityBase:getTerm() end

---Entity term index
---@return integer @term index
function LuaGameEntityBase:getTermIndex() end

---注册自动同步属性的entity
---@param eid EntityId
---@return void
function LuaGameEntityBase:registerRecoverSync(eid) end

---注销自动同步属性的entity
---@param eid EntityId
---@return void
function LuaGameEntityBase:unregisterRecoverSync(eid) end

--endregion

--region LuaGateEntityBase

---@class LuaGateEntityBase:LuaEntityBase
LuaGateEntityBase = {}

---开始对外提供服务，监听端⼝，等待客户端连接。
---@param host string @监听的域名或ip
---@param port integer @监听的端⼝
---@param backlog integer @建⽴中的连接的最⼤数量
function LuaGateEntityBase:start(host, port, backlog) end

--endregion LuaGateEntityBase

--region LuaKafkaProducer

---@class LuaKafkaProducer: LuaEntityBase
LuaKafkaProducer = {}

---开始对外提供服务
---@param conf table @扁平化的producer配置
---@param interval? number @callback的帧率，单位是秒
---@return boolean
function LuaKafkaProducer:start(conf, interval) end

---停止对外提供服务
function LuaKafkaProducer:stop() end

---发送数据
---@param topic string
---@param payload string
---@param key? string @默认为""
---@param partition? int @默认为-1
---@param cb? fun(err:int):void @发送回调
---@return boolean
function LuaKafkaProducer:produce(topic, payload, key, partition, cb) end

---获取错误说明
---@param err int
---@return string
function LuaKafkaProducer.errormsg(err) end

--endregion LuaKafkaProducer

--region LuaKafkaConsumer

---@class LuaKafkaConsumer: LuaEntityBase
LuaKafkaConsumer = {}

---开始拉取数据，注意控制qps
---@param conf table @扁平化的consumer配置
---@param cb fun(err:int, topic:string, payload:string, key:string, partition:int):void @拉取数据后的回调
---@param interval? number @自动拉取数据(poll)的帧率，单位是秒，默认为0.2秒
---@param num? int @每帧处理数据的条数，默认为100
---@param topics string[] @subscribe的topic列表
---@param topic_partition table<string,int> @assign的topic和partition，key为topic，value为partition
---@return boolean
function LuaKafkaConsumer:start(conf, cb, interval, num, topics, topic_partition) end

---停止拉取数据
function LuaKafkaConsumer:stop() end

---进行一次数据拉取；一般不需要使用；可用来自定义拉取的逻辑
---@return int | nil @err，如果为nil说明没有拉取到数据
function LuaKafkaConsumer:poll() end

---获取错误说明
---@param err int
---@return string
function LuaKafkaConsumer:errormsg(err) end

--endregion LuaKafkaConsumer

--region LuaProp

---@class LuaProp
LuaProp = {}

---获取存储数据的lua table。
---@return table @存储数据的table
function LuaProp:raw() end

---向list中插⼊元素
---@overload fun(value: any): void
---@param pos? integer @插⼊的位置，不传⼊则默认插⼊list尾
---@param value any @待插⼊的元素
---@return void
function LuaProp:insert(pos, value) end

---从list中删除元素
---@overload fun(): void
---@param pos? integer @待删除元素的位置，不传⼊则默认删除list最后⼀个
---@return void
function LuaProp:remove(pos) end

---将list中的元素排序
---@generic ELEM
---@overload fun(): void
---@param comp? fun(x: ELEM, y: ELEM): boolean @排序⽐较函数，不传⼊则使⽤<。
---@return void
function LuaProp:sort(comp) end

--endregion

--region LuaMailbox

---@class LuaMailbox
LuaMailbox = {}

---将mailbox信息格式化为LuaMailbox(entity_id)
---@return string @格式化后的字符串
function LuaMailbox:toString() end

---获取mailbox对应的EntityId
---@return EtnityId @EntityId
function LuaMailbox:getEntityId() end

---将mailbox序列化为字符串
---@return string @序列化后的字符串
function LuaMailbox:serializeToString() end

---从mailbox序列化的字符串反序列化并创建mailbox
---@param data string @序列化后的字符串
---@return LuaMailbox
function LuaMailbox.createFromString(data) end

--endregion

--region LuaDBMgr

---@class LuaDBMgr
LuaDBMgr = {}

--endregion

--region LuaHttpServer

---@class LuaHttpServer
---@field new fun(): LuaHttpServer
LuaHttpServer = {}

---@class LuaHttpRequest
---@field version string
---@field method string
---@field path string
---@field params string
---@field headers string
---@field body string

--endregion

--endregion

--region global

containerId = EZE.getContainerId()

containerType = EZE.getContainerType()

--endregion
