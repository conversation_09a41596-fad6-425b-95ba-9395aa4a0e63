{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "2"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "3": {"Type": "HealNode", "Field": {"targetIds": [], "healFactor": {"Type": "string", "Value": ""}, "healFactorNew": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": ""}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedHeal": {"Type": "boolean", "Value": "False"}, "fixedHealValue": {"Type": "number", "Value": "0"}, "healHeroIds": [], "healPackageIds": [], "missHeroIds": []}}, "4": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "2"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "6": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "heal1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "包扎治疗率，a=智力"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"next": ["1.prev"]}, "1": {"next": ["4.prev"]}, "4": {"next": ["6.prev"]}, "6": {"next": ["3.prev"]}}, "DataFlows": {"1": {"targetIds": ["3.targetIds"]}, "4": {"attributeValues": ["6.a"]}, "6": {"result": ["3.<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}