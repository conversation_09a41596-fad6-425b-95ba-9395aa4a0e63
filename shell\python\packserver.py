import os
import re
import hashlib
import argparse
import datetime
import getpass
from ftplib import error_perm, FTP
from posixpath import dirname
from tqdm import tqdm


class Conf:
    ROOT = os.path.join(os.path.dirname(__file__), "../..")
    PACKAGE_EXCLUDE = ["./log", "*/.svn", "*.pdb", "*.exe", "*.dll", "*.lib", "./docs"]
    PATCH_INCLUDE = ["script/*", "shell/*"]
    PATCH_EXCLUDE = [
        "script/Defs/*",
        "script/App/Utils/Annotation.lua",
        "script/App/Utils/AnnotationDef.lua",
        "script/Framework/Utils/AnnotationEZCommon.lua",
        "script/Framework/Utils/AnnotationEZServer.lua",
    ]
    PATCH_FILE_PROCESS = [
        lambda file: file.replace("/", "."),
        lambda file: re.sub(r'\.lua$', r'', file),
        lambda file: re.sub(r'^script\.App\.', r'', file),
        lambda file: re.sub(r'^script\.', r'', file),
    ]
    SVN_URL_BRANCH = re.compile(r".+/(\S+)/Server")
    FTP_ADDR = ["*************", 21, "ftpadmin", "PQ4uZ54ShzU6"]

    PACKAGE_EXCLUDE_STR = " --exclude=".join(PACKAGE_EXCLUDE)
    PATCH_INCLUDE_PATTERNS = [re.compile(p) for p in PATCH_INCLUDE]
    PATCH_EXCLUDE_PATTERNS = [re.compile(p) for p in PATCH_EXCLUDE]


def getBranch():
    url = os.popen("svn info --show-item url").read()
    search = Conf.SVN_URL_BRANCH.search(url)
    if search and search.group(1):
        return search.group(1)
    return "branch"


def getFileMd5(fpath: str):
    m = hashlib.md5()  #创建md5对象
    with open(fpath,'rb') as fobj:
        while True:
            data = fobj.read(4096)
            if not data:
                break
            m.update(data)  #更新md5对象

    return m.hexdigest()  #返回md5对象


def getVersion(default: str = "0.0.0"):
    with open("version.txt", 'r') as fobj:
        return fobj.readline() or default
    return default


def calcFileName(fpath: str, baseRev: "int | None" = None):
    if not fpath.endswith(".tar.gz"):
        raise RuntimeError(f"package file error: {fpath}")
    branch = getBranch()
    tm = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    md5 = getFileMd5(fpath)
    ver = getVersion()
    svn = int(os.popen("svn info --show-item revision").read())
    fp = f"{fpath[:-7]}_{branch}_{ver}_{baseRev}_{svn}_{tm}_{md5}.tar.gz" if baseRev else f"{fpath[:-7]}_{branch}_{ver}_{svn}_{tm}_{md5}.tar.gz"
    print("*" * (len(fp) + 8))
    print(f"**  {' '*len(fp)}  **")
    print(f"**  {fp}  **")
    print(f"**  {' '*len(fp)}  **")
    print("*" * (len(fp) + 8))
    _, rfn = os.path.split(fp)
    return fp, "/package/" + re.compile(r"(.+?)_(.+)_(.+?)_(.+?).tar.gz").search(fp).group(2) + "/" + rfn


def ftpCwd(ftp, path, first_call=True):
    try:
        ftp.cwd(path)
    except error_perm:
        ftpCwd(ftp, dirname(path), False)
        ftp.mkd(path)
        if first_call:
            ftp.cwd(path)


def uploadPackage(fp, rfp, host, port, username, password, active=False):
    ftp = FTP()
    # ftp.set_debuglevel(2)
    ftp.connect(host, port)
    ftp.login(username, password)
    if active:
        ftp.set_pasv(False)
    rpath, rfn = os.path.split(rfp)
    ftpCwd(ftp, rpath)

    with open(fp, 'rb') as f:
        filesize = os.path.getsize(fp)
        with tqdm(unit='blocks', unit_scale=True, leave=True, miniters=1, desc='Uploading......', total=filesize) as tqdm_instance:
            ftp.storbinary('STOR ' + rfn, f, callback = lambda sent: tqdm_instance.update(len(sent)))
    ftp.close()


def processPatchFile(filepath: str):
    for proc in Conf.PATCH_FILE_PROCESS:
        filepath = proc(filepath)
    return filepath


class PswAction(argparse.Action):
    def __call__(self, parser, namespace, values, option_string):
        if values is None:
            values = getpass.getpass()
        setattr(namespace, self.dest, values)


if __name__ == "__main__":

    parser = argparse.ArgumentParser()
    parser.add_argument("-u", "--username", required=False, help="svn username")
    parser.add_argument("-p", "--password", required=False, action=PswAction, nargs='?', dest='password', help="svn password")
    parser.add_argument("-v", "--version", required=False, help="svn version")
    parser.add_argument("-a", "--patch", action="store_true", required=False, help="pack patch for reload")
    parser.add_argument("-b", "--base", required=False, help="basic svn version(patch only)")
    parser.add_argument("-l", "--localonly", required=False, action="store_true", help="do not upload to remote")
    parser.add_argument("--asanpath", required=False, type=str, help="svn path(relative to server) with address sanitize")
    args = parser.parse_args()

    userpass = ""
    if args.username:
        userpass += f" --username {args.username}"
    if args.password:
        userpass += f" --password {args.password}"

    # update
    os.chdir(Conf.ROOT)
    os.system("svn revert . -R")
    os.system("svn cleanup . --remove-unversioned")
    os.system(f"svn up -r {args.version or 'HEAD'} {userpass}")
    # asan
    if args.asanpath:
        url_server = os.popen("svn info --show-item url").read()
        if not url_server.startswith("https://"):
            raise RuntimeError(f"unsupportted svn path: {url_server}")
        sim_path = url_server[len("https://"):]
        url_asan = "https://" + os.path.normpath(os.path.join(sim_path, args.asanpath)).replace("\\", "/")
        print("url asan: ", url_asan)
        os.system(f"svn export {url_asan} ./bin --force")
    # package
    if args.patch:
        if not args.base:
            raise ValueError("require --base/-b while gen patch")
        # 比较两个版本的差异
        files = []  # type: list[str]
        diffs = os.popen(f"svn log -r {args.base}:{args.version or 'HEAD'} -v -q {userpass}").read().split("\n")
        for line in diffs:
            if not line:
                continue
            subs = line.split()
            if len(subs) == 2:
                matches = [pt.search(subs[1]).span()[0] for pt in Conf.PATCH_INCLUDE_PATTERNS if pt.search(subs[1])]
                if len(matches) != 1:
                    if len(matches) > 1:
                        print(f"ERROR - file {subs[1]} mismatch path: {Conf.PATCH_INCLUDE}")
                    continue
                fp = subs[1][matches[0]:]
                if any([pt.search(fp) for pt in Conf.PATCH_EXCLUDE_PATTERNS]):
                    print(f"ERROR - file {fp} is in invalid path: {Conf.PATCH_EXCLUDE}")
                    continue
                if subs[0] == "D":
                    print(f"ignore deleted file: {fp}")
                    continue
                files.append(fp)
        # 去重
        files = list(set(files))
        if not files:
            raise RuntimeError("no diff files!")
        # reload文件列表
        with open("reload_files.txt", "w") as f:
            f.write('\n'.join([processPatchFile(f) for f in files if f.endswith('.lua')]))
        files.append("reload_files.txt")
        # 打包文件
        print("tar".center(50, "-"))
        os.system(f"tar czvf patch.tar.gz {' '.join(files)}")
        fp, rfp = calcFileName('patch.tar.gz', args.base)
        os.system(f"mv patch.tar.gz {fp}")
    else:
        svnbase = int(os.popen('svn info --show-item revision').read())
        os.system(f"echo {svnbase} > svn_base.txt")
        print("tar".center(50, "-"))
        os.system(f"tar czvf server.tar.gz --exclude={Conf.PACKAGE_EXCLUDE_STR} ./")
        fp, rfp = calcFileName('server.tar.gz')
        os.system(f"mv server.tar.gz {fp}")
    if not args.localonly:
        print("upload to remote:", rfp)
        uploadPackage(fp, rfp, *Conf.FTP_ADDR)
