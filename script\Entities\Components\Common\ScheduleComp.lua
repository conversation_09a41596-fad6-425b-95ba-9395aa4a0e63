local ComponentBase = xrequire("Framework.Entities.Components.ComponentBase")


ScheduleRecTag = {
    Avatar = 1,
    AmbitionsService = 2,

    MaxValue = 1000,
}

---@type table<int, Entity>
ScheduleHost = {}

---@class SScheduleProps
---@field scheduleRec table<ScheduleKey, ScheduleCSV>

---@class SScheduleComp: ComponentBase, SEntity
---@field ScheduleRecTag int
---@field props SScheduleProps
ScheduleComp = DefineClass("ScheduleComp", ComponentBase.ComponentBase)

function ScheduleComp:ctor()
    self:initSchedules()
end

-- 自动设置ScheduleRecTag
function ScheduleComp.onComposed(comp, host)
    ComponentBase.ComponentBase.onComposed(comp, host)
    host.ScheduleRecTag = ScheduleRecTag[host.__cname]
    ScheduleHost[host.ScheduleRecTag] = host
end

---@param scheduleId ScheduleId
---@return ScheduleKey
function ScheduleComp:toScheduleKey(scheduleId)
    assert(self.ScheduleRecTag < ScheduleRecTag.MaxValue)
    return scheduleId * ScheduleRecTag.MaxValue + self.ScheduleRecTag
end

---@param key ScheduleKey
---@return ScheduleId
function ScheduleComp:fromScheduleKey(key)
    return math.floor(key / ScheduleRecTag.MaxValue)
end

---@param schdId ScheduleId
---@param csv? ScheduleCSV
function ScheduleComp:recordSchedule(schdId, csv)
    local key = self:toScheduleKey(schdId)
    self.props.scheduleRec[key] = csv or EZGlobal.Schedule:getCurScheduleCSV(schdId)
end

function ScheduleComp:GetCsv(schdId)
    local key = self:toScheduleKey(schdId)
    return self.props.scheduleRec[key]
end

function ScheduleComp:initSchedules()
    for schdId, slot in pairs(EZGlobal.Schedule.scheduleSlots) do
        local schdKey = self:toScheduleKey(schdId)
        for key, calls in pairs(slot) do
            if key == self.ScheduleRecTag then
                local csv = EZGlobal.Schedule:getCurScheduleCSV(schdId)
                if self.props.scheduleRec[schdKey] ~= csv then
                    for cb, _ in pairs(calls) do
                        lxpcall(self[cb], self)
                    end
                    self.props.scheduleRec[schdKey] = csv
                end
            end
        end
    end
end

---@param schdId ScheduleId
---@param cb EntityCallback
---@param checkRec boolean
---@param rec boolean
function ScheduleComp:scheduleOnce(schdId, cb, checkRec, rec)
    local csv = EZGlobal.Schedule:getCurScheduleCSV(schdId)
    if not checkRec then
        if self.props.scheduleRec[self:toScheduleKey(schdId)] == csv then
            return
        end
    end
    if rec then
        self.props.scheduleRec[self:toScheduleKey(schdId)] = csv
    end
    self:entityCallback(cb, csv)
end


-- for emmylua,generate by eze_annotation
return {
    ScheduleRecTag = ScheduleRecTag,
    ScheduleHost = ScheduleHost,
    ScheduleComp = ScheduleComp,
}
