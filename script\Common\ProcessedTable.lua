local enum_details = xrequire("Table.Defines.enum_details")

--启动运行时初始化的一些Table数据，例如表的逆映射
--后处理不方便生成新的独立数据结构，先在初始化处理

function _InitTacticToHeroMap()
    --武将自带战法id到武将id的映射
    TACTIC_TO_HERO_MAP = {}
    for heroId, data in pairs(GameCommon.TableDataManager:GetAllTemplateHeroData()) do
        if data.selfTacticId ~= 0 then
            TACTIC_TO_HERO_MAP[data.selfTacticId] = heroId
        end
    end
end

function _InitHomeTechMaxLevelMap()
    HOME_TECH_MAX_LEVEL_MAP = {}
    for _, data in ipairs(GameCommon.TableDataManager:GetTable("common_tbhometech")) do
        if not HOME_TECH_MAX_LEVEL_MAP[data.id] then
            HOME_TECH_MAX_LEVEL_MAP[data.id] = data.maxLevel
        else
            HOME_TECH_MAX_LEVEL_MAP[data.id] = math.max(HOME_TECH_MAX_LEVEL_MAP[data.id], data.maxLevel)
        end
    end
end

_CHAPTER_TASK_DIV = 10000

function _InitTaskChapterMap()
    TASK_CHAPTER_MAP = {}
    local table = GameCommon.TableDataManager:GetTable("common_tbtask")
    
    
    for _, data in pairs(table) do
        -- TODO(qun): 周期任务接入
        if data.chapters == 0 then
            if not TASK_CHAPTER_MAP[data.id] then
                TASK_CHAPTER_MAP[data.id] = {}
            end
        else
            -- 断言一下防止死循环
            assert(table[data.chapters].chapters == 0, "task chapter error")
            if not TASK_CHAPTER_MAP[data.chapters] then
                TASK_CHAPTER_MAP[data.chapters] = {}
            end
            TASK_CHAPTER_MAP[data.chapters][data.id] = true
        end
    end
    -- 校验章节任务
    for taskId, _ in pairs(TASK_CHAPTER_MAP) do
        assert(taskId == GameCommon.TableDataManager:GetTaskConstData("InitTask") or TASK_CHAPTER_MAP[taskId - _CHAPTER_TASK_DIV])
        LAST_TASK_CHAPTER_ID = math.max(taskId, LAST_TASK_CHAPTER_ID or 0)
    end
end

function _InitInteractiveBehaviorJudge()
    INTERACTIVE_BEHAVIOR_JUDGE = {}
    tb = INTERACTIVE_BEHAVIOR_JUDGE
    for _, info in pairs(GameCommon.TableDataManager:GetTable("common_tbjudgebehaviorto")) do
        if not tb[info.valid] then
            tb[info.valid] = {}
        end
        for _, first in ipairs(info.first) do
            if not tb[info.valid][first] then
                tb[info.valid][first] = {}
            end
            for _, second in ipairs(info.second) do
                if not tb[info.valid][first][second] then
                    tb[info.valid][first][second] = {}
                end
                for _, relation in ipairs(info.relation) do 
                    tb[info.valid][first][second][relation] = true
                end
            end
        end
    end
end

function _InitBehaviorStatus()
    INDEX_2_STATUS = {
        {groupName="main", propsName="mainStatus"},
        {groupName="combat", propsName="combatStatus"},
        {groupName="deploy", propsName="deployStatus"},
    }
    ST_GROUP_NAME_2_INDEX = {}
    ST_PROPS_NAME_2_INDEX = {}
    for index, info in ipairs(INDEX_2_STATUS) do
        ST_GROUP_NAME_2_INDEX[info.groupName] = index
        ST_PROPS_NAME_2_INDEX[info.propsName] = index
    end
    -- 部队状态分组
    ARMY_STATUS_GROUP = {}
    local groupCnt = 0
    local inGroupCnt = {}
    for status, info in pairs(enum_details.GetEnumDetail("ArmyStatus")) do
        for tag, _ in pairs(info.Tags) do
            if not ARMY_STATUS_GROUP[tag] then
                ARMY_STATUS_GROUP[tag] = {}
                groupCnt = groupCnt + 1
                inGroupCnt[tag] = 0
            end
            ARMY_STATUS_GROUP[tag][status] = true
            inGroupCnt[tag] = inGroupCnt[tag] + 1
            break
        end
    end
    -- 单体状态行为判断
    BEHAVIOR_JUDGE = {blacklist = {}, whitelist = {}, errorTextIdList = {}}
    for behaviorType, info in pairs(enum_details.GetEnumDetail("BehaviorType")) do
        local col = info.Name
        local list = {}
        if info.Tags.blacklist then
            BEHAVIOR_JUDGE.blacklist[behaviorType] = list
            assert(not info.Tags.whitelist)
            for id, row in pairs(GameCommon.TableDataManager:GetTable("common_tbvalidbehavior")) do
                if not row[col] then
                    list[row.status] = true
                end
            end
        else
            BEHAVIOR_JUDGE.whitelist[behaviorType] = list
            assert(info.Tags.whitelist)
            for id, row in pairs(GameCommon.TableDataManager:GetTable("common_tbvalidbehavior")) do
                if row[col] then
                    list[row.status] = true
                end
            end
        end
    end
    for id, row in pairs(GameCommon.TableDataManager:GetTable("common_tbvalidbehavior")) do
        BEHAVIOR_JUDGE.errorTextIdList[row.status] = row.error_text_id
    end
    -- 状态规则配置
    STATUS_RULE_CONFIG = {}
    assert(groupCnt <= 6)  -- 状态id占用10bit，最多6个分组

    function _ParseConfig(info, statusIndex, key, match)
        if not INDEX_2_STATUS[statusIndex] then
            if STATUS_RULE_CONFIG[key] then
                if STATUS_RULE_CONFIG[key].match > match or STATUS_RULE_CONFIG[key].rule < info.id then
                    return
                end
            end
            STATUS_RULE_CONFIG[key] = {rule = info.id, match = match}
            return
        end
        local groupName = INDEX_2_STATUS[statusIndex].groupName
        if table.isempty(info[groupName]) then
            for status, _ in pairs(ARMY_STATUS_GROUP[groupName]) do
                _ParseConfig(info, statusIndex + 1, bit.bor(key, bit.lshift(status, (statusIndex - 1) * 10)), match)
            end
        else
            for _, status in ipairs(info[groupName]) do
                _ParseConfig(info, statusIndex + 1, bit.bor(key, bit.lshift(status, (statusIndex - 1) * 10)), match + 1)
            end
        end
    end

    for _, info in pairs(GameCommon.TableDataManager:GetTable("common_tbbehaviorconfig")) do
        _ParseConfig(info, 1, 0, 0)
    end
    local needRuleCnt = 1
    for tag, cnt in pairs(inGroupCnt) do
        needRuleCnt = needRuleCnt * cnt
    end
    local hasRuleCnt = 0
    for _, _ in pairs(STATUS_RULE_CONFIG) do
        hasRuleCnt = hasRuleCnt + 1
    end
    assert(needRuleCnt == hasRuleCnt)
end

function _InitActorContainer()
    ACTOR_CONTAINER_JUDGE = {}

    for _, info in pairs(GameCommon.TableDataManager:GetTable("common_tbactorcontainer")) do
        if not ACTOR_CONTAINER_JUDGE[info.valid] then
            ACTOR_CONTAINER_JUDGE[info.valid] = {}
        end
        for _, first in ipairs(info.first) do
            if not ACTOR_CONTAINER_JUDGE[info.valid][first] then
                ACTOR_CONTAINER_JUDGE[info.valid][first] = {}
            end
            for _, second in ipairs(info.second) do
                if not ACTOR_CONTAINER_JUDGE[info.valid][first][second] then
                    ACTOR_CONTAINER_JUDGE[info.valid][first][second] = {}
                end
                for _, relation in ipairs(info.relation) do
                    ACTOR_CONTAINER_JUDGE[info.valid][first][second][relation] = true
                end
            end
        end
    end
end

function _InitCareerTree()
    CAREER_TALENT_POINT_MAX = 0
    for k, v in pairs(GameCommon.TableDataManager:GetTable("common_tbcareertree")) do
        CAREER_TALENT_POINT_MAX = CAREER_TALENT_POINT_MAX + 1
    end

    -- 基础策牌列表
    BASE_STRATEGY_LIST = {}
    for _, data in pairs(GameCommon.TableDataManager:GetTable("common_tbstrategy")) do
        if data.is_base then
            table.insert(BASE_STRATEGY_LIST, data.id)
        end
    end
end

function _InitMapElement()
    MAP_ELEMENT_TYPE_LEVEL_MAP = {}
    for _, data in pairs(GameCommon.TableDataManager:GetTable("slg_tbmapelement")) do
        if not MAP_ELEMENT_TYPE_LEVEL_MAP[data.type] then
            MAP_ELEMENT_TYPE_LEVEL_MAP[data.type] = {}
        end
        MAP_ELEMENT_TYPE_LEVEL_MAP[data.type][data.level] = data.id
    end
end

function _InitTokenMap()
    HERO_2_TOKEN_MAP = {}
    TACTIC_2_TOKEN_MAP = {}
    for tokenId, info in pairs(GameCommon.TableDataManager:GetAllTokenData()) do
        if info.hero ~= 0 then
            HERO_2_TOKEN_MAP[info.hero] = tokenId
        end
        if info.tactic ~= 0 then
            TACTIC_2_TOKEN_MAP[info.tactic] = tokenId
        end
    end
end

function _InitEquip()
    ALL_EQUIP_IDS = {}  -- 各类装备的id列表，用于随机
    for _, data in pairs(GameCommon.TableDataManager:GetTable("common_tbequip")) do
        if not ALL_EQUIP_IDS[data.equip_type] then
            ALL_EQUIP_IDS[data.equip_type] = {}
        end
        table.insert(ALL_EQUIP_IDS[data.equip_type], data.id)
    end
    for _, data in pairs(GameCommon.TableDataManager:GetTable("common_tbhorse")) do
        if not ALL_EQUIP_IDS[data.equip_type] then
            ALL_EQUIP_IDS[data.equip_type] = {}
        end
        table.insert(ALL_EQUIP_IDS[data.equip_type], data.id)
    end
    ALL_QUALITY_TO_EFFECT = {}
    for effectId, info in pairs(GameCommon.TableDataManager:GetTable("common_tbequipeffect")) do
        if not ALL_QUALITY_TO_EFFECT[info.quality] then
            ALL_QUALITY_TO_EFFECT[info.quality] = {}
        end
        table.insert(ALL_QUALITY_TO_EFFECT[info.quality], effectId)
    end
end

function _InitSegmentMap()
    ID_SEG_TO_TYPE = {}
    for idType, info in pairs(GameCommon.TableDataManager:GetTable("common_tbidsegment")) do
        for segId = info.id_start, info.id_end do
            assert(not ID_SEG_TO_TYPE[segId], 
                string.format("Segment ID %d already exists in segment %d, table %s", segId, idType, info.table_name))
            ID_SEG_TO_TYPE[segId] = idType
        end
    end
end

function _InitSeasonData()
    -- 生成按照开始时间排序的霸业阶段id列表
    AMBITIONS_PROCESS_LIST = {}
    -- 生成各个霸业阶段对应的配置
    AMBITIONS_PROCESS_CONFIG = {}

    local tmp = {}
    local switchStatus = {}
    for processId, config in pairs(GameCommon.TableDataManager:GetAmbitionsData()) do
        table.insert(tmp, config)
    end
    table.sort(tmp, function(a, b)
        return a.begin < b.begin
    end)
    for _, config in ipairs(tmp) do
        table.insert(AMBITIONS_PROCESS_LIST, config.id)
        for _, swConfig in ipairs(config.switches) do
            if swConfig._type_ == "SwitchFunc" then
                switchStatus[swConfig.switch_name] = swConfig.set_status
            end
        end
        local curProcessConfig = {
            limit_pack = config.limit_pack,
            switches = {},
        }
        for k, v in pairs(switchStatus) do
            curProcessConfig.switches[k] = v
        end
        for _, swConfig in ipairs(config.switches) do
            if swConfig._type_ == "LimitSwitchFunc" then
                curProcessConfig.switches[swConfig.switch_name] = swConfig.set_status
            end
        end
        AMBITIONS_PROCESS_CONFIG[config.id] = curProcessConfig
    end
end

--region 对外接口

function GetNextChapterTaskId(taskId)
    assert(TASK_CHAPTER_MAP[taskId])
    local nextTaskId = taskId + _CHAPTER_TASK_DIV
    if not TASK_CHAPTER_MAP[nextTaskId] then
        return nil
    end
    return nextTaskId
end

function GetKeyByStatus(statusProps)
    local key = 0
    for propName, index in pairs(ST_PROPS_NAME_2_INDEX) do
        key = bit.bor(key, bit.lshift(statusProps[propName], (index - 1) * 10))
    end
    return key
end

function GetMapElementIdByIdAndLevel(tp, level)
    local config = GameCommon.TableDataManager:GetMapElementData(tp)
    if not MAP_ELEMENT_TYPE_LEVEL_MAP[config.type] then
        return nil
    end
    return MAP_ELEMENT_TYPE_LEVEL_MAP[config.type][level]
end

function GetMapElementIdByTypeAndLevel(type, level)
    if not MAP_ELEMENT_TYPE_LEVEL_MAP[type] then
        return nil
    end
    return MAP_ELEMENT_TYPE_LEVEL_MAP[type][level]
end

--endregion

function _InitModule()
    _InitTacticToHeroMap()
    _InitHomeTechMaxLevelMap()
    _InitTaskChapterMap()
    _InitInteractiveBehaviorJudge()
    _InitBehaviorStatus()
    _InitActorContainer()
    _InitCareerTree()
    _InitMapElement()
    _InitTokenMap()
    _InitEquip()
    _InitSegmentMap()
    _InitSeasonData()
end

_InitModule()
