﻿{
	"parent": "conf_default.json",
	"cluster": 1000,
	"client_checksum": true,
	"global_entities": {
		"CmdService": {},
		"BattleService": {"dependence": ["DBMgr"]},
		"WorldService": {"dependence": ["DBMgr"]},
		"GidService": {"dependence": ["DBMgr"], "active_standby": true},
		"ActorCallerService": {"dependence": ["DBMgr"]},
		"AllyService": {"dependence": ["DBMgr"], "active_standby": true},
		"NaviService": {},
		"AmbitionsService": {}
	},

	"devloggers": {
		"Test": false,
		"TestOn": true,
		"MainCityMap": false,
		"Aoi": false
	},

	"containers": {
		"gate": 1,
		"game": 0,
		"service": 1,
		"dbmgr": 1,
		"world": 1
	},

	"container_service": {
		"tags": ["svc:RoleService", "svc:CmdService", "svc:BattleService", "GidService", "svc:ActorCallerService", "svc:NaviService", "svc:AllyService", "svc:AmbitionsService"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 10002
		}
	},

	"container_world": {
		"global": {
			"enable_login": true
		},
		"tags": ["svc:WorldService"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 10003
		}
	}
}