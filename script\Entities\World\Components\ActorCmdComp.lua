local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ConstsEnv = xrequire("Utils.Consts")
local ServerTimeEnv = xrequire("Utils.ServerTime")

ActorCmdComp = DefineClass("ActorCmdComp", ComponentBaseEnv.ComponentBase)

function ActorCmdComp:ctor()

end

function ActorCmdComp:ReqStartCmd(startCmdCb)
    local cmdUid = EZE.genUUID()
    self[startCmdCb.method](self, cmdUid, unpack(startCmdCb.userdata))
    if self.props.actions.uid == cmdUid then
        -- 指令成功被写入且还在执行中，需要将指令存盘，用于下次重启后恢复
        self.props.cmd = {startCmdCb = startCmdCb, uid = cmdUid}
    end
end

function ActorCmdComp:StartCmd(cmdUid, actList, failedActList)
    failedActList = failedActList or {}
    if #self.props.actions.stack > 0 then
        self:TryInterruptCmd()
        if #self.props.actions.stack > 0 then
            return false
        end
    end
    self.props.actions.uid = cmdUid
    for i = #actList, 1, -1 do
        self.props.actions.stack:insert(actList[i])
    end
    self.props.actions.failedActions = failedActList
    local actProp, action = self:GetCurAction()
    assert(actProp.status == ConstsEnv.ActorActionStatus.NotStarted)
    actProp.status = ConstsEnv.ActorActionStatus.Running
    action:Start(self, actProp)
    return true
end

function ActorCmdComp:InsertAction(action)
    local insideActProp, insideActCls = self:GetCurAction()
    if insideActProp and insideActProp.status == ConstsEnv.ActorActionStatus.Running then
        insideActProp.status = ConstsEnv.ActorActionStatus.Paused
        insideActCls:Pause(self, insideActProp)
    end
    self.props.actions.stack:insert(action)
    local actProp, actCls = self:GetCurAction()
    assert(actProp.status == ConstsEnv.ActorActionStatus.NotStarted)
    actProp.status = ConstsEnv.ActorActionStatus.Running
    actCls:Start(self, actProp)
end

function ActorCmdComp:GetCurAction()
    local actProp = self.props.actions.stack[#self.props.actions.stack]
    if not actProp then
        return nil, nil
    end
    assert(self._actionClass[actProp.action] ~= nil, string.format("action class not found: %s", actProp.action))
    return actProp, self._actionClass[actProp.action]
end

function ActorCmdComp:OnActionFinished()
    local action, cls = self:GetCurAction()
    if not action then
        return
    end
    self:logCurAction("Stop", "OnActionFinished")
    cls:Stop(self, action, true)
    self:popAction()
    if #self.props.actions.stack ~= 0 then
        local action, cls = self:GetCurAction()
        if action.status == ConstsEnv.ActorActionStatus.Paused then
            self:logCurAction("Resume", "OnActionFinished")
            action.status = ConstsEnv.ActorActionStatus.Running
            cls:Resume(self, action)
        elseif action.status == ConstsEnv.ActorActionStatus.NotStarted then
            self:logCurAction("Start", "OnActionFinished")
            action.status = ConstsEnv.ActorActionStatus.Running
            cls:Start(self, action)
        end
        return
    end
    -- 没有action可执行，清理Cmd
    if self.props.actions.uid ~= "" and self.props.actions.uid == self.props.cmd.uid then
        self.props.cmd = {}
    end
    self.props.actions = {}
    if self.props.cmd.uid ~= "" then
        -- TOOD(qun) 尝试启动记录中的cmd
    end
end

function ActorCmdComp:popAction()
    local actProp = self.props.actions.stack:remove()
    return actProp
end

function ActorCmdComp:TryInterruptCmd()
    -- TODO 后续如果需要区分正常结束、打断、失败再改
    self:OnActionFailed()
end

function ActorCmdComp:OnActionFailed()
    for i = #self.props.actions.stack, 1, -1 do
        local actProp, action = self:GetCurAction()
        if not actProp then
            break
        end
        if actProp.status ~= ConstsEnv.ActorActionStatus.NotStarted then
            self:logCurAction("Stop", "OnActionFailed")
            action:Stop(self, actProp, false)
        end
        self:popAction()
    end
    if #self.props.actions.failedActions > 0 then
        for i = #self.props.actions.failedActions, 1, -1 do
            self.props.actions.stack:insert(self.props.actions.failedActions[i]:raw())
        end
        self.props.actions.failedActions = {}
        local actProp, action = self:GetCurAction()
        assert(actProp.status == ConstsEnv.ActorActionStatus.NotStarted)
        actProp.status = ConstsEnv.ActorActionStatus.Running
        action:Start(self, actProp)
    end
end

function ActorCmdComp:clearFailedActions()
    -- 某个Action执行完成后，和failedCB关联的目标已经达成了，因此可以直接手动clear掉，后续行为失败也不再处理
    self.props.actions.failedActions = {}
end

function ActorCmdComp:makeActionCallback(actProp, func, ...)
    actProp.params._waitCbKey = EZE.genUUID()
    self:logInfo("makeActionCallback: %s %s", func, actProp.params._waitCbKey)
    return EntCb("doActionCallback", {cbKey=actProp.params._waitCbKey, func=func, args={...}})
end

function ActorCmdComp:doActionCallback(userdata, ...)
    local actProp, action = self:GetCurAction()
    if actProp and actProp.params._waitCbKey and actProp.params._waitCbKey == userdata.cbKey then
        self:logInfo("doActionCallback: %s %s", userdata.func, userdata.cbKey)
        actProp.params._waitCbKey = nil
        local t = {}
        for i = 1, #userdata.args do
            table.insert(t, userdata.args[i])
        end
        for i = 1, select("#", ...) do
            table.insert(t, select(i, ...))
        end
        return action[userdata.func](action, self, actProp, table.unpack(t))
    end
    self:logWarn("doActionCallback: %s %s failed", userdata.func, userdata.cbKey)
end

function ActorCmdComp:StopCmd()
    self:TryInterruptCmd()
end

function ActorCmdComp:logCurAction(op, reason)
    local actProp, actionCls = self:GetCurAction()
    if not actProp then
        self:logDev("ActorCmd", "no action")
        return
    end
    local msg = string.format("action %s %s, reason: %s", actionCls.__cname, op, reason)
    msg = EZE.tracebackWithLocals(msg, 2)
    self:logDev("ActorCmd", msg)
end

function ActorCmdComp:GmSetWaitTime(time)
    local actProp, actionCls = self:GetCurAction()
    if actProp and actionCls.GmSetWaitTime then
        actionCls:GmSetWaitTime(self, actProp, time)
    end
end

---@class ActionBase : LuaClass
ActionBase = DefineClass("ActionBase")
function ActionBase.Start(cls, actor, actProp) error(cls.__cname .. ": Start not implemented") end
function ActionBase.Stop(cls, actor, actProp, success) error(cls.__cname .. ": Stop not implemented") end
function ActionBase.Pause(cls, actor, actProp) error(cls.__cname .. ": Pause not supported") end
function ActionBase.Resume(cls, actor, actProp) error(cls.__cname .. ": Resume not supported") end
function ActionBase.ensureParam(cls, actProp, name)
    assert(actProp.params[name] ~= nil, string.format("param %s not found", name))
end

FastAction = DefineClass("FastAction", ActionBase)
FastAction._fast = true
function FastAction.Stop(cls, actor, actProp, success)
    -- 无需清理，所以什么都不用做
end
function FastAction.Pause(cls, actor, actProp) error(cls.__cname .. ": Pause not supported") end
function FastAction.Resume(cls, actor, actProp) error(cls.__cname .. ": Resume not supported") end

CallbackAction = DefineClass("CallbackAction", FastAction)
function CallbackAction.Start(cls, actor, actProp)
    cls:ensureParam(actProp, "cb")
    actor:entityCallback(actProp.params.cb)
end

RpcCallbackAction = DefineClass("RpcCallbackAction", FastAction)
function RpcCallbackAction.Start(cls, actor, actProp)
    cls:ensureParam(actProp, "cb")
    actor:callbackRpc(actProp.params.cb)
    actor:OnActionFinished()
end

WaitAction = DefineClass("WaitDurationAction", ActionBase)
function WaitAction.Start(cls, actor, actProp)
    cls:ensureParam(actProp, "remainTime")
    cls:ensureParam(actProp, "startTime")
    cls:ensureParam(actProp, "timerId")
    cls:Resume(actor, actProp)
end
function WaitAction.Resume(cls, actor, actProp)
    assert(actProp.params.timerId == 0)
    actProp.params.startTime = ServerTimeEnv.GetServerNow()
    actProp.params.timerId = actor:addTimer(actProp.params.remainTime, 0, function()
        actProp.params.timerId = 0
        cls:onTimeout(actor, actProp)
    end)
    actor:SetStatueEndLeftTime(actProp.params.remainTime)
    cls:onStartWait(actor, actProp)
end
function WaitAction.Stop(cls, actor, actProp, success)
    cls:Pause(actor, actProp)
end
function WaitAction.Pause(cls, actor, actProp)
    actProp.params.remainTime = math.max(0, actProp.params.remainTime - (ServerTimeEnv.GetServerNow() - actProp.params.startTime))
    if actProp.params.timerId ~= 0 then
        actor:delTimer(actProp.params.timerId)
        actProp.params.timerId = 0
    end
    actor:SetStatueEndLeftTime(0)
    cls:onPauseWait(actor, actProp)
end
function WaitAction.GmSetWaitTime(cls, actor, actProp, time)
    -- 用于GM指令设置等待时间
    local isRunning = (actProp.params.timerId ~= 0)
    if isRunning then
        cls:Pause(actor, actProp)
    end
    actProp.params.remainTime = math.max(0, time)
    if isRunning then
        cls:Resume(actor, actProp)
    end
end
-- 等待Action实现下面几个方法和Start的额外参数检查即可
function WaitAction.onStartWait(cls, actor, actProp) end
function WaitAction.onPauseWait(cls, actor, actProp) end
function WaitAction.onTimeout(cls, actor, actProp) end
