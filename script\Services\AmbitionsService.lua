local ServiceEnv = xrequire("Framework.Services.Service")
local ScheduleCompEnv = xrequire("Entities.Components.Common.ScheduleComp")
local Schedule = xrequire("Modules.Schedule.Schedule")
local GlobalDataMgrEnv = xrequire("Framework.Utils.GlobalDataMgr")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local GlobalDataSwitchEnv = xrequire("Modules.CustomGlobalData.GlobalDataSwitch")
local RankListEnv = xrequire("Modules.Rank.RankList")

---@class AmbitionsService: Service
AmbitionsService = DefineEntity("AmbitionsService", {ServiceEnv.Service}, {
    ScheduleCompEnv.ScheduleComp,
})

function AmbitionsService:ctor()
    self.finishedTargets = {}
    self:regist(true)
    self:addTimer(0, 0, function()
        EZGlobal.Schedule:addScheduleSlots(TableConst.enums.ScheduleType.AmbitionsDaily, self.id, nil, EntCb("OnAmbitionsDailySchedule"))
    end)
    self:updateProcess()
    self:initRankList()
end

function AmbitionsService:SyncAmbitionsProcess()
    local config = ProcessedTableEnv.AMBITIONS_PROCESS_CONFIG[self.props.processId]
    if not config then
        self:logError("SyncAmbitionsProcess failed, processId %s not found in ambitions data", self.props.processId)
        return
    end
    local limitPack = {}
    if config.limit_pack then
        for _, packId in ipairs(config.limit_pack) do
            limitPack[packId] = true
        end
    end
    -- 推送进度&限时卡包
    GlobalDataMgrEnv.instance:push("ambitions", {
        processId = self.props.processId,
        limitPack = limitPack,
        finishedTargets = finishedTargets,
    })
    -- 推送开关
    for name, value in pairs(config.switches) do
        GlobalDataSwitchEnv.SetSwitch(name, value)
    end
end

-- 霸业刷天
function AmbitionsService:OnAmbitionsDailySchedule(userdata, nowCsv)
    local lastCsv = self:GetCsv(TableConst.enums.ScheduleType.AmbitionsDaily) or nowCsv
    local lastCycle = Schedule.fromScheduleCSV(lastCsv)
    local nowCycle = Schedule.fromScheduleCSV(nowCsv)
    self:logInfo("OnAmbitionsDailySchedule (%s) %s -> %s", nowCsv, lastCycle, nowCycle)
    self:updateProcess()
end

function AmbitionsService:updateProcess()
    local cycleId, stageId, nextTime = EZGlobal.Schedule:getCurSchedule(TableConst.enums.ScheduleType.AmbitionsDaily)
    local newProcessId = 0
    for _, prcId in ipairs(ProcessedTableEnv.AMBITIONS_PROCESS_LIST) do
        local prcConfig = GameCommon.TableDataManager:GetAmbitionsData(prcId)
        if prcConfig.begin <= cycleId then
            newProcessId = prcId
        end
    end
    if not GameCommon.TableDataManager:GetAmbitionsData(newProcessId) then
        self:logError("updateProcess failed, newProcessId %s not found in ambitions data, cycle %s", newProcessId, cycleId)
        return
    end
    self.props.processId = newProcessId
    self:SyncAmbitionsProcess()
end

function AmbitionsService:ObserveAmbitions(client)
    self:connectObserver(client)
end

--region 进度管理

function AmbitionsService:OnOccupyLand(level)
    self.props.targets.occupyLand[level] = (self.props.targets.occupyLand[level] or 0) + 1
    if self.props.processId == 0 then
        return
    end
    for _, pid in pairs(ProcessedTableEnv.AMBITIONS_PROCESS_LIST) do
        local prcConfig = GameCommon.TableDataManager:GetAmbitionsData(pid)
        if prcConfig and prcConfig.targets._type_ == "OccupyLand" then
            if prcConfig.targets.level <= level then
                self.props.views[pid] = (self.props.views[pid] or 0) + 1
                if self.props.views[pid] >= prcConfig.targets.num then
                    self.finishedTargets[pid] = true
                    self:SyncAmbitionsProcess()
                end
            end
        end
        if pid == self.props.processId then
            break
        end
    end
end

--endregion

--region 霸业积分排行

function AmbitionsService:initRankList()
    self.allRank = RankListEnv.JumpList(true, self.props.allRank, 3000)
end

function AmbitionsService:UpdateAvatarRank(gid, score)
    self.allRank:UpdateElement({id = gid, value = score})
end

function AmbitionsService:GetTopN(n)
    return self.allRank:GetTopN(n)
end

--endregion
