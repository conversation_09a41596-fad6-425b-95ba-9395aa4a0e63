local CoordUtils = xrequire("Common.Utils.CoordUtils")
local AdministrativeHierarchy =  TableConst.enums["slg.AdministrativeHierarchy"]   ---@type slg.AdministrativeHierarchy

local ffi = require("ffi")
ffi.cdef[[
    typedef struct {
        int q;
        int r;
    } Axial;
]]

-- using axial coordinates
---@class MainCityMap: LuaClass
---@field mapData MapData
---@field minQ integer
---@field birthRandomPosQueues table
MainCityMap = DefineClass("MainCityMap")
-- 控制出生区域划分的行政层级
MainCityMap.BIRTH_HIERARCHY = AdministrativeHierarchy.Commandery

function MainCityMap:ctor(xSize, ySize, space)
    self.space = space   ---@type WorldSpace
    self.xSize = xSize
    self.ySize = ySize
    self.minQ = CoordUtils.Offset2Axial(0, self.ySize)
    self.maxQ = self.xSize
    self.minR = 0
    self.maxR = self.ySize

    self:ReloadTableDataCache()
    self:initProvinceRandomPosQueues()
end

function MainCityMap:GetConfigs()
    return GameCommon.TableDataManager:GetMainCityRandomConfigs()
end

function MainCityMap:ReloadTableDataCache()
    local scanRadius = self:GetConfigs().ResScanRadius
    for _, data in pairs(GameCommon.TableDataManager:GetMainCityDistScoreTable()) do
        if scanRadius < data.max_distance then
            scanRadius = data.max_distance
        end
    end
    self.scanRadius = scanRadius

    local elementDistScore = {}
    local distScoreTable = GameCommon.TableDataManager:GetMainCityDistScoreTable()
    for _, data in pairs(GameCommon.TableDataManager:GetMapElementTable()) do
        if distScoreTable[data.type] then
            elementDistScore[data.id] = distScoreTable[data.type]
        end
    end
    self.elementDistScore = elementDistScore

    local resElementLevel = {}
    local resScoreTable = GameCommon.TableDataManager:GetMainCityResScoreTable()
    local MapElementType = TableConst.enums["slg.MapElementType"]
    for _, data in pairs(GameCommon.TableDataManager:GetMapElementTable()) do
        if data.type == MapElementType.Food 
            or data.type == MapElementType.Stone
            or data.type == MapElementType.Wood
            or data.type == MapElementType.Iron then
            if resScoreTable[data.level] then
                resElementLevel[data.id] = data.level
            end
        end
    end
    self.resElementLevel = resElementLevel

    self.maxScoreLv = #self:GetConfigs().ScoreLvRange + 1

    local obstacleElements = {}
    for _, data in pairs(GameCommon.TableDataManager:GetMapElementTable()) do
        if data.maincity_obstacle then
            obstacleElements[data.id] = true
        end
    end
    self.obstacleElements = obstacleElements
end

function MainCityMap:Load(filename)
   -- 打开文件
    local file = io.open(filename, "rb")
    if not file then
        return false, string.format("open file %s failed", filename)
    end

    -- 读取整个文件内容
    local data = file:read("*a")
    file:close()

    -- 创建缓冲区
    local buf = ffi.new("char[?]", #data)
    ffi.copy(buf, data, #data)

    local offset = 0

    -- 读取地图尺寸并校验
    local xSize = ffi.cast("int16_t*", buf + offset)[0]
    offset = offset + ffi.sizeof("int16_t")
    local ySize = ffi.cast("int16_t*", buf + offset)[0]
    offset = offset + ffi.sizeof("int16_t")
    assert(xSize == self.xSize and ySize == self.ySize, string.format("map size mismatch %d %d", xSize, ySize))

    -- 读取地图数据
    self.validMap = ffi.new(string.format("int8_t[%d][%d]", self.maxQ - self.minQ, self.maxR - self.minR))
    ffi.fill(self.validMap, ffi.sizeof(self.validMap), 1)
    self.scoreMap = ffi.new(string.format("int8_t[%d][%d]", self.maxQ - self.minQ, self.maxR - self.minR))
    ffi.fill(self.scoreMap, ffi.sizeof(self.scoreMap), 0)

    local scoreSize = ffi.sizeof("int8_t")
    for x = 0, xSize - 1 do
        for y = 0, ySize - 1 do
            local score = ffi.cast("int8_t*", buf + offset)[0]
            offset = offset + scoreSize
            local q, r = CoordUtils.Offset2Axial(x, y)
            self.scoreMap[q - self.minQ][r] = score
            self.validMap[q - self.minQ][r] = (score <= 0) and 1 or 0
            self:pushProvinceRandomPos(q, r)
        end
    end
    self:shuffleProvinceRandomPosQueues()

    -- test
    -- self:printMap("maincity_01.txt")
    -- local axial = CoordUtils.Axial.new()
    -- for x = 0, xSize - 1 do
    --     for y = 0, ySize - 1 do
    --         local q, r = CoordUtils.Offset2Axial(x, y)
    --         if self.validMap[q - self.minQ][r] == 0 then
    --             axial:Set(q, r)
    --             self.scoreMap[q - self.minQ][r] = self:calcScore(axial)
    --         end
    --     end
    -- end
    -- self:printMap("maincity_02.txt")

    return true, ""
end

function MainCityMap:MainCitySpiral(col, row)
    return self:BuildingSpiral(col, row, self:GetConfigs().MainCityRadius)
end

function MainCityMap:BuildingSpiral(col, row, radius)
    local axial = CoordUtils.Axial.new(CoordUtils.Offset2Axial(CoordUtils.Doubled2Offset(col, row)))
    return axial:Spiral(radius)
end

---@param col int Doubled坐标系
---@param row int Doubled坐标系
function MainCityMap:addObstacle(col, row, radius, needRecalc)
    DevLog("MainCityMap", "addObstacle at (%s, %s), radius: %s, needRecalc: %s", col, row, radius, needRecalc)
    radius = radius + self:GetConfigs().MainCityRadius
    local x, y = CoordUtils.Doubled2Offset(col, row)
    local center = CoordUtils.Axial.new(CoordUtils.Offset2Axial(x, y))
    local minQ = self.minQ
    local recalc = false

    -- 设置障碍
    for q, r in center:Spiral(radius) do
        if q >= self.minQ and q < self.maxQ and r >= self.minR and r < self.maxR then
            recalc = self.validMap[q - minQ][r] == 0
            self.validMap[q - minQ][r] = self.validMap[q - minQ][r] + 1
            self.scoreMap[q - minQ][r] = 0
        end
    end

    if not needRecalc or not recalc then
        return
    end

    -- 重算积分
    local cur = CoordUtils.Axial.new()
    for q, r in center:Spiral(self.scanRadius, radius + 1) do
        if q >= self.minQ and q < self.maxQ and r >= self.minR and r < self.maxR then
            if self.validMap[q - minQ][r] <= 0 then
                -- 积分一定是变少的，等待高优先级的随机队列消费时，再重新加入队列
                cur:Set(q, r)
                self.scoreMap[q - minQ][r] = self:calcScore(cur)
            end
        end
    end
end

---@param col int Doubled坐标系
---@param row int Doubled坐标系
function MainCityMap:removeObstacle(col, row, radius, needRecalc)
    DevLog("MainCityMap", "removeObstacle at (%s, %s), radius: %s, needRecalc: %s", col, row, radius, needRecalc)
    radius = radius + self:GetConfigs().MainCityRadius
    local x, y = CoordUtils.Doubled2Offset(col, row)
    local center = CoordUtils.Axial.new(CoordUtils.Offset2Axial(x, y))
    local minQ = self.minQ
    local recalc = false

    -- 取消障碍
    local cur = CoordUtils.Axial.new()
    for q, r in center:Spiral(radius) do
        if q >= self.minQ and q < self.maxQ and r >= self.minR and r < self.maxR then
            local origVal = self.validMap[q - minQ][r]
            assert(origVal >= 1, string.format("invalid value %s at (%s, %s) in validMap", origVal, q, r))

            self.validMap[q - minQ][r] = origVal - 1
            if origVal == 1 then
                cur:Set(q, r)
                self.scoreMap[q - minQ][r] = self:calcScore(cur)
                -- 从障碍变为非障碍，需要重新加入到随机队列
                self:pushProvinceRandomPos(q, r)
                recalc = true
            end
        end
    end

    if not needRecalc or not recalc then
        return
    end

    -- 重算积分
    for q, r in center:Spiral(self.scanRadius, radius + 1) do
        if q >= self.minQ and q < self.maxQ and r >= self.minR and r < self.maxR then
            if self.validMap[q - minQ][r] == 0 then
                cur:Set(q, r)
                self.scoreMap[q - minQ][r] = self:calcScore(cur)
                -- 这里也不会加入到随机队列中，避免队列过度膨胀。
            end
        end
    end
end

function MainCityMap:AddBuilding(range)
    local col, row = range[1].x, range[1].y
    local tp = self:getRawElementTypeByDoubled(col, row)
    if self.obstacleElements[tp] then
        return
    end
    local data = GameCommon.TableDataManager:GetMapElementData(tp)
    assert(data, "no map element data for " .. tp)
    local MapElementType = TableConst.enums["slg.MapElementType"]
    if data.type == MapElementType.MainCity then
        self:addObstacle(col, row, self:GetConfigs().MainCityRadius, true)
    else
        for _, pos in ipairs(range) do
            self:addObstacle(pos.x, pos.y, 0, true)
        end
    end
end

function MainCityMap:RemoveBuilding(range)
    local col, row = range[1].x, range[1].y
    local tp = self:getRawElementTypeByDoubled(col, row)
    if self.obstacleElements[tp] then
        return
    end
    local data = GameCommon.TableDataManager:GetMapElementData(tp)
    assert(data, "no map element data for " .. tp)
    local MapElementType = TableConst.enums["slg.MapElementType"]
    if data.type == MapElementType.MainCity then
        self:removeObstacle(range[1].x, range[1].y, self:GetConfigs().MainCityRadius, true)
    else
        for _, pos in ipairs(range) do
            self:removeObstacle(pos.x, pos.y, 0, true)
        end
    end
end

function MainCityMap:AddOccupation(col, row)
    local tp = self:getRawElementTypeByDoubled(col, row)
    if self.obstacleElements[tp] then
        return
    end
    self:addObstacle(col, row, 0, false)
end

function MainCityMap:RemoveOccupation(col, row)
    local tp = self:getRawElementTypeByDoubled(col, row)
    if self.obstacleElements[tp] then
        return
    end
    self:removeObstacle(col, row, 0, false)
end

function MainCityMap:RandomPos(commandery)
    local prefectureConfig = GameCommon.TableDataManager:GetPrefectureConfig(commandery)
    assert(prefectureConfig, "invalid birth commandery: " .. commandery)
    local administrativeId = commandery
    if self.BIRTH_HIERARCHY == AdministrativeHierarchy.Prefecture then
        administrativeId = prefectureConfig.id
    end
    local col, row = self:doRandomPos(administrativeId)
    return col, row
end

function MainCityMap:doRandomPos(administrativeId)
    local queues = self.birthRandomPosQueues[administrativeId]
    if not queues then
        ErrorLog("Random maincity position failed, invalid province id %s", administrativeId)
        return nil
    end

    for lv = 1, self.maxScoreLv do
        local queue = queues[lv]
        local v = queue:PopFront()
        if v then
            local score = self.scoreMap[v.q - self.minQ][v.r]
            -- 如果发现是阻碍就直接抛弃
            if score > 0 then
                local scoreLv = self:calcScoreLv(score)
                if scoreLv < lv then
                    -- 如果发现等级比需要的小，就重新放入正确的队列
                    self:pushProvinceRandomPos(v.q, v.r)
                else
                    local x, y = CoordUtils.Axial2Offset(v.q, v.r)
                    local col, row = CoordUtils.Offset2Doubled(x, y)
                    return col, row
                end
            end
        end
    end

    return nil
end

function MainCityMap:initProvinceRandomPosQueues()
    self.birthRandomPosQueues = {}
    for _, data in GameCommon.TableDataManager:GetMapGISTableIter(self.BIRTH_HIERARCHY) do
        local queues = {}
        for lv = 1, self.maxScoreLv do
            queues[lv] = Deque.new("Axial")
        end
        assert(not self.birthRandomPosQueues[data.id])
        self.birthRandomPosQueues[data.id] = queues
    end
end

function MainCityMap:shuffleProvinceRandomPosQueues()
    for _, queues in pairs(self.birthRandomPosQueues) do
        for _, queue in pairs(queues) do
            self:shuffleRandomPosQueue(queue)
        end
    end
end

function MainCityMap:pushProvinceRandomPos(q, r)
    local county = self.space:GetCountyByAxial(q, r)
    assert(county)
    local administrativeId = county
    if self.BIRTH_HIERARCHY == AdministrativeHierarchy.Commandery then
        local config = GameCommon.TableDataManager:GetCommanderyConfig(county)
        assert(config)
        administrativeId = config.id
    elseif self.BIRTH_HIERARCHY == AdministrativeHierarchy.Prefecture then
        local config = GameCommon.TableDataManager:GetPrefectureConfig(county)
        assert(config)
        administrativeId = config.id
    end
    local score = self.scoreMap[q - self.minQ][r]
    local scoreLv = self:calcScoreLv(score)
    local queue = self.birthRandomPosQueues[administrativeId][scoreLv]
    local v = queue:PushBack()
    v.q = q
    v.r = r
end

function MainCityMap:shuffleRandomPosQueue(queue)
    local size = queue.size
    local arr = ffi.new("Axial[?]", size)
    for i = 0, size - 1 do
        arr[i] = queue:PopFront()
    end

    for i = size - 1, 1, -1 do
        local j = math.random(0, i)
        local q, r = arr[i].q, arr[i].r
        arr[i].q, arr[i].r = arr[j].q, arr[j].r
        arr[j].q, arr[j].r = q, r
    end
    for i = 0, size - 1 do
        local v = queue:PushBack()
        v.q = arr[i].q
        v.r = arr[i].r
    end
end

function MainCityMap:calcScore(axial)
    assert(axial.q >= self.minQ and axial.q < self.maxQ and axial.r >= self.minR and axial.r < self.maxR)
    if self.validMap[axial.q - self.minQ][axial.r] > 0 then
        return 0
    end

    local resScores = {}
    local mainCityRadius = self:GetConfigs().MainCityRadius
    local score = self:GetConfigs().MaxScore
    for dist = 1, self.scanRadius do
        for q, r in axial:Ring(dist + mainCityRadius) do
            if q >= self.minQ and q < self.maxQ and r >= self.minR and r < self.maxR then
                local elementId = self:getElementType(q, r)
                if not elementId then
                    goto continue
                end

                -- 距离积分
                local data = self.elementDistScore[elementId]
                if data and dist < data.max_distance  then
                    score = score - data.score * (1 - dist / data.max_distance)
                end

                -- 资源积分
                local level = self.resElementLevel[elementId]
                if level then
                    resScores[level] = (resScores[level] or 0) + 1
                end

                ::continue::
            end
        end
    end

    local resScoreTable = GameCommon.TableDataManager:GetMainCityResScoreTable()
    for level, data in pairs(resScoreTable) do
        local count = resScores[level] or 0
        if count < data.max_count then
            score = score - data.score * (1 - count / data.max_count)
        end
    end

    -- DevLog("MainCityMap", "calc axial(%s, %s) score %s", axial.q, axial.r, score)

    return score
end

function MainCityMap:getElementType(q, r)
    local x, y = CoordUtils.Axial2Offset(q, r)
    if x < 0 or x >= self.xSize or y < 0 or y >= self.ySize then
        return nil
    end
    local col, row = CoordUtils.Offset2Doubled(x, y)
    return self.space:GetGridType(col, row)
end

function MainCityMap:getRawElementTypeByDoubled(col, row)
    return self.space:GetRawGridType(col, row)
end

function MainCityMap:calcScoreLv(score)
    local ranges = self:GetConfigs().ScoreLvRange
    for lv, start in ipairs(ranges) do
        if score >= start then
            return lv
        end
    end
    return #ranges + 1
end

function MainCityMap:printMap(filename, detail)
    local sizeX = self.xSize
    local sizeY = self.ySize
    local minQ = self.minQ
    local lines = {}

    local startChar = "A"
    local startByte = string.byte(startChar)
    for x = sizeX - 1, 0, -1 do
        local line = {}
        for y = 0, sizeY - 1 do
            local q, r = CoordUtils.Offset2Axial(x, y)
            local score = self.scoreMap[q - minQ][r]
            if detail then
                table.insert(line, string.format("%03d", score))
            else
                if score <= 0 then
                    table.insert(line, "X")
                else
                    local scoreLv = self:calcScoreLv(score)
                    table.insert(line, string.char(startByte + scoreLv - 1))
                end
            end
        end
        table.insert(lines, table.concat(line, " "))
    end

    filename = filename or string.format("maincity_map_%s.txt", os.date("%Y%m%d%H%M%S", os.time()))
    local fp = io.open(filename, "w+")
    fp:write(table.concat(lines, "\n"))
    fp:close()
end


---@class DequeSlice : LuaClass
---@field next DequeSlice
---@field prev DequeSlice
---@field data any
---@field size integer
DequeSlice = DefineClass("DequeSlice")
function DequeSlice:ctor(data, size)
    self.data = data
    self.size = size
    self.next = nil
    self.prev = nil
end

---@class Deque : LuaClass
---@field size integer
---@field head DequeSlice
---@field tail DequeSlice
Deque = DefineClass("Deque")
Deque.MIN_SLICE_SIZE = 16
Deque.PAGE_SIZE = 4096

function Deque:ctor(elementType)
    self.size = 0
    self.tp = elementType
    self.maxSliceSize = math.max(Deque.MIN_SLICE_SIZE, math.floor(Deque.PAGE_SIZE / ffi.sizeof(elementType)))
    self.sliceCache = {}

    self.head = nil
    self.tail = nil
    self.headIndex = -1
    self.tailIndex = -1
    self:addSlice()
end

function Deque:addSlice()
    local sliceSize = Deque.MIN_SLICE_SIZE
    while sliceSize < self.size do
        sliceSize = sliceSize * 2
    end
    if sliceSize > self.maxSliceSize then
        sliceSize = self.maxSliceSize
    end

    local data = ffi.new(string.format("%s[?]", self.tp), sliceSize)
    local slice = DequeSlice.new(data, sliceSize) ---@type DequeSlice
    if self.head == nil then
        self.head = slice
        self.tail = slice
        self.headIndex = math.floor(sliceSize / 2)
        self.tailIndex = self.headIndex - 1

        slice.prev = slice
        slice.next = slice
    else
        slice.next = self.head
        slice.prev = self.head.prev
        self.head.prev.next = slice
        self.head.prev = slice
    end
end

function Deque:Front()
    if self.size ~= 0 then
        return self.head.data[self.headIndex]
    else
        return nil
    end
end

function Deque:Back()
    if self.size ~= 0 then
        return self.tail.data[self.tailIndex]
    else
        return nil
    end
end

function Deque:PushFront()
    self:checkValid()

    if self.headIndex > 0 then
        self.headIndex = self.headIndex - 1
        self.size = self.size + 1
        return self:Front()
    else
        if self.head.prev == self.tail then
            self:addSlice()
        end
        self.head = self.head.prev
        self.headIndex = self.head.size - 1
        self.size = self.size + 1
        return self:Front()
    end
end

function Deque:PopFront()
    if self.size == 0 then
        return nil
    end

    self:checkValid()

    local v = self:Front()
    if self.headIndex < self.head.size - 1 then
        self.headIndex = self.headIndex + 1
        self.size = self.size - 1
        return v
    else
        self.head = self.head.next
        self.headIndex = 0
        self.size = self.size - 1
        return v
    end
end

function Deque:PushBack()
    self:checkValid()

    if self.tailIndex < self.tail.size - 1 then
        self.tailIndex = self.tailIndex + 1
        self.size = self.size + 1
        return self:Back()
    else
        if self.tail.next == self.head then
            self:addSlice()
        end
        self.tail = self.tail.next
        self.tailIndex = 0
        self.size = self.size + 1
        return self:Back()
    end
end

function Deque:PopBack()
    if self.size == 0 then
        return nil
    end

    self:checkValid()

    local v = self:Back()
    if self.tailIndex > 0 then
        self.tailIndex = self.tailIndex - 1
        self.size = self.size - 1
        return v
    else
        self.tail = self.tail.prev
        self.tailIndex = self.tail.size - 1
        self.size = self.size - 1
        return v
    end
end

function Deque:checkValid()
    assert(self.headIndex >= 0 and self.headIndex < self.head.size)
    assert(self.tailIndex >= 0 and self.tailIndex < self.tail.size)
    if self.head == self.tail then
        assert(self.size >= 0)
        assert(self.size == self.tailIndex - self.headIndex + 1)
    else
        local size = self.head.size - self.headIndex
        size = size + self.tailIndex + 1
        local slice = self.head.next
        while slice ~= self.tail do
            size = size + slice.size
            slice = slice.next
        end
        assert(self.size == size)
    end
end