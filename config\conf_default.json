﻿{
	"name": "ezserver",
	"node": 1,
	"cluster": 1000,
	"node_ttl": 10,
	"res_path": "",
	"def_path": "script/Defs",
	"script_path": "script",
	"client_checksum": false,
	"network_threadnum": 2,
	"migrate_timeout": 30,
	"migrate_fail_cd": 10,
	"global_entities": {
		"DBMgr": {"instances_required": 1, "dependence": [], "strategy": "random_once"},
		"RoleService": {"active_standby": true},
		"CmdService": {}
	},
	"active_standby": {
		"start_delay": 3,
		"recover_delay": 1
	},
	"plugins": [],
	"options": {
		"traceback_with_locals": true
	},
	"fyc": {  // 业务向配置
		"timezone": "Asia/Shanghai",
		"season": "s1"
	},

	"log": {
		"level": "info",
		"output_udp_port": 19000
	},

	"containers": {
		"gate": 1,
		"game": 1,
		"dbmgr": 1
	},

	"container": {
		"global": {
			"enable_login": false
		},
		"local_login": false,
		"login_entity": "Account",
		"database": "proj_{cluster}",
		"database_undroppable": "proj_{cluster}_undroppable",
		"timer_tick_shift": 5,
		"timer_delay_warn": 100,
		"telnet_server": {
			"host": "127.0.0.1",
			"port": null
		}
	},

	"container_gate": {
		"tags": ["Gate"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 10000
		}
	},

	"container_dbmgr": {
		"tags": ["DBMgr"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 10001
		},
		"create_entities_indexes": true
	},

	"container_game": {
		"global": {
			"enable_login": true
		},
		"tags": ["RoleService", "CmdService", "lbdebug", "emmy"],
		"timer_tick_shift": 3,
		"timer_delay_warn": 50,
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 10002
		}
	},

	"gate": {
		"host": "0.0.0.0",	// 监听的地址
		"port":  30000,		// 监听的端口
		"threads": 2,		// 网络线程数
		"kcp": {
			"mtu": 996,		// mtu
			"nodelay": 1,	// 包重传延迟策略，0：线性增长，1：0.5倍指数增长，2：根据当前的rto的0.5倍增长
			"interval": 10,	// flush刷新间隔，单位ms
			"resend": 2,	// 触发快速重传的重复ack个数
			"nocwnd": 1,	// 取消拥塞控制
			"sndwnd": 32,	// 发送窗口大小
			"rcvwnd": 128,	// 接收窗口大小
			"stream": 1,

			"timeout": 30000,			// 超时时间，单位ms，超时后链接断开
			"heartbeat": 6000,			// 客户端发送心跳包的间隔，单位ms
			"syn_retry_interval": 200,	// 客户端建立Kcp连接过程中Udp数据包丢包重试间隔，单位ms
			"syn_max_retry": 25,		// 客户端建立Kcp连接过程中Udp数据包丢包重试最大次数

			"fec_encoder": {			// Forward Error Correction(FEC)，通过发送冗余包，达到即使存在数据包丢失的情况下，也可以通过冗余包进行恢复，避免重传延迟
				"algorithm": "none",	// FEC算法，目前支持：none、xor
				"data_shard": 9,		// 数据包数量，即每n个数据包发送k个冗余包
				"parity_shard": 1		// 冗余包数量，xor目前只支持1个冗余包
			}
		},
		"crypto_types": ["NONE", "RC4", "ChaCha20", "AES-128-CFB", "AES-192-CFB", "AES-256-CFB", "AES-128-OFB", "AES-192-OFB", "AES-256-OFB"],
		"rsa_private_key": "config/rsa_private_key.pem"
	},

	"dbmgr": {
		"mongo_cluster": {
			"game": {
				"hosts": [
					"**************:27017"
				],
				"username": "liubo03",
				"password": "yueying",
				"auth_source": "admin",
				"max_pool_size": 10
			}
		},
		"mongo_database": {
			"proj_{cluster}": "game"
		},
		"worker_thread_num": 4
	},

	"kafka": {
		"default": {
			"common": {
				"bootstrap": {
					"servers": "**************:9092"
				}
			},
			"producer": {
			},
			"consumer": {
				"group.id": "default_consumer_group",
				"auto.offset.reset": "earliest"
			}
		}
	},

	"cmdservice": {
		"host": "0.0.0.0",
		"port": 9900
	},

	"sidecar": {
		"host": "127.0.0.1",
		"port": 20001
	},

	"raft_cluster": [
		{"id": 1, "host": "127.0.0.1", "port": 9001, "client_port": 9101}
	]
}
