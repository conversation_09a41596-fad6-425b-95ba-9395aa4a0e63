---@meta

---@class CmdProxy
---@field aeId string

---@class CmdBase
---@field name string

---@class Cmd: CmdBase
---@field args any
---@field ignoreLogRes boolean

---@alias EntityId int
---@alias <PERSON>id string
---@alias <PERSON><PERSON> string
---@alias <PERSON>ttpSessionId int
---@alias <PERSON><PERSON>ty<PERSON><PERSON>back any
---@alias AvatarGid int
---@alias <PERSON><PERSON><PERSON> int
---@alias <PERSON>Id int
---@alias WorldId int
---@alias TemplateHeroId int
---@alias CompleteHeroUid string
---@alias Health int
---@alias BattleCamp int
---@alias NpcTeamId int
---@alias <PERSON><PERSON><PERSON><PERSON><PERSON> int
---@alias ArmySlotIdx int
---@alias UnitIdx int
---@alias <PERSON><PERSON><PERSON> int
---@alias HomeBuildingId int
---@alias TaskId int
---@alias <PERSON>Id int
---@alias <PERSON>ed<PERSON>os int
---@alias PackedPosList PackedPos[]
---@alias It<PERSON>Id int
---@alias To<PERSON>Id int
---@alias <PERSON><PERSON><PERSON> int
---@alias <PERSON><PERSON><PERSON><PERSON>id Uid
---@alias HorseUid Uid
---@alias ReasonEnum int
---@alias Prefecture int
---@alias Commandery int
---@alias County int
---@alias CityId int
---@alias Career int
---@alias HeadPic int
---@alias HeadFrame int
---@alias InstanceId int
---@alias SiegeGroupIdx int

---@class PeriodicData
---@field sum int
---@field period int

---@alias PeriodicDataSummary Dict<int, PeriodicData>
---@alias Merit int
---@alias MeritSummary PeriodicDataSummary
---@alias ItemToNum Dict<ItemId, int>
---@alias TypeToItemDict Dict<int, ItemToNum>
---@alias ProduceToFloatNum Dict<int, float>
---@alias Switches Dict<string, bool>

---@class AABB
---@field minX number
---@field minZ number
---@field maxX number
---@field maxZ number

---@class Position
---@field x number
---@field y number

---@alias DoubledPosition Position
---@alias OffsetPosition Position
---@alias PackedPosition int
---@alias PositionList Position[]

---@class MainCityView
---@field uid Uid
---@field id EntityId
---@field pos Position
---@field level int

---@class Relation
---@field gid AvatarGid
---@field allyId AllyId
---@field clanId ClanId

---@alias SlotSoldierNum Dict<UnitIdx, int>
---@alias GridType int

---@class OneLandProduceAdd
---@field buffId int
---@field percent number
---@field timeout number

---@alias OneLandProduceAddList OneLandProduceAdd[]

---@class BaseOccupation
---@field gid AvatarGid
---@field allyId AllyId
---@field name string

---@class GridOccupation: BaseOccupation
---@field occupyTime number
---@field levelAddTo int
---@field produceBuffs OneLandProduceAddList

---@class GridBuilding: BaseOccupation
---@field buildingUid Uid
---@field tp GridType
---@field isCenter boolean

---@class InGridOrderDetail
---@field matchGroupType int
---@field order int
---@field isSurrounding boolean?

---@alias InGridOrder Dict<PackedPos, InGridOrderDetail>
---@alias IntList int[]
---@alias StringList string[]
---@alias StrStrDict Dict<string, string>
---@alias StrBoolDict Dict<string, bool>
---@alias IntIntDict Dict<int, int>
---@alias IntFloatDict Dict<int, float>
---@alias IntBoolDict Dict<int, bool>
---@alias UidIntDict Dict<Uid, int>
---@alias UidStrDict Dict<Uid, string>
---@alias UidList Uid[]
---@alias HeroList HeroId[]
---@alias AvatarGidList AvatarGid[]
---@alias AvatarFieldList IntList
---@alias ArmyTypeList ArmyType[]
---@alias TacticId int

---@class TacticData
---@field id TacticId
---@field level int
---@field stars int

---@class Tactic
---@field id TacticId
---@field uid Uid
---@field level int
---@field stars int

---@class EquipEntry
---@field id int
---@field value number

---@class EquipEffect
---@field id int

---@alias EquipEntriesList EquipEntry[]

---@class EquipForge
---@field uid EquipUid?
---@field waitChooseEntries EquipEntriesList

---@alias EquipEffectsList EquipEffect[]

---@class EquipRebuild
---@field uid EquipUid?
---@field waitChooseEffect EquipEffectsList

---@class ForgeHistory
---@field continuousDecCnt int
---@field historyBest int

---@class Equip
---@field id int
---@field entries EquipEntriesList
---@field effects EquipEffectsList
---@field forgeHistory ForgeHistory
---@field heroId HeroId

---@alias EquipBag Dict<EquipUid, Equip>
---@alias EquipUidList EquipUid[]
---@alias MailAddEquip Dict<EquipUid, Equip>

---@class Horse
---@field id int
---@field entries EquipEntriesList
---@field effects EquipEffectsList
---@field heroId HeroId

---@alias HorseBag Dict<HorseUid, Horse>
---@alias HorseUidList HorseUid[]
---@alias MailAddHorse Dict<HorseUid, Horse>

---@class HorseTraining
---@field horse Horse
---@field trainingEndTime number

---@class ArmyUsageDetail
---@field id int
---@field tactics Dict<int, TacticId>

---@alias ArmyUsage Dict<int, ArmyUsageDetail>

---@class HeroStaminaDetail
---@field val int
---@field lastRecover number

---@class ArmyStaminaDetail: HeroStaminaDetail
---@field max int

---@alias HeroAddPointsDict Dict<int, int>

---@class HeroEquipAddData
---@field points HeroAddPointsDict
---@field expAddPercent int

---@alias HeroTacticData Dict<int, TacticData>
---@alias IdentitySlots Dict<int, int>

---@class HeroData
---@field id HeroId
---@field level int
---@field exp int
---@field staminaDetail HeroStaminaDetail
---@field injuredTime number
---@field stars int
---@field tactics HeroTacticData
---@field soldierNum int
---@field soldierMax int
---@field recoverableSoldierNum int
---@field addPoints HeroAddPointsDict
---@field equips Equip[]
---@field horses Horse[]
---@field identitySlots IdentitySlots

---@alias HeroDataDict Dict<int, HeroData>

---@class Hero
---@field id HeroId
---@field uid Uid
---@field level int
---@field stars int
---@field tactics Dict<int, TacticId>

---@alias BattleAttritubes Dict<int, any>

---@class BattleHero
---@field heroId HeroId
---@field level int
---@field exp int
---@field stars int
---@field dynasty int
---@field selfTactics any
---@field carryingTactics any
---@field attributes BattleAttritubes
---@field equips Equip[]
---@field horses Horse[]
---@field identitySlots IdentitySlots

---@class BattleArmyData
---@field heroes Dict<int, BattleHero>
---@field armyType ArmyType
---@field isDirty boolean

---@class ArmyPreorderSiege
---@field siegeCamp EntityId
---@field isMainForce boolean
---@field timeout number

---@class ArmyInAutoPath
---@field index int
---@field path PositionList

---@class MainCityData
---@field uid Uid
---@field position Position
---@field entityId EntityId
---@field mailbox any

---@class ArmyHomeData
---@field baseId Uid
---@field pos Position

---@class ArmyDeployHomeData: ArmyHomeData
---@field arrived boolean

---@class ArmySiegeHomeData: ArmyDeployHomeData
---@field isMainForce boolean
---@field assaultRecord string?

---@class ArmyBuff
---@field value number
---@field buffId int
---@field level int
---@field isPercentage boolean
---@field effect_cnt int
---@field heroOnly HeroId?

---@alias ArmyBuffDict Dict<string, ArmyBuff>
---@alias ArmyBuffDictBySrc Dict<int, ArmyBuffDict>

---@class BattleTeam
---@field eid EntityId
---@field playerName string
---@field gid AvatarGid
---@field allyId AllyId
---@field clanId ClanId
---@field camp int
---@field armySlotIdx int
---@field buffs ArmyBuffDictBySrc
---@field supply int
---@field representHeroId HeroId?
---@field npcDataId int?
---@field battleArmyData BattleArmyData?

---@alias GuidDict Dict<BattleCamp, Uid>

---@class BattleInfo
---@field position PackedPosition
---@field gridName string
---@field battleType int
---@field guid GuidDict
---@field fastBattle boolean
---@field landLevel int?
---@field npcPoolId int?
---@field maxStaminaCost int?
---@field hasStaminaCost boolean?
---@field drillLevel int?

---@alias TeamHeroesHealth Dict<int, Health>

---@class BattleResult
---@field success boolean
---@field winCamp int
---@field heroesHealth Dict<BattleCamp, TeamHeroesHealth>
---@field teamConsumeHealth Dict<BattleCamp, Health>
---@field heroesRecoverableHealth Dict<BattleCamp, TeamHeroesHealth>
---@field addExp Dict<BattleCamp, IntIntDict>
---@field addMerit Dict<BattleCamp, int>

---@class ArmyFormDetails
---@field injuredTime number?
---@field staminaDetail ArmyStaminaDetail?
---@field armyMinLevel int
---@field pvpStaminaCost int?
---@field supplyMax int?

---@alias CampFormInfo Dict<int, ArmyFormDetails>

---@class AttributeData
---@field attack int
---@field defense int
---@field health int
---@field intelligence int
---@field speed int

---@class InputsData
---@field event int
---@field step int
---@field args any

---@class Vector3Int
---@field x int
---@field y int
---@field z int

---@class Vector3Float
---@field x number
---@field y number
---@field z number

---@class Tips
---@field id int
---@field args any

---@class Callback
---@field mailbox string
---@field func string
---@field args any

---@class CurrencyDetail
---@field num int
---@field limit int

---@alias CurrencyDict Dict<int, CurrencyDetail>
---@alias CurrencyAdds Dict<int, int>

---@class HomeAddition
---@field armySoldierAdd int
---@field armyBuffTemp ArmyBuffDict
---@field armyFateBuff ArmyBuffDict
---@field armySupplyAdd int

---@class WorldBuff
---@field id int
---@field timeout number

---@alias WorldBuffDict Dict<string, WorldBuff>
---@alias HeroTacticDict Dict<int, TacticId>
---@alias EquipSlots Dict<int, Uid>

---@class HeroPackDetail
---@field level int
---@field exp int
---@field staminaDetail HeroStaminaDetail
---@field injuredTime number
---@field stars int
---@field addPoints HeroAddPointsDict
---@field bornWithTacticLevel int
---@field tactics HeroTacticDict
---@field equipSlots EquipSlots
---@field mainIdentity int
---@field identitySlots IdentitySlots
---@field inArmy Uid

---@alias HeroPack Dict<HeroId, HeroPackDetail>

---@class TacticPackDetail
---@field level int
---@field stars int
---@field equipBy HeroId

---@alias TacticPack Dict<TacticId, TacticPackDetail>

---@class TacticConfig
---@field validResetCnt int

---@class HomeBuildingDetail
---@field level int

---@alias HomeBuildingDict Dict<HomeBuildingId, HomeBuildingDetail>
---@alias ResBuffByRatio Dict<int, float>
---@alias ResProduceAddType Dict<int, int>
---@alias ResProduceAddSrc Dict<int, ResProduceAddType>

---@class ResProduceBuff
---@field addByRatio ResBuffByRatio

---@alias ResProduceBuffs Dict<string, ResProduceBuff>

---@class ResProduce
---@field addition ResProduceAddSrc
---@field lastTime number
---@field produceBuffs ResProduceBuffs
---@field partProduceFloat ProduceToFloatNum

---@class SoldierProduce
---@field speed int
---@field fastCost int
---@field fast boolean
---@field lastTime number
---@field partProduceFloat number

---@alias HomeTechs Dict<int, int>

---@class MainTaskDetail
---@field satisfied boolean
---@field processForShow int

---@alias MainTaskDict Dict<int, MainTaskDetail>
---@alias CompleteMainTaskDict Dict<int, bool>

---@class CycleTaskDetail
---@field isComplete boolean

---@alias CycleTaskDict Dict<int, CycleTaskDetail>

---@class ActorCmd
---@field processingCmd string
---@field uid Uid

---@class ActorActionElement
---@field action string
---@field params any
---@field status int

---@alias ActorActionElementList ActorActionElement[]

---@class ActorActions
---@field uid Uid
---@field stack ActorActionElementList
---@field failedActions ActorActionElementList

---@class AvatarOneLand
---@field tp GridType
---@field occupyTime number
---@field giveUpTime number?
---@field locked boolean
---@field levelAddTo int
---@field produceBuffs OneLandProduceAddList

---@alias AvatarLands Dict<int, AvatarOneLand>

---@class AvatarOneRoad
---@field tp GridType
---@field occupyTime number
---@field giveUpTime number?
---@field locked boolean

---@alias AvatarRoads Dict<PackedPosition, AvatarOneRoad>

---@class AvatarOneFavorite
---@field name string
---@field type int

---@alias AvatarFavorite Dict<PackedPosition, AvatarOneFavorite>

---@class LandLimitAdd
---@field resLand int
---@field roadLand int
---@field homeTechResLand int
---@field homeTechRoadLand int

---@alias CareerTree Dict<int, bool>

---@class AllyMemberDetail
---@field online boolean

---@alias AllyMembers Dict<AvatarGid, AllyMemberDetail>
---@alias ClanList ClanId[]

---@class ClanMemberBriefInfo
---@field gid AvatarGid
---@field name string
---@field birthCommandery Commandery
---@field birthplace Prefecture
---@field headpic int
---@field career Career
---@field merit MeritSummary
---@field prosperity int
---@field mainCityView MainCityView
---@field rebornFlag boolean

---@class ClanMemberDetail: ClanMemberBriefInfo
---@field grade int
---@field enterTimeStamp int
---@field online boolean
---@field onOfflineStamp int

---@alias ClanMembers Dict<AvatarGid, ClanMemberDetail>

---@class ClanApplicantDetail: ClanMemberBriefInfo
---@field description string
---@field timestamp int

---@alias ClanApplicants Dict<AvatarGid, ClanApplicantDetail>
---@alias ClanViceLeaderList AvatarGid[]

---@class ClanBadge
---@field pattern int
---@field patternWord string
---@field patternWordVertical boolean
---@field scale int
---@field backgroundColor int
---@field patternColor int
---@field rotate int

---@class ClanApplyVerify
---@field needVerify boolean
---@field influenceRequire int
---@field isApplyOpen boolean

---@alias ContributionDetail Dict<AvatarGid, PeriodicDataSummary>

---@class CreateClanInfo
---@field clanId int
---@field name string
---@field birthCommandery Commandery
---@field birthplace Prefecture
---@field clanBadge ClanBadge
---@field clanMotto string
---@field applyVerify ClanApplyVerify
---@field leaderGid AvatarGid
---@field members ClanMembers
---@field membersCnt int
---@field contributionDetail ContributionDetail

---@class ClanBriefInfo
---@field clanId int
---@field name string
---@field birthCommandery Commandery
---@field birthplace Prefecture
---@field clanBadge ClanBadge
---@field clanMotto string
---@field leaderGid AvatarGid
---@field leaderName string
---@field leaderHeadpic int
---@field membersCnt int
---@field level int
---@field influence int
---@field totalMerit Merit
---@field weekMerit Merit
---@field applyVerify ClanApplyVerify

---@alias ClanBriefInfoList ClanBriefInfo[]

---@class ClanSyncPrefectureInfo
---@field membersCnt int

---@alias ClanSyncPrefectureInfoDict Dict<ClanId, ClanSyncPrefectureInfo>

---@class ClanSortInfo
---@field clanId ClanId
---@field birthCommandery Commandery
---@field birthplace Prefecture
---@field influence int

---@alias ClanSortInfoList ClanSortInfo[]

---@class ClanManagerBriefInfo
---@field gid AvatarGid
---@field name string
---@field headpic int

---@alias ClanManagerBriefInfoList ClanManagerBriefInfo[]

---@class ClanDetailInfo: ClanBriefInfo
---@field commRank int
---@field viceLeaderInfoList ClanManagerBriefInfoList

---@class ClanNameSearch
---@field name string
---@field isCurrComm boolean?

---@class ClanInvitePlayerBriefInfo
---@field gid AvatarGid
---@field name string
---@field birthCommandery Commandery
---@field headpic int
---@field career Career
---@field prosperity int
---@field kingBuildingLv int

---@alias ClanInvitePlayerBriefInfoList ClanInvitePlayerBriefInfo[]

---@class StrategyPackDetail
---@field useLimitAdd int
---@field useCnt int
---@field hasCnt int
---@field storageLimitAdd int

---@alias StrategyPack Dict<StrategyId, StrategyPackDetail>

---@class StrategyAddition
---@field wuZhongShengYouAddResRatio int
---@field tuoJiangRadiusAdd int
---@field tuoJiangDailyCardAdd int
---@field poXianLandLevelLimitTo int
---@field woYeProduceExRatio int
---@field woYeDuration int
---@field jieCeSaveStrategy int
---@field xuShiStrategyRecoverAdd int
---@field xuShiYingStrategyLimitAdd int
---@field jiKuiResAdd int
---@field resAddRatio4WuZhongShengYou IntIntDict

---@class CityDevGrid
---@field landType int

---@alias CityDevGrids Dict<int, CityDevGrid>

---@class ItemDetail
---@field num int
---@field expireTime number

---@alias ItemDetailList ItemDetail[]

---@class Item
---@field num int
---@field special ItemDetailList

---@alias ItemBag Dict<ItemId, Item>

---@class UseItemArg
---@field id ItemId
---@field num int
---@field choice int

---@alias UseItemArgList UseItemArg[]
---@alias ExpiredItems Dict<ItemId, int>
---@alias TokenIdList TokenId[]
---@alias AmbitionsRewardsRecord Dict<int, bool>
---@alias AmbitionOccupyLand Dict<int, int>

---@class AmbitionTarget
---@field occupyLand AmbitionOccupyLand

---@alias AmbitionTargetView Dict<int, int>

---@class OccupyBattleRecord
---@field recordUid Uid?
---@field recordGuid Uid?
---@field totalLosses int
---@field teamAbstractDict IntIntDict

---@class BaseRankElement
---@field id int
---@field value int
---@field recordUid Uid?
---@field recordGuid Uid?
---@field totalLosses int?
---@field teamAbstractDict IntIntDict?

---@class RankElement: BaseRankElement
---@field seq int

---@alias RankElementMap Dict<int, RankElement>

---@class RankList
---@field lastSeq int
---@field elements RankElementMap

---@class Rank
---@field cycle int?
---@field all RankList?
---@field byCareer Dict<int, RankList>?
---@field byLevel Dict<int, RankList>?

---@class TopNElement: BaseRankElement
---@field detail any?

---@alias RankTopN TopNElement[]

---@class RankResult
---@field rank int
---@field allyRank int
---@field allyId AllyId

---@class PersonalRankResult: RankResult
---@field valid boolean

---@alias RankResultDict Dict<AvatarGid, RankResult>

---@class RankSettleElement
---@field rank int
---@field allyRank int

---@alias RankSettleList RankSettleElement[]

---@class RankSettleResult
---@field valid boolean
---@field rank RankResultDict

---@class RankFilter
---@field career int?
---@field level int?

---@class CityOccupationDetail
---@field allyId AllyId
---@field occupyTime number

---@alias CityOccupation Dict<CityId, CityOccupationDetail>
---@alias RoleMap Dict<Prefecture, int>

---@class NationBriefInfo
---@field foundTime number

---@class AllyBriefInfo
---@field name string
---@field tmpClanId ClanId
---@field birthplace Prefecture
---@field nation NationBriefInfo?
---@field ambitionsScore int

---@alias AllyBrief Dict<AllyId, AllyBriefInfo>

---@class S1SuggestChooseRole
---@field suggestRecord Dict<AvatarGid, int>
---@field summary Dict<int, int>

---@class S1ChoosingRoleSequence
---@field birthplace Prefecture
---@field allyId AllyId

---@alias S1ChoosingRoleSequenceList S1ChoosingRoleSequence[]

---@class S1WaitChooseCard
---@field role int
---@field selectBy Prefecture

---@class S1ChoosingRole
---@field sequence S1ChoosingRoleSequenceList
---@field selectingIdx int
---@field curTimeout number
---@field cards Dict<int, S1WaitChooseCard>

---@class DrillRoundBattle
---@field winCamp int
---@field selfOrderIdx int
---@field enemyOrderIdx int
---@field selfSoldierLeft int
---@field enemySoldierLeft int
---@field selfRecoverable int
---@field enemyRecoverable int

---@alias DrillRound DrillRoundBattle[]
---@alias DrillResult DrillRound[]

---@class DrillChapterBox
---@field chapterId int
---@field boxIdx int

---@alias DrillProduce Dict<int, int>
---@alias DrillFixedRewardProcess Dict<ItemId, float>

---@class DrillHangingBox
---@field lastPickTime number
---@field lastSettleTime number
---@field fixedRewardProcess DrillFixedRewardProcess
---@field partProduceFloat ProduceToFloatNum
---@field speed DrillProduce

---@class DrillDailyChance
---@field cycleId int
---@field count int

---@class SupplyRecover
---@field last number?
---@field timer int?

---@class Prosperity
---@field sum int
---@field src Dict<int, int>

---@class TentView
---@field id EntityId
---@field pos Position
---@field buildStatus int
---@field processTimeout number?
---@field alias string?

---@alias TentViewDict Dict<Uid, TentView>

---@class StoneVehicle
---@field id EntityId
---@field pos Position
---@field buildStatus int
---@field processTimeout number?

---@alias StoneVehicleDict Dict<Uid, StoneVehicle>
---@alias MailType int
---@alias MailUid int
---@alias MailUidList MailUid[]
---@alias MailArgs string[]

---@class Mail
---@field uid MailUid
---@field time int
---@field templateId int?
---@field args MailArgs?
---@field title string?
---@field content string?
---@field type MailType?
---@field sender AvatarGid?
---@field attachment Dict<ItemId, int>?
---@field gift boolean
---@field read boolean

---@alias MailList Mail[]

---@class Shop
---@field cycle int
---@field refreshTime number
---@field shelf IntIntDict

---@alias AvatarShop Dict<int, Shop>

---@class AllySiegeCampView
---@field city CityId
---@field cityPos Position
---@field pos Position
---@field buildStatus int
---@field processTimeout number?
---@field siegeUid Uid?
---@field announceTimeout number?
---@field siegeTimeout number?
---@field mainForceNum int
---@field duraForceNum int

---@alias AllySiegeCampViewDict Dict<EntityId, AllySiegeCampView>

---@class AllyCityView
---@field instanceId InstanceId
---@field pos Position

---@alias AllyCityViewDict Dict<EntityId, AllyCityView>

---@class AllyVehicleView
---@field pos Position
---@field operator EntityId?
---@field buildStatus int
---@field processTimeout number?

---@alias AllyVehicleViewDict Dict<EntityId, AllyVehicleView>

---@class SysDefenderElement
---@field id int
---@field team any
---@field defenderInCombat boolean

---@alias SysDefender Dict<Uid, SysDefenderElement>
---@alias SiegeLastHit Dict<int, any>

---@class SiegeRankElement
---@field gid AvatarGid
---@field value int

---@alias SiegeRank SiegeRankElement[]

---@class SiegeRankDetailElement: SiegeRankElement
---@field detail any

---@alias SiegeDetailRank SiegeRankDetailElement[]

---@class CityFirstOccupy
---@field allyId AllyId
---@field dura SiegeRank
---@field kill SiegeRank

---@class AllyInWarDetail
---@field siegeGroupIdx int
---@field startAssault boolean

---@alias AllyInWar Dict<AllyId, AllyInWarDetail>
---@alias AutoRoadPassGrids Dict<int, bool>

---@class AutoRoad
---@field queryId int
---@field path PositionList
---@field serverQueryId int
---@field passGrids AutoRoadPassGrids

---@class GachaInfo
---@field drawCnt int
---@field dailyDrawCnt int
---@field dailyPeriod int
---@field lastFreeDrawStamp int
---@field lastHalfDrawStamp int

---@alias GachaManager Dict<int, GachaInfo>

---@class RandomItemInfo
---@field guaranteeProg IntIntDict

---@alias RandomItemManager Dict<int, RandomItemInfo>

---@class RewardDisplayDetail
---@field gain int
---@field transfer int

---@alias RewardDisplayData Dict<ItemId, RewardDisplayDetail>
---@alias RewardDisplayDataList RewardDisplayData[]

---@class ImpasseDetail
---@field endTime number
---@field timer int
---@field packedPos PackedPos

---@alias Impasse Dict<int, ImpasseDetail>

---@class ContinousBattle
---@field merit Merit
---@field winCount int
---@field staminaCost int

---@class DurabilityRecover
---@field enabled boolean
---@field fullStop boolean
---@field last number?

---@class ArmyView
---@field mainStatus int
---@field combatStatus int
---@field deployStatus int
---@field vehicleStatus int
---@field statusEndTime number
---@field moveStartPos Position
---@field moveStartTime number
---@field moveTargetPos Position
---@field movePath Position[]
---@field moveSpeed number
---@field pos Position
---@field supply int
---@field supplyMax int
---@field preorderSiege ArmyPreorderSiege
---@field inAutoPath ArmyInAutoPath
---@field home ArmyHomeData
---@field deployHome ArmyDeployHomeData?
---@field siegeHome ArmySiegeHomeData?

---@class ArmyData
---@field uid Uid
---@field armyType ArmyType
---@field heroes HeroDataDict
---@field autoFullSoldier boolean
---@field representHeroId HeroId
---@field armyInjuredTime number
---@field fateDynasty int?
---@field battleArmyData BattleArmyData
---@field gmBattleArmyData BattleArmyData
---@field entityId EntityId
---@field view ArmyView

---@class CardData
---@field id int
---@field level int

---@class CompletedHero
---@field attack int
---@field attack_limit int
---@field defense int
---@field defense_limit int
---@field health int
---@field health_limit int
---@field intelligence int
---@field intelligence_limit int
---@field speed int
---@field speed_limit int
---@field monopolyHeroId string
---@field templateHeroId int
---@field cards Dict<int, CardData>

---@alias CompletedHeroes Dict<string, CompletedHero>

---@class BattleHeadInfo
---@field battleType int
---@field modeType int
---@field battleStageId int
---@field selfUid string
---@field enemyUid string

---@class BattleFormationData
---@field displayName string
---@field lastModified number
---@field formationIndex int
---@field heroes Dict<int, string>

---@class BattleStageHistoryData
---@field formationIndex int

---@alias BattleStageHistory BattleStageHistoryData[]

---@class FocusActorView
---@field moveStartPos Position
---@field moveStartTime number
---@field moveTargetPos Position
---@field movePath Position[]
---@field moveSpeed number
---@field relation Relation

---@alias CompletedHeroUid string

---@class FormationElement
---@field id TemplateHeroId
---@field uid CompletedHeroUid

---@alias Formation Dict<int, FormationElement>
---@alias Formations Formation[]

---@class HeroAbstract
---@field id HeroId
---@field level int
---@field stars int
---@field startAlive boolean
---@field endAlive boolean

---@class BattleTeamAbstract
---@field gid AvatarGid
---@field playerName string
---@field allyId AllyId
---@field clanId ClanId
---@field health int
---@field maxHealth int
---@field recoverableHealth int
---@field supply int
---@field heroAbstractDict Dict<int, HeroAbstract>

---@class BattleDurabilityInfo
---@field consumeDurability int
---@field buildingOwner string
---@field buildingName string
---@field zeroed boolean

---@class BattleAbstract
---@field uid Uid
---@field guid GuidDict?
---@field battleType int
---@field position PackedPosition
---@field gridName string
---@field timestamp number
---@field winCamp BattleCamp
---@field energyCost int
---@field durabilityInfo BattleDurabilityInfo?
---@field teamAbstractDict BattleTeamAbstract[]
---@field drillLevel int?

---@class BattleAbstractGroup: BattleAbstract
---@field groupedAbstracts BattleAbstract[]?
---@field groupedAbstractUids Uid[]?

---@alias BattleAbstracts BattleAbstractGroup[]

---@class BattleAbstractsRedDot
---@field globalStamp int
---@field guid2Stamp UidIntDict
---@field globalOuterStamp int
---@field outerCount int
---@field innerCount int

---@class PackedBattleAbstractGroup
---@field firstAbstract string
---@field groupedAbstractUids UidList?

---@alias PackedBattleAbstractGroups PackedBattleAbstractGroup[]

---@class PackedDrillBattleAbstractGroup: PackedBattleAbstractGroup
---@field firstAbstractUid Uid
---@field guid GuidDict

---@alias PackedDrillBattleAbstractGroups PackedDrillBattleAbstractGroup[]

---@class BattleAbstractFilter
---@field battleType int?
---@field position PackedPosition?
---@field allyId AllyId?
---@field clanId ClanId?
---@field playerGid AvatarGid?
---@field heroList HeroList?
---@field needHeroWin boolean?

---@class BattleTacticStatistic
---@field tacticData TacticData
---@field useCount int
---@field damage int
---@field heal int

---@class BattleHeroStatistic
---@field armyType ArmyType
---@field armyTypeQualification int
---@field equips Equip[]
---@field horses Horse[]
---@field identitySlots IdentitySlots
---@field attributes BattleAttritubes
---@field health int
---@field maxHealth int
---@field recoverableHealth int
---@field tacticStatisticDict BattleTacticStatistic[]
---@field gainExp int
---@field toLevel int

---@class BattleTeamStatistic
---@field merit int
---@field totalKill int
---@field heroStatisticDict Dict<int, BattleHeroStatistic>

---@class BattleStatistic
---@field position Position
---@field winCamp BattleCamp
---@field teamStatisticDict Dict<BattleCamp, BattleTeamStatistic>
---@field extraDebugInfo any

---@class ImTokenInfo
---@field token string
---@field expireStamp int

---@class ReportPlayerInfo
---@field avatarGid AvatarGid
---@field reason IntList
---@field desc string

---@class ChannelAvatarInfo
---@field gid AvatarGid
---@field name string
---@field headpic HeadPic
---@field headFrame HeadFrame
---@field career Career
---@field allyName string

---@class ChannelMsgInfo
---@field uid Uid
---@field content string

---@class RequestChannelMsgInfo
---@field content string
---@field refer ChannelMsgInfo?

---@class ChannelReferMsgInfo: ChannelMsgInfo
---@field refer ChannelMsgInfo?

---@class ChannelMsg
---@field avatarInfo ChannelAvatarInfo
---@field msg ChannelReferMsgInfo
---@field stamp int

---@alias ChannelMsgList ChannelMsg[]

---@class PayInfoRequest
---@field goodsId int
---@field tb string

---@class PayInfoResponse: PayInfoRequest
---@field orderId Uid?
---@field shipUrl string?

---@alias PayInfoCache Dict<Uid, PayInfoResponse>

---@class AutoPathAgent
---@field gid AvatarGid
---@field allyId AllyId
---@field clanId ClanId
---@field level int
---@field startPos Position?
---@field passGrids AutoRoadPassGrids
---@field stepLeft int

---@class DeployArgs
---@field deployType int
---@field isMainForce boolean

---@class HostPort
---@field host string
---@field port int
---@field ssl boolean

---@class GmMirrorMemberDetail
---@field gid AvatarGid
---@field confirmed boolean

---@alias GmMirrorMembers Dict<string, GmMirrorMemberDetail>

---@class GmMirror
---@field index int?
---@field members GmMirrorMembers

---@class GmMirrorBy
---@field gid AvatarGid
---@field index int
---@field waitConfirm Callback?

---@class WaitBattleWithBase
---@field result boolean?

---@class WaitBattleWithLandDefender: WaitBattleWithBase
---@field hasStaminaCost boolean
---@field occupyBattleRecord any?

---@class WaitBattleWithSysDefender: WaitBattleWithBase

---@class WaitBattleWithDuraDefender: WaitBattleWithBase
---@field recordUid Uid?

---@class WaitBattleArgs
---@field landDefender WaitBattleWithLandDefender?
---@field sysDefender WaitBattleWithSysDefender?
---@field duraDefender WaitBattleWithDuraDefender?

---@class WaitBattle: WaitBattleArgs
---@field cb EntityCallback?

---@class SlgCity
---@field level int
---@field pos Position
---@field actorType ActorTypeEnum
---@field subId int
---@field county int?

---@alias SlgCityInfo Dict<CityId, SlgCity>

---@class TmpSlgSummary
---@field city SlgCityInfo

---@class AccountProps: AvatarEntityProps, ClientHotfixCompProps, ServerTimeCompProps
---@field urs string

---@class ActiveStandbyServiceProps: ServiceProps, GlobalDataAckCompProps

---@class ActorCallerServiceProps

---@class ActorCmdCompProps
---@field cmd ActorCmd  @当前执行的指令，已经确认开始执行
---@field actions ActorActions  @指令的具体行为栈

---@class ActorContainerCompProps
---@field contents Uid[]

---@class ActorContentCompProps
---@field inCon Dict<Uid, int>  @actor所处的容器id及其对应的容器关系类型
---@field inConByType Dict<int, Uid>  @按容器类型索引的actor所处容器id

---@class ActorLoadCompProps

---@class ActorOwnerCompProps

---@class ActorViewCompProps
---@field mainCityView MainCityView?
---@field tentView TentViewDict
---@field stoneVehicleView StoneVehicleDict
---@field focusActorEntityId EntityId?
---@field focusActorView FocusActorView?

---@class AllyProps: GMCompProps, RankHostCompProps, SiegeCompProps
---@field allyId AllyId
---@field name string
---@field birthplace Prefecture
---@field leaderGid AvatarGid
---@field members AllyMembers
---@field cityLandProduce Dict<int, ResBuffByRatio>
---@field personalRank Rank
---@field ambitionsScore int
---@field nation NationBriefInfo?

---@class AllyCompProps

---@class AllyServiceProps
---@field nextAllyId AllyId

---@class AmbitionsCompProps
---@field ambitionsRewards AmbitionsRewardsRecord
---@field midRank PersonalRankResult
---@field finalRank PersonalRankResult
---@field s1IdentitySuggest int

---@class AmbitionsServiceProps: ScheduleCompProps, S1IdentityCompProps, RankHostCompProps
---@field processId int
---@field targets AmbitionTarget
---@field views AmbitionTargetView
---@field allyRank Rank
---@field prosperityRank Rank
---@field meritRank Rank
---@field meritWeekRank Rank
---@field firstOccupyRank Rank
---@field clanProsperityRank Rank
---@field clanMeritRank Rank
---@field clanMeritWeekRank Rank
---@field firstOccupyRankMaxLevel int
---@field cityOccupation CityOccupation
---@field allyBrief AllyBrief
---@field middleRankSettle RankSettleResult
---@field finalRankSettle RankSettleResult
---@field tmpSlgSummary TmpSlgSummary

---@class ArmyProps: WorldActorProps, MoveCompProps, ActorCmdCompProps, AvatarOwningCompProps, InGridCompProps, ArmyCombatCompProps, ArmyBuffCompProps, ActorContentCompProps, NaviCompProps, ArmyStrategyCompProps, ArmySupplyCompProps, VehicleDriverCompProps, ArmyHomeCompProps, ArmySiegeCompProps, OutpostAttackerCompProps
---@field armySlotIdx int  @部队槽位索引
---@field armyType ArmyType  @部队兵种类型 GeneralArmyType
---@field advanceArmyType ArmyType  @废弃待定删除
---@field mainStatus int  @部队主状态属性
---@field combatStatus int  @部队战斗状态属性
---@field deployStatus int  @部队调动状态属性
---@field vehicleStatus int  @部队器械状态属性
---@field inDefendGrid PositionList?  @当前驻守地块的格子列表
---@field inDefendBuilding PositionList?  @当前驻守建筑的格子列表
---@field inAutoPath ArmyInAutoPath  @自动铺路
---@field staminaDetail ArmyStaminaDetail  @体力详情
---@field armyMinLevel int  @部队最低的武将等级

---@class ArmyBuffCompProps
---@field buffs ArmyBuffDictBySrc  @部队战斗buff
---@field worldBuffs WorldBuffDict  @部队沙盘buff，会影响沙盘表现

---@class ArmyCombatCompProps
---@field waitBattle WaitBattle?  @等待交战
---@field inCombat int?  @交战中的目标id，可以是部队的entityId或者是NpcType
---@field combatPos PackedPos?  @交战发生的未知，驻守部队交战位置可以是其他格子
---@field injuredTime number?  @部队重伤结束时间戳，不一定和部队内所有武将相同
---@field occupyLand boolean  @是否在占领地块，状态标记
---@field combatGuid Uid?  @出征交战的唯一id，用于战报合并
---@field impasse Impasse  @当前处于僵持中的对象合集、包括NPC和玩家部队
---@field continousBattle ContinousBattle  @连续战斗的数据记录，用于战斗播报

---@class ArmyFormationCompProps
---@field armys Dict<ArmySlotIdx, ArmyData>
---@field staminaMax int

---@class ArmyHomeCompProps
---@field home ArmyHomeData
---@field deployHome ArmyDeployHomeData?
---@field siegeHome ArmySiegeHomeData?
---@field autoFullSoldier boolean

---@class ArmyMgrCompProps
---@field autoRoad AutoRoad

---@class ArmySiegeCompProps
---@field preorderSiege ArmyPreorderSiege

---@class ArmyStrategyCompProps
---@field playingStrategy int
---@field waitContinuePlayStrategy boolean

---@class ArmySupplyCompProps
---@field supply int
---@field supplyMax int
---@field supplyRecover SupplyRecover

---@class AutoPathCompProps

---@class AvatarProps: AvatarEntityProps, WorldViewportCompProps, ServerTimeCompProps, BattleRecordCompProps, HeroPackCompProps, TacticPackCompProps, AvatarHomeCompProps, TaskComponentProps, CareerStrategyCompProps, ScheduleCompProps, EquipCompProps, BagCompProps, AmbitionsCompProps, DrillCompProps, MailCompProps, ShopCompProps, GachaCompProps, FriendCompProps, ChannelCompProps, PayCompProps, NewbeeGuideCompProps, ClientHotfixCompProps, SensitiveWordCompProps
---@field urs string
---@field gid AvatarGid
---@field name string
---@field firstCreate boolean
---@field createTime int
---@field loginTime int
---@field logoutTime int
---@field levelId int
---@field clusterId int
---@field completedHeroes CompletedHeroes
---@field battleFormations Dict<int, BattleFormationData>
---@field battleStageHistory Dict<int, BattleStageHistory>
---@field maxFormations int
---@field maxStageIds int

---@class AvatarArmyCombatCompProps
---@field merit Merit
---@field meritSummary MeritSummary

---@class AvatarEntityProps: EntityProps

---@class AvatarGMCompProps
---@field gmMirror GmMirror
---@field gmMirrorBy GmMirrorBy?
---@field gmAccess boolean

---@class AvatarHomeCompProps
---@field homeBuildings HomeBuildingDict
---@field homeAddition HomeAddition
---@field homeTechs HomeTechs
---@field farmerWorkConfig IntIntDict
---@field autoPathMaxLength int

---@class AvatarInfoServiceProps

---@class AvatarOwningCompProps
---@field birthplace Prefecture  @出生州，归属Avatar的Actor会有的属性

---@class BagCompProps
---@field itemBag ItemBag
---@field materialBag ItemBag
---@field tokenBag ItemBag
---@field expiredItems ExpiredItems

---@class BalanceBattleProps: EntityProps

---@class BalanceBattleServiceProps: ServiceProps

---@class BatchOpCompProps

---@class BattleRecordCompProps
---@field drillBattleAbstracts PackedDrillBattleAbstractGroups
---@field starredBattleAbstracts BattleAbstracts
---@field battleAbstractsRedDot BattleAbstractsRedDot

---@class BattleServiceProps

---@class BuildMgrCompProps

---@class CareerStrategyCompProps
---@field career int
---@field strategyPoint int
---@field strategyPointLRT number
---@field careerTalentPoint int
---@field careerTree CareerTree
---@field strategyPack StrategyPack
---@field strategyCycle int
---@field strategyAddition StrategyAddition

---@class ChannelCompProps

---@class ChunkProps: WorldActorProps
---@field xIndex int
---@field yIndex int
---@field gridOccupations Dict<int, GridOccupation>
---@field gridBuildings Dict<int, GridBuilding>

---@class ChunkMgrCompProps

---@class CityProps: WorldActorProps, ActorContainerCompProps, SysBuildingCompProps, WorldBuildingCompProps, CityCompProps, OutpostCompProps
---@field devGrids CityDevGrids

---@class CityBarracksProps: WorldActorProps, ActorContainerCompProps, ActorContentCompProps, InGridCompProps, WorldBuildingCompProps, OutpostCompProps
---@field belongCity CityId?

---@class CityCompProps
---@field firstOccupy CityFirstOccupy?
---@field siegeGroupsByIdx Dict<int, IntIntDict>
---@field allyInWar AllyInWar

---@class CityGateProps: WorldActorProps, ActorContainerCompProps, ActorContentCompProps, WorldBuildingCompProps, InGridCompProps, OutpostCompProps, CityGateCompProps

---@class CityGateCompProps
---@field belongCity CityId?
---@field siegeGroupIdx int?
---@field inWarWith AllyId?

---@class ClanProps: EntityProps, ScheduleCompProps
---@field clanId ClanId
---@field name string
---@field birthCommandery Commandery
---@field birthplace Prefecture
---@field clanBadge ClanBadge
---@field clanMotto string
---@field leaderGid AvatarGid
---@field viceLeaderList ClanViceLeaderList
---@field members ClanMembers
---@field membersCnt int
---@field applyVerify ClanApplyVerify
---@field applicants ClanApplicants
---@field applicantsCnt int
---@field invitees ClanApplicants
---@field inviteesCnt int
---@field level int
---@field exp int
---@field influence int
---@field commRank int
---@field funds int
---@field contributionDetail ContributionDetail
---@field cityLandProduce Dict<int, ResBuffByRatio>

---@class ClanCompProps
---@field clanId ClanId
---@field clanName string
---@field enterLeaveStamp int
---@field appliedClanList ClanList
---@field invitedClanList ClanList
---@field invitedClanCnt int
---@field giveRebornTimeFlag boolean

---@class ClanInviteServiceProps

---@class ClanServiceProps
---@field nextClanId ClanId
---@field createDbIndexFlag boolean

---@class ClientHotfixCompProps

---@class CmdServiceProps: MicroserviceProps

---@class CurrencyCompProps
---@field currency CurrencyDict

---@class DBMgrProps

---@class DrillCompProps
---@field currentDrillChapter int
---@field finishedDrillLevel int?
---@field drillChapterBox DrillChapterBox
---@field drillHangingBox DrillHangingBox
---@field drillDailyChance DrillDailyChance
---@field drillChallengeCount int

---@class EntityProps

---@class EquipCompProps
---@field equipBag EquipBag
---@field horseBag HorseBag
---@field horseTraining HorseTraining
---@field equipForge EquipForge
---@field equipRebuild EquipRebuild

---@class FerryProps: WorldActorProps, ActorContainerCompProps, SysBuildingCompProps, WorldBuildingCompProps, CityCompProps, OutpostCompProps

---@class FollowMoveCompProps
---@field inFollowMoving EntityId?
---@field posTime number

---@class FriendCompProps
---@field imTokenInfo ImTokenInfo?
---@field imAppId string

---@class GMCompProps

---@class GachaCompProps
---@field gachaMgr GachaManager
---@field randomItemMgr RandomItemManager

---@class GlobalDataAckCompProps

---@class GridMgrCompProps

---@class HeroPackCompProps
---@field heroPack HeroPack

---@class ImSdkCompProps

---@class InGridCompProps
---@field inGridOrder InGridOrder  @实体进入格子内的编号，客户端用于计算显示优先级

---@class InnerCityGateProps: WorldActorProps, ActorContainerCompProps, ActorContentCompProps, WorldBuildingCompProps, InGridCompProps, OutpostCompProps, CityGateCompProps

---@class JiShenJiDianActorProps: WorldActorProps
---@field jiDianEndTime number
---@field jiDianPaused boolean

---@class LandMgrCompProps
---@field lands AvatarLands
---@field roads AvatarRoads
---@field favorites AvatarFavorite
---@field landLimitAdd LandLimitAdd
---@field firstOccupyLand IntIntDict
---@field landDefenderRecover IntFloatDict

---@class LogicProxyProps: ImSdkCompProps, XiaoShanCompProps, BatchOpCompProps

---@class LogicServiceProps

---@class LoginMgrCompProps

---@class MailCompProps
---@field mailHost HostPort
---@field favoriteMail MailUidList

---@class MailServiceProps

---@class MainCityProps: WorldActorProps, ActorContainerCompProps, AvatarOwningCompProps, WorldBuildingCompProps, InGridCompProps, OutpostCompProps
---@field level int

---@class MainCityCompProps
---@field mainCity MainCityData

---@class MicroserviceProps: ServiceProps

---@class MonitorServiceProps

---@class MoveCompProps
---@field moveStartPos Position  @移动开始位置
---@field moveStartTime number  @移动开始时间
---@field moveLastTickTime number  @上个格子移动结束时间
---@field moveTargetPos Position  @移动目标位置
---@field movePath Position[]  @移动路径点列表
---@field moveSpeed number  @移动速度，移动一格的耗时(秒)
---@field moveType int  @移动枚举类型
---@field targetEid EntityId?  @移动目标实体ID，暂时没用上
---@field moveLineId int
---@field posTime number  @到达当前格子的时间点
---@field moveSpeedAdd Dict<string, int>  @移动加成记录

---@class NaviCompProps

---@class NaviServiceProps

---@class NaviTestProps

---@class NewbeeGuideCompProps
---@field newbeeGuideProg int
---@field newbeeGuideGachaCnt int

---@class OutpostAttackerCompProps
---@field inOutpost Uid?
---@field inBreakDura boolean

---@class OutpostCompProps
---@field durability int
---@field durabilityMax int
---@field sysDefender SysDefender
---@field sysDefenderNum int
---@field sysDefenderInCombatCount int
---@field sysDefenderRecover number
---@field barracksDefenderType IntIntDict
---@field duraDefender UidList
---@field defenders UidList
---@field attackers UidList
---@field deployers UidList
---@field duraRecover DurabilityRecover

---@class PassProps: WorldActorProps, ActorContainerCompProps, SysBuildingCompProps, WorldBuildingCompProps, CityCompProps, OutpostCompProps

---@class PayCompProps
---@field orderCache PayInfoCache
---@field firstDoubleJadeRecord IntList

---@class PayServiceProps

---@class PrefectureSyncProps
---@field clanInfos ClanSyncPrefectureInfoDict

---@class ProsperityCompProps
---@field prosperity Prosperity

---@class RankHostCompProps

---@class ResProduceCompProps
---@field resProduce ResProduce
---@field soldierProduce SoldierProduce
---@field resProduceSpeed IntIntDict

---@class RoleServiceProps: LoginMgrCompProps, GlobalDataAckCompProps
---@field createDbIndexFlag boolean

---@class S1IdentityCompProps
---@field roleMap RoleMap
---@field chooseStartTime number
---@field choosingRole S1ChoosingRole?
---@field choosingRoleSuggest S1SuggestChooseRole

---@class ScheduleCompProps
---@field scheduleRec Dict<int, int>

---@class SensitiveWordCompProps

---@class ServerTimeCompProps

---@class ServiceProps: EntityProps, GlobalDataAckCompProps

---@class ShopCompProps
---@field shop AvatarShop

---@class SiegeCampProps: WorldActorProps, ActorContainerCompProps, ActorContentCompProps, WorldBuildingCompProps, InGridCompProps, OutpostCompProps
---@field belongCity CityId?
---@field siegeGroupIdx int?
---@field siegeUid Uid?
---@field announceTimeout number?
---@field siegeTimeout number?
---@field mainForce UidList
---@field duraForce UidList

---@class SiegeCompProps
---@field siegeCamp AllySiegeCampViewDict
---@field city AllyCityViewDict
---@field vehicle AllyVehicleViewDict

---@class SpaceBuildingMgrCompProps

---@class StoneVehicleProps: WorldActorProps, ActorContainerCompProps, AvatarOwningCompProps, WorldBuildingCompProps, InGridCompProps, OutpostCompProps, FollowMoveCompProps
---@field stoneNum int

---@class SysBuildingCompProps
---@field instanceId int

---@class TacticPackCompProps
---@field tacticPack TacticPack
---@field tactic TacticConfig

---@class TaskComponentProps
---@field mainTasks MainTaskDict
---@field completeMainTasks CompleteMainTaskDict
---@field cycleTasks CycleTaskDict

---@class TentProps: WorldActorProps, ActorContainerCompProps, AvatarOwningCompProps, WorldBuildingCompProps, InGridCompProps, OutpostCompProps

---@class VehicleDriverCompProps
---@field throwingStone Position?
---@field inVehicle int?

---@class WorldProps: GlobalDataAckCompProps
---@field gridSize number
---@field boundary AABB

---@class WorldActorProps
---@field uid Uid  @Actor唯一id，可持久化，不同步给客户端
---@field gid AvatarGid  @归属的Avatar的gid
---@field allyId AllyId  @归属的同盟
---@field clanId ClanId  @归属的氏族
---@field name string
---@field pos DoubledPosition  @位置，存盘用于起服加载恢复位置
---@field uninstall int?  @标记对象被卸载，被标记的对象默认不加载
---@field statusEndTime number

---@class WorldAvatarProps: AvatarEntityProps, AvatarGMCompProps, ScheduleCompProps, CurrencyCompProps, ArmyMgrCompProps, ActorOwnerCompProps, ResProduceCompProps, ArmyFormationCompProps, AvatarArmyCombatCompProps, MainCityCompProps, ActorViewCompProps, LandMgrCompProps, AllyCompProps, ClanCompProps, ProsperityCompProps, BuildMgrCompProps, WorldBattleRecordCompProps
---@field urs string
---@field gid AvatarGid
---@field name string
---@field allyId AllyId
---@field birthCommandery Commandery
---@field birthplace Prefecture
---@field belongState Prefecture
---@field ambitionsScore int
---@field career int
---@field headpic int
---@field headFrame int
---@field rebornFlag boolean

---@class WorldAvatarMgrCompProps

---@class WorldBattleRecordCompProps

---@class WorldBuildingCompProps
---@field subId int
---@field slgTp int?
---@field staticMapElementId int?
---@field buildStatus int
---@field processTimeout number?
---@field operator EntityId?
---@field alias string?

---@class WorldChannelServiceProps

---@class WorldServiceProps: WorldAvatarMgrCompProps

---@class WorldSpaceProps: GridMgrCompProps, ChunkMgrCompProps, ActorLoadCompProps, SpaceBuildingMgrCompProps, AutoPathCompProps
---@field boundary AABB

---@class WorldViewportCompProps

---@class XiaoShanCompProps

