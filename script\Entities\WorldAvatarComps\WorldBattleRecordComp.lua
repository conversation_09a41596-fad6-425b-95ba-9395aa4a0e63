local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local DBUtils = xrequire(EZFPath .. ".Utils.DBUtils")
local BattleRecordOpEnv = xrequire("Modules.Battle.BattleRecordOp")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local BattleTeamUtils = xrequire("Common.Battle.BattleTeamUtils")

---@class (partial) WorldAvatar
---@class WorldBattleRecordComp
WorldBattleRecordComp = DefineClass("WorldBattleRecordComp", ComponentBaseEnv.ComponentBase)

---@diagnostic disable-next-line: duplicate-set-field
function WorldBattleRecordComp:ctor()
    self.outpostDuraAbstractCache = {}

    self.collAbstract = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_abstract")
    self.collStatistics = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_statistics")
    self.collDetails = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_details")

end

function WorldBattleRecordComp:cacheOutpostDuraAbstract(battleAbstracts)
    local abstract = battleAbstracts[1]
    if abstract.battleType == TableConst.enums.BattleType.PVE_OUTPOST_DURA then
        self:logInfo("cacheOutpostDuraAbstract. abstract: %v", abstract)
        self.outpostDuraAbstractCache[abstract.uid] = abstract
    end
end

---沙盘攻击产出战报
function WorldBattleRecordComp:GenDemolishBattleRecord(battleRecordPart)
    self:logDebug("GenDemolishBattleRecord. battleRecordPart: %v", battleRecordPart)
    local attTeam = battleRecordPart.armyBattleData  ---@type BattleTeam?
    if attTeam then
        local attBattleArmyData = self:getBattleArmyData(attTeam["armySlotIdx"])
        if attBattleArmyData then
            self:modifyBattleTeamData(attTeam, attTeam["armySlotIdx"])
        end
    end
    local heroAbstractDict = {}
    local totalHealth = 0
    for idx, unit in pairs(attTeam.battleArmyData.heroes) do
        if unit.attributes[TableConst.enums.BattleAttributeType.Health] > 0 then
            totalHealth = totalHealth + unit.attributes[TableConst.enums.BattleAttributeType.Health]
            heroAbstractDict[idx] = BattleTeamUtils.GenHeroAbstract(
                unit.heroId,
                unit.level,
                unit.stars,
                true,
                true
            )
        end
    end
    local teamAbstract = BattleTeamUtils.GenBattleTeamAbstract(
        self.props.gid,
        self.props.name,
        self.props.allyId,
        self.props.clanId,
        totalHealth,
        totalHealth,
        0,
        heroAbstractDict
    )
    local abstract = {
        uid = EZE.genUUID(),
        guid = { [TableConst.enums.Camp.A] = battleRecordPart.guid },
        battleType = TableConst.enums.BattleType.PVE_OUTPOST_DURA,
        position = battleRecordPart.position,
        gridName = battleRecordPart.gridName,
        timestamp = ServerTimeEnv.GetServerNow() * 1000,
        winCamp = TableConst.enums.Camp.A,
        energyCost = 0,
        durabilityInfo = battleRecordPart.durabilityInfo,
        teamAbstractDict = {teamAbstract}
    }
    if self.avatar then
        self.avatar:OnGetBattleRecord(abstract)
    end
    BattleRecordOpEnv.saveBattleAbstractRecord(self.collAbstract, abstract)
end

---沙盘更新战报属性，如耐久伤害
function WorldBattleRecordComp:UpdateBattleRecordFromWorld(recordUid, durabilityInfo)
    if not self.outpostDuraAbstractCache[recordUid] then
        self:logInfo("UpdateBattleRecordFromWorld Fail. Cannot Find Abstract. recordUid: %s, durabilityInfo: %s", recordUid, durabilityInfo)
        return
    end

    self:logDebug("UpdateBattleRecordFromWorld. recordUid: %s, durabilityInfo: %s, cachedAbstract: %v", recordUid, durabilityInfo, self.outpostDuraAbstractCache[recordUid])
    local abstract = self.outpostDuraAbstractCache[recordUid]
    abstract.durabilityInfo = durabilityInfo
    if self.avatar then
        self.avatar:OnGetBattleRecord(abstract)
    end
    BattleRecordOpEnv.saveBattleAbstractRecord(self.collAbstract, abstract)
end