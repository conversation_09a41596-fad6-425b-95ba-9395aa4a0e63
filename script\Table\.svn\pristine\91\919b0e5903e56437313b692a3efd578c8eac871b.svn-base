-- Auto generated by Luban XPipeline!

local enum_details =
{
    Actor = {
        [1] = {
            Name = "Source",
            <PERSON>as = "自身",
            Comment = "自身",
        },
        [2] = {
            Name = "Target",
            <PERSON><PERSON> = "目标",
            Comment = "目标",
        },
    },
    ActorType = {
        [1001] = {
            Name = "ARMY",
            <PERSON><PERSON> = "部队",
            Comment = "部队",
        },
        [1002] = {
            Name = "MAIN_CITY",
            <PERSON>as = "主堡",
            Comment = "主堡",
        },
        [1003] = {
            Name = "TENT",
            <PERSON><PERSON> = "营帐",
            Comment = "营帐",
        },
        [1004] = {
            Name = "CITY",
            <PERSON><PERSON> = "城池",
            Comment = "城池",
        },
        [1005] = {
            Name = "CHUNK",
            <PERSON><PERSON> = "地块",
            Comment = "地块",
        },
        [1006] = {
            Name = "JI_SHEN_JI_DIAN",
            <PERSON>as = "稷神祭典",
            Comment = "稷神祭典",
        },
    },
    Alignment = {
        [1] = {
            Name = "Left",
            <PERSON><PERSON> = "居左",
            Comment = "居左",
        },
        [2] = {
            Name = "Right",
            <PERSON><PERSON> = "居右",
            Comment = "居右",
        },
        [3] = {
            Name = "Center",
            <PERSON>as = "居中",
            Comment = "居中",
        },
    },
    ArmyStatus = {
        [101] = {
            Name = "Rest",
            Alias = "休息",
            Comment = "城内(主堡城池营帐)休息",
            Tags = {
                main = "main",
            },
        },
        [102] = {
            Name = "Stay",
            Alias = "驻扎",
            Comment = "野外停留",
            Tags = {
                main = "main",
            },
        },
        [103] = {
            Name = "March",
            Alias = "行军",
            Comment = "行军",
            Tags = {
                main = "main",
            },
        },
        [104] = {
            Name = "DefendGrid",
            Alias = "驻守格子",
            Comment = "驻守格子",
            Tags = {
                main = "main",
            },
        },
        [105] = {
            Name = "DefendBuilding",
            Alias = "驻守建筑",
            Comment = "驻守建筑",
            Tags = {
                main = "main",
            },
        },
        [106] = {
            Name = "Occupy",
            Alias = "占领中",
            Comment = "占领地块倒计时中",
            Tags = {
                main = "main",
            },
        },
        [107] = {
            Name = "Retreat",
            Alias = "撤退",
            Comment = "战败溃逃、主动回城",
            Tags = {
                main = "main",
            },
        },
        [108] = {
            Name = "Sing",
            Alias = "吟唱",
            Comment = "释放策牌吟唱",
            Tags = {
                main = "main",
            },
        },
        [109] = {
            Name = "WaitSing",
            Alias = "等待吟唱",
            Comment = "释放策牌吟唱被暂停",
            Tags = {
                main = "main",
            },
        },
        [201] = {
            Name = "IdleCombat",
            Alias = "非战斗",
            Comment = "非战斗",
            Tags = {
                combat = "combat",
            },
        },
        [202] = {
            Name = "WaitCombat",
            Alias = "等待战斗",
            Comment = "等待战斗",
            Tags = {
                combat = "combat",
            },
        },
        [203] = {
            Name = "Combat",
            Alias = "战斗",
            Comment = "战斗",
            Tags = {
                combat = "combat",
            },
        },
        [204] = {
            Name = "Injured",
            Alias = "重伤",
            Comment = "重伤",
            Tags = {
                combat = "combat",
            },
        },
        [301] = {
            Name = "IdleDeploy",
            Alias = "非调动",
            Comment = "非调动",
            Tags = {
                deploy = "deploy",
            },
        },
        [302] = {
            Name = "GoDeploy",
            Alias = "前往调动",
            Comment = "前往调动",
            Tags = {
                deploy = "deploy",
            },
        },
        [303] = {
            Name = "Deploy",
            Alias = "调动",
            Comment = "调动",
            Tags = {
                deploy = "deploy",
            },
        },
        [304] = {
            Name = "GoSiegeRally",
            Alias = "前往攻城集结",
            Comment = "前往攻城集结",
            Tags = {
                deploy = "deploy",
            },
        },
        [305] = {
            Name = "SiegeRally",
            Alias = "攻城集结",
            Comment = "攻城集结",
            Tags = {
                deploy = "deploy",
            },
        },
    },
    ArmyTypeQualificationTarget = {
        [0] = {
            Name = "Self",
            Alias = "自己",
            Comment = "自己",
        },
    },
    AudioGroup = {
        [0] = {
            Name = "Default",
            Alias = "默认",
            Comment = "默认",
        },
        [1] = {
            Name = "Male",
            Alias = "男",
            Comment = "男",
        },
        [2] = {
            Name = "Female",
            Alias = "女",
            Comment = "女",
        },
    },
    BattleCameraType = {
        [1] = {
            Name = "Full",
            Alias = "全景",
            Comment = "全景",
        },
        [2] = {
            Name = "Follow",
            Alias = "跟随",
            Comment = "跟随",
        },
        [3] = {
            Name = "Focus",
            Alias = "聚焦",
            Comment = "聚焦",
        },
        [4] = {
            Name = "Special",
            Alias = "特殊",
            Comment = "特殊",
        },
    },
    BattleCharacterAnimation = {
        [0] = {
            Name = "skill_01",
            Alias = "",
            Comment = "skill_01",
        },
        [1] = {
            Name = "skill_02",
            Alias = "",
            Comment = "skill_02",
        },
        [2] = {
            Name = "skill_11",
            Alias = "",
            Comment = "skill_11",
        },
        [3] = {
            Name = "skill_12",
            Alias = "",
            Comment = "skill_12",
        },
        [4] = {
            Name = "skill_13",
            Alias = "",
            Comment = "skill_13",
        },
        [5] = {
            Name = "skill_21",
            Alias = "",
            Comment = "skill_21",
        },
        [6] = {
            Name = "skill_22",
            Alias = "",
            Comment = "skill_22",
        },
        [7] = {
            Name = "skill_23",
            Alias = "",
            Comment = "skill_23",
        },
        [8] = {
            Name = "skill_31",
            Alias = "",
            Comment = "skill_31",
        },
        [9] = {
            Name = "skill_32",
            Alias = "",
            Comment = "skill_32",
        },
        [10] = {
            Name = "skill_41",
            Alias = "",
            Comment = "skill_41",
        },
        [11] = {
            Name = "charge",
            Alias = "",
            Comment = "charge",
        },
        [12] = {
            Name = "dodge",
            Alias = "",
            Comment = "dodge",
        },
        [13] = {
            Name = "guard",
            Alias = "",
            Comment = "guard",
        },
        [14] = {
            Name = "hit",
            Alias = "",
            Comment = "hit",
        },
        [15] = {
            Name = "float_up",
            Alias = "",
            Comment = "float_up",
        },
        [16] = {
            Name = "float_mid",
            Alias = "",
            Comment = "float_mid",
        },
        [17] = {
            Name = "float_down",
            Alias = "",
            Comment = "float_down",
        },
        [18] = {
            Name = "special_01",
            Alias = "",
            Comment = "special_01",
        },
        [19] = {
            Name = "special_02",
            Alias = "",
            Comment = "special_02",
        },
    },
    BattleDamageType = {
        [1] = {
            Name = "Attack",
            Alias = "兵刃伤害",
            Comment = "物理伤害",
        },
        [2] = {
            Name = "Intelligence",
            Alias = "谋略伤害",
            Comment = "法术伤害",
        },
        [3] = {
            Name = "Reality",
            Alias = "真实伤害",
            Comment = "真实伤害",
        },
        [4] = {
            Name = "Chain",
            Alias = "连锁伤害",
            Comment = "连锁伤害",
        },
        [5] = {
            Name = "Share",
            Alias = "分摊伤害",
            Comment = "分摊伤害",
        },
    },
    BattleEvent = {
        [0] = {
            Name = "None",
            Alias = "",
            Comment = "",
        },
        [1] = {
            Name = "FormationMorale",
            Alias = "布阵-士气",
            Comment = "布阵-士气",
        },
        [2] = {
            Name = "FormationArmyType",
            Alias = "布阵-兵种",
            Comment = "布阵-兵种",
        },
        [14] = {
            Name = "FormationTechnology",
            Alias = "布阵-科技",
            Comment = "布阵-科技",
        },
        [3] = {
            Name = "FormationEquipment",
            Alias = "布阵-装备",
            Comment = "布阵-装备",
        },
        [13] = {
            Name = "FormationTacticsArmyType",
            Alias = "布阵-战法-兵种",
            Comment = "布阵-战法-兵种",
        },
        [12] = {
            Name = "FormationTacticsFormation",
            Alias = "布阵-战法-阵法",
            Comment = "布阵-战法-阵法",
        },
        [4] = {
            Name = "FormationTacticsOther",
            Alias = "布阵-战法-其他",
            Comment = "布阵-战法-其他",
        },
        [5] = {
            Name = "RoundPrepare",
            Alias = "回合准备",
            Comment = "回合准备",
        },
        [6] = {
            Name = "HeroTurnStart",
            Alias = "英雄轮次开始",
            Comment = "英雄轮次开始",
        },
        [7] = {
            Name = "HeroTurnAction",
            Alias = "英雄轮次行动",
            Comment = "英雄轮次行动",
        },
        [8] = {
            Name = "HeroTurnEnd",
            Alias = "英雄轮次结束",
            Comment = "英雄轮次结束",
        },
        [9] = {
            Name = "RoundEnd",
            Alias = "回合结束",
            Comment = "回合结束",
        },
        [10] = {
            Name = "BattleResult",
            Alias = "战斗结算",
            Comment = "战斗结算",
        },
        [11] = {
            Name = "Initialize",
            Alias = "初始化",
            Comment = "初始化",
        },
        [100] = {
            Name = "BeforeDamage",
            Alias = "伤害前",
            Comment = "伤害前",
        },
        [101] = {
            Name = "AfterDamage",
            Alias = "伤害后",
            Comment = "伤害后",
        },
        [102] = {
            Name = "BeforeHeal",
            Alias = "治疗前",
            Comment = "治疗前",
        },
        [103] = {
            Name = "AfterHeal",
            Alias = "治疗后",
            Comment = "治疗后",
        },
        [105] = {
            Name = "BeforeUseTactic",
            Alias = "使用战法前",
            Comment = "使用战法前",
        },
        [106] = {
            Name = "AfterUseTactic",
            Alias = "使用战法后",
            Comment = "使用战法后",
        },
        [107] = {
            Name = "TacticProbFailed",
            Alias = "战法发动率判定失败",
            Comment = "战法发动率判定失败",
        },
        [108] = {
            Name = "BeforeBeDamaged",
            Alias = "受到伤害前",
            Comment = "受到伤害前",
        },
        [109] = {
            Name = "AfterBeDamaged",
            Alias = "受到伤害后",
            Comment = "受到伤害后",
        },
        [111] = {
            Name = "AfterPlainAttackProcess",
            Alias = "普攻流程后",
            Comment = "普攻流程后",
        },
        [112] = {
            Name = "AfterPlainAttack",
            Alias = "普攻后",
            Comment = "普攻后",
        },
        [113] = {
            Name = "BeforePlainAttack",
            Alias = "普攻前",
            Comment = "普攻前",
        },
        [114] = {
            Name = "BeforeDodge",
            Alias = "闪避前",
            Comment = "闪避前",
        },
        [115] = {
            Name = "AfterDodgeSuccess",
            Alias = "闪避成功后",
            Comment = "闪避成功后",
        },
        [116] = {
            Name = "AfterDodgeFail",
            Alias = "闪避失败后",
            Comment = "闪避失败后",
        },
        [117] = {
            Name = "BeforeBeHealed",
            Alias = "受到治疗前",
            Comment = "受到治疗前",
        },
        [118] = {
            Name = "AfterBeHealed",
            Alias = "受到治疗后",
            Comment = "受到治疗后",
        },
        [119] = {
            Name = "BeforeFatalDamage",
            Alias = "受致命伤前",
            Comment = "受致命伤前",
        },
    },
    BattleModeType = {
        [0] = {
            Name = "OneOnOne",
            Alias = "1v1",
            Comment = "1v1",
        },
        [1] = {
            Name = "Bo3",
            Alias = "",
            Comment = "",
        },
        [2] = {
            Name = "Kof3",
            Alias = "",
            Comment = "",
        },
    },
    BattlePVEType = {
        [10] = {
            Name = "LandDefender",
            Alias = "",
            Comment = "",
        },
    },
    BattleRecordScope = {
        [1] = {
            Name = "Individual",
            Alias = "个人",
            Comment = "个人",
        },
        [2] = {
            Name = "Ally",
            Alias = "同盟",
            Comment = "同盟",
        },
    },
    BattleTacticGrade = {
        [1] = {
            Name = "White",
            Alias = "白",
            Comment = "白",
        },
        [2] = {
            Name = "Blue",
            Alias = "蓝",
            Comment = "蓝",
        },
        [3] = {
            Name = "Purple",
            Alias = "紫",
            Comment = "紫",
        },
        [4] = {
            Name = "Gold",
            Alias = "橙",
            Comment = "橙",
        },
    },
    BattleType = {
        [1] = {
            Name = "PVE",
            Alias = "开拓",
            Comment = "开拓",
        },
        [2] = {
            Name = "PVP",
            Alias = "交战",
            Comment = "交战",
        },
        [3] = {
            Name = "All",
            Alias = "全部",
            Comment = "全部",
        },
        [99] = {
            Name = "Test",
            Alias = "测试",
            Comment = "测试",
        },
    },
    BehaviorType = {
        [1] = {
            Name = "March",
            Alias = "行军",
            Comment = "A前往行军",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [2] = {
            Name = "OccupyGrid",
            Alias = "攻占",
            Comment = "A前往占领地块",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [3] = {
            Name = "Halt",
            Alias = "中断",
            Comment = "中断任意行为",
            Tags = {
                whitelist = "whitelist",
            },
        },
        [4] = {
            Name = "Return",
            Alias = "回城",
            Comment = "回城",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [5] = {
            Name = "EnterSpace",
            Alias = "进入场景",
            Comment = "进入场景才可被看见",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [6] = {
            Name = "UseStrategy",
            Alias = "使用策牌",
            Comment = "使用策牌",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [7] = {
            Name = "ModifyArmy",
            Alias = "编队",
            Comment = "编队",
            Tags = {
                blacklist = "blacklist",
            },
        },
    },
    BuffGroupConflictRule = {
        [1] = {
            Name = "Invalid",
            Alias = "失效",
            Comment = "目标存在同类buff组的buff时，新BUFF无法添加",
        },
        [2] = {
            Name = "Cover",
            Alias = "覆盖",
            Comment = "目标存在同类buff组的buff时，新BUFF可以添加，且添加前清除该BUFF组其他BUFF",
        },
        [3] = {
            Name = "Round",
            Alias = "回合",
            Comment = "目标存在同类buff组的buff时,新BUFF如果剩余回合数更长,则执行【覆盖】,否则【失效】",
        },
        [4] = {
            Name = "Attribute",
            Alias = "属性",
            Comment = "目标存在同类buff组的buff时，如果来源【%属性】更高，则执行【覆盖】，否则【失效】",
        },
    },
    BuffGroupType = {
        [1] = {
            Name = "DamaageDot1",
            Alias = "伤害dot1",
            Comment = "伤害dot1",
        },
        [2] = {
            Name = "DamaageDot2",
            Alias = "伤害dot2",
            Comment = "伤害dot2",
        },
        [3] = {
            Name = "NegativeDot",
            Alias = "负面dot",
            Comment = "负面dot",
        },
        [4] = {
            Name = "Control",
            Alias = "控制效果",
            Comment = "控制效果",
        },
        [5] = {
            Name = "NegativeCanStacked",
            Alias = "减益可叠加",
            Comment = "减益可叠加",
        },
        [6] = {
            Name = "NegativeNotStacked",
            Alias = "减益不可叠加",
            Comment = "减益不可叠加",
        },
        [7] = {
            Name = "PositiveCanStacked",
            Alias = "增益可叠加",
            Comment = "增益可叠加",
        },
        [8] = {
            Name = "PositiveNotStacked",
            Alias = "增益不可叠加",
            Comment = "增益不可叠加",
        },
    },
    BuffOverlayRule = {
        [1] = {
            Name = "Replace",
            Alias = "顶替",
            Comment = "新buff顶替旧buff",
        },
        [2] = {
            Name = "Refresh",
            Alias = "刷新",
            Comment = "新buff无法添加；但是旧buff的持续时间刷新至最新",
        },
        [3] = {
            Name = "ReplaceMaxRemainRound",
            Alias = "顶替（剩余最大回合）",
            Comment = "根据剩余最大回合来顶替",
        },
    },
    BuffRemoveTiming = {
        [1] = {
            Name = "AfterHeroTurnEnd",
            Alias = "英雄自身轮次结束后",
            Comment = "英雄自身轮次结束后",
        },
        [2] = {
            Name = "AfterRoundEnd",
            Alias = "大回合结束后",
            Comment = "大回合结束后",
        },
    },
    BuffSourceType = {
        [1] = {
            Name = "HomeTech",
            Alias = "内城科技",
            Comment = "内城科技",
        },
        [2] = {
            Name = "SandMap",
            Alias = "沙盘地图",
            Comment = "沙盘地图",
        },
        [3] = {
            Name = "Equip",
            Alias = "装备",
            Comment = "装备",
        },
    },
    BuffStackRule = {
        [1] = {
            Name = "Mutex",
            Alias = "互斥",
            Comment = "每层独立计算时间",
        },
        [2] = {
            Name = "Refresh",
            Alias = "刷新",
            Comment = "刷新所有层时间",
        },
    },
    BuffType = {
        [0] = {
            Name = "Normal",
            Alias = "普通",
            Comment = "普通",
        },
        [1] = {
            Name = "Charge",
            Alias = "蓄力",
            Comment = "蓄力",
        },
    },
    BuildingType = {
        [1000001] = {
            Name = "TaiXue",
            Alias = "太学",
            Comment = "太学",
        },
        [1000002] = {
            Name = "ZhengBingSuo",
            Alias = "征兵所",
            Comment = "征兵所",
        },
        [1000003] = {
            Name = "JunYing",
            Alias = "军营",
            Comment = "军营",
        },
        [1000004] = {
            Name = "JunWangDian",
            Alias = "君王殿",
            Comment = "君王殿",
        },
    },
    Camp = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无阵营",
        },
        [1] = {
            Name = "A",
            Alias = "左侧方",
            Comment = "左边的阵营",
        },
        [2] = {
            Name = "B",
            Alias = "右侧方",
            Comment = "右边的阵营",
        },
    },
    CareerType = {
        [0] = {
            Name = "DEFAULT",
            Alias = "空",
            Comment = "空",
        },
        [101] = {
            Name = "FIELD_MASTER",
            Alias = "田师",
            Comment = "田师",
        },
    },
    CoinType = {
        [100001] = {
            Name = "Gold",
            Alias = "金币",
            Comment = "金币",
        },
        [100002] = {
            Name = "Copper",
            Alias = "铜币",
            Comment = "铜币",
        },
        [100003] = {
            Name = "Wood",
            Alias = "木头",
            Comment = "木头",
        },
        [100004] = {
            Name = "Iron",
            Alias = "铁块",
            Comment = "铁块",
        },
        [100005] = {
            Name = "Stone",
            Alias = "石头",
            Comment = "石头",
        },
        [100006] = {
            Name = "Food",
            Alias = "粮草",
            Comment = "粮草",
        },
        [100007] = {
            Name = "Soldier",
            Alias = "预备兵",
            Comment = "预备兵",
        },
        [100008] = {
            Name = "Jade",
            Alias = "魂玉",
            Comment = "魂玉",
        },
    },
    CrowdControlType = {
        [1] = {
            Name = "Silence",
            Alias = "沉默",
            Comment = "沉默",
        },
        [2] = {
            Name = "Disarm",
            Alias = "缴械",
            Comment = "缴械",
        },
        [3] = {
            Name = "Stun",
            Alias = "震慑",
            Comment = "震慑",
        },
        [4] = {
            Name = "Starve",
            Alias = "断粮",
            Comment = "断粮",
        },
        [5] = {
            Name = "AvoidDeath",
            Alias = "免死",
            Comment = "免死",
        },
        [6] = {
            Name = "AdditionalCaptain",
            Alias = "额外军职-主公",
            Comment = "额外军职-主公",
        },
        [7] = {
            Name = "AdditionalPioneer",
            Alias = "额外军职-先锋",
            Comment = "额外军职-先锋",
        },
        [8] = {
            Name = "AdditionalAdviser",
            Alias = "额外军职-军师",
            Comment = "额外军职-军师",
        },
        [9] = {
            Name = "Taunt",
            Alias = "嘲讽",
            Comment = "普攻锁定目标",
        },
        [10] = {
            Name = "Deterrence",
            Alias = "威慑",
            Comment = "主动、追击战法锁定目标",
        },
        [11] = {
            Name = "Confusion",
            Alias = "混乱",
            Comment = "混乱",
        },
    },
    DamageFormulaArgs = {
        [1] = {
            Name = "A",
            Alias = "进攻方兵力",
            Comment = "进攻方兵力",
        },
        [2] = {
            Name = "B",
            Alias = "兵力系数",
            Comment = "兵力系数",
        },
        [3] = {
            Name = "C",
            Alias = "属性差值",
            Comment = "属性差值",
        },
        [4] = {
            Name = "C1",
            Alias = "进攻方武力/智力值",
            Comment = "进攻方武力/智力值",
        },
        [5] = {
            Name = "C2",
            Alias = "防守方统帅值",
            Comment = "防守方统帅值",
        },
        [6] = {
            Name = "C3",
            Alias = "防守方智力值",
            Comment = "防守方智力值",
        },
        [7] = {
            Name = "C4",
            Alias = "进攻方破甲/看破百分比",
            Comment = "进攻方破甲/看破百分比",
        },
        [8] = {
            Name = "C5",
            Alias = "常量<C5>",
            Comment = "常量<C5>",
        },
        [9] = {
            Name = "D",
            Alias = "战法伤害系数",
            Comment = "战法伤害系数",
        },
        [10] = {
            Name = "E",
            Alias = "过程类增减伤",
            Comment = "过程类增减伤",
        },
        [11] = {
            Name = "E1",
            Alias = "伤害增加",
            Comment = "伤害增加",
        },
        [12] = {
            Name = "E3",
            Alias = "防守方受伤减少",
            Comment = "防守方受伤减少",
        },
        [13] = {
            Name = "E4",
            Alias = "防守方红度减伤",
            Comment = "防守方红度减伤",
        },
        [14] = {
            Name = "F",
            Alias = "结算类增减伤",
            Comment = "结算类增减伤",
        },
        [15] = {
            Name = "F1",
            Alias = "进攻方施加的受到伤害增加",
            Comment = "进攻方施加的受到伤害增加",
        },
        [16] = {
            Name = "F2",
            Alias = "防守方施加的造成伤害减少",
            Comment = "防守方施加的造成伤害减少",
        },
        [17] = {
            Name = "F3",
            Alias = "会心/奇谋伤害",
            Comment = "会心/奇谋伤害",
        },
        [18] = {
            Name = "G",
            Alias = "独立增减伤",
            Comment = "独立增减伤",
        },
        [19] = {
            Name = "G1",
            Alias = "红度增伤",
            Comment = "红度增伤",
        },
        [20] = {
            Name = "G2",
            Alias = "兵种克制",
            Comment = "兵种克制",
        },
        [21] = {
            Name = "G3",
            Alias = "进攻方独立兵刃/谋略伤害增减伤",
            Comment = "进攻方独立兵刃/谋略伤害增减伤",
        },
        [22] = {
            Name = "G4",
            Alias = "防守方受到兵刃/独立谋略伤害增减伤",
            Comment = "防守方受到兵刃/独立谋略伤害增减伤",
        },
        [23] = {
            Name = "W",
            Alias = "兵力保底伤害",
            Comment = "兵力保底伤害",
        },
        [24] = {
            Name = "M",
            Alias = "常量<M>",
            Comment = "常量<M>",
        },
    },
    DamageRange = {
        [0] = {
            Name = "Single",
            Alias = "单体伤害",
            Comment = "单体伤害",
        },
        [1] = {
            Name = "Aoe",
            Alias = "群体伤害",
            Comment = "群体伤害",
        },
        [2] = {
            Name = "Buff",
            Alias = "Buff伤害",
            Comment = "Buff伤害",
        },
    },
    DetailRecordType = {
        [1] = {
            Name = "SetHero",
            Alias = "",
            Comment = "",
        },
        [101] = {
            Name = "BattleStart",
            Alias = "",
            Comment = "",
        },
        [102] = {
            Name = "BattleStartMorale",
            Alias = "",
            Comment = "",
        },
        [103] = {
            Name = "BattleStartArmyType",
            Alias = "",
            Comment = "",
        },
        [104] = {
            Name = "BattleStartEquipment",
            Alias = "",
            Comment = "",
        },
        [105] = {
            Name = "BattleStartTactics",
            Alias = "",
            Comment = "",
        },
        [106] = {
            Name = "BattleStartTechnology",
            Alias = "",
            Comment = "",
        },
        [151] = {
            Name = "RoundStart",
            Alias = "",
            Comment = "",
        },
        [152] = {
            Name = "HeroTurnStart",
            Alias = "",
            Comment = "",
        },
        [153] = {
            Name = "RoundEnd",
            Alias = "",
            Comment = "",
        },
        [199] = {
            Name = "Result",
            Alias = "",
            Comment = "",
        },
        [201] = {
            Name = "Tactic",
            Alias = "",
            Comment = "",
        },
        [202] = {
            Name = "Damage",
            Alias = "",
            Comment = "",
        },
        [203] = {
            Name = "Heal",
            Alias = "",
            Comment = "",
        },
        [204] = {
            Name = "AddBuff",
            Alias = "",
            Comment = "",
        },
        [205] = {
            Name = "RemoveBuff",
            Alias = "",
            Comment = "",
        },
        [206] = {
            Name = "TriggerBuff",
            Alias = "",
            Comment = "",
        },
        [207] = {
            Name = "DecreaseBuff",
            Alias = "",
            Comment = "",
        },
        [208] = {
            Name = "AddCrowdControl",
            Alias = "",
            Comment = "",
        },
        [209] = {
            Name = "RemoveCrowdControl",
            Alias = "",
            Comment = "",
        },
        [210] = {
            Name = "ModifyAttribute",
            Alias = "",
            Comment = "",
        },
        [211] = {
            Name = "SetVariable",
            Alias = "",
            Comment = "",
        },
        [212] = {
            Name = "TacticFailed",
            Alias = "",
            Comment = "",
        },
        [213] = {
            Name = "TriggerBuffFailed",
            Alias = "",
            Comment = "",
        },
        [214] = {
            Name = "TacticPrepare",
            Alias = "",
            Comment = "",
        },
        [215] = {
            Name = "Combo",
            Alias = "",
            Comment = "",
        },
        [216] = {
            Name = "ComboFailed",
            Alias = "",
            Comment = "",
        },
        [217] = {
            Name = "Dodge",
            Alias = "",
            Comment = "",
        },
        [218] = {
            Name = "RefreshBuff",
            Alias = "",
            Comment = "",
        },
        [301] = {
            Name = "Silence",
            Alias = "",
            Comment = "",
        },
        [302] = {
            Name = "Disarm",
            Alias = "",
            Comment = "",
        },
        [303] = {
            Name = "Stun",
            Alias = "",
            Comment = "",
        },
        [304] = {
            Name = "Starve",
            Alias = "",
            Comment = "",
        },
        [999] = {
            Name = "PlainAttackTarget",
            Alias = "",
            Comment = "",
        },
    },
    ElementalType = {
        [0] = {
            Name = "Wind",
            Alias = "风",
            Comment = "风",
        },
        [1] = {
            Name = "Forest",
            Alias = "林",
            Comment = "林",
        },
        [2] = {
            Name = "Fire",
            Alias = "火",
            Comment = "火",
        },
        [3] = {
            Name = "Mountain",
            Alias = "山",
            Comment = "山",
        },
    },
    EquipEntriesType = {
        [0] = {
            Name = "None",
            Alias = "无词条",
            Comment = "无词条",
        },
        [1] = {
            Name = "Single",
            Alias = "单词条",
            Comment = "单词条",
        },
        [2] = {
            Name = "Double",
            Alias = "双词条",
            Comment = "双词条",
        },
        [3] = {
            Name = "ReverseDouble",
            Alias = "正负词条",
            Comment = "正负词条",
        },
    },
    EquipQuality = {
        [-1] = {
            Name = "None",
            Alias = "无",
            Comment = "无",
        },
        [1] = {
            Name = "Normal",
            Alias = "普通",
            Comment = "普通",
        },
        [2] = {
            Name = "Rare",
            Alias = "稀有",
            Comment = "稀有",
        },
        [3] = {
            Name = "Legend",
            Alias = "传说",
            Comment = "传说",
        },
        [4] = {
            Name = "Special",
            Alias = "专属",
            Comment = "专属",
        },
    },
    EquipType = {
        [1] = {
            Name = "Weapon",
            Alias = "武器",
            Comment = "武器",
        },
        [2] = {
            Name = "Defense",
            Alias = "防具",
            Comment = "防具",
        },
        [3] = {
            Name = "Horse",
            Alias = "坐骑",
            Comment = "坐骑",
        },
    },
    FlyStatusChange = {
        [1] = {
            Name = "ReachTop",
            Alias = "最高点",
            Comment = "最高点",
        },
        [2] = {
            Name = "LandGround",
            Alias = "落地",
            Comment = "落地",
        },
    },
    GeneralArmyType = {
        [1] = {
            Name = "Shield",
            Alias = "盾",
            Comment = "盾兵",
        },
        [2] = {
            Name = "Cavalry",
            Alias = "骑",
            Comment = "骑兵",
        },
        [3] = {
            Name = "Pikemen",
            Alias = "枪",
            Comment = "枪兵",
        },
        [4] = {
            Name = "Archers",
            Alias = "弓",
            Comment = "弓兵",
        },
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无高级兵种",
        },
        [41] = {
            Name = "TongQueTaiLing",
            Alias = "铜雀台伶",
            Comment = "铜雀台伶",
        },
        [42] = {
            Name = "YuanRongNuBing",
            Alias = "元戎弩兵",
            Comment = "元戎弩兵",
        },
    },
    GeneralAttrType = {
        [1] = {
            Name = "WuLi",
            Alias = "武力",
            Comment = "武力",
        },
        [2] = {
            Name = "ZhiLi",
            Alias = "智力",
            Comment = "智力",
        },
        [3] = {
            Name = "TongShuai",
            Alias = "统率",
            Comment = "统率",
        },
        [4] = {
            Name = "XianGong",
            Alias = "先攻",
            Comment = "先攻",
        },
    },
    GeneralDynasty = {
        [1] = {
            Name = "Wei",
            Alias = "魏国",
            Comment = "魏国",
        },
        [2] = {
            Name = "Shu",
            Alias = "蜀国",
            Comment = "蜀国",
        },
        [3] = {
            Name = "Wu",
            Alias = "吴国",
            Comment = "吴国",
        },
        [4] = {
            Name = "Qun",
            Alias = "群雄",
            Comment = "群雄",
        },
    },
    GeneralRoleType = {
        [1] = {
            Name = "BingRen",
            Alias = "兵刃",
            Comment = "兵刃",
        },
        [2] = {
            Name = "MouLue",
            Alias = "谋略",
            Comment = "谋略",
        },
        [3] = {
            Name = "WenWu",
            Alias = "文武",
            Comment = "文武",
        },
        [4] = {
            Name = "FuZhu",
            Alias = "辅助",
            Comment = "辅助",
        },
        [5] = {
            Name = "ZhiLiao",
            Alias = "治疗",
            Comment = "治疗",
        },
        [6] = {
            Name = "FangYu",
            Alias = "防御",
            Comment = "防御",
        },
    },
    HealFormulaArgs = {
        [1] = {
            Name = "A",
            Alias = "施法者智力值",
            Comment = "施法者智力值",
        },
        [2] = {
            Name = "B",
            Alias = "战法恢复系数",
            Comment = "战法恢复系数",
        },
        [3] = {
            Name = "C",
            Alias = "恢复效果提升",
            Comment = "恢复效果提升",
        },
        [4] = {
            Name = "D",
            Alias = "兵力系数",
            Comment = "兵力系数",
        },
        [5] = {
            Name = "D1",
            Alias = "常量<D1>",
            Comment = "常量<D1>",
        },
        [6] = {
            Name = "M",
            Alias = "常量<M>",
            Comment = "常量<M>",
        },
    },
    HudLayer = {
        [0] = {
            Name = "Geography",
            Alias = "地理信息",
            Comment = "地理信息",
        },
        [1] = {
            Name = "Building",
            Alias = "建筑",
            Comment = "建筑",
        },
        [2] = {
            Name = "Army",
            Alias = "军队",
            Comment = "军队",
        },
        [3] = {
            Name = "SelfArmy",
            Alias = "玩家军队",
            Comment = "玩家军队",
        },
        [4] = {
            Name = "Interactive",
            Alias = "交互界面",
            Comment = "交互界面",
        },
        [5] = {
            Name = "Always",
            Alias = "常显",
            Comment = "常显",
        },
    },
    IdSegType = {
        [1] = {
            Name = "Currency",
            Alias = "货币",
            Comment = "货币",
        },
        [2] = {
            Name = "Item",
            Alias = "道具",
            Comment = "道具",
        },
        [3] = {
            Name = "Equip",
            Alias = "装备",
            Comment = "装备",
        },
        [4] = {
            Name = "Horse",
            Alias = "坐骑",
            Comment = "坐骑",
        },
        [5] = {
            Name = "Token",
            Alias = "信物",
            Comment = "信物",
        },
        [6] = {
            Name = "Material",
            Alias = "材料",
            Comment = "材料",
        },
    },
    InConType = {
        [101] = {
            Name = "InBase",
            Alias = "在基地",
            Comment = "部队在回城点",
        },
        [102] = {
            Name = "InDefend",
            Alias = "在驻守",
            Comment = "驻守容器中",
        },
    },
    InteractiveBehavior = {
        [1] = {
            Name = "Attack",
            Alias = "攻击",
            Comment = "A攻击B",
        },
        [2] = {
            Name = "Defend",
            Alias = "驻守",
            Comment = "A驻守B",
        },
        [3] = {
            Name = "Occupy",
            Alias = "攻占",
            Comment = "A攻占B地块",
        },
        [4] = {
            Name = "Raid",
            Alias = "扫荡",
            Comment = "A扫荡B地块",
        },
        [5] = {
            Name = "Exploit",
            Alias = "开发",
            Comment = "A开发B地块",
        },
    },
    LandBuffType = {
        [101] = {
            Name = "AddProduct",
            Alias = "增加产量",
            Comment = "增加产量",
        },
    },
    ModifiablePosition = {
        [2] = {
            Name = "TeamPioneer",
            Alias = "我方先锋",
            Comment = "我方先锋",
        },
        [3] = {
            Name = "TeamAdviser",
            Alias = "我方军师",
            Comment = "我方军师",
        },
    },
    NodeTaskStatus = {
        [1] = {
            Name = "Default",
            Alias = "默认",
            Comment = "默认",
        },
        [2] = {
            Name = "Running",
            Alias = "运行时",
            Comment = "运行时",
        },
    },
    ObstacleType = {
        [0] = {
            Name = "Land",
            Alias = "空地",
            Comment = "空地",
        },
        [1] = {
            Name = "Obstacle",
            Alias = "阻挡",
            Comment = "阻挡",
        },
        [2] = {
            Name = "DynamicObstacle",
            Alias = "动态阻挡",
            Comment = "动态阻挡",
        },
    },
    OwnedStatus = {
        [1] = {
            Name = "Owned",
            Alias = "已拥有",
            Comment = "已拥有",
        },
        [2] = {
            Name = "NotOwned",
            Alias = "未拥有",
            Comment = "未拥有",
        },
    },
    Position = {
        [1] = {
            Name = "Captain",
            Alias = "主公",
            Comment = "1号位是主公",
        },
        [2] = {
            Name = "Pioneer",
            Alias = "先锋",
            Comment = "2号位是先锋",
        },
        [3] = {
            Name = "Adviser",
            Alias = "军师",
            Comment = "3号位是军师",
        },
    },
    ProduceSrcType = {
        [1] = {
            Name = "WorldLand",
            Alias = "大世界地块",
            Comment = "大世界地块",
        },
        [2] = {
            Name = "FarmLand",
            Alias = "家园开垦地",
            Comment = "家园开垦地",
        },
        [3] = {
            Name = "CityLand",
            Alias = "城池开垦地",
            Comment = "城池开垦地",
        },
        [4] = {
            Name = "Else",
            Alias = "其他",
            Comment = "其他",
        },
    },
    PurseType = {
        [0] = {
            Name = "Normal",
            Alias = "普通",
            Comment = "普通",
        },
        [1] = {
            Name = "Extra",
            Alias = "启动",
            Comment = "启动",
        },
    },
    QualificationRank = {
        [0] = {
            Name = "C",
            Alias = "",
            Comment = "",
        },
        [1] = {
            Name = "B",
            Alias = "",
            Comment = "",
        },
        [2] = {
            Name = "A",
            Alias = "",
            Comment = "",
        },
        [3] = {
            Name = "S",
            Alias = "",
            Comment = "",
        },
    },
    Quality = {
        [1] = {
            Name = "Blue",
            Alias = "蓝",
            Comment = "蓝",
        },
        [2] = {
            Name = "Violet",
            Alias = "紫",
            Comment = "紫",
        },
        [3] = {
            Name = "Gold",
            Alias = "金",
            Comment = "金",
        },
        [4] = {
            Name = "Red",
            Alias = "红",
            Comment = "红",
        },
    },
    Reason = {
        [0] = {
            Name = "GM",
            Alias = "管理员",
            Comment = "管理员",
            Tags = {
                overflow = "overflow",
            },
        },
        [101] = {
            Name = "MODIFY_ARMY",
            Alias = "调整部队",
            Comment = "调整部队",
        },
        [102] = {
            Name = "RESET_TACTIC",
            Alias = "重置战法",
            Comment = "重置战法",
        },
        [103] = {
            Name = "UPGRADE_TACTIC",
            Alias = "升级战法",
            Comment = "升级战法",
        },
        [104] = {
            Name = "UPGRADE_HERO_STAR",
            Alias = "武将升星",
            Comment = "武将升星",
        },
        [105] = {
            Name = "UPGRADE_TACTIC_STAR",
            Alias = "战法升星",
            Comment = "战法升星",
        },
        [201] = {
            Name = "DECOMPOSE_TOKEN",
            Alias = "分解信物",
            Comment = "分解信物",
            Tags = {
                notify = "notify",
            },
        },
        [202] = {
            Name = "DECOMPOSE_EQUIP",
            Alias = "分解装备",
            Comment = "分解装备",
        },
        [203] = {
            Name = "FORGE_EQUIP",
            Alias = "锻造装备",
            Comment = "锻造装备",
        },
        [204] = {
            Name = "REBUILD_EQUIP",
            Alias = "重塑装备",
            Comment = "重塑装备",
        },
        [205] = {
            Name = "BUILD_EQUIP",
            Alias = "打造装备",
            Comment = "打造装备",
        },
        [206] = {
            Name = "TRAIN_HORSE",
            Alias = "驯马",
            Comment = "驯马",
        },
        [207] = {
            Name = "SOLD_HORSE",
            Alias = "出售坐骑",
            Comment = "出售坐骑",
        },
        [301] = {
            Name = "JI_DIAN_REWARD",
            Alias = "稷神祭典奖励",
            Comment = "稷神祭典奖励",
        },
        [302] = {
            Name = "WO_ZHONG_SHENG_YOU",
            Alias = "无中生有",
            Comment = "无中生有",
        },
        [401] = {
            Name = "USE_ITEM",
            Alias = "使用道具",
            Comment = "使用道具",
            Tags = {
                notify = "notify",
            },
        },
        [501] = {
            Name = "TASK_REWARD",
            Alias = "任务奖励",
            Comment = "任务奖励",
            Tags = {
                overflow = "overflow",
                notify = "notify",
            },
        },
        [601] = {
            Name = "HOME_BUILDING",
            Alias = "内城建造",
            Comment = "内城建造",
        },
        [602] = {
            Name = "UPGRADE_HOME_TECH",
            Alias = "升级内城科技",
            Comment = "升级内城科技",
        },
        [603] = {
            Name = "PRODUCE",
            Alias = "产出",
            Comment = "产出",
        },
        [701] = {
            Name = "AMBITIONS_REWARD",
            Alias = "霸业阶段奖励",
            Comment = "霸业阶段奖励",
            Tags = {
                notify = "notify",
            },
        },
    },
    RelationShip = {
        [0] = {
            Name = "Self",
            Alias = "自己",
            Comment = "自己",
        },
        [1] = {
            Name = "SameAlly",
            Alias = "盟友",
            Comment = "盟友",
        },
        [2] = {
            Name = "FreeMan",
            Alias = "散人",
            Comment = "散人",
        },
        [3] = {
            Name = "DiffAlly",
            Alias = "不同联盟",
            Comment = "不同联盟",
        },
        [4] = {
            Name = "Monster",
            Alias = "野怪",
            Comment = "野怪",
        },
    },
    SAvatarEvent = {
        [0] = {
            Name = "DEFAULT",
            Alias = "空",
            Comment = "空",
        },
        [10001] = {
            Name = "PROSPERITY_CHANGE",
            Alias = "繁荣度变化",
            Comment = "繁荣度变化",
        },
        [10002] = {
            Name = "HOME_BUILDING_UPGRADE",
            Alias = "建筑升级建造",
            Comment = "建筑升级建造",
        },
        [10003] = {
            Name = "RES_PRODUCE_SPEED_CHANGE",
            Alias = "资源产量变化",
            Comment = "资源产量变化",
        },
        [10004] = {
            Name = "HOME_TECH_UPGRADE",
            Alias = "内城科技升级解锁",
            Comment = "内城科技升级解锁",
        },
        [10005] = {
            Name = "HERO_LEVE_UPGRADE",
            Alias = "武将升级",
            Comment = "武将升级",
        },
        [10006] = {
            Name = "ARMY_SOLDIER_CHANGE",
            Alias = "部队兵力变化",
            Comment = "部队兵力变化",
        },
        [10007] = {
            Name = "GET_NEW_TACTIC",
            Alias = "获得战法",
            Comment = "获得战法",
        },
        [10008] = {
            Name = "HOME_FARM_UPGRADE",
            Alias = "开垦地升级",
            Comment = "开垦地升级",
        },
        [10009] = {
            Name = "NEW_RES_LAND",
            Alias = "获得资源地",
            Comment = "获得资源地",
        },
        [10010] = {
            Name = "UNLOCK_TALENT",
            Alias = "解锁天赋",
            Comment = "解锁天赋",
        },
        [10011] = {
            Name = "ADD_COIN",
            Alias = "获得货币",
            Comment = "获得货币",
        },
        [10012] = {
            Name = "DEC_COIN",
            Alias = "减少货币",
            Comment = "减少货币",
        },
        [10013] = {
            Name = "TASK_COMPLETE",
            Alias = "任务完成",
            Comment = "任务完成",
        },
    },
    ScheduleType = {
        [1] = {
            Name = "CommonDaily",
            Alias = "日常刷天",
            Comment = "日常刷天",
        },
        [2] = {
            Name = "AmbitionsDaily",
            Alias = "霸业刷天",
            Comment = "霸业刷天",
        },
    },
    AdministrativeHierarchy = {
        [1] = {
            Name = "County",
            Alias = "县",
            Comment = "县",
        },
        [2] = {
            Name = "Commandery",
            Alias = "郡",
            Comment = "郡",
        },
        [3] = {
            Name = "Perfecture",
            Alias = "州",
            Comment = "州",
        },
    },
    MapElementType = {
        [0] = {
            Name = "Empty",
            Alias = "空地",
            Comment = "空地",
        },
        [1] = {
            Name = "Mountain",
            Alias = "山",
            Comment = "山",
        },
        [2] = {
            Name = "River",
            Alias = "河",
            Comment = "河",
        },
        [4] = {
            Name = "Food",
            Alias = "粮食",
            Comment = "粮食",
        },
        [8] = {
            Name = "Stone",
            Alias = "石料",
            Comment = "石料",
        },
        [16] = {
            Name = "Wood",
            Alias = "木材",
            Comment = "木材",
        },
        [32] = {
            Name = "Iron",
            Alias = "铁矿",
            Comment = "铁矿",
        },
        [64] = {
            Name = "FortifiedCity",
            Alias = "城池",
            Comment = "城池",
        },
        [128] = {
            Name = "CityGate",
            Alias = "城门",
            Comment = "城门",
        },
        [256] = {
            Name = "CityWall",
            Alias = "城墙",
            Comment = "城墙",
        },
        [512] = {
            Name = "MainCity",
            Alias = "主城",
            Comment = "主城",
        },
        [1024] = {
            Name = "DevelopableResourceNode",
            Alias = "开垦地",
            Comment = "开垦地",
        },
    },
    StrategyCardType = {
        [0] = {
            Name = "NONE",
            Alias = "空",
            Comment = "空",
        },
        [2000101] = {
            Name = "SHA",
            Alias = "杀",
            Comment = "杀",
        },
        [2000102] = {
            Name = "SHAN",
            Alias = "闪",
            Comment = "闪",
        },
        [2000103] = {
            Name = "WU_ZHONG_SHENG_YOU",
            Alias = "无中生有",
            Comment = "无中生有",
        },
        [2000201] = {
            Name = "WU_GU_FENG_DENG",
            Alias = "五谷丰登",
            Comment = "五谷丰登",
        },
        [2000202] = {
            Name = "KAI_KEN",
            Alias = "开垦",
            Comment = "开垦",
        },
        [2000203] = {
            Name = "JI_SHEN_JI_DIAN",
            Alias = "稷神祭典",
            Comment = "稷神祭典",
        },
    },
    TacticAttributeType = {
        [0] = {
            Name = "Default",
            Alias = "",
            Comment = "",
        },
        [1] = {
            Name = "LeftCD",
            Alias = "剩余CD",
            Comment = "剩余CD",
        },
        [2] = {
            Name = "TotalCD",
            Alias = "总CD",
            Comment = "总CD",
        },
        [3] = {
            Name = "GlobalFactor",
            Alias = "伤害系数",
            Comment = "伤害系数",
        },
        [4] = {
            Name = "IsEnabled",
            Alias = "是否可用",
            Comment = "是否可用",
        },
        [5] = {
            Name = "UseFailedProbability",
            Alias = "使用失败概率",
            Comment = "使用失败概率",
        },
    },
    TacticFeature = {
        [1] = {
            Name = "Assistance",
            Alias = "辅助",
            Comment = "辅助",
        },
        [2] = {
            Name = "CivilAndMilitary",
            Alias = "文武",
            Comment = "文武",
        },
        [3] = {
            Name = "Pursue",
            Alias = "治疗",
            Comment = "治疗",
        },
        [4] = {
            Name = "Defend",
            Alias = "防御",
            Comment = "防御",
        },
        [5] = {
            Name = "Strategy",
            Alias = "谋略",
            Comment = "谋略",
        },
        [6] = {
            Name = "Weapons",
            Alias = "兵刃",
            Comment = "兵刃",
        },
    },
    TacticType = {
        [1] = {
            Name = "Active",
            Alias = "主动",
            Comment = "主动",
        },
        [2] = {
            Name = "Passive",
            Alias = "被动",
            Comment = "被动",
        },
        [3] = {
            Name = "Pursue",
            Alias = "追击",
            Comment = "追击",
        },
        [4] = {
            Name = "Command",
            Alias = "指挥",
            Comment = "指挥",
        },
        [5] = {
            Name = "PlainAttack",
            Alias = "普攻",
            Comment = "普攻",
        },
        [6] = {
            Name = "ArmyType",
            Alias = "兵种",
            Comment = "兵种",
        },
        [7] = {
            Name = "Formation",
            Alias = "阵法",
            Comment = "阵法",
        },
    },
    TriggerTiming = {
        [1] = {
            Name = "RoundStart",
            Alias = "回合开始",
            Comment = "回合开始",
        },
        [2] = {
            Name = "ConsumeCard",
            Alias = "消耗卡牌",
            Comment = "消耗卡牌",
        },
    },
    WorldBuffType = {
        [101] = {
            Name = "AddMoveSpeed",
            Alias = "增加移速",
            Comment = "增加移速",
        },
        [102] = {
            Name = "AddDefendRange",
            Alias = "增加驻守范围",
            Comment = "增加驻守范围",
        },
    },
    BattleAttributeType = {
        [1] = {
            Name = "Attack",
            Alias = "",
            Comment = "武力",
        },
        [2] = {
            Name = "Intelligence",
            Alias = "",
            Comment = "智力",
        },
        [3] = {
            Name = "RecoverableHealth",
            Alias = "",
            Comment = "伤兵",
        },
        [4] = {
            Name = "Defense",
            Alias = "",
            Comment = "防御",
        },
        [5] = {
            Name = "Speed",
            Alias = "",
            Comment = "速度",
        },
        [6] = {
            Name = "AttackCritRate",
            Alias = "",
            Comment = "会心几率",
        },
        [7] = {
            Name = "AttackCritDamage",
            Alias = "",
            Comment = "会心伤害率",
        },
        [8] = {
            Name = "AttackDamageAdjustment",
            Alias = "",
            Comment = "造成兵刃伤害",
        },
        [9] = {
            Name = "AttackDamageFinalAdjustment",
            Alias = "",
            Comment = "造成兵刃伤害结果",
        },
        [10] = {
            Name = "HurtAttackAdjustment",
            Alias = "",
            Comment = "受到兵刃伤害",
        },
        [11] = {
            Name = "HurtAttackFinalAdjustment",
            Alias = "",
            Comment = "受到兵刃伤害结果",
        },
        [12] = {
            Name = "IntelligenceCritRate",
            Alias = "",
            Comment = "奇谋几率",
        },
        [13] = {
            Name = "IntelligenceCritDamage",
            Alias = "",
            Comment = "奇谋伤害率",
        },
        [14] = {
            Name = "IntelligenceDamageAdjustment",
            Alias = "",
            Comment = "造成谋略伤害",
        },
        [15] = {
            Name = "IntelligenceDamageFinalAdjustment",
            Alias = "",
            Comment = "造成谋略伤害结果",
        },
        [16] = {
            Name = "HurtIntelligenceAdjustment",
            Alias = "",
            Comment = "受到谋略伤害",
        },
        [17] = {
            Name = "HurtIntelligenceFinalAdjustment",
            Alias = "",
            Comment = "受到谋略伤害结果",
        },
        [18] = {
            Name = "HealAdjustment",
            Alias = "",
            Comment = "造成治疗",
        },
        [19] = {
            Name = "BeHealedAdjustment",
            Alias = "",
            Comment = "受到治疗",
        },
        [20] = {
            Name = "Health",
            Alias = "",
            Comment = "兵力",
        },
        [21] = {
            Name = "ComboRate",
            Alias = "",
            Comment = "连击率",
        },
        [22] = {
            Name = "AttackIgnoreDefense",
            Alias = "",
            Comment = "破甲",
        },
        [23] = {
            Name = "IntelligenceIgnoreDefense",
            Alias = "",
            Comment = "看破",
        },
        [24] = {
            Name = "FinalDamageCoefficient",
            Alias = "",
            Comment = "格挡",
        },
        [25] = {
            Name = "ArmyTypeQualification",
            Alias = "",
            Comment = "兵种适应性",
        },
        [26] = {
            Name = "ActiveTacticProbability",
            Alias = "",
            Comment = "主动战法发动率",
        },
        [27] = {
            Name = "PursueTacticProbability",
            Alias = "",
            Comment = "追击战法发动率",
        },
        [28] = {
            Name = "SelfActiveTacticProbability",
            Alias = "",
            Comment = "自带主动战法发动率",
        },
        [29] = {
            Name = "SelfPursueTacticProbability",
            Alias = "",
            Comment = "自带追击战法发动率",
        },
        [30] = {
            Name = "DodgeRate",
            Alias = "",
            Comment = "闪避率",
        },
        [31] = {
            Name = "ExtraAttackDamageAdjustment",
            Alias = "",
            Comment = "最终造成兵刃伤害",
        },
        [32] = {
            Name = "HurtExtraAttackDamageAdjustment",
            Alias = "",
            Comment = "最终受到兵刃伤害",
        },
        [33] = {
            Name = "ExtraIntelligenceDamageAdjustment",
            Alias = "",
            Comment = "最终造成谋略伤害",
        },
        [34] = {
            Name = "HurtExtraIntelligenceDamageAdjustment",
            Alias = "",
            Comment = "最终受到谋略伤害",
        },
    },
}
function GetEnumDetail(enumName)
    return enum_details[enumName]
end

function GetEnumItemDetail(enumName, enumValue)
    return enum_details[enumName][enumValue]
end

function GetEnumItemName(enumName, enumValue)
    local enumItemDetail = GetEnumItemDetail(enumName, enumValue)
    return enumItemDetail.Name
end

function GetEnumItemAlias(enumName, enumValue)
    local enumItemDetail = GetEnumItemDetail(enumName, enumValue)
    return enumItemDetail.Alias
end

function GetEnumItemComment(enumName, enumValue)
    local enumItemDetail = GetEnumItemDetail(enumName, enumValue)
    return enumItemDetail.Comment
end
