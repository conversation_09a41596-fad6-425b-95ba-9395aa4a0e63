local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local EntityUtilsEnv = xrequire("Common.Utils.EntityUtils")

--[[行为判断工具集合
1、判断A是否能对B做某件事。
2、判断A以当前的状态能否开始做某件事。
]]

---判断primary是否能对secondary做interactiveBehavior
function ValidBehaviorTo(primary, secondary, primaryType, secondaryType, interactiveBehavior)
    local relation = EntityUtilsEnv.GetRelationShip(primary, secondary)
    return ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior] 
        and ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior][primaryType]
        and ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior][primaryType][secondaryType]
        and ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior][primaryType][secondaryType][relation]
end

function ValidBehaviorToByRelation(relation, primaryType, secondaryType, interactiveBehavior)
    return ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior] 
        and ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior][primaryType]
        and ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior][primaryType][secondaryType]
        and ProcessedTableEnv.INTERACTIVE_BEHAVIOR_JUDGE[interactiveBehavior][primaryType][secondaryType][relation]
end

---判断处于status时，能否开始做behaviorType
function ValidStartBehavior(status, behaviorType)
    if ProcessedTableEnv.BEHAVIOR_JUDGE.blacklist[behaviorType] then  -- 通过黑名单机制判断，有一个条件不满足则不能开始
        for _, st in ipairs(ProcessedTableEnv.INDEX_2_STATUS) do
            if ProcessedTableEnv.BEHAVIOR_JUDGE.blacklist[behaviorType][status[st.propsName]] then
                return false
            end
        end
        return true
    elseif ProcessedTableEnv.BEHAVIOR_JUDGE.whitelist[behaviorType] then
        for _, st in ipairs(ProcessedTableEnv.INDEX_2_STATUS) do
            if ProcessedTableEnv.BEHAVIOR_JUDGE.whitelist[behaviorType][status[st.propsName]] then
                return true
            end
        end
        return false
    end
    return false
end

---获取负责状态对应规则配置
function GetStatusRuleConfig(status)
    local key = ProcessedTableEnv.GetKeyByStatus(status)
    local configId = ProcessedTableEnv.STATUS_RULE_CONFIG[key].rule
    return GameCommon.TableDataManager:GetRow("common_tbbehaviorconfig", configId)
end

function ValidActorContainer(primary, secondary, primaryType, secondaryType, inConType)
    local relation = EntityUtilsEnv.GetRelationShip(primary, secondary)
    local configId = ProcessedTableEnv.ACTOR_CONTAINER_JUDGE[inConType] and
        ProcessedTableEnv.ACTOR_CONTAINER_JUDGE[inConType][primaryType] and
        ProcessedTableEnv.ACTOR_CONTAINER_JUDGE[inConType][primaryType][secondaryType] and
        ProcessedTableEnv.ACTOR_CONTAINER_JUDGE[inConType][primaryType][secondaryType][relation]
    return not not configId, configId
end

function ClientValidBehaviourType(status, behaviourType)
    local key = ProcessedTableEnv.GetKeyByStatus(status)
    local configId = ProcessedTableEnv.STATUS_RULE_CONFIG[key].rule
    return ProcessedTableEnv.CLIENT_VALID_BEHAVIOUR_TYPES[configId][behaviourType] or false
end

return {
    ValidBehaviorTo = ValidBehaviorTo,
    ValidBehaviorToByRelation = ValidBehaviorToByRelation,
    ValidStartBehavior = ValidStartBehavior,
    GetStatusRuleConfig = GetStatusRuleConfig,
    ValidActorContainer = ValidActorContainer,
    ClientValidBehaviourType = ClientValidBehaviourType,
}