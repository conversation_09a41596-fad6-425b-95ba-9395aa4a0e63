{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "6"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}}}, "2": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [{"Type": "string", "BlackboardValue": "SourceId"}], "buffId": {"Type": "number", "Value": "11100503"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}}, "Links": {"0": {"HeroTurnStartNode": ["2.prev"]}}, "DataFlows": {}}