[
    // EZEngine
    {"name": "DBMgr", "server_only": true},
    {"name": "<PERSON>ti<PERSON>"},
    {"name": "Service", "server_only": true},
    {"name": "Microservice", "server_only": true},

    // Service
    {"name": "ActorCallerService", "server_only": true},
    {"name": "BattleService", "server_only": true},
    {"name": "CmdService", "server_only": true},
    {"name": "NaviService", "server_only": true},
    {"name": "RoleService", "server_only": true},
    {"name": "WorldService", "server_only": true},
    {"name": "AllyService", "server_only": true},
    {"name": "ClanService", "server_only": true},
    {"name": "ClanInviteService", "server_only": true},
    {"name": "AmbitionsService"},
    {"name": "AvatarInfoService", "server_only": true},
    {"name": "MailService", "server_only": true},
    {"name": "LogicService", "server_only": true},
    {"name": "WorldChannelService"},
    {"name": "PayService", "server_only": true},
    {"name": "MonitorService", "server_only": true},

    // Organization
    {"name": "Ally"},
    {"name": "Clan"},

    // LogicProxy
    {"name": "LogicProxy"},

    // PrefectureSync
    {"name": "PrefectureSync"},

    // BoardEntities
    {"name": "SiegeBoard"},
    {"name": "SiegeDetailBoard"},

    // Player
    {"name": "Account"},
    {"name": "Avatar"},
    {"name": "WorldAvatar"},
    {"name": "NaviTest"},

    // World
    {"name": "World", "server_only": true},
    {"name": "WorldSpace", "server_only": true},
    {"name": "Chunk"},
    {"name": "WorldActor"},
    {"name": "BaseArmy"},
    {"name": "Army"},
    {"name": "NpcArmy"},
    {"name": "City"},
    {"name": "Pass"},
    {"name": "Ferry"},
    {"name": "MainCity"},
    {"name": "Tent"},
    {"name": "StoneVehicle"},
    {"name": "SiegeCamp"},
    {"name": "CityGate"},
    {"name": "InnerCityGate"},
    {"name": "CityBarracks"},
    {"name": "JiShenJiDianActor"},

    {"name": "TestGcEntity"},
    {"name": "LuaPropPerf"},
    
    // Balance Test
    {"name": "BalanceBattle", "server_only": true},
    {"name": "BalanceBattleService", "server_only": true},
    {"name": "BalanceRunnerService", "server_only": true}
]