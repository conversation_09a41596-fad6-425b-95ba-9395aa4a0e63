{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"consts": {"extraValue1": {"VariableRangeType": 1, "ResetOnExecute": false, "Type": "number", "Value": 0.0}}, "event1": {"Type": "number", "Value": "14"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "2": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "8"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "BlackboardValue": "extraValue1"}, "value": {"Type": "number", "Value": "0"}}}, "3": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "14"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "BlackboardValue": "extraValue1"}, "value": {"Type": "number", "Value": "0"}}}, "5": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "10"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0"}, "value": {"Type": "number", "Value": "0"}}}, "6": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "16"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0"}, "value": {"Type": "number", "Value": "0"}}}, "7": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "True"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "9": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "BlackboardValue": "extraValue1"}, "formula": {"Type": "string", "Value": ""}, "formulaItems": [[{"Type": "string", "Value": "-a"}, {"Type": "number", "Value": 0.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "0"}, "noLeftBoundary": {"Type": "boolean", "Value": "True"}, "noRightBoundary": {"Type": "boolean", "Value": "True"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}}, "Links": {"0": {"FormationTechnologyNode": ["7.prev"]}, "2": {"next": ["3.prev"]}, "3": {"next": ["5.prev"]}, "5": {"next": ["6.prev"]}, "7": {"next": ["9.prev"]}, "9": {"next": ["2.prev"]}}, "DataFlows": {"9": {"result": ["5.value<PERSON><PERSON><PERSON>", "6.<PERSON><PERSON><PERSON><PERSON>"]}}}