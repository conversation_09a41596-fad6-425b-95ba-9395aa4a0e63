{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "103"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}, "event2": {"Type": "number", "Value": "115"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "False"}}}, "3": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "0"}, "key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "5": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "6": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": "a+1"}, "list": {"Items": []}, "vlist": {"Items": []}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "7": {"Type": "SetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "input": {"Type": "nil", "Value": "null"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "8": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "9": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "4"}, "number2": {"Type": "number", "Value": "2"}}}, "10": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}, "conditions": {}}}, "12": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "buffId": {"Type": "number", "Value": "10009"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "14": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "0"}, "key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "False"}, "showName": {"Type": "string", "Value": ""}, "percent": {"Type": "boolean", "Value": "False"}}}, "16": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "other1"}, "desc": {"Type": "string", "Value": "陷阵营施加抵御概率"}, "result": {"Type": "number", "Value": "0"}}}, "17": {"Type": "RandomNode", "Field": {"randomType": {"Type": "number", "Value": "1"}, "useFormula": {"Type": "boolean", "Value": "False"}, "probability": {"Type": "number", "Value": "0"}, "probabilityFormula": {"Type": "string", "Value": ""}, "record": {"Type": "boolean", "Value": "False"}, "integer": {"Type": "boolean", "Value": "False"}, "min": {"Type": "number", "Value": "0"}, "max": {"Type": "number", "Value": "0"}, "minFormula": {"Type": "string", "Value": ""}, "maxFormula": {"Type": "string", "Value": ""}, "numberResult": {"Type": "number", "Value": "0"}, "byWeight": {"Type": "boolean", "Value": "False"}, "idList": {"Items": []}, "weights": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "result": {"Items": []}}}}, "Links": {"0": {"onLayerIncreased": ["3.prev"], "AfterHealNode": ["5.prev"], "AfterDodgeSuccessNode": ["5.prev"]}, "5": {"next": ["6.prev"]}, "6": {"next": ["7.prev"]}, "7": {"next": ["8.prev"]}, "8": {"next": ["9.prev"]}, "9": {"next": ["14.prev"]}, "10": {"next": ["12.prev"]}, "14": {"next": ["16.prev"]}, "16": {"next": ["17.prev"]}, "17": {"next": ["10.prev"]}}, "DataFlows": {"5": {"output": ["6.a"]}, "6": {"result": ["7.input"]}, "8": {"output": ["9.number1"]}, "10": {"targetIds": ["12.targetIds"]}, "16": {"result": ["17.probability"]}}}