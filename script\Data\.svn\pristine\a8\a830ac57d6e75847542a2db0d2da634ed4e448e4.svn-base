{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "100"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}, "event2": {"Type": "number", "Value": "119"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "True"}}}, "2": {"Type": "ModifyDamageNode", "Field": {"damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "immune": {"Type": "boolean", "Value": "False"}, "modifyTarget": {"Type": "boolean", "Value": "False"}, "newTargetId": {"Type": "string", "Value": ""}, "modifyCritAttr": {"Type": "boolean", "Value": "False"}, "alwaysCrit": {"Type": "boolean", "Value": "False"}, "neverCrit": {"Type": "boolean", "Value": "False"}, "damageFactor": {"Type": "number", "Value": "1"}, "modifyComboAttr": {"Type": "boolean", "Value": "False"}, "isComboDisabled": {"Type": "boolean", "Value": "False"}}}, "3": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "TargetId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "4": {"Type": "OverwriteBattleFormulaNode", "Field": {"formulaType": {"Type": "number", "Value": "0"}, "damageArgs": [{"Type": "string", "Value": "G:2:-0.3"}], "healArgs": [], "output": {"Type": "nil", "Value": "null"}}}, "5": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "damageRange": {"Type": "number", "BlackboardValue": "DamageRange"}, "damageType": {"Type": "number", "Value": "5"}, "sourceId": {"Type": "string", "BlackboardValue": "EventSourceId"}, "tacticId": {"Type": "string", "BlackboardValue": "InputTacticId"}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "True"}, "isFixedDamage": {"Type": "boolean", "Value": "True"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "BlackboardValue": "IsInevitableDamage"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "7": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "TargetId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "8": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "BlackboardValue": "DamageValue"}, "formula": {"Type": "string", "Value": "a*3/7"}, "formulaItems": [[{"Type": "string", "Value": "a*3/7"}, {"Type": "number", "Value": 0.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "-100"}, "noLeftBoundary": {"Type": "boolean", "Value": "True"}, "noRightBoundary": {"Type": "boolean", "Value": "True"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "9": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "EventSourceId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "10": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "False"}, "tacticId": {"Type": "number", "Value": "0"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [], "checkDamage": {"Type": "boolean", "Value": "True"}, "damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "checkIsCrit": {"Type": "boolean", "Value": "True"}, "isCrit": {"Type": "boolean", "Value": "True"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "1"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}, "11": {"Type": "ModifyDamageNode", "Field": {"damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "immune": {"Type": "boolean", "Value": "False"}, "modifyTarget": {"Type": "boolean", "Value": "False"}, "newTargetId": {"Type": "string", "Value": ""}, "modifyCritAttr": {"Type": "boolean", "Value": "False"}, "alwaysCrit": {"Type": "boolean", "Value": "False"}, "neverCrit": {"Type": "boolean", "Value": "False"}, "damageFactor": {"Type": "number", "Value": "1"}, "modifyComboAttr": {"Type": "boolean", "Value": "False"}, "isComboDisabled": {"Type": "boolean", "Value": "False"}}}, "12": {"Type": "OverwriteBattleFormulaNode", "Field": {"formulaType": {"Type": "number", "Value": "0"}, "damageArgs": [{"Type": "string", "Value": "E:1:0.3"}], "healArgs": [], "output": {"Type": "nil", "Value": "null"}}}, "14": {"Type": "DecreaseBuffLayerNode", "Field": {"targetId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "buffId": {"Type": "number", "Value": "11400301"}, "count": {"Type": "number", "Value": "1"}}}, "16": {"Type": "ModifyDamageNode", "Field": {"damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "immune": {"Type": "boolean", "Value": "True"}, "modifyTarget": {"Type": "boolean", "Value": "False"}, "newTargetId": {"Type": "string", "Value": ""}, "modifyCritAttr": {"Type": "boolean", "Value": "False"}, "alwaysCrit": {"Type": "boolean", "Value": "False"}, "neverCrit": {"Type": "boolean", "Value": "False"}, "damageFactor": {"Type": "number", "Value": "1"}, "modifyComboAttr": {"Type": "boolean", "Value": "False"}, "isComboDisabled": {"Type": "boolean", "Value": "False"}}}, "18": {"Type": "DecreaseBuffLayerNode", "Field": {"targetId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "buffId": {"Type": "number", "Value": "11400301"}, "count": {"Type": "number", "Value": "1"}}}, "19": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "2"}, "a": {"Type": "number", "Value": "5"}, "b": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": "a*b"}, "formulaItems": [[{"Type": "string", "Value": "a*3/7"}, {"Type": "number", "Value": 0.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "-100"}, "noLeftBoundary": {"Type": "boolean", "Value": "True"}, "noRightBoundary": {"Type": "boolean", "Value": "True"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "20": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "周泰承伤减伤 a=统率"}, "result": {"Type": "number", "Value": "0"}}}, "21": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "4"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}}, "Links": {"0": {"BeforeDamageNode": ["9.prev", "7.prev", "3.prev"], "BeforeFatalDamageNode": ["16.prev"]}, "3": {"next": ["4.prev"]}, "4": {"next": ["2.prev"]}, "7": {"next": ["8.prev"]}, "8": {"next": ["21.prev"]}, "9": {"next": ["10.prev"]}, "10": {"next": ["12.prev"]}, "11": {"next": ["14.prev"]}, "12": {"next": ["11.prev"]}, "16": {"next": ["18.prev"]}, "19": {"next": ["5.prev"]}, "20": {"next": ["19.prev"]}, "21": {"next": ["20.prev"]}}, "DataFlows": {"4": {"output": ["2.overwrite<PERSON><PERSON>s"]}, "8": {"result": ["19.a"]}, "12": {"output": ["11.overw<PERSON><PERSON><PERSON>s"]}, "19": {"result": ["5.fixedDamageValue"]}, "20": {"result": ["19.b"]}, "21": {"attributeValues": ["20.a"]}}}