﻿local NodeBaseServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.NodeBaseServer")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

---@class RemoveBuffNodeServer : NodeBaseServer
local RemoveBuffNodeServer = DefineClass("RemoveBuffNodeServer", NodeBaseServerEnv.NodeBaseServer)

function RemoveBuffNodeServer:ctor()
    self:MarkExecute()
end

function RemoveBuffNodeServer:Execute(graphData)
    local recordData = {}
    local targetIds = graphData:GetProperty(self, "targetIds")
    local random = graphData:GetProperty(self, "random")
    local byId = graphData:GetProperty(self, "byId")
    local byType = graphData:GetProperty(self, "byType")
    local byBuffGroupType = graphData:GetProperty(self, "byBuffGroupType")
    local count = graphData:GetProperty(self, "count")
    local isDebuff = graphData:GetProperty(self, "isDebuff")
    local buffId = graphData:GetProperty(self, "buffId")
    local typeList = graphData:GetProperty(self, "buffType")
    local buffGroupTypeList = graphData:GetProperty(self, "buffGroupType")
    for _, targetId in ipairs(targetIds) do
        local target = graphData.caster.battleGame.heroUniqueIdMap[targetId]
        recordData[targetId] = {}
        if random then
            for i = 1, count do
                local buffs = {}
                for _, buff in ipairs(target.buffs) do
                    if buff.buffData.dispellable and buff.buffData.is_debuff == isDebuff then
                        table.insert(buffs, buff)
                    end
                end
                local buff = math.randomchoice(buffs)
                if buff then
                    target:RemoveBuff(buff)
                    table.insert(recordData[targetId], buff.buffId)
                end
            end
        end
        
        if byId then
            local buff = target:GetBuff(buffId)
            if buff then
                target:RemoveBuff(buff)
                table.insert(recordData[targetId], buffId)
            end
        end

        if byType then
            for _, buff in ipairs(target.buffs) do
                for _, type in pairs(typeList) do
                    local buffData = GameCommon.TableDataManager:GetBuffData(buff.buffId)
                    if buffData.type == type then
                        target:RemoveBuff(buff)
                    end
                end
            end
        end

        if byBuffGroupType then
            for _, buff in ipairs(target.buffs) do
                for _, groupType in pairs(buff.buffData.groupTypeList) do
                    for _, type in pairs(buffGroupTypeList) do
                        if groupType == type then
                            target:RemoveBuff(buff)
                            goto nextBuff
                        end
                    end
                end
                ::nextBuff::
            end
        end
    end
end
