local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local GlobalDataMgrEnv = xrequire("Framework.Utils.GlobalDataMgr")

---@class AmbitionsComp: ComponentBase, Avatar
AmbitionsComp = DefineClass("AmbitionsComp", ComponentBaseEnv.ComponentBase)

--region 霸业奖励

function AmbitionsComp:GetAmbitionsReward(processId)
    if self.props.ambitionsRewards[processId] then
        self:logDebug("GetAmbitionsReward: processId %s already rewarded", processId)
        return
    end
    local validReward = GlobalDataMgrEnv.instance:get("ambitions").finishedTargets
    if not validReward[processId] then
        self:logDebug("GetAmbitionsReward: processId %s not valid", processId)
        return
    end
    local config = GameCommon.TableDataManager:GetAmbitionsData(processId)
    if not config or not config.rewards then
        self:logError("GetAmbitionsReward: processId %s not found in ambitions data", processId)
        return
    end
    self:logInfo("GetAmbitionsReward: processId %s rewarded", processId)
    self.props.ambitionsRewards[processId] = true
    self:AddReward(config.rewards, TableConst.enums.Reason.AMBITIONS_REWARD)
end

--endregion
