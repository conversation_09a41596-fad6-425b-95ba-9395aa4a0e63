local CityGateUtilsEnv = xrequire("Common.Actor.Components.CityGateUtils")

--- 城门有一些通用的行为，是内外城门共享的，可以放到这里统一处理

---@class CityGateComp: CommonCityGate
---@field props CityGateCompProps
CityGateComp = DefineClass("CityGateComp", CityGateUtilsEnv.CommonCityGate)

---@diagnostic disable: duplicate-set-field
function CityGateComp:ctor()
end

---@param inWarWith AllyId?
function CityGateComp:SetInWar(inWarWith)
    self.props.inWarWith = inWarWith
    if inWarWith then
        self:SetTimedRecoverDurability(false)
    end
    if not inWarWith then
        -- 变为不可交战状态，等待的部队离开交战等待
        self:DelayCheckAllBattle()
    end
    -- 宣战结束，城门进入战争状态后，停止兵营支援
    self:stopBarracksSupport()
end

---@return SiegeCamp|nil
function CityGateComp:getCamp()
    local city = self:getCity()
    if not city or not self.props.siegeGroupIdx then
        return nil
    end
    return city.siegeGroups[self.props.siegeGroupIdx][TableConst.enums.ActorType.SIEGE_CAMP]
end

function CityGateComp:settleWaitAllAttackerLeave()
    --[[最后的部队离开后，判断占领失败
    情况一：最后的部队拆除了耐久，走完了攻占流程，占领成功，所有部队离开攻占状态
    情况二：最后的部队没有拆除耐久，无法开始下一次拆耐久流程，被从attacker踢下去，在这里触发占领失败
    --]]
    local settleCallback = function()
        self:logInfo("All attackers left outpost, check settle")
        local siegeCamp = self:getCamp()
        if not siegeCamp then
            return
        end
        if siegeCamp.props.allyId == self.props.allyId then
            return
        end
        siegeCamp:OnTimeoutOccupyFail()
    end
    if table.isempty(self.props.attackers) then
        settleCallback()
    else
        self.settleWaitCb = settleCallback
    end
end

function CityGateComp:OnAttackerLeaveOutpost(actor)
    -- 处理攻击者离开据点的逻辑
    if not self.settleWaitCb or not table.isempty(self.props.attackers) then
        return
    end
    local cb = self.settleWaitCb
    self.settleWaitCb = nil
    cb()
end

function CityGateComp:OnAllyChange(oriAllyId)
    local siegeCamp = self:getCamp()
    if not siegeCamp then
        self:logInfo("CityGate allyChange no siegeCamp")
        return
    end
    if not self.lastHitFrom then
        return
    end
    -- local attackers = self:ForceAllAttackerLeave()
    local city = self:getCity()  ---@type City
    if self._actorType == TableConst.enums.ActorType.CITY_GATE then
        -- 内城门被拆了，外城门也要被拆
        local innerGate = siegeCamp:getInnerGate()
        if innerGate then
            siegeCamp:setSiegeAssaultStart(innerGate.id, true)
            innerGate:CheckAllSysDefenderDie()
            return
        end
    end
    city:OnOccupiedBy(siegeCamp.props.allyId, siegeCamp, self)
    siegeCamp:OnGateDuraZero()
end

function CityGateComp:OnSysDefenderDie(leftNum)
    self:CheckAllSysDefenderDie()
end

function CityGateComp:CheckAllSysDefenderDie()
    -- 外城门等待所有系统守军死亡后触发耐久部队的出发
    if self.props.sysDefenderNum > 0 then
        return
    end
    local siegeCamp = self:getCamp()
    if not siegeCamp then
        return
    end
    siegeCamp:OnCityGateSysDefenderZero(self)
end

function CityGateComp:onCityOccupyEnd()
    self:setNewSysDefender()
end

function CityGateComp:OnCityOccupyFailed()
    self:onCityOccupyEnd()
    self:SetTimedRecoverDurability(true, true)
end

function CityGateComp:OnCityOccupySucceed()
    self:onCityOccupyEnd()
    self:FullDurability("CityOccupied")
end
