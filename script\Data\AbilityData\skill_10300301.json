{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "3"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "4": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": "0.45+lv*0.05"}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": ""}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "5": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "3"}, "number2": {"Type": "number", "Value": "0"}}}, "6": {"Type": "GetAttributeNode", "Field": {"heroIds": [], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "7": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "3"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "True"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "9": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "10": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": "0.18+lv*0.02"}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": ""}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "12": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "3"}, "number2": {"Type": "number", "Value": "0"}}}, "13": {"Type": "GetAttributeNode", "Field": {"heroIds": [], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "14": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "3"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "True"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "16": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "17": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": "0.18+lv*0.02"}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": ""}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "19": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "3"}, "number2": {"Type": "number", "Value": "0"}}}, "20": {"Type": "GetAttributeNode", "Field": {"heroIds": [], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "21": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "3"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "True"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "23": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "24": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": "0.18+lv*0.02"}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": ""}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "25": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam2"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "破阵额外伤害率"}, "result": {"Type": "number", "Value": "0"}}}, "26": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "破阵基础伤害率"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"next": ["1.prev"]}, "1": {"next": ["26.prev"]}, "4": {"next": ["7.prev"]}, "5": {"next": ["25.prev"]}, "6": {"next": ["9.prev"]}, "7": {"next": ["6.prev"]}, "9": {"next": ["5.prev"]}, "10": {"next": ["14.prev"]}, "12": {"next": ["17.prev"]}, "13": {"next": ["16.prev"]}, "14": {"next": ["13.prev"]}, "16": {"next": ["12.prev"]}, "17": {"next": ["21.prev"]}, "19": {"next": ["24.prev"]}, "20": {"next": ["23.prev"]}, "21": {"next": ["20.prev"]}, "23": {"next": ["19.prev"]}, "25": {"next": ["10.prev"]}, "26": {"next": ["4.prev"]}}, "DataFlows": {"1": {"targetIds": ["4.targetIds"]}, "6": {"attributeValues": ["5.number2"]}, "7": {"targetIds": ["6.heroIds", "10.targetIds"]}, "9": {"attributeValues": ["5.number1"]}, "13": {"attributeValues": ["12.number2"]}, "14": {"targetIds": ["13.heroIds", "17.targetIds"]}, "16": {"attributeValues": ["12.number1"]}, "20": {"attributeValues": ["19.number2"]}, "21": {"targetIds": ["20.heroIds", "24.targetIds"]}, "23": {"attributeValues": ["19.number1"]}, "25": {"result": ["10.damageFactor", "17.damageFactor", "24.damageFactor"]}, "26": {"result": ["4.damageFactor"]}}}