---@return number
local function defaultTimerFn()
    return os.clock()
    -- return EZE.getTimestampUS() / 1000000
end

---@class TimeIt.Timer : LuaClass
---@field fn fun() @需要执行的函数
---@field setupfn fun()? @执行前的setup函数
---@field teardownfn fun()? @执行后的teardown函数
---@field tmfn fun():number @获取时间的函数，返回单位为秒
---@field env table @执行环境，默认新建环境
---@field origEnv table[] @原始环境
Timer = {}
Timer.__index = Timer

---@param fn fun()
---@param setup fun()?
---@param teardown fun()? @执行后的teardown函数
---@param tmfn fun():number | nil
---@param env table?
function Timer.new(fn, setup, teardown, tmfn, env)
    local self = {}
    setmetatable(self, Timer)
    self.fn = fn
    self.setupfn = setup
    self.teardownfn = teardown
    self.tmfn = tmfn or defaultTimerFn
    if env == nil then
        env = {}
        setmetatable(env, {__index=_G})
    end
    self.env = env
    return self
end

function Timer:setup()
    local origSetupEnv, origFnEnv, origTeardownEnv
    if self.setupfn then
        origSetupEnv = getfenv(self.setupfn)
        setfenv(self.setupfn, self.env)
    end
    if self.fn then
        origFnEnv = getfenv(self.fn)
        setfenv(self.fn, self.env)
    end
    if self.teardownfn then
        origTeardownEnv = getfenv(self.teardownfn)
        setfenv(self.teardownfn, self.env)
    end
    self.origEnv = {origSetupEnv, origFnEnv, origTeardownEnv}

    if self.setupfn then
        self.setupfn()
    end
end

function Timer:teardown()
    if self.teardownfn then
        self.teardownfn()
    end
    local origSetupEnv, origFnEnv, origTeardownEnv = table.unpack(self.origEnv)
    if self.setupfn then
        setfenv(self.setupfn, origSetupEnv)
    end
    if self.fn then
        setfenv(self.fn, origFnEnv)
    end
    if self.teardownfn then
        setfenv(self.teardownfn, origTeardownEnv)
    end
end

-- 执行n次，返回总耗时
---@param n number 执行次数
---@return number @总耗时
function Timer:TimeIt(n)
    self:setup()
    local t0 = self.tmfn()
    for _ = 1, n do
        self.fn()
    end
    local t1 = self.tmfn()
    self:teardown()
    return t1 - t0
end

-- 执行rep次，每次执行n次，返回每次的耗时集合
---@param rep number @执行次数
---@param n number @每次执行次数
---@return number[] @每次的耗时集合
function Timer:Repeat(rep, n)
    local res = {}
    for _ = 1, rep do
        res[#res + 1] = self:TimeIt(n)
    end
    return res
end

-- 自动调整执行次数，使总耗时达到0.2s
---@return number, number @总耗时, @总执行次数
function Timer:AutoRange()
    self:setup()

    local THRESHOLD = 0.2
    local total = 0
    local count = 0
    local n = 1
    while total < THRESHOLD do
        local t0 = self.tmfn()
        for _ = 1, n do
            self.fn()
        end
        local t1 = self.tmfn()
        total = total + (t1 - t0)
        count = count + n
        if (t1 - t0) < (THRESHOLD / 64) then
            n = n * 2
        end
    end

    self:teardown()
    return total, count
end

-- 执行n次，返回总耗时
---@param fn fun() @需要执行的函数
---@param n number? @执行次数，为nil时使用AutoRange自动调整次数
---@param setup fun()? @执行前的setup函数
---@param teardown fun()? @执行后的teardown函数
---@param env table? @执行环境
---@param tmfn fun():number | nil @获取时间的函数，默认使用EZE.getTimestampUS() / 1000000
---@return number @总耗时,单位取决于tmfn
function TimeIt(fn, n, setup, teardown, env, tmfn)
    local tm = Timer.new(fn, setup, teardown, tmfn, env) ---@type TimeIt.Timer
    return tm:TimeIt(n or 1000000)
end

-- 执行rep次，每次执行n次，返回每次的耗时集合
---@param fn fun() @需要执行的函数
---@param rep number @执行次数
---@param n number? @每次执行次数，默认1000000
---@param setup fun()? @执行前的setup函数
---@param teardown fun()? @执行后的teardown函数
---@param env table? @执行环境
---@param tmfn fun():number | nil @获取时间的函数，默认使用EZE.getTimestampUS() / 1000000
---@return number[] @每次的耗时集合
function Repeat(fn, rep, n, setup, teardown, env, tmfn)
    local tm = Timer.new(fn, setup, teardown, tmfn, env) ---@type TimeIt.Timer
    return tm:Repeat(rep, n or 1000000)
end

-- for emmylua
return {Timer=Timer, TimeIt=TimeIt, Repeat=Repeat}
