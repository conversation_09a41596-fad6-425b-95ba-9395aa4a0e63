﻿local enum_details = xrequire("Table.Defines.enum_details")
local BattleAttributeType = TableConst.enums.BattleAttributeType

function battle_xpcall(f, msgh, arg1, ...)
    if _G.BATTLE_ENABLE_BLOCK_MODE then
        f(arg1, ...)
        return true
    else
        return xpcall(f, msgh, arg1, ...)
    end
end

function GetOpponentCamp(camp)
    if camp == TableConst.enums.Camp.A then
        return TableConst.enums.Camp.B
    elseif camp == TableConst.enums.Camp.B then
        return TableConst.enums.Camp.A
    else
        return TableConst.enums.Camp.None
    end
end

function GetGraphTacticId(node, graphData)
    local tacticId = graphData:GetProperty(node, "tacticId")
    if string.isnilorempty(tacticId) then
        tacticId = graphData.tacticId or graphData.battleBuff.sourceTacticId
    end
    return tacticId
end

function GetGraphGetSourceTacticIdAndHero(graphData)
    if graphData.battleBuff then
        return graphData.battleBuff.sourceTacticId, graphData.battleBuff.sourceTacticHero
    else
        return graphData.inputArgs.tactic:GetSourceTacticIdAndHero()
    end
end

function GetJsonFilePath(jsonFileName)
    return "AbilityData/" .. jsonFileName
end

function GetBattleHeroesFromFormation(formation, completeHeroes, camp)
    local battleHeroes = {}
    for i, elem in pairs(formation) do
        local hero = completeHeroes[elem.uid]
        if hero then
            battleHeroes[i] = {
                heroId = hero.templateHeroId,
                index = i,
                camp = camp,
                attack = hero.attack,
                intelligence = hero.intelligence,
                defense = hero.defense,
                speed = hero.speed,
                tactics = hero.tactics,

                armyType = TableConst.enums.GeneralArmyType.Archers,
                armyTypeQualification = TableConst.enums.QualificationRank.S,

                -- TODO
                heroAbilities = {}
            }
        end
    end

    validateBattleData(battleHeroes)

    -- TODO 临时
    return {
        camp = camp,
        heroes = battleHeroes,
    }
end

function GetBattleTeamFromNpcTeamTable(npcTeamId, camp)
    local npcTeamData = GameCommon.TableDataManager:GetBattleStageNpcTeamData(npcTeamId)
    local npcHeroes = {}
    for index, npcId in ipairs(npcTeamData.npc) do
        local npcData = GameCommon.TableDataManager:GetBattleStageNpcData(npcId)
        local heroData = {
            heroId = npcData.template_hero_id,
            attackCritRate = npcData.attack_crit_rate,
            attackCritDamage = npcData.attack_crit_damage_multiplier,
            intelligenceCritRate = npcData.intelligence_crit_rate,
            intelligenceCritDamage = npcData.intelligence_crit_damage_multiplier,
            armyType = npcData.arm_type,
            elementalType = npcTeamData.elemental_type,
            armyTypeQualification = TableConst.enums.QualificationRank.S,
        }
        -- attributes
        heroData.maxHealth = heroData.health
        -- tactics
        heroData.tactics = table.clone(npcData.tactics)
        -- abilities
        local templateHeroData = GameCommon.TableDataManager:GetTemplateHeroData(npcData.template_hero_id)
        heroData.heroAbilities = templateHeroData.hero_skills
        npcHeroes[index] = heroData
    end

    return {
        camp = camp,
        heroes = npcHeroes,
    }
end

-- 战斗数据校验函数
function validateBattleData(battleHeroes)
    --检验每个英雄数据
    for _, hero in pairs(battleHeroes) do
        validateHeroData(hero)
    end

    --额外规则
end

function validateHeroData(heroData, path)
    path = path or ""
    for key, expectedTypes in pairs(heroStructure) do
        local value = heroData[key]
        local currentPath = path .. (path == "" and "" or ".") .. key

        if type(expectedTypes) == "table" then
            for _, expectedType in pairs(expectedTypes) do
                if type(value) == expectedType then
                    goto continue
                end
            end
            local concatExpectedTypes = table.concat(expectedTypes, '或')
            assert(false, "英雄数据错误: " .. currentPath .. " 应该是 " .. concatExpectedTypes .. "类型, 但类型是 " .. type(value))
        else
            assert(type(value) == expectedTypes, "英雄数据错误: " .. currentPath .. " 应该是 " .. expectedTypes .. "类型, 但类型是 " .. type(value))
        end
        ::continue::
    end
    for key, value in pairs(heroData.attributes) do
        assert(type(value) == "number", "英雄属性错误: " .. enum_details.GetEnumItemName("BattleAttributeType", key) .. " 应该是 number 类型, 但类型是 " .. type(value))
    end
end

function ExecuteFormulaForTacticLevel(formula, level, star)
    level = level or 1
    local env = { lv = level - 1, star = star }-- 策划要求lv代表英雄提升了的等级

    local func = loadstring("return " .. formula)
    if not func then
        ErrorLog("公式填写有误: %s", formula)
    end
    setfenv(func, env)

    return func()
end

nilOrTableOrUserData = { "nil", "table", "userdata" }
nilOrNumber = { "nil", "number" }

heroStructure = {
    heroId = "number",
    selfTactics = nilOrTableOrUserData,
    carryingTactics = nilOrTableOrUserData,
}

function IsPlainAttack(tacticId)
    -- 目前普攻ID不会变，如果变了，再用下面的方式
    return tacticId == Config.Battle.PlainAttackTacticId
    --local tacticData = GameCommon.TableDataManager:GetTacticData(tacticId)
    --if not tacticData then
    --    return false
    --end
    --return tacticData.tactic_type == TableConst.enums.TacticType.PlainAttack
end

function Safe_access(t, ...)
    local keys = { ... }
    local current = t
    for _, key in ipairs(keys) do
        if type(current) ~= "table" or current[key] == nil then
            DebugLog("key为nil:", key)
            return nil
        end
        current = current[key]
    end
    return current
end


