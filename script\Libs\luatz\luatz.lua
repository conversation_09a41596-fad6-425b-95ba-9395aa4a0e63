local _M = {
	gettime = require "luatz.gettime";
	parse = require "luatz.parse";
	strftime = require "luatz.strftime";
	timetable = require "luatz.timetable";
	tzcache = require "luatz.tzcache";
}

--- Top-level aliases for common functions

_M.time = _M.gettime.gettime
_M.get_tz = _M.tzcache.get_tz

--- Handy functions

---@param timestamp int
---@param tz string
_M.timestamp_to_timestruct = function(timestamp, tz)
	timestamp = _M.get_tz(tz):localize(timestamp)
	return _M.timetable.new_from_timestamp(timestamp)
end

---@param tz string
_M.timestruct_to_timestamp = function(timestruct, tz)
	return _M.get_tz(tz):utctime(timestruct:timestamp())
end

---@param timetuple TimeTuple
_M.timetuple_to_timestruct = function(timetuple)
	return _M.timetable.new_from_timetuple(timetuple)
end

---@param timetuple TimeTuple
---@param tz string
---@return int
_M.timetuple_to_timestamp = function(timetuple, tz)
	return _M.timestruct_to_timestamp(_M.timetuple_to_timestruct(timetuple), tz)
end

--- C-like functions

_M.gmtime = function(ts)
	return _M.timetable.new_from_timestamp(ts)
end

_M.localtime = function(ts, tz)
	ts = _M.time_in(tz, ts)
	return _M.gmtime(ts)
end

_M.ctime = function(ts, tz)
	return _M.strftime.asctime(_M.localtime(ts, tz))
end

return _M
