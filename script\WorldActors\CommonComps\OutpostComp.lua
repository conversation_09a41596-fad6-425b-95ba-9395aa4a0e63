local OutpostUtilsEnv = xrequire("Common.Actor.Components.OutpostUtils")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local ChooseEnv = xrequire("Common.Choose")
local CoordUtils = xrequire("Common.Utils.CoordUtils")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local BattleTeamUtilsEnv = xrequire("Common.Battle.BattleTeamUtils")
local ActorUtils = xrequire("Utils.ActorUtils")
local EntityUtils = xrequire("Utils.EntityUtils")

DURA_RECOVER_INTERVAL = 60  -- 耐久恢复间隔

---@class OutpostComp: CommonOutpost
---@field props OutpostCompProps
OutpostComp = DefineClass("OutpostComp", OutpostUtilsEnv.CommonOutpost)

--[[据点组件
管理据点的守军：驻守部队（玩家）、驻城部队（系统）、城防守军（耐久）
管理据点的耐久：建造中、建造完成耐久变化；升级、buff耐久变化；扣除和恢复耐久
管理据点的进攻方：挂载在城墙上的部队
管理建筑上战斗：守军交战，耐久拆除
]]

---@diagnostic disable-next-line: duplicate-set-field
function OutpostComp:ctor()
    self:refreshOutpostConfig()
    self.barracksSupportTimer = nil  ---@type TimerId|nil
    -- 保证至少有一点耐久，避免有无法扣除耐久的建筑出现
    if self.props.durability <= 0 then
        self.props.durability = 1
    end
    self.recoverDuraTimer = nil   ---@type TimerId?
    self.sysDefenderBattleGuid = {}  ---@type table<int, string>
    self.duraDefenderBattleGuid = {
        ally = {},
        gid = {},
    }   ---@type table<string, table<int, string>>
    self.attackerCallback = {}
end

function OutpostComp:refreshOutpostConfig()
    --- @type slg.TableBuilding
    self.outpostConfig = ProcessedTableEnv.SLG_BUILDING.allBuilding[self._actorType][self.props.subId]
    assert(self.outpostConfig, string.format("No outpost config for type: %s %s", self._actorType, self.props.subId))
    self:setNewDurabilityMax(self.outpostConfig.durability)
    self:setNewSysDefender()
end

--region Event

function OutpostComp:OnProcessBuildingEnd()
    self:refreshOutpostConfig()
end

--endregion

--region 耐久

function OutpostComp:setNewDurabilityMax(durabilityMax)
    durabilityMax = math.max(1, durabilityMax)
    -- TODO(qun): 这里需要补充减少和增加的变化规则（对当前耐久的影响）
    self.props.durabilityMax = durabilityMax
    self.props.durability = durabilityMax
end

function OutpostComp:TakeRemoteDamage(damage, attacker)
    --TODO(qun): 处理远程器械最大消耗耐久限制
    return self:consumeDurability(damage, attacker)
end

---@return bool @是否成功扣除
---@return number @造成的耐久伤害
---@return number @剩余耐久
function OutpostComp:consumeDurability(damage, attacker)
    if self.props.durability <= 0 then
        return false, 0, self.props.durability
    end
    local oriDurability = self.props.durability
    local trueDamage = math.min(damage, self.props.durability)
    self.props.durability = math.max(0, self.props.durability - trueDamage)
    self:logInfo("Outpost durability changed, from %s to %s by %s", oriDurability, self.props.durability, attacker)
    self:fireEntityEvent("OnOutpostConsumeDurability", trueDamage, attacker)
    if self.props.durability <= 0 then
        self.lastHitFrom = attacker.props.gid
        self:fireEntityEvent("OnOutpostDurabilityZero", attacker)
        self.lastHitFrom = nil
    end
    return true, trueDamage, self.props.durability
end

function OutpostComp:addDurability(recover, reason)
    if (self.props.durability >= self.props.durabilityMax) or (recover <= 0) then
        return
    end
    local oriDurability = self.props.durability
    self.props.durability = math.min(self.props.durabilityMax, self.props.durability + recover)
    self:logInfo("Outpost durability changed, from %s to %s by %s", oriDurability, self.props.durability, reason)
end

function OutpostComp:FullDurability(reason)
    self:addDurability(self.props.durabilityMax, reason)
end

function OutpostComp:HandleDurabilityZeroFailed()
    self.props.durability = math.max(1, self.props.durability)
end

function OutpostComp:SetTimedRecoverDurability(enabled, fullStop)
    -- 刚开启定时恢复耐久，需要重置开始恢复时间
    if enabled and not self.props.duraRecover.enabled then
        self.props.duraRecover.last = ServerTimeEnv.GetServerNow()
    end
    self.props.duraRecover.enabled = enabled
    if enabled then
        self.props.duraRecover.fullStop = fullStop
    end
    self:checkTimedRecoverDurability()
end

function OutpostComp:checkTimedRecoverDurability()
    if not self.props.duraRecover.enabled then
        if self.recoverDuraTimer then
            self:delTimer(self.recoverDuraTimer)
            self.recoverDuraTimer = nil
        end
        return
    end
    if self.recoverDuraTimer then
        return
    end
    local delay = math.max(0, self.props.duraRecover.last - ServerTimeEnv.GetServerNow() + DURA_RECOVER_INTERVAL)
    self.recoverDuraTimer = self:addTimer(delay, DURA_RECOVER_INTERVAL, function()
        if not self.props.duraRecover.enabled then
            self:checkTimedRecoverDurability()
            return
        end
        if self.props.durability >= self.props.durabilityMax then
            if self.props.duraRecover.fullStop then
                self.props.duraRecover.enabled = false
                self:checkTimedRecoverDurability()
                return
            end
            return
        end
        local recoverCnt = math.max(0, math.floor((ServerTimeEnv.GetServerNow() - self.props.duraRecover.last) / DURA_RECOVER_INTERVAL))
        if recoverCnt <= 0 then
            return
        end
        self:addDurability(self.outpostConfig.dura_recover * recoverCnt, "TimedRecover")
    end)
end

--endregion

--region 驻守

function OutpostComp:addDefender(defender)
    -- 检查当前据点可以被驻守
    if not self.outpostConfig.is_defender_host then
        return false
    end
    -- 校验部队坐标在建筑可驻守范围内
    if not self:checkDefenderInRange(defender) then
        return false
    end
    -- 检验两者为可驻守关系
    if not BehaviorJudgeEnv.ValidBehaviorTo(defender.props, self.props, defender._actorType, self._actorType, TableConst.enums.InteractiveBehavior.Defend) then
        return false
    end
    -- 进入容器驻守状态，进入驻守等待交战队列
    return self:dealActorEnter(defender, TableConst.enums.InConType.InDefend)
end

function OutpostComp:checkDefenderInRange(defender)
    local selfX, selfY = self:GetDoubledPos2D()
    local defenderX, defenderY = defender:GetDoubledPos2D()
    return selfX == defenderX and selfY == defenderY
end

function OutpostComp:onContentEnter(actor, inConType, inRecover)
    if inConType == TableConst.enums.InConType.InDefend then
        EntityUtils.InsertPropIfNotExist(self.props.defenders, actor.props.uid)
    end
end

function OutpostComp:onContentLeave(actor, inConType)
    if inConType == TableConst.enums.InConType.InDefend then
        EntityUtils.RemovePropIfExist(self.props.defenders, actor.props.uid)
        if #self.props.defenders <= 0 then
            self:ActiveAttackerInWait()
        end
    end
end

--endregion

--region 调动

---@param deployArgs DeployArgs
function OutpostComp:ReqDeployTo(actorUid, relation, actorType, deployArgs, rpcCb)
    local pos = self:GetDoubledPos2DV()
    local succ = self:doReqDeployTo(actorUid, relation, actorType, deployArgs)
    self:callbackRpc(rpcCb, succ, self.props.uid, pos, deployArgs)
end

---@param deployArgs DeployArgs
function OutpostComp:doReqDeployTo(actorUid, relation, actorType, deployArgs)
    if deployArgs.deployType == ActorUtils.DeployType.DEPLOY then
        if not self.outpostConfig.is_deploy_host then
            self:logInfo("Not a deploy host")
            return false
        end
        for _, v in ipairs(self.props.deployers) do
            if v == actorUid then
                return false
            end
        end
        if #self.props.deployers >= self.outpostConfig.max_deploy then
            self:logInfo("Deployers full")
            return false
        end
    elseif deployArgs.deployType == ActorUtils.DeployType.SIEGE then
        if not self.outpostConfig.is_siege_host then
            self:logInfo("Not a siege host")
            return false
        end
    end
    -- 通用判断
    if not BehaviorJudgeEnv.ValidActorContainer(relation, self.props, actorType, self._actorType, TableConst.enums.InConType.InBase) then
        self:logInfo("Invalid actor container")
        return false
    end
    -- 调动在建筑记录：前往调动集结人数
    if deployArgs.deployType == ActorUtils.DeployType.DEPLOY then
        self.props.deployers:insert(actorUid)
    end
    return true
end

function OutpostComp:LeaveDeployHome(uid, deployType)
    self:logInfo("Actor %s leave deploy home %s", uid, deployType)
    if deployType == ActorUtils.DeployType.DEPLOY then
        local index = table.index(self.props.deployers, uid)
        if index then
            self.props.deployers:remove(index)
        end
    end
end

--endregion

--region 系统守军:驻城&城防

-- 获取驻城守军
---@return boolean, string?, table?
function OutpostComp:getSysDefender(markInCombat)
    if table.isempty(self.props.sysDefender) then
        return false
    end
    local validList = {}
    for uid, info in pairs(self.props.sysDefender) do
        if not info.defenderInCombat then
            table.insert(validList, uid)
        end
    end
    if table.isempty(validList) then
        return true
    end
    local chooseUid = ChooseEnv.RandomChoose(validList)
    local dataId = self.props.sysDefender[chooseUid].id
    if not self.props.sysDefender[chooseUid].team then
        self.props.sysDefender[chooseUid].team = BattleTeamUtilsEnv.GetNpcBattleTeam(dataId, self.props)
    end
    if markInCombat then
        self.props.sysDefender[chooseUid].defenderInCombat = true
        self.props.sysDefenderInCombatCount = self.props.sysDefenderInCombatCount + 1
    end
    return true, chooseUid, self.props.sysDefender[chooseUid]
end

-- 获取城防守军
---@return int?, string?
function OutpostComp:getDuraDefender(actor)
    local battleGuid = self:GetDuraBattleGuid(actor)
    if table.isempty(self.outpostConfig.dura_defender) then
        return nil, battleGuid
    end
    return ChooseEnv.RandomChoose(self.outpostConfig.dura_defender), battleGuid
end

function OutpostComp:GetDuraBattleGuid(actor)
    if actor.props.allyId ~= 0 then
        if not self.duraDefenderBattleGuid.ally[actor.props.allyId] then
            self.duraDefenderBattleGuid.ally[actor.props.allyId] = EZE.genUUID()
        end
        return self.duraDefenderBattleGuid.ally[actor.props.allyId]
    end
    if not self.duraDefenderBattleGuid.gid[actor.props.gid] then
        self.duraDefenderBattleGuid.gid[actor.props.gid] = EZE.genUUID()
    end
    return self.duraDefenderBattleGuid.gid[actor.props.gid]
end

function OutpostComp:clearSysDefender()
    -- 重置兵营守军配置
    self:stopBarracksSupport()
    self.props.barracksDefenderType = {}
    -- 重置通用守军配置
    self.props.sysDefenderNum = 0
    self.props.sysDefender = {}
    self.props.sysDefenderInCombatCount = 0
end

function OutpostComp:stopBarracksSupport()
    if self.barracksSupportTimer then
        self:delTimer(self.barracksSupportTimer)
        self.barracksSupportTimer = nil
    end
end

function OutpostComp:setNewSysDefender()
    self:clearSysDefender()
    if not table.isempty(self.outpostConfig.sys_defender) and self.outpostConfig.sys_defender_num > 0 then
        -- 重置常规守军配置
        local sysDefender = {}
        for i = 1, self.outpostConfig.sys_defender_num do
            local dataId = ChooseEnv.RandomChoose(self.outpostConfig.sys_defender)
            sysDefender[EZE.genUUID()] = {
                id = dataId,
            }
        end
        self.props.sysDefender = sysDefender
        -- 用于同步给客户端显示数量
        self.props.sysDefenderNum = self.outpostConfig.sys_defender_num
   end
   self:ResetBattleGuid()
end

function OutpostComp:AddSysDefender(dataId)
    self.props.sysDefender[EZE.genUUID()] = {
        id = dataId,
    }
    self.props.sysDefenderNum = self.props.sysDefenderNum + 1
end

function OutpostComp:StartBarracksSupportDefender()
    -- 宣战开始，开始兵营补兵tick
    if table.isempty(self.outpostConfig.barracks_defender_list) or self.outpostConfig.barracks_defender <= 0 then
        self:logInfo("No barracks defender config")
        return
    end
    self:clearSysDefender()
    self:doBarracksSupportDefender(self.outpostConfig.barracks_defender)
end

function OutpostComp:doBarracksSupportDefender(leftTick)
    -- 判断城门的两个相邻军营是否存在，且军营和相邻的城门都未被占领，此时军营可以向城门派遣守军
    if not self.GetOutpostBarracks then
        return
    end
    local barracksList = self:GetOutpostBarracks()
    local armyTypeToBarrack = {}
    for _, barracks in ipairs(barracksList) do
        local type = barracks:getBarracksDefenderType()
        if self.outpostConfig.barracks_defender_list[type] and not table.isempty(self.outpostConfig.barracks_defender_list[type]) then
            local dataId = ChooseEnv.RandomChoose(self.outpostConfig.barracks_defender_list[type])
            self:AddSysDefender(dataId)
            self:allClientsRpc("BcNewBarracksDefender", barracks.id, barracks:getBarracksDefenderType())
            armyTypeToBarrack[barracks:getBarracksDefenderType()] = barracks.id
        end
    end
    if table.isempty(self.props.barracksDefenderType) then
        self.props.barracksDefenderType = armyTypeToBarrack
    end
    -- 等待下一次tick
    if self.barracksSupportTimer then
        self:delTimer(self.barracksSupportTimer)
        self.barracksSupportTimer = nil
    end
    leftTick = leftTick - 1
    if leftTick <= 0 then
        return
    end
    self.barracksSupportTimer = self:addTimer(GameCommon.TableDataManager:GetSiegeConst("BarrackTempArmyInterval1"), 0, function()
        self:doBarracksSupportDefender(leftTick)
    end)
end

function OutpostComp:OnSiegeEnd(allyId)
    if self.outpostConfig.barracks_defender > 0 then
        -- 攻城结束，只有兵营派遣的系统守军需要被清理
        self:clearSysDefender()
    end
    self:ResetBattleGuid(allyId)
end

function OutpostComp:ResetBattleGuid(allyId)
    --[[重置战报合并
    1、建筑销毁或被打飞（普通建筑） TODO(qun): 打飞接入，销毁不用管
    2、驻城守军刷新（普通建筑）
    3、攻城结束（城池及其附属建筑）
    不需要手动重置驻城守军的GUID，会跟随守军自动刷新
    ]]
    if not allyId then
        self.duraDefenderBattleGuid = {
            ally = {},
            gid = {},
        }
        return
    end
    self.duraDefenderBattleGuid.ally[allyId] = nil
    self.sysDefenderBattleGuid = {}
end

--endregion

--region 攻击者

function OutpostComp:AddAttacker(actor, entityCb)
    for _, uid in ipairs(self.props.attackers) do
        if uid == actor.props.uid then
            return false
        end
    end
    if not self:ValidBeAttack(actor) then
        return false
    end
    self.attackerCallback[actor.id] = entityCb
    self.props.attackers:insert(actor.props.uid)
    actor.props.inOutpost = self.props.uid
    -- 添加据点到格子匹配器
    local packedPos = CoordUtils.PackPos(actor:GetDoubledPos2D())
    self:CheckAddOutpostNode(packedPos)
    return true
end

function OutpostComp:AttackerLeave(actor)
    -- 仅清理关联的数据
    local index = table.index(self.props.attackers, actor.props.uid)
    if not index then
        return false
    end
    self.attackerCallback[actor.id] = nil
    self.props.attackers:remove(index)
    actor.props.inOutpost = nil
    self:fireEntityEvent("OnAttackerLeaveOutpost", actor)
    return true
end

function OutpostComp:OnOutpostDurabilityZero(_)
    local cbMap = self.attackerCallback
    self.attackerCallback = {}
    for actorId, cb in pairs(cbMap) do
        local actor = self:getActorById(actorId)   ---@type WorldActor?
        if actor then
            actor:entityCallback(cb, true)
        end
    end
end

function OutpostComp:validMatchAttack(attacker)
    -- 已经在墙上的部队能否继续攻击城墙
    if attacker.props.inOutpost ~= self.props.uid then
        return false
    end
    if not self:ValidBeAttack(attacker) then
        return false
    end
    return true
end

---@param attacker Army
---@return boolean, string?, table?, string?  @是否有可交战的系统守军，系统守军uid，系统守军Team数据，系统守军战报GUID
function OutpostComp:GetNextBattleSysDefender(attacker, markInCombat)
    local hasDefender, sysDefenderUid, sysDefender = self:getSysDefender(markInCombat)
    -- 所有驻城部队，单个玩家的单个部队的战报需要合并
    if hasDefender and sysDefenderUid then
        if not self.sysDefenderBattleGuid[attacker.id] then
            self.sysDefenderBattleGuid[attacker.id] = EZE.genUUID()
        end
        return true, sysDefenderUid, sysDefender.team, self.sysDefenderBattleGuid[attacker.id]
    end
    return hasDefender
end

---@param attacker Army
function OutpostComp:OnSysDefenderCombatEnd(sysDefenderUid, attacker, sysDefenderDead, leftHealth)
    local totalBeKilled = 0
    if self.props.sysDefender[sysDefenderUid] then
        self.props.sysDefender[sysDefenderUid].defenderInCombat = false
        self.props.sysDefenderInCombatCount = math.max(0, self.props.sysDefenderInCombatCount - 1)
        _, totalBeKilled = BattleTeamUtilsEnv.UpdateBattleTeamHealth(self.props.sysDefender[sysDefenderUid].team, leftHealth)
    end
    if not self:validMatchAttack(attacker) then
        return
    end
    if totalBeKilled > 0 then
        self:fireEntityEvent("OnOutpostSysDefenderBeKilled", totalBeKilled, attacker)
    end
    if not sysDefenderDead then
        return
    end
    self.props.sysDefenderNum = math.max(0, self.props.sysDefenderNum - 1)
    if self.props.sysDefender[sysDefenderUid] then
        self.props.sysDefender[sysDefenderUid] = nil
    end
    self:fireEntityEvent("OnSysDefenderDie", self.props.sysDefenderNum)
    attacker:DelayCheckBattle()
    if self.props.sysDefenderNum <= 0 then
        self:ActiveAttackerInWait()
        -- 清理兵营支援部队，通知客户端表现守军撤退
        self.props.barracksDefenderType = {}
    end
end

function OutpostComp:ForceAllAttackerLeave()
    -- 强制所有的攻击者离开
    local attackers = self.props.attackers:copy()
    for _, uid in ipairs(attackers) do
        --- @type Army
        local actor = self:getActor(uid)
        if actor then
            actor:ForceAttackerLeave()
        end
    end
    return attackers
end

function OutpostComp:ActiveAttackerInWait()
    for _, attackerUid in ipairs(self.props.attackers) do
        local attacker = self:getActor(attackerUid)  ---@type Army?
        -- self:logInfo("Try battle with SysDefender in impasse: actor=%s wait=%s combat=%s impasse=%s", attacker, attacker and attacker:IsWaitCombat(), attacker and attacker:IsInCombat(), attacker and attacker:IsInImpasse())
        if attacker and attacker:IsWaitCombat() and not attacker:IsInCombat() and not attacker:IsInImpasse() then
            attacker:DelayCheckBattle()
        end
    end
end

--endregion
