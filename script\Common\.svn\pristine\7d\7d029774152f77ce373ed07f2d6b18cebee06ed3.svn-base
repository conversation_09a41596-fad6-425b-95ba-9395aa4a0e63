﻿-- https://docs.xiaoshanim.com/i/nodes/7gNKMlbrq3WXAxEn2vOu4zv5p6Ad9nL1
-- 战报数据说明

local ServerTimeEnv = xrequire("Utils.ServerTime")
local SwitchesEnv = xrequire("Common.Switches")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")
local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local CoordUtils = xrequire("Common.Utils.CoordUtils")

local CampA = TableConst.enums.Camp.A
local CampB = TableConst.enums.Camp.B

local DetailRecordType = TableConst.enums.DetailRecordType
local Nil = "$"

---@class BattleRecorder
local BattleRecorder = DefineClass("BattleRecorder")

function BattleRecorder:ctor(battleGame)
    self.battleGame = battleGame
    self.battleRecord = {}
    self.nestedRecord = {}
    self._nestIdGenerator = 0

    self.tempTacticRecord = {}
    self.detailCurrentHeroId = nil
    self.detailPointer = 1

    -- 新的记录
    self.battleBrief = {} -- 用于大世界战斗表现
    self.battleAbstract = {} -- 战报列表摘要
    self.battleStatistic = {} -- 战报统计数据
    self.battleDetails = {} -- 战报详情数据及回放功能
end

function BattleRecorder:Reset()
    self.battleRecord = {}
    self.nestedRecord = {}
    self._nestIdGenerator = 0
    self.battleBrief = {}
    self.battleAbstract = {}
    self.battleStatistic = {}
    self.detailPointer = 1
    self.battleDetails = {}
end

function BattleRecorder:GetNestId()
    self._nestIdGenerator = self._nestIdGenerator + 1
    return self._nestIdGenerator
end

function BattleRecorder:GetRecord()
    self:modifyAbstractStatistic()
    self.battleStatistic.winCamp = self.battleGame.winCamp
    self.battleBrief.winCamp = self.battleGame.winCamp
    for i = #self.battleDetails, self.detailPointer, -1 do
        self.battleDetails[i] = nil
    end
    return self.battleBrief, self.battleAbstract, self.battleStatistic, self.battleDetails
end

function BattleRecorder:modifyAbstractStatistic()
    for camp, team in pairs(self.battleAbstract.teamAbstractDict) do
        team.maxHealth = 0
        team.health = 0
        team.recoverableHealth = 0
        for heroIdx, hero in pairs(team.heroAbstractDict) do
            team.maxHealth = team.maxHealth + hero.maxHealth
            team.health = team.health + hero.health
            team.recoverableHealth = team.recoverableHealth + hero.recoverableHealth
            local heroStatistic = self.battleStatistic.teamStatisticDict[camp].heroStatisticDict[heroIdx]
            heroStatistic.maxHealth = hero.maxHealth
            heroStatistic.health = hero.health
            heroStatistic.recoverableHealth = hero.recoverableHealth
            hero.startAlive = hero.maxHealth > 0
            hero.endAlive = hero.health > 0
            hero.maxHealth = nil
            hero.health = nil
            hero.recoverableHealth = nil
        end
    end
end

---------------------------------------------------------------------------------------

function BattleRecorder:InitRecorder()
    local battleGame = self.battleGame
    assert(battleGame.battleInfo.guid[TableConst.enums.Camp.A])
    self.battleAbstract = {
        guid = battleGame.battleInfo.guid,
        position = battleGame.battleInfo.position or 0,
        gridName = battleGame.battleInfo.gridName or TableConst.enums['slg.MapElementType'].Empty,
        battleType = battleGame.battleInfo.battleType,
        timestamp = ServerTimeEnv.GetServerNow() * 1000,
        energyCost = battleGame.battleInfo.energyCost or 0,
        teamAbstractDict = {
            [CampA] = {
                gid = battleGame.battleTeamA.gid,
                playerName = battleGame.battleTeamA.playerName,
                allyId = battleGame.battleTeamA.allyId,
                clanId = battleGame.battleTeamA.clanId,
                supply = battleGame.battleTeamA.supply,
                heroAbstractDict = {},
            },
            [CampB] = {
                gid = battleGame.battleTeamB.gid,
                playerName = battleGame.battleTeamB.playerName,
                allyId = battleGame.battleTeamB.allyId,
                clanId = battleGame.battleTeamB.clanId,
                supply = battleGame.battleTeamB.supply,
                heroAbstractDict = {},
            }
        }
    }
    self.battleBrief = {
        baseInfo = {
            [TableConst.enums.Camp.A] = {
                gid = battleGame.battleTeamA.gid,
                name = battleGame.battleTeamA.playerName,
                armyType = battleGame.battleTeamA.battleArmyData.armyType,
                heroIds = {},
                health = 0,
            },
            [TableConst.enums.Camp.B] = {
                gid = battleGame.battleTeamB.gid,
                name = battleGame.battleTeamB.playerName,
                armyType = battleGame.battleTeamB.battleArmyData.armyType,
                heroIds = {},
                health = 0,
            },
        },
        roundBrief = {},
        winCamp = TableConst.enums.Camp.None,
    }
    self.tempTacticRecord = {}
    self.battleStatistic.teamStatisticDict = {
        [TableConst.enums.Camp.A] = {merit = 0, totalKill = 0, heroStatisticDict = {}},
        [TableConst.enums.Camp.B] = {merit = 0, totalKill = 0, heroStatisticDict = {}},
    }
    for camp, campHeroes in pairs(self.battleGame.heroes) do
        self.tempTacticRecord[camp] = {}
        local teamStatistic = {}
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero then
                table.insert(self.battleBrief.baseInfo[camp].heroIds, hero.uniqueId)
                self.tempTacticRecord[camp][hero.uniqueId] = {}
                local tacticStatisticDict = {}
                for slot, tacticData in ipairs(hero.tacticsData) do
                    self.tempTacticRecord[camp][hero.uniqueId][tacticData.id] = {
                        useCount = 0,
                        damage = 0,
                        heal = 0,
                    }
                    tacticStatisticDict[slot] = {
                        tacticData = {
                            id = tacticData.id,
                            level = tacticData.level or 1,
                            stars = tacticData.stars or 0,
                        }
                    }
                end
                self.tempTacticRecord[camp][hero.uniqueId][hero.plainAttackId] = {
                    useCount = 0,
                    damage = 0,
                    heal = 0,
                }
                tacticStatisticDict[4] = { -- 4表示普攻
                    tacticData = {
                        id = hero.plainAttackId,
                        level = 1,
                        stars = 0,
                    }
                }
                local heroStatistic = {
                    armyType = hero.armyType,
                    armyTypeQualification = hero:GetAttribute(TableConst.enums.BattleAttributeType.ArmyTypeQualification),
                    identitySlots = hero.data.identitySlots,
                    equips = hero.data.equips,
                    horses = hero.data.horses,
                    tacticStatisticDict = tacticStatisticDict,
                    attributes = {
                        [TableConst.enums.BattleAttributeType.Attack] = hero.attributeHolder.attributes[TableConst.enums.BattleAttributeType.Attack],
                        [TableConst.enums.BattleAttributeType.Intelligence] = hero.attributeHolder.attributes[TableConst.enums.BattleAttributeType.Intelligence],
                        [TableConst.enums.BattleAttributeType.Defense] = hero.attributeHolder.attributes[TableConst.enums.BattleAttributeType.Defense],
                        [TableConst.enums.BattleAttributeType.Speed] = hero.attributeHolder.attributes[TableConst.enums.BattleAttributeType.Speed]
                    }
                }
                teamStatistic[hero.index] = heroStatistic

                local health = hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
                self.battleAbstract.teamAbstractDict[camp].heroAbstractDict[hero.index] = {
                    id = hero.heroId,
                    level = hero.level or 50,
                    stars = hero.stars or 0,
                    maxHealth = health,
                }
                self.battleBrief.baseInfo[camp].health = self.battleBrief.baseInfo[camp].health + health
            end
        end
        self.battleStatistic.teamStatisticDict[camp].heroStatisticDict = teamStatistic
    end
    for index = 1, 3 do
        for _, camp in ipairs({CampA, CampB}) do
            local hero = battleGame.heroes[camp][index]
            if hero then
                local heroAbstract = {
                    health = hero:GetAttribute(TableConst.enums.BattleAttributeType.Health),
                    recoverableHealth = hero:GetAttribute(TableConst.enums.BattleAttributeType.RecoverableHealth),
                }
                table.update(self.battleAbstract.teamAbstractDict[camp].heroAbstractDict[index], heroAbstract)
            end
        end
    end

    if SwitchesEnv.BATTLE_DEBUG_SWITCH and not _G.BATTLE_PROFILE_SWITCH then
        self.battleStatistic.extraDebugInfo = {
            [CampA] = {
                buffs = battleGame.battleTeamA.buffs
            },
            [CampB] = {
                buffs = battleGame.battleTeamB.buffs
            },
        }
    end
end

function BattleRecorder:RecordBrief(camp, heroUniqueId, tacticId, briefRecordKey, briefRecordValue)
    if not CheckNumberId(tacticId) then
        -- 技能ID不存在，说明不是技能，可能是身份演化、科技、装备等造成的伤害和治疗
        return
    end
    local round = self.battleGame.round
    local tacticBrief = table.ensure(self.battleBrief, "roundBrief", round, camp, heroUniqueId, tacticId)
    if briefRecordKey == BattleConstEnv.BriefRecordKey.Damage then
        tacticBrief[BattleConstEnv.BriefRecordKey.Damage] = (tacticBrief[BattleConstEnv.BriefRecordKey.Damage] or 0) + briefRecordValue
    elseif briefRecordKey == BattleConstEnv.BriefRecordKey.Heal then
        tacticBrief[BattleConstEnv.BriefRecordKey.Heal] = (tacticBrief[BattleConstEnv.BriefRecordKey.Heal] or 0) + briefRecordValue
    elseif briefRecordKey == BattleConstEnv.BriefRecordKey.CrowdControls then
        local crowdControls = table.ensure(tacticBrief, BattleConstEnv.BriefRecordKey.CrowdControls)
        crowdControls[briefRecordValue] = (crowdControls[briefRecordValue] or 0) + 1
    elseif briefRecordKey == BattleConstEnv.BriefRecordKey.Count then
        tacticBrief[BattleConstEnv.BriefRecordKey.Count] = (tacticBrief[BattleConstEnv.BriefRecordKey.Count] or 0) + briefRecordValue
    end
end

function BattleRecorder:RecordAllBriefHealthPerRound()
    local campHealth = {[TableConst.enums.Camp.A] = 0, [TableConst.enums.Camp.B] = 0}
    local campRecoverableHealth = {[TableConst.enums.Camp.A] = 0, [TableConst.enums.Camp.B] = 0}
    for camp, campHeroes in pairs(self.battleGame.heroes) do
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero then
                campRecoverableHealth[camp] = campRecoverableHealth[camp] + hero:GetAttribute(TableConst.enums.BattleAttributeType.RecoverableHealth)
                if hero:IsAlive() then
                    campHealth[camp] = campHealth[camp] + hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
                end
            end
        end
    end
    self:RecordBriefHealthPerRound(TableConst.enums.Camp.A, campHealth[TableConst.enums.Camp.A], campRecoverableHealth[TableConst.enums.Camp.A])
    self:RecordBriefHealthPerRound(TableConst.enums.Camp.B, campHealth[TableConst.enums.Camp.B], campRecoverableHealth[TableConst.enums.Camp.B])
end

function BattleRecorder:RecordBriefHealthPerRound(camp, health, recoverableHealth)
    local round = self.battleGame.round
    local campBrief = table.ensure(self.battleBrief, "roundBrief", round, camp)
    campBrief.health = health
    campBrief.recoverableHealth = recoverableHealth
end

function BattleRecorder:TacticStatistic(camp, heroUniqueId, tacticId, useCount, damage, heal)
    if not CheckNumberId(tacticId) then
        -- 技能ID不存在，说明不是技能，可能是身份演化、科技、装备等造成的伤害和治疗
        return
    end
    local recorderTarget = BattleUtilsEnv.Safe_access(self.tempTacticRecord, camp, heroUniqueId, tacticId)
    if not recorderTarget then
        return
    end
    recorderTarget.useCount = recorderTarget.useCount + useCount
    recorderTarget.damage = recorderTarget.damage + damage
    recorderTarget.heal = recorderTarget.heal + heal
end

function BattleRecorder:AddTotalKill(camp, killNum)
    self.battleStatistic.teamStatisticDict[camp].totalKill = self.battleStatistic.teamStatisticDict[camp].totalKill + killNum
end

function BattleRecorder:OnBattleFinished()
    local battleGame = self.battleGame
    self.battleAbstract.winCamp = battleGame.winCamp
    for index = 1, 3 do
        for _, camp in ipairs({CampA, CampB}) do
            local hero = battleGame.heroes[camp][index]
            if hero then
                local heroAbstract = {
                    health = hero:GetAttribute(TableConst.enums.BattleAttributeType.Health),
                    recoverableHealth = hero:GetAttribute(TableConst.enums.BattleAttributeType.RecoverableHealth),
                }
                table.update(self.battleAbstract.teamAbstractDict[camp].heroAbstractDict[index], heroAbstract)
            end
        end
    end
    self.battleStatistic.position = battleGame.battleInfo.position or 0
    self.battleStatistic.winCamp = battleGame.winCamp
    for camp, tempCampTacticRecord in pairs(self.tempTacticRecord) do
        for heroUniqueId, tempHeroTacticRecord in pairs(tempCampTacticRecord) do
            local hero = battleGame.heroUniqueIdMap[heroUniqueId]
            local index = hero.index
            for _, battleTacticStatistic in pairs(self.battleStatistic.teamStatisticDict[camp].heroStatisticDict[index].tacticStatisticDict) do
                local tempTacticRecord = tempHeroTacticRecord[battleTacticStatistic.tacticData.id]
                battleTacticStatistic.useCount = tempTacticRecord.useCount
                battleTacticStatistic.damage = tempTacticRecord.damage
                battleTacticStatistic.heal = tempTacticRecord.heal
            end
        end
    end
end

-------------- details --------------

function BattleRecorder:GetDetailPointerAndHero()
    return self.detailPointer, self.detailCurrentHeroId
end

function BattleRecorder:SetDetailPointerAndHero(pointer, heroId)
    self.detailPointer = pointer
    self.detailCurrentHeroId = heroId
end

function BattleRecorder:GetNextDetail(pointer, detailRecordType)
    for p = pointer, self.detailPointer do
        if self.battleDetails[p] and self.battleDetails[p] == detailRecordType then
            return p
        end
    end
    return nil
end

function BattleRecorder:AddDetail(detailPointer, ...)
    local count = select('#', ...)
    if detailPointer then
        for i = 1, count do
            local data = select(i, ...)
            data = data == nil and Nil or data
            table.insert(self.battleDetails, detailPointer, data)
            detailPointer = detailPointer + 1
            self.detailPointer = self.detailPointer + 1
        end
    else
        for i = 1, count do
            local data = select(i, ...)
            data = data == nil and Nil or data
            self.battleDetails[self.detailPointer] = data
            self.detailPointer = self.detailPointer + 1
        end
    end
end

function BattleRecorder:ReplaceDetail(data, detailPointer)
    self.battleDetails[detailPointer] = data
end

function BattleRecorder:SetHero(heroId)
    if self.detailCurrentHeroId ~= heroId then
        self.detailCurrentHeroId = heroId
        self:AddDetail(nil, DetailRecordType.SetHero, heroId)
    end
end

function BattleRecorder:DetailBattleStart(detailRecordType, orderedHeroes)
    self:AddDetail(nil, detailRecordType)
    if detailRecordType == DetailRecordType.BattleStart then
        for i = 1, 6 do
            local hero = orderedHeroes[i]
            if hero and hero:IsAlive() then
                self:AddDetail(nil, hero.uniqueId, table.deepclone(hero.attributeHolder.attributes))
            else
                self:AddDetail(nil, Nil, Nil)
            end
        end
    end
end

function BattleRecorder:DetailBattleStartMorale(supplies)
    self:AddDetail(nil, DetailRecordType.BattleStartMorale, supplies)
end

function BattleRecorder:DetailRoundStart(roundId, orderedHeroes)
    self:AddDetail(nil, DetailRecordType.RoundStart, roundId)
    for i = 1, 6 do
        local hero = orderedHeroes[i]
        if hero and hero:IsAlive() then
            self:AddDetail(nil, hero.uniqueId)
        else
            self:AddDetail(nil, Nil)
        end
    end
end

function BattleRecorder:DetailHeroTurnStart(heroId)
    self.detailCurrentHeroId = heroId -- 自动设置当前英雄
    self:AddDetail(nil, DetailRecordType.HeroTurnStart, heroId)
end

function BattleRecorder:DetailRoundEnd(roundId)
    self:AddDetail(nil, DetailRecordType.RoundEnd, roundId)
end

function BattleRecorder:DetailTacticPrepare(heroId, tacticId, remainStep, prob)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.TacticPrepare, tacticId, remainStep, prob)
end

function BattleRecorder:DetailTactic(heroId, tacticId, prob)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Tactic, tacticId, prob)
end

function BattleRecorder:DetailTacticFailed(heroId, tacticId, prob)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.TacticFailed, tacticId, prob)
end

function BattleRecorder:DetailDamage(heroId, targetId, damageType, damage, recoverable, crit, tacticId, debugData)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Damage, targetId, damageType, damage, recoverable, crit, tacticId, debugData)
end

function BattleRecorder:DetailHeal(heroId, targetId, heal, debug)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Heal, targetId, heal, debug)
end

function BattleRecorder:DetailAddBuff(heroId, buffId, newCount, detailPointer)
    local buffData = GameCommon.TableDataManager:GetBuffData(buffId)
    if not buffData.record then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(detailPointer, DetailRecordType.AddBuff, buffId, newCount)
end

function BattleRecorder:DetailRemoveBuff(heroId, buffId)
    local buffData = GameCommon.TableDataManager:GetBuffData(buffId)
    if not buffData.record then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.RemoveBuff, buffId)
end

function BattleRecorder:DetailTriggerBuff(heroId, buffId, sourceTacticId, sourceTacticHeroId, reason)
    local buffData = GameCommon.TableDataManager:GetBuffData(buffId)
    if not buffData.record then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.TriggerBuff, buffId, sourceTacticId, sourceTacticHeroId, reason)
end

function BattleRecorder:DetailTriggerBuffFailed(heroId, buffId, sourceTacticId, sourceTacticHeroId, reason)
    local buffData = GameCommon.TableDataManager:GetBuffData(buffId)
    if not buffData.record then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.TriggerBuffFailed, buffId, sourceTacticId, sourceTacticHeroId, reason)
end

function BattleRecorder:DetailDecreaseBuff(heroId, buffId, newCount)
    local buffData = GameCommon.TableDataManager:GetBuffData(buffId)
    if not buffData.record or not buffData.can_stack then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.DecreaseBuff, buffId, newCount)
end

function BattleRecorder:DetailModifyAttribute(heroId, attributeType, newValue)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.ModifyAttribute, attributeType, newValue)
end

function BattleRecorder:DetailSetVariable(heroId, showName, oldValue, newValue, showPercent)
    if newValue == oldValue then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.SetVariable, showName, oldValue, newValue, showPercent)
end

function BattleRecorder:DetailResult(winCamp)
    self:AddDetail(nil, DetailRecordType.Result, winCamp)
end

function BattleRecorder:DetailPlainAttackTarget(heroId, targetId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.PlainAttackTarget, targetId)
end

function BattleRecorder:DetailCombo(heroId, prob)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Combo, prob)
end

function BattleRecorder:DetailComboFailed(heroId, prob)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.ComboFailed, prob)
end

function BattleRecorder:DetailDodge(heroId, damageValue, debug)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Dodge, damageValue, debug)
end

function BattleRecorder:DetailRefreshBuff(heroId, buffId)
    local buffData = GameCommon.TableDataManager:GetBuffData(buffId)
    if not buffData.record then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.RefreshBuff, buffId)
end

function BattleRecorder:DetailAttackVamp(heroId, heal, debug)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.AttackVamp, heal, debug)
end

function BattleRecorder:DetailIntelligenceVamp(heroId, heal, debug)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.IntelligenceVamp, heal, debug)
end

function BattleRecorder:DetailImmune(heroId, damage, debug)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Immune, damage, debug)
end

function BattleRecorder:DetailCheckCrowdControl(heroId, ccType, remainRound)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.CheckCrowdControl, ccType, remainRound)
end

function BattleRecorder:DetailModifyBuff(heroId, buffId, attributeType, oldValue, newValue)
    if newValue == oldValue then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.ModifyBuff, buffId, attributeType, oldValue, newValue)
end

function BattleRecorder:DetailModifyTactic(heroId, tacticId, attributeType, isInstant, oldValue, newValue)
    if newValue == oldValue then
        return
    end
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.ModifyTactic, tacticId, attributeType, isInstant, oldValue, newValue)
end

function BattleRecorder:DetailTacticDisable(heroId, tacticId, reasonBuffId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.TacticDisable, tacticId, reasonBuffId)
end

function BattleRecorder:DetailPlainAttackDisable(heroId, reasonBuffId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.PlainAttackDisable, reasonBuffId)
end

function BattleRecorder:DetailTacticAwakening(heroId, tacticId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.TacticAwakening, tacticId)
end

function BattleRecorder:DetailHeroTurnEnd(heroId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.HeroTurnEnd)
end

function BattleRecorder:DetailInterruptPrepare(heroId, tacticId, reasonBuffId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.InterruptPrepare, tacticId, reasonBuffId)
end

function BattleRecorder:DetailInsight(heroId, ccType, insight)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.Insight, ccType, insight)
end

function BattleRecorder:DetailRecoverableToDeath(heroId, convertCount)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.RecoverableToDeath, convertCount)
end

function BattleRecorder:DetailManuTriggerBuff(heroId, tacticId)
    self:SetHero(heroId)
    self:AddDetail(nil, DetailRecordType.ManuTriggerBuff, tacticId)
end