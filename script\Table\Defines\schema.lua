
--[[------------------------------------------------------------------------------
-- <auto-generated>
--     This code was generated by a tool.
--     Changes to this file may cause incorrect behavior and will be lost if
--     the code is regenerated.
-- </auto-generated>
--]]------------------------------------------------------------------------------


local enums =
{
    ---@class Actor
     ---@field public Source integer @自身
     ---@field public Target integer @目标
    ['Actor'] = {   Source=1,  Target=2,  };
    ---@class ActorType @地图内角色类型
     ---@field public ARMY integer @部队
     ---@field public MAIN_CITY integer @主堡
     ---@field public TENT integer @营帐
     ---@field public CITY integer @城池
     ---@field public CHUNK integer @地块
     ---@field public JI_SHEN_JI_DIAN integer @稷神祭典
    ['ActorType'] = {   ARMY=1001,  MAIN_CITY=1002,  TENT=1003,  CITY=1004,  CHUNK=1005,  JI_SHEN_JI_DIAN=1006,  };
    ---@class Alignment
     ---@field public Left integer @居左
     ---@field public Right integer @居右
     ---@field public Center integer @居中
    ['Alignment'] = {   Left=1,  Right=2,  Center=3,  };
    ---@class ArmyStatus @部队主状态
     ---@field public Rest integer @城内(主堡城池营帐)休息
     ---@field public Stay integer @野外停留
     ---@field public March integer @行军
     ---@field public DefendGrid integer @驻守格子
     ---@field public DefendBuilding integer @驻守建筑
     ---@field public Occupy integer @占领地块倒计时中
     ---@field public Retreat integer @战败溃逃、主动回城
     ---@field public Sing integer @释放策牌吟唱
     ---@field public WaitSing integer @释放策牌吟唱被暂停
     ---@field public IdleCombat integer @非战斗
     ---@field public WaitCombat integer @等待战斗
     ---@field public Combat integer @战斗
     ---@field public Injured integer @重伤
     ---@field public IdleDeploy integer @非调动
     ---@field public GoDeploy integer @前往调动
     ---@field public Deploy integer @调动
     ---@field public GoSiegeRally integer @前往攻城集结
     ---@field public SiegeRally integer @攻城集结
    ['ArmyStatus'] = {   Rest=101,  Stay=102,  March=103,  DefendGrid=104,  DefendBuilding=105,  Occupy=106,  Retreat=107,  Sing=108,  WaitSing=109,  IdleCombat=201,  WaitCombat=202,  Combat=203,  Injured=204,  IdleDeploy=301,  GoDeploy=302,  Deploy=303,  GoSiegeRally=304,  SiegeRally=305,  };
    ---@class ArmyTypeQualificationTarget @兵种适应性修改目标
     ---@field public Self integer @自己
    ['ArmyTypeQualificationTarget'] = {   Self=0,  };
    ---@class AudioGroup @音效组
     ---@field public Default integer @默认
     ---@field public Male integer @男
     ---@field public Female integer @女
    ['AudioGroup'] = {   Default=0,  Male=1,  Female=2,  };
    ---@class BattleCameraType @相机类型
     ---@field public Full integer @全景
     ---@field public Follow integer @跟随
     ---@field public Focus integer @聚焦
     ---@field public Special integer @特殊
    ['BattleCameraType'] = {   Full=1,  Follow=2,  Focus=3,  Special=4,  };
    ---@class BattleCharacterAnimation @策划能控制的角色动作
     ---@field public skill_01 integer @skill_01
     ---@field public skill_02 integer @skill_02
     ---@field public skill_11 integer @skill_11
     ---@field public skill_12 integer @skill_12
     ---@field public skill_13 integer @skill_13
     ---@field public skill_21 integer @skill_21
     ---@field public skill_22 integer @skill_22
     ---@field public skill_23 integer @skill_23
     ---@field public skill_31 integer @skill_31
     ---@field public skill_32 integer @skill_32
     ---@field public skill_41 integer @skill_41
     ---@field public charge integer @charge
     ---@field public dodge integer @dodge
     ---@field public guard integer @guard
     ---@field public hit integer @hit
     ---@field public float_up integer @float_up
     ---@field public float_mid integer @float_mid
     ---@field public float_down integer @float_down
     ---@field public special_01 integer @special_01
     ---@field public special_02 integer @special_02
    ['BattleCharacterAnimation'] = {   skill_01=0,  skill_02=1,  skill_11=2,  skill_12=3,  skill_13=4,  skill_21=5,  skill_22=6,  skill_23=7,  skill_31=8,  skill_32=9,  skill_41=10,  charge=11,  dodge=12,  guard=13,  hit=14,  float_up=15,  float_mid=16,  float_down=17,  special_01=18,  special_02=19,  };
    ---@class BattleDamageType
     ---@field public Attack integer @物理伤害
     ---@field public Intelligence integer @法术伤害
     ---@field public Reality integer @真实伤害
     ---@field public Chain integer @连锁伤害
     ---@field public Share integer @分摊伤害
    ['BattleDamageType'] = {   Attack=1,  Intelligence=2,  Reality=3,  Chain=4,  Share=5,  };
    ---@class BattleEvent
     ---@field public None integer
     ---@field public FormationMorale integer @布阵-士气
     ---@field public FormationArmyType integer @布阵-兵种
     ---@field public FormationTechnology integer @布阵-科技
     ---@field public FormationEquipment integer @布阵-装备
     ---@field public FormationTacticsArmyType integer @布阵-战法-兵种
     ---@field public FormationTacticsFormation integer @布阵-战法-阵法
     ---@field public FormationTacticsOther integer @布阵-战法-其他
     ---@field public RoundPrepare integer @回合准备
     ---@field public HeroTurnStart integer @英雄轮次开始
     ---@field public HeroTurnAction integer @英雄轮次行动
     ---@field public HeroTurnEnd integer @英雄轮次结束
     ---@field public RoundEnd integer @回合结束
     ---@field public BattleResult integer @战斗结算
     ---@field public Initialize integer @初始化
     ---@field public BeforeDamage integer @伤害前
     ---@field public AfterDamage integer @伤害后
     ---@field public BeforeHeal integer @治疗前
     ---@field public AfterHeal integer @治疗后
     ---@field public BeforeUseTactic integer @使用战法前
     ---@field public AfterUseTactic integer @使用战法后
     ---@field public TacticProbFailed integer @战法发动率判定失败
     ---@field public BeforeBeDamaged integer @受到伤害前
     ---@field public AfterBeDamaged integer @受到伤害后
     ---@field public AfterPlainAttackProcess integer @普攻流程后
     ---@field public AfterPlainAttack integer @普攻后
     ---@field public BeforePlainAttack integer @普攻前
     ---@field public BeforeDodge integer @闪避前
     ---@field public AfterDodgeSuccess integer @闪避成功后
     ---@field public AfterDodgeFail integer @闪避失败后
     ---@field public BeforeBeHealed integer @受到治疗前
     ---@field public AfterBeHealed integer @受到治疗后
     ---@field public BeforeFatalDamage integer @受致命伤前
    ['BattleEvent'] = {   None=0,  FormationMorale=1,  FormationArmyType=2,  FormationTechnology=14,  FormationEquipment=3,  FormationTacticsArmyType=13,  FormationTacticsFormation=12,  FormationTacticsOther=4,  RoundPrepare=5,  HeroTurnStart=6,  HeroTurnAction=7,  HeroTurnEnd=8,  RoundEnd=9,  BattleResult=10,  Initialize=11,  BeforeDamage=100,  AfterDamage=101,  BeforeHeal=102,  AfterHeal=103,  BeforeUseTactic=105,  AfterUseTactic=106,  TacticProbFailed=107,  BeforeBeDamaged=108,  AfterBeDamaged=109,  AfterPlainAttackProcess=111,  AfterPlainAttack=112,  BeforePlainAttack=113,  BeforeDodge=114,  AfterDodgeSuccess=115,  AfterDodgeFail=116,  BeforeBeHealed=117,  AfterBeHealed=118,  BeforeFatalDamage=119,  };
    ---@class BattleModeType @战斗模式
     ---@field public OneOnOne integer @1v1
     ---@field public Bo3 integer
     ---@field public Kof3 integer
    ['BattleModeType'] = {   OneOnOne=0,  Bo3=1,  Kof3=2,  };
    ---@class BattlePVEType @PVE战斗类型
     ---@field public LandDefender integer
    ['BattlePVEType'] = {   LandDefender=10,  };
    ---@class BattleRecordScope
     ---@field public Individual integer @个人
     ---@field public Ally integer @同盟
    ['BattleRecordScope'] = {   Individual=1,  Ally=2,  };
    ---@class BattleTacticGrade
     ---@field public White integer @白
     ---@field public Blue integer @蓝
     ---@field public Purple integer @紫
     ---@field public Gold integer @橙
    ['BattleTacticGrade'] = {   White=1,  Blue=2,  Purple=3,  Gold=4,  };
    ---@class BattleType
     ---@field public PVE integer @开拓
     ---@field public PVP integer @交战
     ---@field public All integer @全部
     ---@field public Test integer @测试
    ['BattleType'] = {   PVE=1,  PVP=2,  All=3,  Test=99,  };
    ---@class BehaviorType @单个体执行的行为
     ---@field public March integer @A前往行军
     ---@field public OccupyGrid integer @A前往占领地块
     ---@field public Halt integer @中断任意行为
     ---@field public Return integer @回城
     ---@field public EnterSpace integer @进入场景才可被看见
     ---@field public UseStrategy integer @使用策牌
     ---@field public ModifyArmy integer @编队
    ['BehaviorType'] = {   March=1,  OccupyGrid=2,  Halt=3,  Return=4,  EnterSpace=5,  UseStrategy=6,  ModifyArmy=7,  };
    ---@class BuffGroupConflictRule @Buff组冲突规则
     ---@field public Invalid integer @目标存在同类buff组的buff时，新BUFF无法添加
     ---@field public Cover integer @目标存在同类buff组的buff时，新BUFF可以添加，且添加前清除该BUFF组其他BUFF
     ---@field public Round integer @目标存在同类buff组的buff时,新BUFF如果剩余回合数更长,则执行【覆盖】,否则【失效】
     ---@field public Attribute integer @目标存在同类buff组的buff时，如果来源【%属性】更高，则执行【覆盖】，否则【失效】
    ['BuffGroupConflictRule'] = {   Invalid=1,  Cover=2,  Round=3,  Attribute=4,  };
    ---@class BuffGroupType @Buff组类型
     ---@field public DamaageDot1 integer @伤害dot1
     ---@field public DamaageDot2 integer @伤害dot2
     ---@field public NegativeDot integer @负面dot
     ---@field public Control integer @控制效果
     ---@field public NegativeCanStacked integer @减益可叠加
     ---@field public NegativeNotStacked integer @减益不可叠加
     ---@field public PositiveCanStacked integer @增益可叠加
     ---@field public PositiveNotStacked integer @增益不可叠加
    ['BuffGroupType'] = {   DamaageDot1=1,  DamaageDot2=2,  NegativeDot=3,  Control=4,  NegativeCanStacked=5,  NegativeNotStacked=6,  PositiveCanStacked=7,  PositiveNotStacked=8,  };
    ---@class BuffOverlayRule @buff叠加规则（层数已满时）
     ---@field public Replace integer @新buff顶替旧buff
     ---@field public Refresh integer @新buff无法添加；但是旧buff的持续时间刷新至最新
     ---@field public ReplaceMaxRemainRound integer @根据剩余最大回合来顶替
    ['BuffOverlayRule'] = {   Replace=1,  Refresh=2,  ReplaceMaxRemainRound=3,  };
    ---@class BuffRemoveTiming @战斗Buff自然移除时机
     ---@field public AfterHeroTurnEnd integer @英雄自身轮次结束后
     ---@field public AfterRoundEnd integer @大回合结束后
    ['BuffRemoveTiming'] = {   AfterHeroTurnEnd=1,  AfterRoundEnd=2,  };
    ---@class BuffSourceType @部队buff来源
     ---@field public HomeTech integer @内城科技
     ---@field public SandMap integer @沙盘地图
     ---@field public Equip integer @装备
    ['BuffSourceType'] = {   HomeTech=1,  SandMap=2,  Equip=3,  };
    ---@class BuffStackRule @buff叠层规则（层数未满时）
     ---@field public Mutex integer @每层独立计算时间
     ---@field public Refresh integer @刷新所有层时间
    ['BuffStackRule'] = {   Mutex=1,  Refresh=2,  };
    ---@class BuffType @buff类型
     ---@field public Normal integer @普通
     ---@field public Charge integer @蓄力
    ['BuffType'] = {   Normal=0,  Charge=1,  };
    ---@class BuildingType @建筑类型
     ---@field public TaiXue integer @太学
     ---@field public ZhengBingSuo integer @征兵所
     ---@field public JunYing integer @军营
     ---@field public JunWangDian integer @君王殿
    ['BuildingType'] = {   TaiXue=1000001,  ZhengBingSuo=1000002,  JunYing=1000003,  JunWangDian=1000004,  };
    ---@class Camp @阵营
     ---@field public None integer @无阵营
     ---@field public A integer @左边的阵营
     ---@field public B integer @右边的阵营
    ['Camp'] = {   None=0,  A=1,  B=2,  };
    ---@class CareerType @职业类型枚举
     ---@field public DEFAULT integer @空
     ---@field public FIELD_MASTER integer @田师
    ['CareerType'] = {   DEFAULT=0,  FIELD_MASTER=101,  };
    ---@class CoinType @货币类型
     ---@field public Gold integer @金币
     ---@field public Copper integer @铜币
     ---@field public Wood integer @木头
     ---@field public Iron integer @铁块
     ---@field public Stone integer @石头
     ---@field public Food integer @粮草
     ---@field public Soldier integer @预备兵
     ---@field public Jade integer @魂玉
    ['CoinType'] = {   Gold=100001,  Copper=100002,  Wood=100003,  Iron=100004,  Stone=100005,  Food=100006,  Soldier=100007,  Jade=100008,  };
    ---@class CrowdControlType
     ---@field public Silence integer @沉默
     ---@field public Disarm integer @缴械
     ---@field public Stun integer @震慑
     ---@field public Starve integer @断粮
     ---@field public AvoidDeath integer @免死
     ---@field public AdditionalCaptain integer @额外军职-主公
     ---@field public AdditionalPioneer integer @额外军职-先锋
     ---@field public AdditionalAdviser integer @额外军职-军师
     ---@field public Taunt integer @普攻锁定目标
     ---@field public Deterrence integer @主动、追击战法锁定目标
     ---@field public Confusion integer @混乱
    ['CrowdControlType'] = {   Silence=1,  Disarm=2,  Stun=3,  Starve=4,  AvoidDeath=5,  AdditionalCaptain=6,  AdditionalPioneer=7,  AdditionalAdviser=8,  Taunt=9,  Deterrence=10,  Confusion=11,  };
    ---@class DamageFormulaArgs @伤害公式参数
     ---@field public A integer @进攻方兵力
     ---@field public B integer @兵力系数
     ---@field public C integer @属性差值
     ---@field public C1 integer @进攻方武力/智力值
     ---@field public C2 integer @防守方统帅值
     ---@field public C3 integer @防守方智力值
     ---@field public C4 integer @进攻方破甲/看破百分比
     ---@field public C5 integer @常量&lt;C5&gt;
     ---@field public D integer @战法伤害系数
     ---@field public E integer @过程类增减伤
     ---@field public E1 integer @伤害增加
     ---@field public E3 integer @防守方受伤减少
     ---@field public E4 integer @防守方红度减伤
     ---@field public F integer @结算类增减伤
     ---@field public F1 integer @进攻方施加的受到伤害增加
     ---@field public F2 integer @防守方施加的造成伤害减少
     ---@field public F3 integer @会心/奇谋伤害
     ---@field public G integer @独立增减伤
     ---@field public G1 integer @红度增伤
     ---@field public G2 integer @兵种克制
     ---@field public G3 integer @进攻方独立兵刃/谋略伤害增减伤
     ---@field public G4 integer @防守方受到兵刃/独立谋略伤害增减伤
     ---@field public W integer @兵力保底伤害
     ---@field public M integer @常量&lt;M&gt;
    ['DamageFormulaArgs'] = {   A=1,  B=2,  C=3,  C1=4,  C2=5,  C3=6,  C4=7,  C5=8,  D=9,  E=10,  E1=11,  E3=12,  E4=13,  F=14,  F1=15,  F2=16,  F3=17,  G=18,  G1=19,  G2=20,  G3=21,  G4=22,  W=23,  M=24,  };
    ---@class DamageRange @伤害范围
     ---@field public Single integer @单体伤害
     ---@field public Aoe integer @群体伤害
     ---@field public Buff integer @Buff伤害
    ['DamageRange'] = {   Single=0,  Aoe=1,  Buff=2,  };
    ---@class DetailRecordType @战报类型
     ---@field public SetHero integer
     ---@field public BattleStart integer
     ---@field public BattleStartMorale integer
     ---@field public BattleStartArmyType integer
     ---@field public BattleStartEquipment integer
     ---@field public BattleStartTactics integer
     ---@field public BattleStartTechnology integer
     ---@field public RoundStart integer
     ---@field public HeroTurnStart integer
     ---@field public RoundEnd integer
     ---@field public Result integer
     ---@field public Tactic integer
     ---@field public Damage integer
     ---@field public Heal integer
     ---@field public AddBuff integer
     ---@field public RemoveBuff integer
     ---@field public TriggerBuff integer
     ---@field public DecreaseBuff integer
     ---@field public AddCrowdControl integer
     ---@field public RemoveCrowdControl integer
     ---@field public ModifyAttribute integer
     ---@field public SetVariable integer
     ---@field public TacticFailed integer
     ---@field public TriggerBuffFailed integer
     ---@field public TacticPrepare integer
     ---@field public Combo integer
     ---@field public ComboFailed integer
     ---@field public Dodge integer
     ---@field public RefreshBuff integer
     ---@field public Silence integer
     ---@field public Disarm integer
     ---@field public Stun integer
     ---@field public Starve integer
     ---@field public PlainAttackTarget integer
    ['DetailRecordType'] = {   SetHero=1,  BattleStart=101,  BattleStartMorale=102,  BattleStartArmyType=103,  BattleStartEquipment=104,  BattleStartTactics=105,  BattleStartTechnology=106,  RoundStart=151,  HeroTurnStart=152,  RoundEnd=153,  Result=199,  Tactic=201,  Damage=202,  Heal=203,  AddBuff=204,  RemoveBuff=205,  TriggerBuff=206,  DecreaseBuff=207,  AddCrowdControl=208,  RemoveCrowdControl=209,  ModifyAttribute=210,  SetVariable=211,  TacticFailed=212,  TriggerBuffFailed=213,  TacticPrepare=214,  Combo=215,  ComboFailed=216,  Dodge=217,  RefreshBuff=218,  Silence=301,  Disarm=302,  Stun=303,  Starve=304,  PlainAttackTarget=999,  };
    ---@class ElementalType @元素类型
     ---@field public Wind integer @风
     ---@field public Forest integer @林
     ---@field public Fire integer @火
     ---@field public Mountain integer @山
    ['ElementalType'] = {   Wind=0,  Forest=1,  Fire=2,  Mountain=3,  };
    ---@class EquipEntriesType @装备词条类型
     ---@field public None integer @无词条
     ---@field public Single integer @单词条
     ---@field public Double integer @双词条
     ---@field public ReverseDouble integer @正负词条
    ['EquipEntriesType'] = {   None=0,  Single=1,  Double=2,  ReverseDouble=3,  };
    ---@class EquipQuality @品质
     ---@field public None integer @无
     ---@field public Normal integer @普通
     ---@field public Rare integer @稀有
     ---@field public Legend integer @传说
     ---@field public Special integer @专属
    ['EquipQuality'] = {   None=-1,  Normal=1,  Rare=2,  Legend=3,  Special=4,  };
    ---@class EquipType @装备类型
     ---@field public Weapon integer @武器
     ---@field public Defense integer @防具
     ---@field public Horse integer @坐骑
    ['EquipType'] = {   Weapon=1,  Defense=2,  Horse=3,  };
    ---@class FlyStatusChange @飞行状态改变
     ---@field public ReachTop integer @最高点
     ---@field public LandGround integer @落地
    ['FlyStatusChange'] = {   ReachTop=1,  LandGround=2,  };
    ---@class GeneralArmyType
     ---@field public Shield integer @盾兵
     ---@field public Cavalry integer @骑兵
     ---@field public Pikemen integer @枪兵
     ---@field public Archers integer @弓兵
     ---@field public None integer @无高级兵种
     ---@field public TongQueTaiLing integer @铜雀台伶
     ---@field public YuanRongNuBing integer @元戎弩兵
    ['GeneralArmyType'] = {   Shield=1,  Cavalry=2,  Pikemen=3,  Archers=4,  None=0,  TongQueTaiLing=41,  YuanRongNuBing=42,  };
    ---@class GeneralAttrType @武将属性
     ---@field public WuLi integer @武力
     ---@field public ZhiLi integer @智力
     ---@field public TongShuai integer @统率
     ---@field public XianGong integer @先攻
    ['GeneralAttrType'] = {   WuLi=1,  ZhiLi=2,  TongShuai=3,  XianGong=4,  };
    ---@class GeneralDynasty
     ---@field public Wei integer @魏国
     ---@field public Shu integer @蜀国
     ---@field public Wu integer @吴国
     ---@field public Qun integer @群雄
    ['GeneralDynasty'] = {   Wei=1,  Shu=2,  Wu=3,  Qun=4,  };
    ---@class GeneralRoleType @武将定位
     ---@field public BingRen integer @兵刃
     ---@field public MouLue integer @谋略
     ---@field public WenWu integer @文武
     ---@field public FuZhu integer @辅助
     ---@field public ZhiLiao integer @治疗
     ---@field public FangYu integer @防御
    ['GeneralRoleType'] = {   BingRen=1,  MouLue=2,  WenWu=3,  FuZhu=4,  ZhiLiao=5,  FangYu=6,  };
    ---@class HealFormulaArgs @治疗公式参数
     ---@field public A integer @施法者智力值
     ---@field public B integer @战法恢复系数
     ---@field public C integer @恢复效果提升
     ---@field public D integer @兵力系数
     ---@field public D1 integer @常量&lt;D1&gt;
     ---@field public M integer @常量&lt;M&gt;
    ['HealFormulaArgs'] = {   A=1,  B=2,  C=3,  D=4,  D1=5,  M=6,  };
    ---@class HudLayer @Hud层级
     ---@field public Geography integer @地理信息
     ---@field public Building integer @建筑
     ---@field public Army integer @军队
     ---@field public SelfArmy integer @玩家军队
     ---@field public Interactive integer @交互界面
     ---@field public Always integer @常显
    ['HudLayer'] = {   Geography=0,  Building=1,  Army=2,  SelfArmy=3,  Interactive=4,  Always=5,  };
    ---@class IdSegType @id段类型
     ---@field public Currency integer @货币
     ---@field public Item integer @道具
     ---@field public Equip integer @装备
     ---@field public Horse integer @坐骑
     ---@field public Token integer @信物
     ---@field public Material integer @材料
    ['IdSegType'] = {   Currency=1,  Item=2,  Equip=3,  Horse=4,  Token=5,  Material=6,  };
    ---@class InConType @和容器关系
     ---@field public InBase integer @部队在回城点
     ---@field public InDefend integer @驻守容器中
    ['InConType'] = {   InBase=101,  InDefend=102,  };
    ---@class InteractiveBehavior @多个体交互行为
     ---@field public Attack integer @A攻击B
     ---@field public Defend integer @A驻守B
     ---@field public Occupy integer @A攻占B地块
     ---@field public Raid integer @A扫荡B地块
     ---@field public Exploit integer @A开发B地块
    ['InteractiveBehavior'] = {   Attack=1,  Defend=2,  Occupy=3,  Raid=4,  Exploit=5,  };
    ---@class LandBuffType @地块Buff类型
     ---@field public AddProduct integer @增加产量
    ['LandBuffType'] = {   AddProduct=101,  };
    ---@class ModifiablePosition @可修改军职
     ---@field public TeamPioneer integer @我方先锋
     ---@field public TeamAdviser integer @我方军师
    ['ModifiablePosition'] = {   TeamPioneer=2,  TeamAdviser=3,  };
    ---@class NodeTaskStatus @节点的执行状态
     ---@field public Default integer @默认
     ---@field public Running integer @运行时
    ['NodeTaskStatus'] = {   Default=1,  Running=2,  };
    ---@class ObstacleType @阻挡类型
     ---@field public Land integer @空地
     ---@field public Obstacle integer @阻挡
     ---@field public DynamicObstacle integer @动态阻挡
    ['ObstacleType'] = {   Land=0,  Obstacle=1,  DynamicObstacle=2,  };
    ---@class OwnedStatus @收集状态
     ---@field public Owned integer @已拥有
     ---@field public NotOwned integer @未拥有
    ['OwnedStatus'] = {   Owned=1,  NotOwned=2,  };
    ---@class Position @军职
     ---@field public Captain integer @1号位是主公
     ---@field public Pioneer integer @2号位是先锋
     ---@field public Adviser integer @3号位是军师
    ['Position'] = {   Captain=1,  Pioneer=2,  Adviser=3,  };
    ---@class ProduceSrcType @资源产出来源
     ---@field public WorldLand integer @大世界地块
     ---@field public FarmLand integer @家园开垦地
     ---@field public CityLand integer @城池开垦地
     ---@field public Else integer @其他
    ['ProduceSrcType'] = {   WorldLand=1,  FarmLand=2,  CityLand=3,  Else=4,  };
    ---@class PurseType @战法子类型
     ---@field public Normal integer @普通
     ---@field public Extra integer @启动
    ['PurseType'] = {   Normal=0,  Extra=1,  };
    ---@class QualificationRank @资质评级
     ---@field public C integer
     ---@field public B integer
     ---@field public A integer
     ---@field public S integer
    ['QualificationRank'] = {   C=0,  B=1,  A=2,  S=3,  };
    ---@class Quality @品质等级
     ---@field public Blue integer @蓝
     ---@field public Violet integer @紫
     ---@field public Gold integer @金
     ---@field public Red integer @红
    ['Quality'] = {   Blue=1,  Violet=2,  Gold=3,  Red=4,  };
    ---@class Reason @变更原因
     ---@field public GM integer @管理员
     ---@field public MODIFY_ARMY integer @调整部队
     ---@field public RESET_TACTIC integer @重置战法
     ---@field public UPGRADE_TACTIC integer @升级战法
     ---@field public UPGRADE_HERO_STAR integer @武将升星
     ---@field public UPGRADE_TACTIC_STAR integer @战法升星
     ---@field public DECOMPOSE_TOKEN integer @分解信物
     ---@field public DECOMPOSE_EQUIP integer @分解装备
     ---@field public FORGE_EQUIP integer @锻造装备
     ---@field public REBUILD_EQUIP integer @重塑装备
     ---@field public BUILD_EQUIP integer @打造装备
     ---@field public TRAIN_HORSE integer @驯马
     ---@field public SOLD_HORSE integer @出售坐骑
     ---@field public JI_DIAN_REWARD integer @稷神祭典奖励
     ---@field public WO_ZHONG_SHENG_YOU integer @无中生有
     ---@field public USE_ITEM integer @使用道具
     ---@field public TASK_REWARD integer @任务奖励
     ---@field public HOME_BUILDING integer @内城建造
     ---@field public UPGRADE_HOME_TECH integer @升级内城科技
     ---@field public PRODUCE integer @产出
     ---@field public AMBITIONS_REWARD integer @霸业阶段奖励
    ['Reason'] = {   GM=0,  MODIFY_ARMY=101,  RESET_TACTIC=102,  UPGRADE_TACTIC=103,  UPGRADE_HERO_STAR=104,  UPGRADE_TACTIC_STAR=105,  DECOMPOSE_TOKEN=201,  DECOMPOSE_EQUIP=202,  FORGE_EQUIP=203,  REBUILD_EQUIP=204,  BUILD_EQUIP=205,  TRAIN_HORSE=206,  SOLD_HORSE=207,  JI_DIAN_REWARD=301,  WO_ZHONG_SHENG_YOU=302,  USE_ITEM=401,  TASK_REWARD=501,  HOME_BUILDING=601,  UPGRADE_HOME_TECH=602,  PRODUCE=603,  AMBITIONS_REWARD=701,  };
    ---@class RelationShip @关系
     ---@field public Self integer @自己
     ---@field public SameAlly integer @盟友
     ---@field public FreeMan integer @散人
     ---@field public DiffAlly integer @不同联盟
     ---@field public Monster integer @野怪
    ['RelationShip'] = {   Self=0,  SameAlly=1,  FreeMan=2,  DiffAlly=3,  Monster=4,  };
    ---@class SAvatarEvent @Avatar事件枚举
     ---@field public DEFAULT integer @空
     ---@field public PROSPERITY_CHANGE integer @繁荣度变化
     ---@field public HOME_BUILDING_UPGRADE integer @建筑升级建造
     ---@field public RES_PRODUCE_SPEED_CHANGE integer @资源产量变化
     ---@field public HOME_TECH_UPGRADE integer @内城科技升级解锁
     ---@field public HERO_LEVE_UPGRADE integer @武将升级
     ---@field public ARMY_SOLDIER_CHANGE integer @部队兵力变化
     ---@field public GET_NEW_TACTIC integer @获得战法
     ---@field public HOME_FARM_UPGRADE integer @开垦地升级
     ---@field public NEW_RES_LAND integer @获得资源地
     ---@field public UNLOCK_TALENT integer @解锁天赋
     ---@field public ADD_COIN integer @获得货币
     ---@field public DEC_COIN integer @减少货币
     ---@field public TASK_COMPLETE integer @任务完成
    ['SAvatarEvent'] = {   DEFAULT=0,  PROSPERITY_CHANGE=10001,  HOME_BUILDING_UPGRADE=10002,  RES_PRODUCE_SPEED_CHANGE=10003,  HOME_TECH_UPGRADE=10004,  HERO_LEVE_UPGRADE=10005,  ARMY_SOLDIER_CHANGE=10006,  GET_NEW_TACTIC=10007,  HOME_FARM_UPGRADE=10008,  NEW_RES_LAND=10009,  UNLOCK_TALENT=10010,  ADD_COIN=10011,  DEC_COIN=10012,  TASK_COMPLETE=10013,  };
    ---@class ScheduleType @排期类型
     ---@field public CommonDaily integer @日常刷天
     ---@field public AmbitionsDaily integer @霸业刷天
    ['ScheduleType'] = {   CommonDaily=1,  AmbitionsDaily=2,  };
    ---@class slg.AdministrativeHierarchy
     ---@field public County integer @县
     ---@field public Commandery integer @郡
     ---@field public Perfecture integer @州
    ['slg.AdministrativeHierarchy'] = {   County=1,  Commandery=2,  Perfecture=3,  };
    ---@class slg.MapElementType
     ---@field public Empty integer @空地
     ---@field public Mountain integer @山
     ---@field public River integer @河
     ---@field public Food integer @粮食
     ---@field public Stone integer @石料
     ---@field public Wood integer @木材
     ---@field public Iron integer @铁矿
     ---@field public FortifiedCity integer @城池
     ---@field public CityGate integer @城门
     ---@field public CityWall integer @城墙
     ---@field public MainCity integer @主城
     ---@field public DevelopableResourceNode integer @开垦地
    ['slg.MapElementType'] = {   Empty=0,  Mountain=1,  River=2,  Food=4,  Stone=8,  Wood=16,  Iron=32,  FortifiedCity=64,  CityGate=128,  CityWall=256,  MainCity=512,  DevelopableResourceNode=1024,  };
    ---@class StrategyCardType @职业策牌枚举
     ---@field public NONE integer @空
     ---@field public SHA integer @杀
     ---@field public SHAN integer @闪
     ---@field public WU_ZHONG_SHENG_YOU integer @无中生有
     ---@field public WU_GU_FENG_DENG integer @五谷丰登
     ---@field public KAI_KEN integer @开垦
     ---@field public JI_SHEN_JI_DIAN integer @稷神祭典
    ['StrategyCardType'] = {   NONE=0,  SHA=2000101,  SHAN=2000102,  WU_ZHONG_SHENG_YOU=2000103,  WU_GU_FENG_DENG=2000201,  KAI_KEN=2000202,  JI_SHEN_JI_DIAN=2000203,  };
    ---@class TacticAttributeType
     ---@field public Default integer
     ---@field public LeftCD integer @剩余CD
     ---@field public TotalCD integer @总CD
     ---@field public GlobalFactor integer @伤害系数
     ---@field public IsEnabled integer @是否可用
     ---@field public UseFailedProbability integer @使用失败概率
    ['TacticAttributeType'] = {   Default=0,  LeftCD=1,  TotalCD=2,  GlobalFactor=3,  IsEnabled=4,  UseFailedProbability=5,  };
    ---@class TacticFeature
     ---@field public Assistance integer @辅助
     ---@field public CivilAndMilitary integer @文武
     ---@field public Pursue integer @治疗
     ---@field public Defend integer @防御
     ---@field public Strategy integer @谋略
     ---@field public Weapons integer @兵刃
    ['TacticFeature'] = {   Assistance=1,  CivilAndMilitary=2,  Pursue=3,  Defend=4,  Strategy=5,  Weapons=6,  };
    ---@class TacticType
     ---@field public Active integer @主动
     ---@field public Passive integer @被动
     ---@field public Pursue integer @追击
     ---@field public Command integer @指挥
     ---@field public PlainAttack integer @普攻
     ---@field public ArmyType integer @兵种
     ---@field public Formation integer @阵法
    ['TacticType'] = {   Active=1,  Passive=2,  Pursue=3,  Command=4,  PlainAttack=5,  ArmyType=6,  Formation=7,  };
    ---@class TriggerTiming
     ---@field public RoundStart integer @回合开始
     ---@field public ConsumeCard integer @消耗卡牌
    ['TriggerTiming'] = {   RoundStart=1,  ConsumeCard=2,  };
    ---@class WorldBuffType @沙盘Buff类型
     ---@field public AddMoveSpeed integer @增加移速
     ---@field public AddDefendRange integer @增加驻守范围
    ['WorldBuffType'] = {   AddMoveSpeed=101,  AddDefendRange=102,  };
    ---@class BattleAttributeType
     ---@field public Attack integer @武力
     ---@field public Intelligence integer @智力
     ---@field public RecoverableHealth integer @伤兵
     ---@field public Defense integer @防御
     ---@field public Speed integer @速度
     ---@field public AttackCritRate integer @会心几率
     ---@field public AttackCritDamage integer @会心伤害率
     ---@field public AttackDamageAdjustment integer @造成兵刃伤害
     ---@field public AttackDamageFinalAdjustment integer @造成兵刃伤害结果
     ---@field public HurtAttackAdjustment integer @受到兵刃伤害
     ---@field public HurtAttackFinalAdjustment integer @受到兵刃伤害结果
     ---@field public IntelligenceCritRate integer @奇谋几率
     ---@field public IntelligenceCritDamage integer @奇谋伤害率
     ---@field public IntelligenceDamageAdjustment integer @造成谋略伤害
     ---@field public IntelligenceDamageFinalAdjustment integer @造成谋略伤害结果
     ---@field public HurtIntelligenceAdjustment integer @受到谋略伤害
     ---@field public HurtIntelligenceFinalAdjustment integer @受到谋略伤害结果
     ---@field public HealAdjustment integer @造成治疗
     ---@field public BeHealedAdjustment integer @受到治疗
     ---@field public Health integer @兵力
     ---@field public ComboRate integer @连击率
     ---@field public AttackIgnoreDefense integer @破甲
     ---@field public IntelligenceIgnoreDefense integer @看破
     ---@field public FinalDamageCoefficient integer @格挡
     ---@field public ArmyTypeQualification integer @兵种适应性
     ---@field public ActiveTacticProbability integer @主动战法发动率
     ---@field public PursueTacticProbability integer @追击战法发动率
     ---@field public SelfActiveTacticProbability integer @自带主动战法发动率
     ---@field public SelfPursueTacticProbability integer @自带追击战法发动率
     ---@field public DodgeRate integer @闪避率
     ---@field public ExtraAttackDamageAdjustment integer @最终造成兵刃伤害
     ---@field public HurtExtraAttackDamageAdjustment integer @最终受到兵刃伤害
     ---@field public ExtraIntelligenceDamageAdjustment integer @最终造成谋略伤害
     ---@field public HurtExtraIntelligenceDamageAdjustment integer @最终受到谋略伤害
    ['BattleAttributeType'] = {   Attack=1,  Intelligence=2,  RecoverableHealth=3,  Defense=4,  Speed=5,  AttackCritRate=6,  AttackCritDamage=7,  AttackDamageAdjustment=8,  AttackDamageFinalAdjustment=9,  HurtAttackAdjustment=10,  HurtAttackFinalAdjustment=11,  IntelligenceCritRate=12,  IntelligenceCritDamage=13,  IntelligenceDamageAdjustment=14,  IntelligenceDamageFinalAdjustment=15,  HurtIntelligenceAdjustment=16,  HurtIntelligenceFinalAdjustment=17,  HealAdjustment=18,  BeHealedAdjustment=19,  Health=20,  ComboRate=21,  AttackIgnoreDefense=22,  IntelligenceIgnoreDefense=23,  FinalDamageCoefficient=24,  ArmyTypeQualification=25,  ActiveTacticProbability=26,  PursueTacticProbability=27,  SelfActiveTacticProbability=28,  SelfPursueTacticProbability=29,  DodgeRate=30,  ExtraAttackDamageAdjustment=31,  HurtExtraAttackDamageAdjustment=32,  ExtraIntelligenceDamageAdjustment=33,  HurtExtraIntelligenceDamageAdjustment=34,  };
}

local beans = {}
    do
    ---@class battle.TableArmyTypeQualification 
     ---@field public id integer @索引
     ---@field public fix boolean @固定
     ---@field public target integer @修改目标
     ---@field public modify_map table<integer,integer> @修改字典
        local class = {
            { name='id', type='integer'},
            { name='fix', type='boolean'},
            { name='target', type='integer'},
            { name='modify_map', type='table<integer,integer>'},
        }
        beans['battle.TableArmyTypeQualification'] = class
    end
    do
    ---@class battle.TableBattleAttribute 
     ---@field public ENUMS string[] @枚举名,注释
     ---@field public default_value number @默认值
     ---@field public min_value number @最小值
     ---@field public max_value number @最大值
     ---@field public sprite_path string @图标
     ---@field public show_percent boolean @以百分比显示
        local class = {
            { name='ENUMS', type='string[]'},
            { name='default_value', type='number'},
            { name='min_value', type='number'},
            { name='max_value', type='number'},
            { name='sprite_path', type='string'},
            { name='show_percent', type='boolean'},
        }
        beans['battle.TableBattleAttribute'] = class
    end
    do
    ---@class battle.TableBattleBuff 
     ---@field public id integer @这是id
     ---@field public name string @名字
     ---@field public desc string @描述
     ---@field public show boolean @是否显示
     ---@field public record boolean @是否记录战报
     ---@field public adjunct boolean @是否是辅助buff
     ---@field public json_file string @技能图
     ---@field public type integer @类型
     ---@field public is_debuff boolean @是否负面
     ---@field public total_round integer @持续回合数
     ---@field public can_stack boolean @是否叠层
     ---@field public stack_rule integer @叠层规则
     ---@field public max_stack_count integer @最大堆叠层数
     ---@field public overlay_rule integer @叠加规则
     ---@field public icon_path string @图标
     ---@field public remove_timing integer @自然移除时机
     ---@field public dispellable boolean @是否可驱散
     ---@field public groupTypeList integer[] @buff组序号
     ---@field public sourceDeadClear boolean @来源死亡时是否清除
     ---@field public effect_id integer @buff特效
     ---@field public effect_socket string @特效挂接点
     ---@field public jump_on_add string @添加时跳字
     ---@field public jump_on_remove string @移除时跳字
     ---@field public max_combat integer @最大战斗次数
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='show', type='boolean'},
            { name='record', type='boolean'},
            { name='adjunct', type='boolean'},
            { name='json_file', type='string'},
            { name='type', type='integer'},
            { name='is_debuff', type='boolean'},
            { name='total_round', type='integer'},
            { name='can_stack', type='boolean'},
            { name='stack_rule', type='integer'},
            { name='max_stack_count', type='integer'},
            { name='overlay_rule', type='integer'},
            { name='icon_path', type='string'},
            { name='remove_timing', type='integer'},
            { name='dispellable', type='boolean'},
            { name='groupTypeList', type='integer[]'},
            { name='sourceDeadClear', type='boolean'},
            { name='effect_id', type='integer'},
            { name='effect_socket', type='string'},
            { name='jump_on_add', type='string'},
            { name='jump_on_remove', type='string'},
            { name='max_combat', type='integer'},
        }
        beans['battle.TableBattleBuff'] = class
    end
    do
    ---@class battle.TableBattleBuffGroup 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public conflict_rule integer @冲突规则
     ---@field public attribute_association integer @属性关联
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='conflict_rule', type='integer'},
            { name='attribute_association', type='integer'},
        }
        beans['battle.TableBattleBuffGroup'] = class
    end
    do
    ---@class battle.TableBattleEffectSound 
     ---@field public id integer
     ---@field public clip_asset table<integer,string> @资源路径
        local class = {
            { name='id', type='integer'},
            { name='clip_asset', type='table<integer,string>'},
        }
        beans['battle.TableBattleEffectSound'] = class
    end
    do
    ---@class battle.TableBattleFormation 
     ---@field public army_type integer @兵种
     ---@field public combat_status integer @战斗状态
     ---@field public main_status integer @主要状态
     ---@field public formation_name string @阵型名
     ---@field public direction_map integer[][] @六方向映射
        local class = {
            { name='army_type', type='integer'},
            { name='combat_status', type='integer'},
            { name='main_status', type='integer'},
            { name='formation_name', type='string'},
            { name='direction_map', type='integer[][]'},
        }
        beans['battle.TableBattleFormation'] = class
    end
    do
    ---@class battle.TableBattleFormulas 
     ---@field public Bparameter1 number @兵力/((10000-1)/(90-9))+9（兵刃谋略）
     ---@field public Bparameter2 number
     ---@field public Bparameter3 number
     ---@field public Bparameter4 number
     ---@field public Bparameter5 number
     ---@field public Bparameter6 number @if {兵力&lt;=2000,(兵力/11.5)},else{(15*兵力)^0.5}（兵刃谋略）
     ---@field public Bparameter7 number
     ---@field public Bparameter8 number
     ---@field public Bparameter9 number
     ---@field public Healparameter1 number @if {兵力&lt;=2000,(兵力/11.5)},else{(15*兵力)^1/2}
     ---@field public Healparameter2 number
     ---@field public Healparameter3 number
     ---@field public Healparameter4 number
     ---@field public Healparameter5 number
     ---@field public Healparameter6 number
     ---@field public Healparameter7 number
     ---@field public Cparameter1 number @(进攻方武力值C&#39;-防守方统帅值C&#39;&#39;*进攻方破甲百分比C&#39;&#39;)*1.1
     ---@field public Cparameter2 number @(进攻方智力值C&#39;-（防守方统帅值C&#39;&#39;+防守方智力值C&#39;&#39;&#39;)/2*进攻方看破百分比C&#39;&#39;&#39;&#39;)*1.1
     ---@field public E2parameter1 number @红度增伤,武将红度*0
     ---@field public E2parameter2 number @红度减伤,武将红度*0
     ---@field public Mparameter number @指定随机数，填0表示不指定
     ---@field public Mparameter1 number @随机数范围{0.95,1.05}
     ---@field public Mparameter2 number
        local class = {
            { name='Bparameter1', type='number'},
            { name='Bparameter2', type='number'},
            { name='Bparameter3', type='number'},
            { name='Bparameter4', type='number'},
            { name='Bparameter5', type='number'},
            { name='Bparameter6', type='number'},
            { name='Bparameter7', type='number'},
            { name='Bparameter8', type='number'},
            { name='Bparameter9', type='number'},
            { name='Healparameter1', type='number'},
            { name='Healparameter2', type='number'},
            { name='Healparameter3', type='number'},
            { name='Healparameter4', type='number'},
            { name='Healparameter5', type='number'},
            { name='Healparameter6', type='number'},
            { name='Healparameter7', type='number'},
            { name='Cparameter1', type='number'},
            { name='Cparameter2', type='number'},
            { name='E2parameter1', type='number'},
            { name='E2parameter2', type='number'},
            { name='Mparameter', type='number'},
            { name='Mparameter1', type='number'},
            { name='Mparameter2', type='number'},
        }
        beans['battle.TableBattleFormulas'] = class
    end
    do
    ---@class battle.TableBattleNPC 
     ---@field public Id integer @Id
     ---@field public HeroId integer @英雄id
     ---@field public Level integer @等级
     ---@field public Health integer @携带兵力
     ---@field public SelfTactics_Lv integer @自身战法等级
     ---@field public Tactics1 integer @主将携带战法1
     ---@field public Tactics1_Lv integer @主将携带战法1等级
     ---@field public Tactics2 integer @主将携带战法2
     ---@field public Tactics2_Lv integer @主将携带战法2等级
        local class = {
            { name='Id', type='integer'},
            { name='HeroId', type='integer'},
            { name='Level', type='integer'},
            { name='Health', type='integer'},
            { name='SelfTactics_Lv', type='integer'},
            { name='Tactics1', type='integer'},
            { name='Tactics1_Lv', type='integer'},
            { name='Tactics2', type='integer'},
            { name='Tactics2_Lv', type='integer'},
        }
        beans['battle.TableBattleNPC'] = class
    end
    do
    ---@class battle.TableBattleNPCTeam 
     ---@field public Team_Id integer @阵容ID
     ---@field public Army_Type integer @部队兵种
     ---@field public NpcIds table<integer,integer> @NpcId(主公，先锋，军师)
        local class = {
            { name='Team_Id', type='integer'},
            { name='Army_Type', type='integer'},
            { name='NpcIds', type='table<integer,integer>'},
        }
        beans['battle.TableBattleNPCTeam'] = class
    end
    do
    ---@class battle.TableBattleRecordText 
     ---@field public detailRecordType integer @战报类型
     ---@field public raw_txt string[] @文本，参数用%s代替，包含缩进
        local class = {
            { name='detailRecordType', type='integer'},
            { name='raw_txt', type='string[]'},
        }
        beans['battle.TableBattleRecordText'] = class
    end
    do
    ---@class battle.TableBattleTactic 
     ---@field public id integer @这是id
     ---@field public name string @名字
     ---@field public tactic_type integer @卡牌类型
     ---@field public probability string @概率公式
     ---@field public prepare_step integer @准备回合数
     ---@field public cd integer @cd回合
     ---@field public json_file string @json文件名
     ---@field public description string @描述
     ---@field public brief_description string @简短描述
     ---@field public sprite_path string @技能展示图
     ---@field public icon_path string @卡牌图
     ---@field public tactic_grade integer @品级
     ---@field public tactic_feature integer @战法特性
     ---@field public born_with boolean @是否武将自带战法
     ---@field public learnable boolean @是否武将可学习战法
     ---@field public TAGS string[] @标签
     ---@field public army_type_qualification_id integer @兵种适应性修改(仅兵种和阵法生效）
     ---@field public formulas table<string,string> @战斗编辑器用到的公式
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='tactic_type', type='integer'},
            { name='probability', type='string'},
            { name='prepare_step', type='integer'},
            { name='cd', type='integer'},
            { name='json_file', type='string'},
            { name='description', type='string'},
            { name='brief_description', type='string'},
            { name='sprite_path', type='string'},
            { name='icon_path', type='string'},
            { name='tactic_grade', type='integer'},
            { name='tactic_feature', type='integer'},
            { name='born_with', type='boolean'},
            { name='learnable', type='boolean'},
            { name='TAGS', type='string[]'},
            { name='army_type_qualification_id', type='integer'},
            { name='formulas', type='table<string,string>'},
        }
        beans['battle.TableBattleTactic'] = class
    end
    do
    ---@class battle.TableEventPriority 
     ---@field public battle_event integer @事件
     ---@field public name string @技能图名
     ---@field public priority integer @优先级
        local class = {
            { name='battle_event', type='integer'},
            { name='name', type='string'},
            { name='priority', type='integer'},
        }
        beans['battle.TableEventPriority'] = class
    end
    do
    ---@class battle.TableFormulaIndex 
     ---@field public name string @技能图名
     ---@field public key string @索引键值
     ---@field public var_name string @分段参数名
     ---@field public final_min number @值域下界
     ---@field public final_max number @值域上界
     ---@field public formulas string[] @公式
        local class = {
            { name='name', type='string'},
            { name='key', type='string'},
            { name='var_name', type='string'},
            { name='final_min', type='number'},
            { name='final_max', type='number'},
            { name='formulas', type='string[]'},
        }
        beans['battle.TableFormulaIndex'] = class
    end
    do
    ---@class battle.TableFormulaIndexName 
     ---@field public keys string[] @伤害率
        local class = {
            { name='keys', type='string[]'},
        }
        beans['battle.TableFormulaIndexName'] = class
    end
    do
    ---@class battle.TableHeroArmyType 
     ---@field public id integer @英雄兵种
     ---@field public name string @名字
     ---@field public full_name string @全名
     ---@field public counter integer @克制兵种
     ---@field public attack_counter_factor number @攻击克制系数
     ---@field public defence_counter_factor number @防御克制系数
     ---@field public army_type_category integer @所属兵种系
     ---@field public former_army_type integer @前置兵种
     ---@field public sprite_path string @图标
     ---@field public sprite2_path string @图标2
     ---@field public sprite_L string
     ---@field public sprite_hud string
     ---@field public prefab string
     ---@field public color string @颜色
     ---@field public desc string @描述
     ---@field public termId integer @词条说明
     ---@field public termId2 integer @词条说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='full_name', type='string'},
            { name='counter', type='integer'},
            { name='attack_counter_factor', type='number'},
            { name='defence_counter_factor', type='number'},
            { name='army_type_category', type='integer'},
            { name='former_army_type', type='integer'},
            { name='sprite_path', type='string'},
            { name='sprite2_path', type='string'},
            { name='sprite_L', type='string'},
            { name='sprite_hud', type='string'},
            { name='prefab', type='string'},
            { name='color', type='string'},
            { name='desc', type='string'},
            { name='termId', type='integer'},
            { name='termId2', type='integer'},
        }
        beans['battle.TableHeroArmyType'] = class
    end
    do
    ---@class battle.TableHeroConst 
     ---@field public HeroMaxStars integer @武将最大红度
     ---@field public AddPointByLevel integer @武将每x级可加点
     ---@field public AddPointByStars integer @武将每x红度可加点
     ---@field public AddPointStep integer @单次增加的加点数值
     ---@field public HeroMinLevel integer @武将最低等级
     ---@field public HeroMaxLevel integer @武将最高等级
     ---@field public SoldierMaxInit integer @武将初始带兵数量
     ---@field public SoldierMaxAddPerLevel integer @武将初始带兵数量
        local class = {
            { name='HeroMaxStars', type='integer'},
            { name='AddPointByLevel', type='integer'},
            { name='AddPointByStars', type='integer'},
            { name='AddPointStep', type='integer'},
            { name='HeroMinLevel', type='integer'},
            { name='HeroMaxLevel', type='integer'},
            { name='SoldierMaxInit', type='integer'},
            { name='SoldierMaxAddPerLevel', type='integer'},
        }
        beans['battle.TableHeroConst'] = class
    end
    do
    ---@class battle.TableHeroDynasty 
     ---@field public id integer @英雄阵营
     ---@field public name string @名字
     ---@field public full_name string @全名
     ---@field public sprite_path string @图标
     ---@field public color_sprite_path string
     ---@field public color string @颜色
     ---@field public desc string @描述
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='full_name', type='string'},
            { name='sprite_path', type='string'},
            { name='color_sprite_path', type='string'},
            { name='color', type='string'},
            { name='desc', type='string'},
        }
        beans['battle.TableHeroDynasty'] = class
    end
    do
    ---@class battle.TableTacticConst 
     ---@field public TacticMaxStars integer @战法最大红度
     ---@field public TacticMaxLevel integer @战法最大等级
     ---@field public TacticUpgradeCost table<integer,integer> @战法升级消耗
     ---@field public TacticUpgradeCostId integer @战法升级消耗货币
     ---@field public TacticRenewDesc string @重置战法界面提示
     ---@field public TacticRenewConfirmTopTip string @重置战法界面提示
     ---@field public TacticRenewConfirmBottomTip string @重置战法界面提示
     ---@field public TacticRenewCost integer @重置战法资源花费
        local class = {
            { name='TacticMaxStars', type='integer'},
            { name='TacticMaxLevel', type='integer'},
            { name='TacticUpgradeCost', type='table<integer,integer>'},
            { name='TacticUpgradeCostId', type='integer'},
            { name='TacticRenewDesc', type='string'},
            { name='TacticRenewConfirmTopTip', type='string'},
            { name='TacticRenewConfirmBottomTip', type='string'},
            { name='TacticRenewCost', type='integer'},
        }
        beans['battle.TableTacticConst'] = class
    end
    do
    ---@class battle.TableTemplateHero 
     ---@field public id integer @英雄id
     ---@field public name string @名字
     ---@field public rarity integer @稀有度
     ---@field public dynasty integer @阵营
     ---@field public armyType_qualification table<integer,integer> @兵种适应性
     ---@field public title integer @身份
     ---@field public role integer[] @定位
     ---@field public recommended_tactics integer[] @推荐战法Id
     ---@field public primary_attr integer @主属性
     ---@field public Attack integer @初始武力
     ---@field public Intelligence integer @初始智力
     ---@field public Defense integer @初始统帅
     ---@field public Speed integer @初始速度
     ---@field public attack_lv number @每级成长武力
     ---@field public intelligence_lv number @每级成长智力
     ---@field public defense_lv number @每级成长统帅
     ---@field public speed_lv number @每级成长速度
     ---@field public AttackCritRate number @兵刃暴击率
     ---@field public AttackCritDamage number @兵刃暴击伤害倍率
     ---@field public IntelligenceCritRate number @谋略暴击率
     ---@field public IntelligenceCritDamage number @谋略暴击伤害倍率
     ---@field public selfTacticId integer @自身战法Id
     ---@field public prefab_path string @模型路径
     ---@field public avatar_path string @头像图片
     ---@field public avatar_sq_path string @头像图片(方)
     ---@field public sprite_path string @展示图片路径
     ---@field public sprite_L_path string @全身像路径
     ---@field public audio_group integer @音效组
     ---@field public is_female boolean @性别
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='rarity', type='integer'},
            { name='dynasty', type='integer'},
            { name='armyType_qualification', type='table<integer,integer>'},
            { name='title', type='integer'},
            { name='role', type='integer[]'},
            { name='recommended_tactics', type='integer[]'},
            { name='primary_attr', type='integer'},
            { name='Attack', type='integer'},
            { name='Intelligence', type='integer'},
            { name='Defense', type='integer'},
            { name='Speed', type='integer'},
            { name='attack_lv', type='number'},
            { name='intelligence_lv', type='number'},
            { name='defense_lv', type='number'},
            { name='speed_lv', type='number'},
            { name='AttackCritRate', type='number'},
            { name='AttackCritDamage', type='number'},
            { name='IntelligenceCritRate', type='number'},
            { name='IntelligenceCritDamage', type='number'},
            { name='selfTacticId', type='integer'},
            { name='prefab_path', type='string'},
            { name='avatar_path', type='string'},
            { name='avatar_sq_path', type='string'},
            { name='sprite_path', type='string'},
            { name='sprite_L_path', type='string'},
            { name='audio_group', type='integer'},
            { name='is_female', type='boolean'},
        }
        beans['battle.TableTemplateHero'] = class
    end
    do
    ---@class battle.TacticTuple 
     ---@field public id integer @战斗战法
     ---@field public level integer @战法等级
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
        }
        beans['battle.TacticTuple'] = class
    end
    do
    ---@class color 
     ---@field public r number
     ---@field public g number
     ---@field public b number
     ---@field public a number
        local class = {
            { name='r', type='number'},
            { name='g', type='number'},
            { name='b', type='number'},
            { name='a', type='number'},
        }
        beans['color'] = class
    end
    do
    ---@class common.CardData 
     ---@field public id integer @ID
     ---@field public level integer @等级
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
        }
        beans['common.CardData'] = class
    end
    do
    ---@class common.Reward 
        local class = {
        }
        beans['common.Reward'] = class
    end
    do
    ---@class common.RewardCoin :common.Reward 
     ---@field public coin table<integer,integer> @货币奖励
        local class = {
            { name='coin', type='table<integer,integer>'},
        }
        beans['common.RewardCoin'] = class
    end
    do
    ---@class common.RewardCoinItem :common.Reward 
     ---@field public coin table<integer,integer> @货币奖励
     ---@field public item table<integer,integer> @道具奖励
        local class = {
            { name='coin', type='table<integer,integer>'},
            { name='item', type='table<integer,integer>'},
        }
        beans['common.RewardCoinItem'] = class
    end
    do
    ---@class common.ScheduleStageBeginDay 
     ---@field public day integer @天
        local class = {
            { name='day', type='integer'},
        }
        beans['common.ScheduleStageBeginDay'] = class
    end
    do
    ---@class common.ScheduleStages 
     ---@field public StageId string @阶段id
     ---@field public BeginDay table<string,integer> @周期开始天数
     ---@field public BeginTime integer[] @周期开始时间
        local class = {
            { name='StageId', type='string'},
            { name='BeginDay', type='table<string,integer>'},
            { name='BeginTime', type='integer[]'},
        }
        beans['common.ScheduleStages'] = class
    end
    do
    ---@class common.TableActorContainer 
     ---@field public id integer
     ---@field public first integer[] @主动方类型
     ---@field public second integer[] @被动方类型
     ---@field public relation integer[] @first看来和second的关系
     ---@field public valid integer @能建立的容器关系
        local class = {
            { name='id', type='integer'},
            { name='first', type='integer[]'},
            { name='second', type='integer[]'},
            { name='relation', type='integer[]'},
            { name='valid', type='integer'},
        }
        beans['common.TableActorContainer'] = class
    end
    do
    ---@class common.TableActorType 
     ---@field public id integer @Actor类型
        local class = {
            { name='id', type='integer'},
        }
        beans['common.TableActorType'] = class
    end
    do
    ---@class common.TableBahaviorConfig 
     ---@field public id integer @索引
     ---@field public main integer[] @主状态
     ---@field public combat integer[] @战斗状态
     ---@field public deploy integer[] @调动状态
     ---@field public name string @显示名
     ---@field public icon string @显示图标
        local class = {
            { name='id', type='integer'},
            { name='main', type='integer[]'},
            { name='combat', type='integer[]'},
            { name='deploy', type='integer[]'},
            { name='name', type='string'},
            { name='icon', type='string'},
        }
        beans['common.TableBahaviorConfig'] = class
    end
    do
    ---@class common.TableBattleConfig 
     ---@field public MaxRound integer @单局战斗最大回合数
     ---@field public MaxContinousDraw integer @最大连续平局次数
     ---@field public MaxBattleFormations integer @玩家最大编队数量
     ---@field public PlainAttackTacticId integer @普攻战法ID
     ---@field public BattleFormationPositionMap table<integer,integer[]> @阵型位置
     ---@field public BattleDefaultFormationName string @默认阵型名
     ---@field public PositionMap vector3[][] @角色位置
     ---@field public DirectionMap vector3[][] @角色位置
     ---@field public CharacterScale number @角色缩放
     ---@field public BackToPositionDurationRoundEnd number @大轮次结束后角色归位的时间
     ---@field public BackToPositionDurationActionEnd number @回合结束后角色归位的时间
     ---@field public DefaultColor color @英雄默认颜色
     ---@field public EffectFadeDuration number @特效淡出时间
     ---@field public CharacterAppearEffectDuration number @角色出现的特效时长
     ---@field public CharacterDieAnimationDuration number @角色死亡的动画时长
     ---@field public WaitTimeWhenFirstRoundStart number @第一回合开始的等待时间
     ---@field public HeroTurnStartWaitTime number @英雄轮次开始时，等待的时间
     ---@field public HeroTurnEndWaitTime number @英雄轮次结束时，等待的时间
     ---@field public CameraFullView table<string,number> @全景相机参数
     ---@field public CameraFocusView table<string,number> @聚焦相机参数
     ---@field public CameraTransitionTime table<string,number> @相机变化时间
     ---@field public RoundStartSoundId integer @回合开始音效id
     ---@field public SkillComboTimeScale number @连击时停时间缩放
     ---@field public AnimationTransition number @动画过渡时长
     ---@field public AngleOffsetWhenOccupied number[] @移动时目标位置如果被占用，偏移角顺序
     ---@field public CameraFollowNode string @Follow相机绑定的节点（空为根节点）
     ---@field public AutoCullFarthestDistanceFactor number @切镜自动裁剪角色的最大距离参数
     ---@field public SlgBattleRoundInterval number @沙盘战斗每回合时间
     ---@field public UISlgBattleTacticInterval number @沙盘战斗UI释放战法时间间隔
     ---@field public UISlgBattleModifyHealthInterval number @沙盘战斗UI修改血量时间间隔
     ---@field public UIShowBattleResultDuration number @沙盘战斗UI展示结果时长
     ---@field public UIAllyHpBarColor color @我方血条颜色
     ---@field public UIAllyHpTxtColor color @我方血量数值颜色
     ---@field public UICardActionSpeed number @出牌动画的速度，原速是1.0
     ---@field public UICardActionDuration number @卡牌自身的显示时长
     ---@field public UIAttackDamageStringColor color @兵刃伤害跳字颜色
     ---@field public UIIntelligenceDamageStringColor color @谋略伤害跳字颜色
     ---@field public UIRealityDamageStringColor color @真实伤害跳字颜色
     ---@field public UIChainDamageStringColor color @连锁伤害跳字颜瑟
     ---@field public UIHealStringColor color @治疗跳字颜色
     ---@field public UIDamageStringScaleMap number[][] @伤害跳字大小映射
     ---@field public UIHealStringScaleMap number[][] @治疗跳字大小映射
     ---@field public UIBattleToastShowDuration number @战斗信息提示（开始/胜利/失败等）显示时长
     ---@field public UIBattleToastFadeOutDuration number @战斗信息提示淡出时长
     ---@field public UIBattleToastBgPath table<string,string> @战斗信息提示图片路径
     ---@field public UIBattleToastText table<string,string> @战斗信息提示文本
     ---@field public UIBattleToastColor table<string,color> @战斗信息提示颜色
     ---@field public UIAllySpeedBarFrameColor color @我方速度条头像框颜色
     ---@field public UIEnemySpeedBarFrameColor color @敌方速度条头像框颜色
     ---@field public UITacticGradeBgPath table<integer,string> @卡牌品级图标路径
     ---@field public UITacticTypeName table<integer,string> @卡牌类型名
     ---@field public UIShowCardsAnimationDuration number @卡牌出现动画时长
     ---@field public UIHideCardsAnimationDuration number @卡牌小时动画时长
     ---@field public UICardUpAnimationDuration number @使用卡牌时上浮动画时长
     ---@field public UICardDownAnimationDuration number @收回卡牌时下沉动画时长
     ---@field public UICardUpDistance number @使用卡牌时上浮的距离
     ---@field public UINormalCardBg string @正常卡牌背景
     ---@field public UITempCardBg string @临时卡牌背景
     ---@field public UIAddCardSourceOffset vector3 @添加卡牌动画起点距角色的偏移
     ---@field public UIAddCardTargetOffset vector3 @添加卡牌动画终点距角色的偏移
     ---@field public UIAddCardAnimationShowDuration number @添加卡牌动画展示卡牌时间
     ---@field public UIAddCardAnimationFlyDuration number @添加卡牌动画卡牌飞行时间
     ---@field public UITempCardFadeOutDuration number @临时卡牌打出时渐隐的时长
     ---@field public UIShowCardFadeInDuration number @卡牌展示出现动画时长
     ---@field public UIShowCardFadeOutDuration number @卡牌展示消失动画时长
     ---@field public HeroIntHealth number @1级武将最大可装配兵力
     ---@field public HeroHealthGrowth number @武将每级最大可装配兵力成长值
     ---@field public DamageChangeToRecoverableHealthScale number @受到伤害时，受到伤害的90%立刻转换为伤兵
     ---@field public RoundEndChangeToRecoverableHealthScale number @每个大回合结束时，单个武将的所有伤兵 会转换10%为死兵
     ---@field public BattleEndChangeToRecoverableHealthScale number @单场战斗结束后，单个武将的所有伤兵 会转换30%为死兵
     ---@field public CaptainDeadChangeToRecoverableHealthScale number @主将死亡后，先锋和军师若存活，则当前兵力立刻转换10%为死兵
     ---@field public BattleRecordDamageColorString string @战报伤害颜色
     ---@field public BattleRecordDamageCirtColorString string @战报暴击伤害颜色
     ---@field public BattleRecordHealColorString string @战报治疗颜色
        local class = {
            { name='MaxRound', type='integer'},
            { name='MaxContinousDraw', type='integer'},
            { name='MaxBattleFormations', type='integer'},
            { name='PlainAttackTacticId', type='integer'},
            { name='BattleFormationPositionMap', type='table<integer,integer[]>'},
            { name='BattleDefaultFormationName', type='string'},
            { name='PositionMap', type='vector3[][]'},
            { name='DirectionMap', type='vector3[][]'},
            { name='CharacterScale', type='number'},
            { name='BackToPositionDurationRoundEnd', type='number'},
            { name='BackToPositionDurationActionEnd', type='number'},
            { name='DefaultColor', type='color'},
            { name='EffectFadeDuration', type='number'},
            { name='CharacterAppearEffectDuration', type='number'},
            { name='CharacterDieAnimationDuration', type='number'},
            { name='WaitTimeWhenFirstRoundStart', type='number'},
            { name='HeroTurnStartWaitTime', type='number'},
            { name='HeroTurnEndWaitTime', type='number'},
            { name='CameraFullView', type='table<string,number>'},
            { name='CameraFocusView', type='table<string,number>'},
            { name='CameraTransitionTime', type='table<string,number>'},
            { name='RoundStartSoundId', type='integer'},
            { name='SkillComboTimeScale', type='number'},
            { name='AnimationTransition', type='number'},
            { name='AngleOffsetWhenOccupied', type='number[]'},
            { name='CameraFollowNode', type='string'},
            { name='AutoCullFarthestDistanceFactor', type='number'},
            { name='SlgBattleRoundInterval', type='number'},
            { name='UISlgBattleTacticInterval', type='number'},
            { name='UISlgBattleModifyHealthInterval', type='number'},
            { name='UIShowBattleResultDuration', type='number'},
            { name='UIAllyHpBarColor', type='color'},
            { name='UIAllyHpTxtColor', type='color'},
            { name='UICardActionSpeed', type='number'},
            { name='UICardActionDuration', type='number'},
            { name='UIAttackDamageStringColor', type='color'},
            { name='UIIntelligenceDamageStringColor', type='color'},
            { name='UIRealityDamageStringColor', type='color'},
            { name='UIChainDamageStringColor', type='color'},
            { name='UIHealStringColor', type='color'},
            { name='UIDamageStringScaleMap', type='number[][]'},
            { name='UIHealStringScaleMap', type='number[][]'},
            { name='UIBattleToastShowDuration', type='number'},
            { name='UIBattleToastFadeOutDuration', type='number'},
            { name='UIBattleToastBgPath', type='table<string,string>'},
            { name='UIBattleToastText', type='table<string,string>'},
            { name='UIBattleToastColor', type='table<string,color>'},
            { name='UIAllySpeedBarFrameColor', type='color'},
            { name='UIEnemySpeedBarFrameColor', type='color'},
            { name='UITacticGradeBgPath', type='table<integer,string>'},
            { name='UITacticTypeName', type='table<integer,string>'},
            { name='UIShowCardsAnimationDuration', type='number'},
            { name='UIHideCardsAnimationDuration', type='number'},
            { name='UICardUpAnimationDuration', type='number'},
            { name='UICardDownAnimationDuration', type='number'},
            { name='UICardUpDistance', type='number'},
            { name='UINormalCardBg', type='string'},
            { name='UITempCardBg', type='string'},
            { name='UIAddCardSourceOffset', type='vector3'},
            { name='UIAddCardTargetOffset', type='vector3'},
            { name='UIAddCardAnimationShowDuration', type='number'},
            { name='UIAddCardAnimationFlyDuration', type='number'},
            { name='UITempCardFadeOutDuration', type='number'},
            { name='UIShowCardFadeInDuration', type='number'},
            { name='UIShowCardFadeOutDuration', type='number'},
            { name='HeroIntHealth', type='number'},
            { name='HeroHealthGrowth', type='number'},
            { name='DamageChangeToRecoverableHealthScale', type='number'},
            { name='RoundEndChangeToRecoverableHealthScale', type='number'},
            { name='BattleEndChangeToRecoverableHealthScale', type='number'},
            { name='CaptainDeadChangeToRecoverableHealthScale', type='number'},
            { name='BattleRecordDamageColorString', type='string'},
            { name='BattleRecordDamageCirtColorString', type='string'},
            { name='BattleRecordHealColorString', type='string'},
        }
        beans['common.TableBattleConfig'] = class
    end
    do
    ---@class common.TableCareerConst 
     ---@field public STRATEGY_POINT_RECOVER_TIME integer @30分钟一点
     ---@field public STRATEGY_POINT_MAX integer @最大策点数量
     ---@field public WU_GU_MAX_LEVEL_UP integer @五谷丰登最大可提升至等级
     ---@field public WU_GU_MAX_LEVEL_UP_GRID integer @单玩家可受五谷丰登提升等级的最多地块数量
        local class = {
            { name='STRATEGY_POINT_RECOVER_TIME', type='integer'},
            { name='STRATEGY_POINT_MAX', type='integer'},
            { name='WU_GU_MAX_LEVEL_UP', type='integer'},
            { name='WU_GU_MAX_LEVEL_UP_GRID', type='integer'},
        }
        beans['common.TableCareerConst'] = class
    end
    do
    ---@class common.TableCareerTelent 
     ---@field public id integer @天赋id
     ---@field public name string @天赋名称
     ---@field public desc string @天赋描述
     ---@field public Strategy integer @关联策牌
     ---@field public TallentEffect Talent.BaseTalent
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='Strategy', type='integer'},
            { name='TallentEffect', type='Talent.BaseTalent'},
        }
        beans['common.TableCareerTelent'] = class
    end
    do
    ---@class common.TableCareerTree 
     ---@field public id integer @树节点id
     ---@field public front_node integer[] @前置节点id
     ---@field public FIELD_MASTER integer @田师
     ---@field public name string @天赋名称
     ---@field public desc string[] @天赋描述
     ---@field public sprite_path string @展示图片路径
        local class = {
            { name='id', type='integer'},
            { name='front_node', type='integer[]'},
            { name='FIELD_MASTER', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string[]'},
            { name='sprite_path', type='string'},
        }
        beans['common.TableCareerTree'] = class
    end
    do
    ---@class common.TableCommonConfig 
     ---@field public ToastShowTime number @提示显示时间
     ---@field public DynastyIconMap table<integer,string> @王朝(魏蜀吴群)图标
        local class = {
            { name='ToastShowTime', type='number'},
            { name='DynastyIconMap', type='table<integer,string>'},
        }
        beans['common.TableCommonConfig'] = class
    end
    do
    ---@class common.TableCurrency 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public desc string @描述
     ---@field public init_value integer @货币初始值
     ---@field public max_value integer @货币上限
     ---@field public icon_path string @icon路径
     ---@field public icon_min_path string @icon_min_路径
     ---@field public using_Data string[] @恭喜获得界面使用提示
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='init_value', type='integer'},
            { name='max_value', type='integer'},
            { name='icon_path', type='string'},
            { name='icon_min_path', type='string'},
            { name='using_Data', type='string[]'},
        }
        beans['common.TableCurrency'] = class
    end
    do
    ---@class common.TableEffect 
     ---@field public id integer
     ---@field public asset_path string @资源路径
     ---@field public is_loop boolean @是否循环
     ---@field public is_pic boolean @是否是图片特效
     ---@field public duration number @持续时间
     ---@field public scale number @缩放
     ---@field public is_hero_effect boolean @是否英雄特效
     ---@field public auto_destroy boolean @自动销毁
        local class = {
            { name='id', type='integer'},
            { name='asset_path', type='string'},
            { name='is_loop', type='boolean'},
            { name='is_pic', type='boolean'},
            { name='duration', type='number'},
            { name='scale', type='number'},
            { name='is_hero_effect', type='boolean'},
            { name='auto_destroy', type='boolean'},
        }
        beans['common.TableEffect'] = class
    end
    do
    ---@class common.TableEquip 
     ---@field public id integer @装备id
     ---@field public name string @装备名
     ---@field public equip_type integer @装备类型
     ---@field public main_prop integer @固定主属性
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='equip_type', type='integer'},
            { name='main_prop', type='integer'},
        }
        beans['common.TableEquip'] = class
    end
    do
    ---@class common.TableEquipConst 
     ---@field public EQUIP_PROPS_TYPE integer[] @装备基础属性类型
     ---@field public QUALITY_TO_PROPS_ADD table<integer,integer> @装备稀有度对应基础属性增幅
     ---@field public BUILD_EQUIP_COST_CURRENCY table<integer,integer> @打造装备消耗货币
     ---@field public BUILD_EQUIP_COST_ITEM table<integer,integer> @打造装备消耗道具
     ---@field public MAX_CONTINUOUS_BUILD integer @最大连续打造次数
     ---@field public FORGE_COST_ITEM table<integer,integer> @锻造消耗道具
     ---@field public FORGE_GUARANTEE_TIME integer @锻造保底次数
     ---@field public FORGE_GUARANTEE_UP_RANGE integer[] @锻造保底触发数值提升范围
     ---@field public FORGE_NORMAL_UP_RANGE integer @单次锻造最大可能提升范围
     ---@field public FORGE_DOUBLE_MAX_DIST integer @锻造时双词条最大差距
     ---@field public REBUILD_COST_ITEM table<integer,integer> @重塑消耗道具
     ---@field public REBUILD_QUALITY_RATIO table<integer,table<integer,integer>> @重塑特效品质概率
     ---@field public DECOMPOSE_EQUIP_REWARD table<integer,table<integer,integer>> @装备分解奖励
     ---@field public TRAIN_HORSE_COST_CURRENCY table<integer,integer> @驯马消耗货币
     ---@field public TRAIN_HORSE_COST_TIME integer @驯马消耗时间
     ---@field public SOLD_HORSE_REWARD_CURRENCY table<integer,table<integer,integer>> @出售马匹获得货币
     ---@field public EQUIP_SLOTS integer[] @装备类型
     ---@field public ENTRIES_BASE_VALUE table<integer,integer> @装备词条基础值
     ---@field public DOUBLE_REVERSE_POSITIVE_ADD integer @正负词条正词条加成(%)
     ---@field public DOUBLE_REVERSE_NEGATIVE_ADD integer @正负词条负词条加成(%)
     ---@field public INIT_ENTRY_VALUE_RATIO integer[] @初始词条进度(%)
        local class = {
            { name='EQUIP_PROPS_TYPE', type='integer[]'},
            { name='QUALITY_TO_PROPS_ADD', type='table<integer,integer>'},
            { name='BUILD_EQUIP_COST_CURRENCY', type='table<integer,integer>'},
            { name='BUILD_EQUIP_COST_ITEM', type='table<integer,integer>'},
            { name='MAX_CONTINUOUS_BUILD', type='integer'},
            { name='FORGE_COST_ITEM', type='table<integer,integer>'},
            { name='FORGE_GUARANTEE_TIME', type='integer'},
            { name='FORGE_GUARANTEE_UP_RANGE', type='integer[]'},
            { name='FORGE_NORMAL_UP_RANGE', type='integer'},
            { name='FORGE_DOUBLE_MAX_DIST', type='integer'},
            { name='REBUILD_COST_ITEM', type='table<integer,integer>'},
            { name='REBUILD_QUALITY_RATIO', type='table<integer,table<integer,integer>>'},
            { name='DECOMPOSE_EQUIP_REWARD', type='table<integer,table<integer,integer>>'},
            { name='TRAIN_HORSE_COST_CURRENCY', type='table<integer,integer>'},
            { name='TRAIN_HORSE_COST_TIME', type='integer'},
            { name='SOLD_HORSE_REWARD_CURRENCY', type='table<integer,table<integer,integer>>'},
            { name='EQUIP_SLOTS', type='integer[]'},
            { name='ENTRIES_BASE_VALUE', type='table<integer,integer>'},
            { name='DOUBLE_REVERSE_POSITIVE_ADD', type='integer'},
            { name='DOUBLE_REVERSE_NEGATIVE_ADD', type='integer'},
            { name='INIT_ENTRY_VALUE_RATIO', type='integer[]'},
        }
        beans['common.TableEquipConst'] = class
    end
    do
    ---@class common.TableEquipEffect 
     ---@field public id integer @特效id
     ---@field public name string @特效名
     ---@field public quality integer @特效品质
     ---@field public equip integer @专属装备
     ---@field public hero integer @专属武将
     ---@field public special_effect equip.Effect @特殊效果
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='quality', type='integer'},
            { name='equip', type='integer'},
            { name='hero', type='integer'},
            { name='special_effect', type='equip.Effect'},
        }
        beans['common.TableEquipEffect'] = class
    end
    do
    ---@class common.TableEquipQualityRatio 
     ---@field public id integer @特效品质
     ---@field public base_build_ratio integer @基础打造品质概率
     ---@field public build_entry_ratio table<integer,integer>
     ---@field public build_effect_ratio table<integer,integer> @打造出品质特效概率
        local class = {
            { name='id', type='integer'},
            { name='base_build_ratio', type='integer'},
            { name='build_entry_ratio', type='table<integer,integer>'},
            { name='build_effect_ratio', type='table<integer,integer>'},
        }
        beans['common.TableEquipQualityRatio'] = class
    end
    do
    ---@class common.TableHeroAttrType 
     ---@field public id integer @英雄属性
     ---@field public name string @名字
     ---@field public full_name string @全名
     ---@field public termId integer @词条说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='full_name', type='string'},
            { name='termId', type='integer'},
        }
        beans['common.TableHeroAttrType'] = class
    end
    do
    ---@class common.TableHeroEffect 
     ---@field public id integer
     ---@field public hero_id integer @英雄id
     ---@field public asset_path string @资源路径
     ---@field public is_loop boolean @是否循环
     ---@field public is_pic boolean @是否是图片特效
     ---@field public duration number @持续时间
     ---@field public scale number @缩放
        local class = {
            { name='id', type='integer'},
            { name='hero_id', type='integer'},
            { name='asset_path', type='string'},
            { name='is_loop', type='boolean'},
            { name='is_pic', type='boolean'},
            { name='duration', type='number'},
            { name='scale', type='number'},
        }
        beans['common.TableHeroEffect'] = class
    end
    do
    ---@class common.TableHeroRoleType 
     ---@field public id integer @英雄定位
     ---@field public name string @名字
     ---@field public sprite_path string @图标
     ---@field public termId integer @词条说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='sprite_path', type='string'},
            { name='termId', type='integer'},
        }
        beans['common.TableHeroRoleType'] = class
    end
    do
    ---@class common.TableHeroSuitRank 
     ---@field public id integer @英雄适性
     ---@field public name string @名字
     ---@field public content string @文字
     ---@field public advise_value integer @推荐值
     ---@field public format string @文字格式
     ---@field public format_full string @文字格式(全)
     ---@field public bg_path string @底框图片
     ---@field public color_top color @颜色
     ---@field public color_bottom color @颜色
     ---@field public termId integer
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='content', type='string'},
            { name='advise_value', type='integer'},
            { name='format', type='string'},
            { name='format_full', type='string'},
            { name='bg_path', type='string'},
            { name='color_top', type='color'},
            { name='color_bottom', type='color'},
            { name='termId', type='integer'},
        }
        beans['common.TableHeroSuitRank'] = class
    end
    do
    ---@class common.TableHeroTitleType 
     ---@field public id integer @英雄身份
     ---@field public name string @名字
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
        }
        beans['common.TableHeroTitleType'] = class
    end
    do
    ---@class common.TableHomeBuilding 
     ---@field public id integer @建筑id
     ---@field public level integer @等级
     ---@field public func home.BuildingFunc @建筑功能
     ---@field public valueList integer[] @建筑加成参数
     ---@field public cost table<integer,integer> @建造&amp;升级消耗
     ---@field public prosperity integer @繁荣度
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
            { name='func', type='home.BuildingFunc'},
            { name='valueList', type='integer[]'},
            { name='cost', type='table<integer,integer>'},
            { name='prosperity', type='integer'},
        }
        beans['common.TableHomeBuilding'] = class
    end
    do
    ---@class common.TableHomeBuildingConst 
     ---@field public InitBuilding integer[] @初始建筑
     ---@field public RecruitmentInstituteRate integer @征兵所速度倍率
        local class = {
            { name='InitBuilding', type='integer[]'},
            { name='RecruitmentInstituteRate', type='integer'},
        }
        beans['common.TableHomeBuildingConst'] = class
    end
    do
    ---@class common.TableHomeBuildingType 
     ---@field public id integer @建筑类型
     ---@field public name string @名称
     ---@field public maxLevel integer @最高等级
     ---@field public prefab_path string @模型路径
     ---@field public UI_path string @2DUI路径
     ---@field public brief_desc string @简短描述
     ---@field public desc string @描述
     ---@field public add_desc string[] @升级加成描述
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='maxLevel', type='integer'},
            { name='prefab_path', type='string'},
            { name='UI_path', type='string'},
            { name='brief_desc', type='string'},
            { name='desc', type='string'},
            { name='add_desc', type='string[]'},
        }
        beans['common.TableHomeBuildingType'] = class
    end
    do
    ---@class common.TableHomeTech 
     ---@field public id integer @科技id
     ---@field public maxLevel integer @最高等级
     ---@field public name string @科技名
     ---@field public func homeTech.TechFunc @建筑功能
     ---@field public pos integer[] @位于科技树的位置（列，行）
     ---@field public cost table<integer,integer[]> @建造&amp;升级消耗
     ---@field public prosperity integer[] @繁荣度
     ---@field public icon_path string @icon资源路径
     ---@field public desc string[] @太学加成描述
     ---@field public additionalConditions integer[] @额外解锁条件
     ---@field public specialBonus homeTech.SpecialBonus @描述
        local class = {
            { name='id', type='integer'},
            { name='maxLevel', type='integer'},
            { name='name', type='string'},
            { name='func', type='homeTech.TechFunc'},
            { name='pos', type='integer[]'},
            { name='cost', type='table<integer,integer[]>'},
            { name='prosperity', type='integer[]'},
            { name='icon_path', type='string'},
            { name='desc', type='string[]'},
            { name='additionalConditions', type='integer[]'},
            { name='specialBonus', type='homeTech.SpecialBonus'},
        }
        beans['common.TableHomeTech'] = class
    end
    do
    ---@class common.TableHorse 
     ---@field public id integer @坐骑id
     ---@field public name string @坐骑名
     ---@field public equip_type integer @装备类型
     ---@field public quality integer @坐骑品质
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='equip_type', type='integer'},
            { name='quality', type='integer'},
        }
        beans['common.TableHorse'] = class
    end
    do
    ---@class common.TableIdSegment 
     ---@field public id integer @id段枚举
     ---@field public id_start integer @id段起始
     ---@field public id_end integer @id段结束
     ---@field public table_name string @导出表名
        local class = {
            { name='id', type='integer'},
            { name='id_start', type='integer'},
            { name='id_end', type='integer'},
            { name='table_name', type='string'},
        }
        beans['common.TableIdSegment'] = class
    end
    do
    ---@class common.TableItem 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public cap integer @持有上限
     ---@field public expire integer @过期时长/秒
     ---@field public red_expire integer @红显时间/秒
     ---@field public active_use boolean @能否主动使用
     ---@field public description string @描述
     ---@field public access string[]
     ---@field public use_func item.UseFunc
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='cap', type='integer'},
            { name='expire', type='integer'},
            { name='red_expire', type='integer'},
            { name='active_use', type='boolean'},
            { name='description', type='string'},
            { name='access', type='string[]'},
            { name='use_func', type='item.UseFunc'},
        }
        beans['common.TableItem'] = class
    end
    do
    ---@class common.TableItemConst 
     ---@field public SINGLE_USE_LIMIT integer @单次使用道具的上限
     ---@field public ITEM_BAG_RED_SHOW integer @背包格子红显数量
     ---@field public HERO_TOKEN_COST integer[] @武将升红需要信物
     ---@field public TACTIC_TOKEN_COST integer[] @战法升红需要信物
     ---@field public ITEM_BAG_CAP integer @背包格子数量上限
     ---@field public EQUIP_BAG_CAP integer @装备背包格子数量上限
     ---@field public HORSE_BAG_CAP integer @坐骑背包格子数量上限
     ---@field public TOKEN_BAG_CAP integer @信物背包格子数量上限
     ---@field public MATERIAL_BAG_CAP integer @材料背包格子数量上限
     ---@field public BAG_ITEM_COL_ROW_MIN_SIZE vector2 @背包格子最少列行数量
     ---@field public TACTIC_TOKEN_NAME_WRAP string @信物名称装饰
     ---@field public HERO_TOKEN_NAME_WRAP string @将魂名称装饰
        local class = {
            { name='SINGLE_USE_LIMIT', type='integer'},
            { name='ITEM_BAG_RED_SHOW', type='integer'},
            { name='HERO_TOKEN_COST', type='integer[]'},
            { name='TACTIC_TOKEN_COST', type='integer[]'},
            { name='ITEM_BAG_CAP', type='integer'},
            { name='EQUIP_BAG_CAP', type='integer'},
            { name='HORSE_BAG_CAP', type='integer'},
            { name='TOKEN_BAG_CAP', type='integer'},
            { name='MATERIAL_BAG_CAP', type='integer'},
            { name='BAG_ITEM_COL_ROW_MIN_SIZE', type='vector2'},
            { name='TACTIC_TOKEN_NAME_WRAP', type='string'},
            { name='HERO_TOKEN_NAME_WRAP', type='string'},
        }
        beans['common.TableItemConst'] = class
    end
    do
    ---@class common.TableJudgeBehaviorTo 
     ---@field public id integer
     ---@field public first integer[] @主动方类型
     ---@field public second integer[] @被动方类型
     ---@field public relation integer[] @first看来和second的关系
     ---@field public valid integer @可以做的行为
        local class = {
            { name='id', type='integer'},
            { name='first', type='integer[]'},
            { name='second', type='integer[]'},
            { name='relation', type='integer[]'},
            { name='valid', type='integer'},
        }
        beans['common.TableJudgeBehaviorTo'] = class
    end
    do
    ---@class common.TableLandBuff 
     ---@field public id integer @Buff id
     ---@field public desc string @描述
     ---@field public type integer @地块Buff类型
     ---@field public arg integer @参数（目前一个够用，后续扩展为bean）
     ---@field public VX_effect string @vx表现
        local class = {
            { name='id', type='integer'},
            { name='desc', type='string'},
            { name='type', type='integer'},
            { name='arg', type='integer'},
            { name='VX_effect', type='string'},
        }
        beans['common.TableLandBuff'] = class
    end
    do
    ---@class common.TableLotteryPack 
     ---@field public id integer @卡包id
     ---@field public name string @卡包名
     ---@field public heroes integer[] @开始天数
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='heroes', type='integer[]'},
        }
        beans['common.TableLotteryPack'] = class
    end
    do
    ---@class common.TableMaterial 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public cap integer @持有上限
     ---@field public expire integer @过期时长/秒
     ---@field public red_expire integer @红显时间/秒
     ---@field public active_use boolean @能否主动使用
     ---@field public description string @描述
     ---@field public access string[]
     ---@field public use_func item.UseFunc
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='cap', type='integer'},
            { name='expire', type='integer'},
            { name='red_expire', type='integer'},
            { name='active_use', type='boolean'},
            { name='description', type='string'},
            { name='access', type='string[]'},
            { name='use_func', type='item.UseFunc'},
        }
        beans['common.TableMaterial'] = class
    end
    do
    ---@class common.TableRarityRank 
     ---@field public id integer @稀有度等级
     ---@field public name string @名字
     ---@field public sprite_headitem4 string @列表头像边框
     ---@field public sprite_headitem string @列表头像边框
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='sprite_headitem4', type='string'},
            { name='sprite_headitem', type='string'},
        }
        beans['common.TableRarityRank'] = class
    end
    do
    ---@class common.TableSchedule 
     ---@field public ScheduleId integer @排期id
     ---@field public TimeZone string @时区（缺省用服务器时区）
     ---@field public BeginDatetime integer[] @开始时间
     ---@field public CycleType string @周期类型
     ---@field public CycleSpan integer @周期跨度
     ---@field public CycleLoop integer @循环次数
     ---@field public Version integer @版本
     ---@field public Stages common.ScheduleStages[] @阶段
        local class = {
            { name='ScheduleId', type='integer'},
            { name='TimeZone', type='string'},
            { name='BeginDatetime', type='integer[]'},
            { name='CycleType', type='string'},
            { name='CycleSpan', type='integer'},
            { name='CycleLoop', type='integer'},
            { name='Version', type='integer'},
            { name='Stages', type='common.ScheduleStages[]'},
        }
        beans['common.TableSchedule'] = class
    end
    do
    ---@class common.TableServerList 
     ---@field public name string @名称
     ---@field public cluster integer @服务器ID
     ---@field public host string @地址
     ---@field public port integer @端口
     ---@field public protocol string @协议
        local class = {
            { name='name', type='string'},
            { name='cluster', type='integer'},
            { name='host', type='string'},
            { name='port', type='integer'},
            { name='protocol', type='string'},
        }
        beans['common.TableServerList'] = class
    end
    do
    ---@class common.TableSlgConfig 
     ---@field public ResourceIconPathDict table<integer,string> @资源图标
     ---@field public GridMarkColorMap table<integer,color> @地块归属颜色
     ---@field public FormationScaleFactor number @阵型缩放参数
     ---@field public ArmySelectPriority table<integer,integer> @军队选择界面的优先级
     ---@field public HudLayerHideMap table<string,integer[]> @操作对应隐藏的Hud层级列表
     ---@field public UIArmyTypeCounterTipImage string @兵种克制说明图
        local class = {
            { name='ResourceIconPathDict', type='table<integer,string>'},
            { name='GridMarkColorMap', type='table<integer,color>'},
            { name='FormationScaleFactor', type='number'},
            { name='ArmySelectPriority', type='table<integer,integer>'},
            { name='HudLayerHideMap', type='table<string,integer[]>'},
            { name='UIArmyTypeCounterTipImage', type='string'},
        }
        beans['common.TableSlgConfig'] = class
    end
    do
    ---@class common.TableStrategy 
     ---@field public id integer @策牌id
     ---@field public name string @策牌名称
     ---@field public name_display string @策牌名称(展示)
     ---@field public is_base boolean @是否基础策牌
     ---@field public day_limit integer @日次数限制
     ---@field public cost integer @消耗策点数量
     ---@field public daily_recover integer @每日回复数量
     ---@field public storage_limit integer @存储上限
     ---@field public sprite_path_card string @展示卡牌
     ---@field public sing_process_view strategy.SingView @吟唱过程表现
     ---@field public sing_end_view strategy.SingView @吟唱结束表现
     ---@field public strategy_func strategy.BaseStrategy @策牌类型
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='name_display', type='string'},
            { name='is_base', type='boolean'},
            { name='day_limit', type='integer'},
            { name='cost', type='integer'},
            { name='daily_recover', type='integer'},
            { name='storage_limit', type='integer'},
            { name='sprite_path_card', type='string'},
            { name='sing_process_view', type='strategy.SingView'},
            { name='sing_end_view', type='strategy.SingView'},
            { name='strategy_func', type='strategy.BaseStrategy'},
        }
        beans['common.TableStrategy'] = class
    end
    do
    ---@class common.TableSwitch 
     ---@field public name string @名称
     ---@field public value boolean @开发
     ---@field public release_value boolean @外放
        local class = {
            { name='name', type='string'},
            { name='value', type='boolean'},
            { name='release_value', type='boolean'},
        }
        beans['common.TableSwitch'] = class
    end
    do
    ---@class common.TableTags 
     ---@field public name string @名字
     ---@field public content integer[] @id列表
        local class = {
            { name='name', type='string'},
            { name='content', type='integer[]'},
        }
        beans['common.TableTags'] = class
    end
    do
    ---@class common.TableTask 
     ---@field public id integer @任务id
     ---@field public title string @任务标题
     ---@field public chapters integer @主线章节
     ---@field public condition table<string,integer>[] @任务条件:id,level,num,value
     ---@field public triggerEvent integer @关联的触发事件
     ---@field public careEventId integer[] @触发事件的id
     ---@field public collectFunc task.collectFunc @搜集进度方法
     ---@field public rewards common.Reward @奖励类型
        local class = {
            { name='id', type='integer'},
            { name='title', type='string'},
            { name='chapters', type='integer'},
            { name='condition', type='table<string,integer>[]'},
            { name='triggerEvent', type='integer'},
            { name='careEventId', type='integer[]'},
            { name='collectFunc', type='task.collectFunc'},
            { name='rewards', type='common.Reward'},
        }
        beans['common.TableTask'] = class
    end
    do
    ---@class common.TableTaskChapterInterlude 
     ---@field public taskId integer @对应的任务id
     ---@field public chapter string @章节名称
     ---@field public title string @章节标题
     ---@field public content string @文字内容
     ---@field public BGPath string @背景图路径
     ---@field public mainRightBigPicPath string @任务主界面右侧大图路径
        local class = {
            { name='taskId', type='integer'},
            { name='chapter', type='string'},
            { name='title', type='string'},
            { name='content', type='string'},
            { name='BGPath', type='string'},
            { name='mainRightBigPicPath', type='string'},
        }
        beans['common.TableTaskChapterInterlude'] = class
    end
    do
    ---@class common.TableTaskConst 
     ---@field public InitTask integer @初始任务
        local class = {
            { name='InitTask', type='integer'},
        }
        beans['common.TableTaskConst'] = class
    end
    do
    ---@class common.TableTermEntry 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public desc string @说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
        }
        beans['common.TableTermEntry'] = class
    end
    do
    ---@class common.TableTermEntry1 
     ---@field public name string @名称
     ---@field public desc string @说明
        local class = {
            { name='name', type='string'},
            { name='desc', type='string'},
        }
        beans['common.TableTermEntry1'] = class
    end
    do
    ---@class common.TableText 
     ---@field public id integer @id
     ---@field public name string @名字
     ---@field public text string @文本
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='text', type='string'},
        }
        beans['common.TableText'] = class
    end
    do
    ---@class common.TableToken 
     ---@field public id integer @道具id
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public hero integer @武将
     ---@field public tactic integer @战法
     ---@field public decompose_coin integer @分解得到货币类型
     ---@field public decompose_num integer @分解得到货币数
     ---@field public cap integer @持有上限
        local class = {
            { name='id', type='integer'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='hero', type='integer'},
            { name='tactic', type='integer'},
            { name='decompose_coin', type='integer'},
            { name='decompose_num', type='integer'},
            { name='cap', type='integer'},
        }
        beans['common.TableToken'] = class
    end
    do
    ---@class common.TableValidBehavior 
     ---@field public id integer @规则索引
     ---@field public status integer @状态
     ---@field public March boolean @行军
     ---@field public OccupyGrid boolean @攻占
     ---@field public Halt boolean @中断
     ---@field public Return boolean @回城
     ---@field public EnterSpace boolean @进入场景，可被玩家看见
     ---@field public UseStrategy boolean @行军
     ---@field public ModifyArmy boolean @编队
     ---@field public error_text_id integer @条件不满足时的提示文本id
        local class = {
            { name='id', type='integer'},
            { name='status', type='integer'},
            { name='March', type='boolean'},
            { name='OccupyGrid', type='boolean'},
            { name='Halt', type='boolean'},
            { name='Return', type='boolean'},
            { name='EnterSpace', type='boolean'},
            { name='UseStrategy', type='boolean'},
            { name='ModifyArmy', type='boolean'},
            { name='error_text_id', type='integer'},
        }
        beans['common.TableValidBehavior'] = class
    end
    do
    ---@class common.TableWorldBuff 
     ---@field public id integer @Buff id
     ---@field public type integer @沙盘Buff类型
     ---@field public arg integer @参数（目前一个够用，后续扩展为bean）
     ---@field public VX_effect string @vx表现
        local class = {
            { name='id', type='integer'},
            { name='type', type='integer'},
            { name='arg', type='integer'},
            { name='VX_effect', type='string'},
        }
        beans['common.TableWorldBuff'] = class
    end
    do
    ---@class equip.Effect  @装备特效
        local class = {
        }
        beans['equip.Effect'] = class
    end
    do
    ---@class equip.AddAttrEffect :equip.Effect 
     ---@field public attrType integer @属性类型
     ---@field public value integer @增加属性值
        local class = {
            { name='attrType', type='integer'},
            { name='value', type='integer'},
        }
        beans['equip.AddAttrEffect'] = class
    end
    do
    ---@class equip.CombatBuff :equip.Effect 
     ---@field public buffId integer @战斗buffId
        local class = {
            { name='buffId', type='integer'},
        }
        beans['equip.CombatBuff'] = class
    end
    do
    ---@class equip.ExpAddition :equip.Effect 
     ---@field public addPercent integer @增加经验百分比
        local class = {
            { name='addPercent', type='integer'},
        }
        beans['equip.ExpAddition'] = class
    end
    do
    ---@class home.BuildingFunc  @建筑功能函数
        local class = {
        }
        beans['home.BuildingFunc'] = class
    end
    do
    ---@class home.EmptyFunc :home.BuildingFunc 
        local class = {
        }
        beans['home.EmptyFunc'] = class
    end
    do
    ---@class home.JunYing :home.BuildingFunc 
        local class = {
        }
        beans['home.JunYing'] = class
    end
    do
    ---@class home.ZhengBingSuoFunc :home.BuildingFunc 
        local class = {
        }
        beans['home.ZhengBingSuoFunc'] = class
    end
    do
    ---@class homeTech.TechFunc  @内城科技功能函数
        local class = {
        }
        beans['homeTech.TechFunc'] = class
    end
    do
    ---@class homeTech.AddAttr :homeTech.TechFunc 
     ---@field public attrType integer @属性类型
     ---@field public value integer[] @参数（点数）
     ---@field public buffId integer @对应的buffId
        local class = {
            { name='attrType', type='integer'},
            { name='value', type='integer[]'},
            { name='buffId', type='integer'},
        }
        beans['homeTech.AddAttr'] = class
    end
    do
    ---@class homeTech.DynastyWoundIncDec :homeTech.TechFunc 
     ---@field public dynastyType integer @兵种类型
     ---@field public value number[] @参数
     ---@field public isPercentage boolean @百分比参数
     ---@field public buffId integer @对应的buffId
        local class = {
            { name='dynastyType', type='integer'},
            { name='value', type='number[]'},
            { name='isPercentage', type='boolean'},
            { name='buffId', type='integer'},
        }
        beans['homeTech.DynastyWoundIncDec'] = class
    end
    do
    ---@class homeTech.SpecialBonus :homeTech.TechFunc 
     ---@field public desc string @描述
     ---@field public buffId integer @对应的战斗buffId
     ---@field public neededLevel integer @所需解锁等级
        local class = {
            { name='desc', type='string'},
            { name='buffId', type='integer'},
            { name='neededLevel', type='integer'},
        }
        beans['homeTech.SpecialBonus'] = class
    end
    do
    ---@class homeTech.WoundIncDec :homeTech.TechFunc 
     ---@field public soldierType integer @兵种类型
     ---@field public value number[] @参数
     ---@field public isPercentage boolean @百分比参数
     ---@field public buffId integer @对应的buffId
        local class = {
            { name='soldierType', type='integer'},
            { name='value', type='number[]'},
            { name='isPercentage', type='boolean'},
            { name='buffId', type='integer'},
        }
        beans['homeTech.WoundIncDec'] = class
    end
    do
    ---@class item.IdNumRatio 
     ---@field public id integer @道具id
     ---@field public num integer @数量
     ---@field public weight integer @概率
        local class = {
            { name='id', type='integer'},
            { name='num', type='integer'},
            { name='weight', type='integer'},
        }
        beans['item.IdNumRatio'] = class
    end
    do
    ---@class item.UseFunc  @道具主动使用方法
        local class = {
        }
        beans['item.UseFunc'] = class
    end
    do
    ---@class item.ChooseItem :item.UseFunc 
     ---@field public options table<integer,integer> @道具id: 数量
        local class = {
            { name='options', type='table<integer,integer>'},
        }
        beans['item.ChooseItem'] = class
    end
    do
    ---@class item.ChooseRes :item.UseFunc 
     ---@field public options table<integer,integer> @资源货币id: 数量
        local class = {
            { name='options', type='table<integer,integer>'},
        }
        beans['item.ChooseRes'] = class
    end
    do
    ---@class item.EquipBlueprint :item.UseFunc 
     ---@field public equipId integer @装备id
        local class = {
            { name='equipId', type='integer'},
        }
        beans['item.EquipBlueprint'] = class
    end
    do
    ---@class item.HorseTrain :item.UseFunc 
        local class = {
        }
        beans['item.HorseTrain'] = class
    end
    do
    ---@class item.RandomItem :item.UseFunc 
     ---@field public rand_list item.IdNumRatio[] @随机列表
        local class = {
            { name='rand_list', type='item.IdNumRatio[]'},
        }
        beans['item.RandomItem'] = class
    end
    do
    ---@class season.BaseFunc  @赛季功能开关
        local class = {
        }
        beans['season.BaseFunc'] = class
    end
    do
    ---@class season.LimitSwitchFunc :season.BaseFunc 
     ---@field public switch_name string @开关名
     ---@field public set_status boolean @开关
        local class = {
            { name='switch_name', type='string'},
            { name='set_status', type='boolean'},
        }
        beans['season.LimitSwitchFunc'] = class
    end
    do
    ---@class season.SwitchFunc :season.BaseFunc 
     ---@field public switch_name string @开关名
     ---@field public set_status boolean @开关
        local class = {
            { name='switch_name', type='string'},
            { name='set_status', type='boolean'},
        }
        beans['season.SwitchFunc'] = class
    end
    do
    ---@class season.BaseTarget  @赛季目标
        local class = {
        }
        beans['season.BaseTarget'] = class
    end
    do
    ---@class season.OccupyLand :season.BaseTarget 
     ---@field public level integer @地块等级
     ---@field public num integer @地块数量
        local class = {
            { name='level', type='integer'},
            { name='num', type='integer'},
        }
        beans['season.OccupyLand'] = class
    end
    do
    ---@class season.TableS1 
     ---@field public id integer @霸业阶段
     ---@field public name string @阶段名
     ---@field public begin integer @开始天数
     ---@field public limit_pack integer[] @限时卡包
     ---@field public switches season.BaseFunc[] @新功能开启
     ---@field public targets season.BaseTarget @达成条件
     ---@field public rewards common.Reward @达成奖励
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='begin', type='integer'},
            { name='limit_pack', type='integer[]'},
            { name='switches', type='season.BaseFunc[]'},
            { name='targets', type='season.BaseTarget'},
            { name='rewards', type='common.Reward'},
        }
        beans['season.TableS1'] = class
    end
    do
    ---@class slg.TableMainCityDistScore 
     ---@field public id integer @id
     ---@field public score integer @积分
     ---@field public max_distance integer @最大影响距离
        local class = {
            { name='id', type='integer'},
            { name='score', type='integer'},
            { name='max_distance', type='integer'},
        }
        beans['slg.TableMainCityDistScore'] = class
    end
    do
    ---@class slg.TableMainCityRandomConfig 
     ---@field public MainCityRadius integer @主城半径（除中心格子外的半径）
     ---@field public MaxScore integer @地块最大、初始积分
     ---@field public ResScanRadius integer @资源地扫描半径
     ---@field public ScoreLvRange integer[] @积分等级，每级范围[start, end)
        local class = {
            { name='MainCityRadius', type='integer'},
            { name='MaxScore', type='integer'},
            { name='ResScanRadius', type='integer'},
            { name='ScoreLvRange', type='integer[]'},
        }
        beans['slg.TableMainCityRandomConfig'] = class
    end
    do
    ---@class slg.TableMainCityResScore 
     ---@field public id integer @资源等级
     ---@field public score integer @积分
     ---@field public max_count integer @最大影响数量
        local class = {
            { name='id', type='integer'},
            { name='score', type='integer'},
            { name='max_count', type='integer'},
        }
        beans['slg.TableMainCityResScore'] = class
    end
    do
    ---@class slg.TableMapElement 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public name_format string @名称(格式)
     ---@field public type integer @类型
     ---@field public level integer @等级
     ---@field public description string @描述
     ---@field public x_coords integer[] @占地坐标(x)
     ---@field public y_coords integer[] @占地坐标(y)
     ---@field public obstacle integer @阻挡类型
     ---@field public prefab_path string
     ---@field public color string @编辑器用
     ---@field public text_color string @编辑器用
     ---@field public tile_text string @编辑器用
     ---@field public occupy_time integer @占领时间（秒）
     ---@field public land_team integer[] @土地阵容随机表
     ---@field public production integer @产出货币类型
     ---@field public productivity integer @x/小时
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='name_format', type='string'},
            { name='type', type='integer'},
            { name='level', type='integer'},
            { name='description', type='string'},
            { name='x_coords', type='integer[]'},
            { name='y_coords', type='integer[]'},
            { name='obstacle', type='integer'},
            { name='prefab_path', type='string'},
            { name='color', type='string'},
            { name='text_color', type='string'},
            { name='tile_text', type='string'},
            { name='occupy_time', type='integer'},
            { name='land_team', type='integer[]'},
            { name='production', type='integer'},
            { name='productivity', type='integer'},
        }
        beans['slg.TableMapElement'] = class
    end
    do
    ---@class slg.TableMapGIS 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public parent_id integer @parent_id
     ---@field public administrative_class integer @行政划分
     ---@field public description string @描述
     ---@field public color string @编辑器用
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='parent_id', type='integer'},
            { name='administrative_class', type='integer'},
            { name='description', type='string'},
            { name='color', type='string'},
        }
        beans['slg.TableMapGIS'] = class
    end
    do
    ---@class slg.TableTypeElement 
     ---@field public type integer @type
     ---@field public name string @名称
     ---@field public color string @颜色
     ---@field public sprite_path string @贴图
        local class = {
            { name='type', type='integer'},
            { name='name', type='string'},
            { name='color', type='string'},
            { name='sprite_path', type='string'},
        }
        beans['slg.TableTypeElement'] = class
    end
    do
    ---@class strategy.BaseStrategy  @策牌功能
        local class = {
        }
        beans['strategy.BaseStrategy'] = class
    end
    do
    ---@class strategy.StrategyJiShenJiDian :strategy.BaseStrategy 
     ---@field public addProducePerMember number
     ---@field public memberThresholds integer
     ---@field public thresholdsExAddProduce integer
     ---@field public singTime integer
     ---@field public radius integer
        local class = {
            { name='addProducePerMember', type='number'},
            { name='memberThresholds', type='integer'},
            { name='thresholdsExAddProduce', type='integer'},
            { name='singTime', type='integer'},
            { name='radius', type='integer'},
        }
        beans['strategy.StrategyJiShenJiDian'] = class
    end
    do
    ---@class strategy.StrategyKaiKen :strategy.BaseStrategy 
     ---@field public singTime integer
        local class = {
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyKaiKen'] = class
    end
    do
    ---@class strategy.StrategySha :strategy.BaseStrategy 
     ---@field public worldBuff integer
     ---@field public worldBuffTime integer
     ---@field public battleBuff integer
     ---@field public singTime integer
        local class = {
            { name='worldBuff', type='integer'},
            { name='worldBuffTime', type='integer'},
            { name='battleBuff', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategySha'] = class
    end
    do
    ---@class strategy.StrategyShan :strategy.BaseStrategy 
     ---@field public worldBuff integer
     ---@field public worldBuffTime integer
     ---@field public battleBuff integer
     ---@field public singTime integer
        local class = {
            { name='worldBuff', type='integer'},
            { name='worldBuffTime', type='integer'},
            { name='battleBuff', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyShan'] = class
    end
    do
    ---@class strategy.StrategyWuGuFengDeng :strategy.BaseStrategy 
     ---@field public gridRange integer
     ---@field public singTime integer
        local class = {
            { name='gridRange', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyWuGuFengDeng'] = class
    end
    do
    ---@class strategy.StrategyWuZhongShengYou :strategy.BaseStrategy 
     ---@field public produceTime integer
     ---@field public singTime integer
        local class = {
            { name='produceTime', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyWuZhongShengYou'] = class
    end
    do
    ---@class strategy.SingView 
     ---@field public preb_path_screen_effect string
     ---@field public preb_path_ground_effect integer
     ---@field public preb_path_VXGoId_effect integer
     ---@field public preb_path_self_VXGoId_effect integer
        local class = {
            { name='preb_path_screen_effect', type='string'},
            { name='preb_path_ground_effect', type='integer'},
            { name='preb_path_VXGoId_effect', type='integer'},
            { name='preb_path_self_VXGoId_effect', type='integer'},
        }
        beans['strategy.SingView'] = class
    end
    do
    ---@class Talent.BaseTalent  @天赋树小天赋节点
        local class = {
        }
        beans['Talent.BaseTalent'] = class
    end
    do
    ---@class Talent.Empty :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.Empty'] = class
    end
    do
    ---@class Talent.FengChan :Talent.BaseTalent 
     ---@field public addResProduce integer
        local class = {
            { name='addResProduce', type='integer'},
        }
        beans['Talent.FengChan'] = class
    end
    do
    ---@class Talent.JiangZuo :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.JiangZuo'] = class
    end
    do
    ---@class Talent.JiangZuoGuang :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.JiangZuoGuang'] = class
    end
    do
    ---@class Talent.JieCe :Talent.BaseTalent 
     ---@field public saveStrategy integer
        local class = {
            { name='saveStrategy', type='integer'},
        }
        beans['Talent.JieCe'] = class
    end
    do
    ---@class Talent.JiKui :Talent.BaseTalent 
     ---@field public resAdd integer
        local class = {
            { name='resAdd', type='integer'},
        }
        beans['Talent.JiKui'] = class
    end
    do
    ---@class Talent.JiWu :Talent.BaseTalent 
     ---@field public occupyGetRes integer
     ---@field public occupyGetResLimit integer
        local class = {
            { name='occupyGetRes', type='integer'},
            { name='occupyGetResLimit', type='integer'},
        }
        beans['Talent.JiWu'] = class
    end
    do
    ---@class Talent.MiaoDe :Talent.BaseTalent 
     ---@field public exResRatio integer
        local class = {
            { name='exResRatio', type='integer'},
        }
        beans['Talent.MiaoDe'] = class
    end
    do
    ---@class Talent.PiKuang :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.PiKuang'] = class
    end
    do
    ---@class Talent.PoXian :Talent.BaseTalent 
     ---@field public landLevelLimitTo integer
        local class = {
            { name='landLevelLimitTo', type='integer'},
        }
        beans['Talent.PoXian'] = class
    end
    do
    ---@class Talent.SuiRen :Talent.BaseTalent 
     ---@field public foodRroduceRatioAdd integer
        local class = {
            { name='foodRroduceRatioAdd', type='integer'},
        }
        beans['Talent.SuiRen'] = class
    end
    do
    ---@class Talent.TuoJiang :Talent.BaseTalent 
     ---@field public radiusAdd integer
     ---@field public dailyCardAdd integer
        local class = {
            { name='radiusAdd', type='integer'},
            { name='dailyCardAdd', type='integer'},
        }
        beans['Talent.TuoJiang'] = class
    end
    do
    ---@class Talent.TuoTu :Talent.BaseTalent 
     ---@field public landLimitAdd integer
        local class = {
            { name='landLimitAdd', type='integer'},
        }
        beans['Talent.TuoTu'] = class
    end
    do
    ---@class Talent.WoYe :Talent.BaseTalent 
     ---@field public produceExRatio integer
     ---@field public duration integer
        local class = {
            { name='produceExRatio', type='integer'},
            { name='duration', type='integer'},
        }
        beans['Talent.WoYe'] = class
    end
    do
    ---@class Talent.XuShi :Talent.BaseTalent 
     ---@field public strategyRecoverAdd integer
        local class = {
            { name='strategyRecoverAdd', type='integer'},
        }
        beans['Talent.XuShi'] = class
    end
    do
    ---@class Talent.XuShiYing :Talent.BaseTalent 
     ---@field public strategyLimitAdd integer
        local class = {
            { name='strategyLimitAdd', type='integer'},
        }
        beans['Talent.XuShiYing'] = class
    end
    do
    ---@class task.collectFunc  @任务进度搜集方法
        local class = {
        }
        beans['task.collectFunc'] = class
    end
    do
    ---@class task._taskGetAllResProduceInHour :task.collectFunc 
        local class = {
        }
        beans['task._taskGetAllResProduceInHour'] = class
    end
    do
    ---@class task._taskGetChapterComplete :task.collectFunc 
        local class = {
        }
        beans['task._taskGetChapterComplete'] = class
    end
    do
    ---@class task._taskGetHomeBuildingLevel :task.collectFunc 
     ---@field public buildingId integer @建筑id
        local class = {
            { name='buildingId', type='integer'},
        }
        beans['task._taskGetHomeBuildingLevel'] = class
    end
    do
    ---@class task._taskGetHomeTechLevel :task.collectFunc 
     ---@field public techId integer @科技id
        local class = {
            { name='techId', type='integer'},
        }
        beans['task._taskGetHomeTechLevel'] = class
    end
    do
    ---@class task._taskGetMaxArmySoldierNum :task.collectFunc 
        local class = {
        }
        beans['task._taskGetMaxArmySoldierNum'] = class
    end
    do
    ---@class task._taskGetTacticNum :task.collectFunc 
        local class = {
        }
        beans['task._taskGetTacticNum'] = class
    end
    do
    ---@class vector2 
     ---@field public x number
     ---@field public y number
        local class = {
            { name='x', type='number'},
            { name='y', type='number'},
        }
        beans['vector2'] = class
    end
    do
    ---@class vector3 
     ---@field public x number
     ---@field public y number
     ---@field public z number
        local class = {
            { name='x', type='number'},
            { name='y', type='number'},
            { name='z', type='number'},
        }
        beans['vector3'] = class
    end
    do
    ---@class vector4 
     ---@field public x number
     ---@field public y number
     ---@field public z number
     ---@field public w number
        local class = {
            { name='x', type='number'},
            { name='y', type='number'},
            { name='z', type='number'},
            { name='w', type='number'},
        }
        beans['vector4'] = class
    end

local tables =
{
    { name='TbTags', file='common_tbtags', mode='map', index='name', value_type='common.TableTags' },
    { name='TbBattleTactic', file='battle_tbbattletactic', mode='map', index='id', value_type='battle.TableBattleTactic' },
    { name='TbTacticConst', file='battle_tbtacticconst', mode='one', value_type='battle.TableTacticConst'},
    { name='TbBattleEffectSound', file='battle_tbbattleeffectsound', mode='map', index='id', value_type='battle.TableBattleEffectSound' },
    { name='TbBattleBuff', file='battle_tbbattlebuff', mode='map', index='id', value_type='battle.TableBattleBuff' },
    { name='TbBattleBuffGroup', file='battle_tbbattlebuffgroup', mode='map', index='id', value_type='battle.TableBattleBuffGroup' },
    { name='TbBattleFormulas', file='battle_tbbattleformulas', mode='one', value_type='battle.TableBattleFormulas'},
    { name='TbBattleAttribute', file='battle_tbbattleattribute', mode='list', index='', value_type='battle.TableBattleAttribute' },
    { name='TbBattleFormation', file='battle_tbbattleformation', mode='list', index='army_type+combat_status+main_status', value_type='battle.TableBattleFormation' },
    { name='TbTemplateHero', file='battle_tbtemplatehero', mode='map', index='id', value_type='battle.TableTemplateHero' },
    { name='TbHeroDynasty', file='battle_tbherodynasty', mode='map', index='id', value_type='battle.TableHeroDynasty' },
    { name='TbHeroArmyType', file='battle_tbheroarmytype', mode='map', index='id', value_type='battle.TableHeroArmyType' },
    { name='TbHeroConst', file='battle_tbheroconst', mode='one', value_type='battle.TableHeroConst'},
    { name='TbBattleNPCTeam', file='battle_tbbattlenpcteam', mode='map', index='Team_Id', value_type='battle.TableBattleNPCTeam' },
    { name='TbBattleNPC', file='battle_tbbattlenpc', mode='map', index='Id', value_type='battle.TableBattleNPC' },
    { name='TbBattleRecordText', file='battle_tbbattlerecordtext', mode='map', index='detailRecordType', value_type='battle.TableBattleRecordText' },
    { name='TbArmyTypeQualification', file='battle_tbarmytypequalification', mode='map', index='id', value_type='battle.TableArmyTypeQualification' },
    { name='TbEventPriority', file='battle_tbeventpriority', mode='list', index='battle_event+name', value_type='battle.TableEventPriority' },
    { name='TbFormulaIndex', file='battle_tbformulaindex', mode='list', index='name+key', value_type='battle.TableFormulaIndex' },
    { name='TbFormulaIndexName', file='battle_tbformulaindexname', mode='one', value_type='battle.TableFormulaIndexName'},
    { name='TbServerList', file='common_tbserverlist', mode='list', index='', value_type='common.TableServerList' },
    { name='TbText', file='common_tbtext', mode='map', index='id', value_type='common.TableText' },
    { name='TbCurrency', file='common_tbcurrency', mode='map', index='id', value_type='common.TableCurrency' },
    { name='TbSwitch', file='common_tbswitch', mode='map', index='name', value_type='common.TableSwitch' },
    { name='TbSchedule', file='common_tbschedule', mode='map', index='ScheduleId', value_type='common.TableSchedule' },
    { name='TbEffect', file='common_tbeffect', mode='map', index='id', value_type='common.TableEffect' },
    { name='TbHeroEffect', file='common_tbheroeffect', mode='list', index='id+hero_id', value_type='common.TableHeroEffect' },
    { name='TbCommonConfig', file='common_tbcommonconfig', mode='one', value_type='common.TableCommonConfig'},
    { name='TbBattleConfig', file='common_tbbattleconfig', mode='one', value_type='common.TableBattleConfig'},
    { name='TbSlgConfig', file='common_tbslgconfig', mode='one', value_type='common.TableSlgConfig'},
    { name='TbActorType', file='common_tbactortype', mode='map', index='id', value_type='common.TableActorType' },
    { name='TbJudgeBehaviorTo', file='common_tbjudgebehaviorto', mode='map', index='id', value_type='common.TableJudgeBehaviorTo' },
    { name='TbValidBehavior', file='common_tbvalidbehavior', mode='map', index='id', value_type='common.TableValidBehavior' },
    { name='TbBehaviorConfig', file='common_tbbehaviorconfig', mode='map', index='id', value_type='common.TableBahaviorConfig' },
    { name='TbActorContainer', file='common_tbactorcontainer', mode='map', index='id', value_type='common.TableActorContainer' },
    { name='TbTermEntry', file='common_tbtermentry', mode='map', index='id', value_type='common.TableTermEntry' },
    { name='TbTermEntry1', file='common_tbtermentry1', mode='map', index='name', value_type='common.TableTermEntry1' },
    { name='TbHeroAttrType', file='common_tbheroattrtype', mode='map', index='id', value_type='common.TableHeroAttrType' },
    { name='TbHeroTitleType', file='common_tbherotitletype', mode='map', index='id', value_type='common.TableHeroTitleType' },
    { name='TbHeroRoleType', file='common_tbheroroletype', mode='map', index='id', value_type='common.TableHeroRoleType' },
    { name='TbHeroSuitRank', file='common_tbherosuitrank', mode='map', index='id', value_type='common.TableHeroSuitRank' },
    { name='TbRarityRank', file='common_tbrarityrank', mode='map', index='id', value_type='common.TableRarityRank' },
    { name='TbWorldBuff', file='common_tbworldbuff', mode='map', index='id', value_type='common.TableWorldBuff' },
    { name='TbLandBuff', file='common_tblandbuff', mode='map', index='id', value_type='common.TableLandBuff' },
    { name='TbLotteryPack', file='common_tblotterypack', mode='map', index='id', value_type='common.TableLotteryPack' },
    { name='TbHomeBuildingType', file='common_tbhomebuildingtype', mode='map', index='id', value_type='common.TableHomeBuildingType' },
    { name='TbHomeBuilding', file='common_tbhomebuilding', mode='list', index='id+level', value_type='common.TableHomeBuilding' },
    { name='TbHomeBuildingConst', file='common_tbhomebuildingconst', mode='one', value_type='common.TableHomeBuildingConst'},
    { name='TbHomeTech', file='common_tbhometech', mode='list', index='', value_type='common.TableHomeTech' },
    { name='TbTask', file='common_tbtask', mode='map', index='id', value_type='common.TableTask' },
    { name='TbTaskChapterInterlude', file='common_tbtaskchapterinterlude', mode='map', index='taskId', value_type='common.TableTaskChapterInterlude' },
    { name='TbTaskConst', file='common_tbtaskconst', mode='one', value_type='common.TableTaskConst'},
    { name='TbItem', file='common_tbitem', mode='map', index='id', value_type='common.TableItem' },
    { name='TbItemConst', file='common_tbitemconst', mode='one', value_type='common.TableItemConst'},
    { name='TbMaterial', file='common_tbmaterial', mode='map', index='id', value_type='common.TableMaterial' },
    { name='TbToken', file='common_tbtoken', mode='map', index='id', value_type='common.TableToken' },
    { name='TbIdSegment', file='common_tbidsegment', mode='map', index='id', value_type='common.TableIdSegment' },
    { name='TbCareerConst', file='common_tbcareerconst', mode='one', value_type='common.TableCareerConst'},
    { name='TbStrategy', file='common_tbstrategy', mode='map', index='id', value_type='common.TableStrategy' },
    { name='TbCareerTelent', file='common_tbcareertelent', mode='map', index='id', value_type='common.TableCareerTelent' },
    { name='TbCareerTree', file='common_tbcareertree', mode='map', index='id', value_type='common.TableCareerTree' },
    { name='TbEquipConst', file='common_tbequipconst', mode='one', value_type='common.TableEquipConst'},
    { name='TbEquip', file='common_tbequip', mode='map', index='id', value_type='common.TableEquip' },
    { name='TbHorse', file='common_tbhorse', mode='map', index='id', value_type='common.TableHorse' },
    { name='TbEquipEffect', file='common_tbequipeffect', mode='map', index='id', value_type='common.TableEquipEffect' },
    { name='TbEquipQualityRatio', file='common_tbequipqualityratio', mode='map', index='id', value_type='common.TableEquipQualityRatio' },
    { name='TbMapElement', file='slg_tbmapelement', mode='map', index='id', value_type='slg.TableMapElement' },
    { name='TbTypeElement', file='slg_tbtypeelement', mode='map', index='type', value_type='slg.TableTypeElement' },
    { name='TbMapGIS', file='slg_tbmapgis', mode='map', index='id', value_type='slg.TableMapGIS' },
    { name='TbMainCityDistScore', file='slg_tbmaincitydistscore', mode='map', index='id', value_type='slg.TableMainCityDistScore' },
    { name='TbMainCityResScore', file='slg_tbmaincityresscore', mode='map', index='id', value_type='slg.TableMainCityResScore' },
    { name='TbMainCityRandomConfig', file='slg_tbmaincityrandomconfig', mode='one', value_type='slg.TableMainCityRandomConfig'},
    { name='TbS1', file='season_tbs1', mode='map', index='id', value_type='season.TableS1' },
}

TableConst = { enums = enums, beans = beans, tables = tables }
DeclareAndSetGlobal("TableConst", TableConst)

