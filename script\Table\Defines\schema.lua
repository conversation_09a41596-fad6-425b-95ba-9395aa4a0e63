
--[[------------------------------------------------------------------------------
-- <auto-generated>
--     This code was generated by a tool.
--     Changes to this file may cause incorrect behavior and will be lost if
--     the code is regenerated.
-- </auto-generated>
--]]------------------------------------------------------------------------------


local enums =
{
    ---@class Actor
     ---@field public Source integer @自身
     ---@field public Target integer @目标
    ['Actor'] = {   Source=1,  Target=2,  };
    ---@class ActorType @地图内角色类型
     ---@field public ARMY integer @部队
     ---@field public MAIN_CITY integer @主堡
     ---@field public TENT integer @营帐
     ---@field public STONE_VEHICLE integer @投石车
     ---@field public CITY integer @城池
     ---@field public CITY_GATE integer @城门
     ---@field public PASS integer @关卡
     ---@field public FERRY integer @渡口
     ---@field public INNER_CITY_GATE integer @城主府门
     ---@field public SIEGE_CAMP integer @攻城大营
     ---@field public CHUNK integer @地块
     ---@field public JI_SHEN_JI_DIAN integer @稷神祭典
     ---@field public CITY_BARRACKS integer @兵营
    ['ActorType'] = {   ARMY=1001,  MAIN_CITY=1002,  TENT=1003,  STONE_VEHICLE=1004,  CITY=1005,  CITY_GATE=1006,  PASS=1007,  FERRY=1008,  INNER_CITY_GATE=1009,  SIEGE_CAMP=1010,  CHUNK=1011,  JI_SHEN_JI_DIAN=1012,  CITY_BARRACKS=1013,  };
    ---@class Alignment
     ---@field public Left integer @居左
     ---@field public Right integer @居右
     ---@field public Center integer @居中
    ['Alignment'] = {   Left=1,  Right=2,  Center=3,  };
    ---@class AllyBuildingTypeLevel @联盟建筑类型
     ---@field public FOUNDATION_OF_RULE integer @霸业之基
     ---@field public PREFECTURE_CAPITAL integer @州府
     ---@field public COMMANDERY_CAPITAL integer @郡府
     ---@field public FERRY integer @码头
     ---@field public PASS integer @关卡
    ['AllyBuildingTypeLevel'] = {   FOUNDATION_OF_RULE=1,  PREFECTURE_CAPITAL=2,  COMMANDERY_CAPITAL=3,  FERRY=4,  PASS=5,  };
    ---@class ArmyAnimationName @兵种动画名称
     ---@field public idle integer @停留
     ---@field public battleidle integer @驻守
     ---@field public walk integer @walk和run根据移动速度自动切换
     ---@field public run integer @walk和run根据移动速度自动切换
     ---@field public attack integer @攻击
     ---@field public victory integer @战斗胜利
     ---@field public retreat integer @主动回城
    ['ArmyAnimationName'] = {   idle=1,  battleidle=2,  walk=3,  run=4,  attack=5,  victory=6,  retreat=7,  };
    ---@class ArmyHomeType @部队出发点类型
     ---@field public BaseHome integer @主城
     ---@field public DeployHome integer @调动点
     ---@field public SiegeHome integer @集结点
    ['ArmyHomeType'] = {   BaseHome=1,  DeployHome=2,  SiegeHome=3,  };
    ---@class ArmyStatus @部队主状态
     ---@field public Rest integer @城内(主堡城池营帐)休息
     ---@field public Stay integer @野外停留
     ---@field public March integer @行军
     ---@field public DefendGrid integer @驻守格子
     ---@field public DefendBuilding integer @驻守建筑
     ---@field public Occupy integer @占领地块倒计时中
     ---@field public Retreat integer @主动回城
     ---@field public Escape integer @战败溃逃
     ---@field public Sing integer @释放策牌吟唱
     ---@field public WaitSing integer @释放策牌吟唱被暂停
     ---@field public IdleCombat integer @非战斗
     ---@field public WaitCombat integer @等待战斗
     ---@field public Combat integer @战斗
     ---@field public Injured integer @重伤
     ---@field public BreakDura integer @拆除耐久
     ---@field public Impasse integer @僵持
     ---@field public IdleDeploy integer @非调动
     ---@field public GoDeploy integer @前往调动
     ---@field public Deploy integer @调动
     ---@field public GoSiegeRally integer @前往攻城集结
     ---@field public SiegeRally integer @攻城集结
     ---@field public IdleVehicle integer @非器械中
     ---@field public InStoneVehicle integer @在投石车中
     ---@field public ThrowStone integer @投石中
    ['ArmyStatus'] = {   Rest=101,  Stay=102,  March=103,  DefendGrid=104,  DefendBuilding=105,  Occupy=106,  Retreat=107,  Escape=108,  Sing=109,  WaitSing=110,  IdleCombat=201,  WaitCombat=202,  Combat=203,  Injured=204,  BreakDura=205,  Impasse=206,  IdleDeploy=301,  GoDeploy=302,  Deploy=303,  GoSiegeRally=304,  SiegeRally=305,  IdleVehicle=401,  InStoneVehicle=402,  ThrowStone=403,  };
    ---@class ArmyTypeQualificationTarget @兵种适应性修改目标
     ---@field public Self integer @自己
    ['ArmyTypeQualificationTarget'] = {   Self=0,  };
    ---@class AudioGroup @音效组
     ---@field public Default integer @默认
     ---@field public Male integer @男
     ---@field public Female integer @女
    ['AudioGroup'] = {   Default=0,  Male=1,  Female=2,  };
    ---@class BattleCameraType @相机类型
     ---@field public Full integer @全景
     ---@field public Follow integer @跟随
     ---@field public Focus integer @聚焦
     ---@field public Special integer @特殊
    ['BattleCameraType'] = {   Full=1,  Follow=2,  Focus=3,  Special=4,  };
    ---@class BattleCharacterAnimation @策划能控制的角色动作
     ---@field public skill_01 integer @skill_01
     ---@field public skill_02 integer @skill_02
     ---@field public skill_11 integer @skill_11
     ---@field public skill_12 integer @skill_12
     ---@field public skill_13 integer @skill_13
     ---@field public skill_21 integer @skill_21
     ---@field public skill_22 integer @skill_22
     ---@field public skill_23 integer @skill_23
     ---@field public skill_31 integer @skill_31
     ---@field public skill_32 integer @skill_32
     ---@field public skill_41 integer @skill_41
     ---@field public charge integer @charge
     ---@field public dodge integer @dodge
     ---@field public guard integer @guard
     ---@field public hit integer @hit
     ---@field public float_up integer @float_up
     ---@field public float_mid integer @float_mid
     ---@field public float_down integer @float_down
     ---@field public special_01 integer @special_01
     ---@field public special_02 integer @special_02
    ['BattleCharacterAnimation'] = {   skill_01=0,  skill_02=1,  skill_11=2,  skill_12=3,  skill_13=4,  skill_21=5,  skill_22=6,  skill_23=7,  skill_31=8,  skill_32=9,  skill_41=10,  charge=11,  dodge=12,  guard=13,  hit=14,  float_up=15,  float_mid=16,  float_down=17,  special_01=18,  special_02=19,  };
    ---@class BattleDamageType
     ---@field public Attack integer @物理伤害
     ---@field public Intelligence integer @法术伤害
     ---@field public Reality integer @真实伤害
     ---@field public Share integer @分摊伤害
    ['BattleDamageType'] = {   Attack=1,  Intelligence=2,  Reality=3,  Share=4,  };
    ---@class BattleEvent
     ---@field public None integer
     ---@field public FormationMorale integer @布阵-士气
     ---@field public FormationArmyType integer @布阵-兵种
     ---@field public FormationTechnology integer @布阵-科技
     ---@field public FormationEquipment integer @布阵-装备
     ---@field public FormationHeroIdentity integer @布阵-身份
     ---@field public FormationTacticsArmyType integer @布阵-战法-兵种
     ---@field public FormationTacticsFormation integer @布阵-战法-阵法
     ---@field public FormationTacticsOther integer @布阵-战法-其他
     ---@field public RoundPrepare integer @回合准备
     ---@field public HeroTurnStart integer @英雄轮次开始
     ---@field public HeroTurnAction integer @英雄轮次行动
     ---@field public HeroTurnEnd integer @英雄轮次结束
     ---@field public RoundEnd integer @回合结束
     ---@field public BattleResult integer @战斗结算
     ---@field public Initialize integer @初始化
     ---@field public BeforeDamage integer @伤害前
     ---@field public AfterDamage integer @伤害后
     ---@field public BeforeHeal integer @治疗前
     ---@field public AfterHeal integer @治疗后
     ---@field public BeforeUseTactic integer @使用战法前
     ---@field public AfterUseTactic integer @使用战法后
     ---@field public TacticProbFailed integer @战法发动率判定失败
     ---@field public BeforeBeDamaged integer @受到伤害前
     ---@field public AfterBeDamaged integer @受到伤害后
     ---@field public AfterPlainAttackProcess integer @普攻流程后
     ---@field public AfterPlainAttack integer @普攻后
     ---@field public BeforePlainAttack integer @普攻前
     ---@field public BeforeDodge integer @闪避前
     ---@field public AfterDodgeSuccess integer @闪避成功后
     ---@field public AfterDodgeFail integer @闪避失败后
     ---@field public BeforeBeHealed integer @受到治疗前
     ---@field public AfterBeHealed integer @受到治疗后
     ---@field public BeforeFatalDamage integer @受致命伤前
     ---@field public BeforeAddBuff integer @添加buff前
     ---@field public AfterAddBuff integer @添加buff后
     ---@field public BeforeBeAddBuff integer @被添加buff前
     ---@field public AfterBeAddBuff integer @被添加buff后
     ---@field public BeforeTryUseTactic integer @尝试使用战法前
     ---@field public BeforeBePlainAttacked integer @被普攻前
     ---@field public AfterBePlainAttacked integer @被普攻后
     ---@field public AfterTacticAwaken integer @战法觉醒后
     ---@field public BeforeBeRemoveBuff integer @被移除buff前
     ---@field public AfterBeRemoveBuff integer @被移除buff后
    ['BattleEvent'] = {   None=0,  FormationMorale=1,  FormationArmyType=2,  FormationTechnology=14,  FormationEquipment=3,  FormationHeroIdentity=15,  FormationTacticsArmyType=13,  FormationTacticsFormation=12,  FormationTacticsOther=4,  RoundPrepare=5,  HeroTurnStart=6,  HeroTurnAction=7,  HeroTurnEnd=8,  RoundEnd=9,  BattleResult=10,  Initialize=11,  BeforeDamage=100,  AfterDamage=101,  BeforeHeal=102,  AfterHeal=103,  BeforeUseTactic=105,  AfterUseTactic=106,  TacticProbFailed=107,  BeforeBeDamaged=108,  AfterBeDamaged=109,  AfterPlainAttackProcess=111,  AfterPlainAttack=112,  BeforePlainAttack=113,  BeforeDodge=114,  AfterDodgeSuccess=115,  AfterDodgeFail=116,  BeforeBeHealed=117,  AfterBeHealed=118,  BeforeFatalDamage=119,  BeforeAddBuff=120,  AfterAddBuff=121,  BeforeBeAddBuff=122,  AfterBeAddBuff=123,  BeforeTryUseTactic=124,  BeforeBePlainAttacked=125,  AfterBePlainAttacked=126,  AfterTacticAwaken=127,  BeforeBeRemoveBuff=128,  AfterBeRemoveBuff=129,  };
    ---@class BattleModeType @战斗模式
     ---@field public OneOnOne integer @1v1
     ---@field public Bo3 integer
     ---@field public Kof3 integer
    ['BattleModeType'] = {   OneOnOne=0,  Bo3=1,  Kof3=2,  };
    ---@class BattlePVEType @PVE战斗类型
     ---@field public LandDefender integer
    ['BattlePVEType'] = {   LandDefender=10,  };
    ---@class BattleRecordScope
     ---@field public Individual integer @个人
     ---@field public Ally integer @同盟
    ['BattleRecordScope'] = {   Individual=1,  Ally=2,  };
    ---@class BattleTacticGrade
     ---@field public White integer @白
     ---@field public Blue integer @蓝
     ---@field public Purple integer @紫
     ---@field public Gold integer @橙
     ---@field public BasicAttack integer @普攻
    ['BattleTacticGrade'] = {   White=1,  Blue=2,  Purple=3,  Gold=4,  BasicAttack=5,  };
    ---@class BattleType
     ---@field public PVP integer @PVP交战
     ---@field public PVE_ALL integer @PVE所有
     ---@field public PVE_LAND integer @PVE打地
     ---@field public PVE_DRILL integer @PVE军演
     ---@field public PVE_OUTPOST_DEF integer @PVE驻城守军
     ---@field public PVE_OUTPOST_DURA integer @PVE城防守军
     ---@field public Test integer @测试
    ['BattleType'] = {   PVP=1,  PVE_ALL=65280,  PVE_LAND=256,  PVE_DRILL=512,  PVE_OUTPOST_DEF=1024,  PVE_OUTPOST_DURA=2048,  Test=0,  };
    ---@class BehaviorType @单个体执行的行为
     ---@field public March integer @A前往行军
     ---@field public OccupyGrid integer @A前往占领地块
     ---@field public AttackBuilding integer @攻城
     ---@field public Defend integer @前往驻守地块或建筑
     ---@field public Deploy integer @前往调动点
     ---@field public Siege integer @前往集结点
     ---@field public Return integer @回城
     ---@field public UseStrategy integer @使用策牌
     ---@field public OperateVehicle integer @操控器械
     ---@field public ThrowStone integer @投石
     ---@field public Halt integer @中断任意行为
     ---@field public EnterSpace integer @进入场景才可被看见
     ---@field public ModifyArmy integer @编队
     ---@field public Supply integer @恢复部队补给
     ---@field public Interactable integer @进入格子交互
    ['BehaviorType'] = {   March=1,  OccupyGrid=2,  AttackBuilding=3,  Defend=4,  Deploy=5,  Siege=6,  Return=7,  UseStrategy=8,  OperateVehicle=9,  ThrowStone=10,  Halt=11,  EnterSpace=12,  ModifyArmy=13,  Supply=14,  Interactable=15,  };
    ---@class BuffAddTiming @战斗Buff自然添加时机
     ---@field public None integer
     ---@field public Initialize integer @初始化
     ---@field public FormationMorale integer @布阵-补给
     ---@field public FormationArmyType integer @布阵-兵种
     ---@field public FormationTechnology integer @布阵-科技
     ---@field public FormationEquipment integer @布阵-装备
     ---@field public FormationHeroIdentity integer @布阵-身份
     ---@field public FormationTacticsArmyType integer @布阵-战法-兵种
     ---@field public FormationTacticsFormation integer @布阵-战法-阵法
     ---@field public FormationTacticsOther integer @布阵-战法-其他
    ['BuffAddTiming'] = {   None=0,  Initialize=1,  FormationMorale=2,  FormationArmyType=3,  FormationTechnology=4,  FormationEquipment=5,  FormationHeroIdentity=6,  FormationTacticsArmyType=7,  FormationTacticsFormation=8,  FormationTacticsOther=9,  };
    ---@class BuffAttributeType @Buff属性类型
     ---@field public TotalRound integer @总回合数
    ['BuffAttributeType'] = {   TotalRound=1,  };
    ---@class BuffGroupConflictRule @Buff组冲突规则
     ---@field public Invalid integer @目标存在同类buff组的buff时，新BUFF无法添加
     ---@field public Cover integer @目标存在同类buff组的buff时，新BUFF可以添加，且添加前清除该BUFF组其他BUFF
     ---@field public Round integer @目标存在同类buff组的buff时,新BUFF如果剩余回合数更长,则执行【覆盖】,否则【失效】
     ---@field public Attribute integer @目标存在同类buff组的buff时，如果来源【%属性】更高，则执行【覆盖】，否则【失效】
     ---@field public Coexist integer @目标存在同类buff组的buff时，两个BUFF共存
    ['BuffGroupConflictRule'] = {   Invalid=1,  Cover=2,  Round=3,  Attribute=4,  Coexist=5,  };
    ---@class BuffGroupType @Buff组类型
     ---@field public DamaageDot1 integer @火攻
     ---@field public DamaageDot2 integer @水攻
     ---@field public NegativeDot integer @雷击
     ---@field public Control integer @控制效果
     ---@field public NegativeCanStacked integer @减益可叠加
     ---@field public NegativeNotStacked integer @减益不可叠加
     ---@field public PositiveCanStacked integer @增益可叠加
     ---@field public PositiveNotStacked integer @增益不可叠加
    ['BuffGroupType'] = {   DamaageDot1=1,  DamaageDot2=2,  NegativeDot=3,  Control=4,  NegativeCanStacked=5,  NegativeNotStacked=6,  PositiveCanStacked=7,  PositiveNotStacked=8,  };
    ---@class BuffOverlayRule @buff叠加规则（层数已满时）
     ---@field public Replace integer @新buff顶替旧buff
     ---@field public Refresh integer @新buff无法添加；但是旧buff的持续时间刷新至最新
     ---@field public ReplaceMaxRemainRound integer @根据剩余最大回合来顶替
    ['BuffOverlayRule'] = {   Replace=1,  Refresh=2,  ReplaceMaxRemainRound=3,  };
    ---@class BuffRemoveTiming @战斗Buff自然移除时机
     ---@field public AfterHeroTurnEnd integer @英雄自身轮次结束后
     ---@field public AfterRoundEnd integer @大回合结束后
    ['BuffRemoveTiming'] = {   AfterHeroTurnEnd=1,  AfterRoundEnd=2,  };
    ---@class BuffSourceType @部队buff来源
     ---@field public InBattle integer @局内
     ---@field public HomeTech integer @内城科技
     ---@field public SandMap integer @沙盘地图
     ---@field public Team integer @编队
    ['BuffSourceType'] = {   InBattle=0,  HomeTech=1,  SandMap=2,  Team=3,  };
    ---@class BuffStackRule @buff叠层规则（层数未满时）
     ---@field public Mutex integer @每层独立计算时间
     ---@field public Refresh integer @刷新所有层时间
    ['BuffStackRule'] = {   Mutex=1,  Refresh=2,  };
    ---@class BuffType @buff类型
     ---@field public Control integer @控制
     ---@field public Sustained integer @持续
     ---@field public Positive integer @增益
     ---@field public Special integer @特殊
    ['BuffType'] = {   Control=1,  Sustained=2,  Positive=3,  Special=4,  };
    ---@class BuildingType @建筑类型
     ---@field public TaiXue integer @太学
     ---@field public ZhengBingSuo integer @征兵所
     ---@field public JunYing integer @军营
     ---@field public JunWangDian integer @君王殿
     ---@field public YuMaYuan integer @御马苑
     ---@field public TieJiangPu integer @铁匠铺
     ---@field public FaMu1 integer @伐木1
     ---@field public FaMu2 integer @伐木2
     ---@field public TieKuang1 integer @铁矿1
     ---@field public TieKuang2 integer @铁矿2
     ---@field public CaiShi1 integer @采石1
     ---@field public CaiShi2 integer @采石2
     ---@field public NongChang1 integer @农场1
     ---@field public NongChang2 integer @农场2
     ---@field public CangKu integer @仓库
     ---@field public JiShi integer @集市
     ---@field public MinJu integer @民居
     ---@field public ZaoBiChang integer @造币厂
    ['BuildingType'] = {   TaiXue=1000001,  ZhengBingSuo=1000002,  JunYing=1000003,  JunWangDian=1000004,  YuMaYuan=1000005,  TieJiangPu=1000006,  FaMu1=1000007,  FaMu2=1000008,  TieKuang1=1000009,  TieKuang2=1000010,  CaiShi1=1000011,  CaiShi2=1000012,  NongChang1=1000013,  NongChang2=1000014,  CangKu=1000015,  JiShi=1000016,  MinJu=1000017,  ZaoBiChang=1000018,  };
    ---@class BuildStatus @建筑状态
     ---@field public Building integer @建造中
     ---@field public Idle integer @空闲
     ---@field public Upgrading integer @升级中
     ---@field public Dismantling integer @拆除中
    ['BuildStatus'] = {   Building=1,  Idle=2,  Upgrading=3,  Dismantling=4,  };
    ---@class Camp @阵营
     ---@field public None integer @无阵营
     ---@field public A integer @左边的阵营
     ---@field public B integer @右边的阵营
    ['Camp'] = {   None=0,  A=1,  B=2,  };
    ---@class CareerType @职业类型枚举
     ---@field public DEFAULT integer @空
     ---@field public FIELD_MASTER integer @田师
    ['CareerType'] = {   DEFAULT=0,  FIELD_MASTER=101,  };
    ---@class ChannelType @频道类型
     ---@field public Sys integer @系统
     ---@field public World integer @世界
     ---@field public Clan integer @世族
     ---@field public Prefecture integer @地区
    ['ChannelType'] = {   Sys=1,  World=2,  Clan=3,  Prefecture=4,  };
    ---@class ChatFriendTab @聊天好友分页枚举
     ---@field public Contacts integer @联系人
     ---@field public Friend integer @好友
     ---@field public Blacklist integer @黑名单
    ['ChatFriendTab'] = {   Contacts=101,  Friend=102,  Blacklist=103,  };
    ---@class CoinType @货币类型
     ---@field public Gold integer @金币
     ---@field public Copper integer @铜币
     ---@field public Wood integer @木头
     ---@field public Iron integer @铁块
     ---@field public Stone integer @石头
     ---@field public Food integer @粮草
     ---@field public Soldier integer @预备兵
     ---@field public Jade integer @魂玉
     ---@field public BambooLetter integer @玉环
     ---@field public Farmer integer @居民
     ---@field public Starstone integer @星石
    ['CoinType'] = {   Gold=100001,  Copper=100002,  Wood=100003,  Iron=100004,  Stone=100005,  Food=100006,  Soldier=100007,  Jade=100008,  BambooLetter=100009,  Farmer=100010,  Starstone=100011,  };
    ---@class CrowdControlType
     ---@field public Silence integer @技穷
     ---@field public Disarm integer @缴械
     ---@field public Stun integer @震慑
     ---@field public Starve integer @断粮
     ---@field public AdditionalCaptain integer @额外军职-主公
     ---@field public AdditionalPioneer integer @额外军职-先锋
     ---@field public AdditionalAdviser integer @额外军职-军师
     ---@field public Taunt integer @普攻锁定目标
     ---@field public Deterrence integer @主动、追击战法锁定目标
     ---@field public Confusion integer @混乱
     ---@field public BanPlainAttack integer @无法使用普通攻击
     ---@field public BanActive integer @无法使用主动战法
     ---@field public BanPursue integer @无法使用追击战法
    ['CrowdControlType'] = {   Silence=1,  Disarm=2,  Stun=3,  Starve=4,  AdditionalCaptain=6,  AdditionalPioneer=7,  AdditionalAdviser=8,  Taunt=9,  Deterrence=10,  Confusion=11,  BanPlainAttack=12,  BanActive=13,  BanPursue=14,  };
    ---@class DamageFormulaArgs @伤害公式参数
     ---@field public A integer @进攻方兵力
     ---@field public B integer @兵力系数
     ---@field public C integer @属性差值
     ---@field public C1 integer @进攻方武力/智力值
     ---@field public C2 integer @防守方统帅值
     ---@field public C3 integer @防守方智力值
     ---@field public C4 integer @进攻方破甲/看破百分比
     ---@field public C5 integer @常量&lt;C5&gt;
     ---@field public D integer @战法伤害系数
     ---@field public E integer @过程类增减伤
     ---@field public E1 integer @伤害增加
     ---@field public E3 integer @防守方受伤减少
     ---@field public E4 integer @防守方红度减伤
     ---@field public F integer @结算类增减伤
     ---@field public F1 integer @进攻方施加的受到伤害增加
     ---@field public F2 integer @防守方施加的造成伤害减少
     ---@field public F3 integer @会心/奇谋伤害
     ---@field public G integer @独立增减伤
     ---@field public G1 integer @红度增伤
     ---@field public G2 integer @兵种克制
     ---@field public G3 integer @进攻方独立兵刃/谋略伤害增减伤
     ---@field public G4 integer @防守方受到兵刃/独立谋略伤害增减伤
     ---@field public G5 integer @补给增减伤
     ---@field public W integer @兵力保底伤害
     ---@field public M integer @常量&lt;M&gt;
     ---@field public R integer @会心/奇谋几率
    ['DamageFormulaArgs'] = {   A=1,  B=2,  C=3,  C1=4,  C2=5,  C3=6,  C4=7,  C5=8,  D=9,  E=10,  E1=11,  E3=12,  E4=13,  F=14,  F1=15,  F2=16,  F3=17,  G=18,  G1=19,  G2=20,  G3=21,  G4=22,  G5=26,  W=23,  M=24,  R=25,  };
    ---@class DamageRange @伤害范围
     ---@field public Single integer @单体伤害
     ---@field public Aoe integer @群体伤害
     ---@field public Buff integer @Buff伤害
    ['DamageRange'] = {   Single=0,  Aoe=1,  Buff=2,  };
    ---@class DetailRecordType @战报类型
     ---@field public SetHero integer
     ---@field public BattleStart integer
     ---@field public BattleStartMorale integer
     ---@field public BattleStartArmyType integer
     ---@field public BattleStartEquipment integer
     ---@field public BattleStartTactics integer
     ---@field public BattleStartTechnology integer
     ---@field public BattleStartHeroIdentity integer
     ---@field public RoundStart integer
     ---@field public HeroTurnStart integer
     ---@field public RoundEnd integer
     ---@field public HeroTurnEnd integer
     ---@field public Result integer
     ---@field public Tactic integer
     ---@field public Damage integer
     ---@field public Heal integer
     ---@field public AddBuff integer
     ---@field public RemoveBuff integer
     ---@field public TriggerBuff integer
     ---@field public DecreaseBuff integer
     ---@field public AddCrowdControl integer
     ---@field public RemoveCrowdControl integer
     ---@field public ModifyAttribute integer
     ---@field public SetVariable integer
     ---@field public TacticFailed integer
     ---@field public TriggerBuffFailed integer
     ---@field public TacticPrepare integer
     ---@field public Combo integer
     ---@field public ComboFailed integer
     ---@field public Dodge integer
     ---@field public RefreshBuff integer
     ---@field public AttackVamp integer
     ---@field public IntelligenceVamp integer
     ---@field public Immune integer
     ---@field public ModifyBuff integer
     ---@field public TacticDisable integer
     ---@field public ModifyTactic integer
     ---@field public PlainAttackDisable integer
     ---@field public TacticAwakening integer
     ---@field public InterruptPrepare integer
     ---@field public Insight integer
     ---@field public CheckCrowdControl integer
     ---@field public ManuTriggerBuff integer
     ---@field public RecoverableToDeath integer
     ---@field public PlainAttackTarget integer
    ['DetailRecordType'] = {   SetHero=1,  BattleStart=101,  BattleStartMorale=102,  BattleStartArmyType=103,  BattleStartEquipment=104,  BattleStartTactics=105,  BattleStartTechnology=106,  BattleStartHeroIdentity=107,  RoundStart=151,  HeroTurnStart=152,  RoundEnd=153,  HeroTurnEnd=154,  Result=199,  Tactic=201,  Damage=202,  Heal=203,  AddBuff=204,  RemoveBuff=205,  TriggerBuff=206,  DecreaseBuff=207,  AddCrowdControl=208,  RemoveCrowdControl=209,  ModifyAttribute=210,  SetVariable=211,  TacticFailed=212,  TriggerBuffFailed=213,  TacticPrepare=214,  Combo=215,  ComboFailed=216,  Dodge=217,  RefreshBuff=218,  AttackVamp=219,  IntelligenceVamp=220,  Immune=221,  ModifyBuff=222,  TacticDisable=223,  ModifyTactic=224,  PlainAttackDisable=225,  TacticAwakening=226,  InterruptPrepare=227,  Insight=228,  CheckCrowdControl=229,  ManuTriggerBuff=230,  RecoverableToDeath=998,  PlainAttackTarget=999,  };
    ---@class ECheckOpen @功能开启模块枚举
     ---@field public None integer @占位
     ---@field public Mail integer @开启邮件功能
    ['ECheckOpen'] = {   None=0,  Mail=1,  };
    ---@class ElementalType @元素类型
     ---@field public Wind integer @风
     ---@field public Forest integer @林
     ---@field public Fire integer @火
     ---@field public Mountain integer @山
    ['ElementalType'] = {   Wind=0,  Forest=1,  Fire=2,  Mountain=3,  };
    ---@class EquipEntriesType @装备词条类型
     ---@field public None integer @无词条
     ---@field public Single integer @单词条
     ---@field public Double integer @双词条
     ---@field public ReverseDouble integer @正负词条
    ['EquipEntriesType'] = {   None=0,  Single=1,  Double=2,  ReverseDouble=3,  };
    ---@class EquipQuality @品质
     ---@field public None integer @无
     ---@field public Normal integer @普通
     ---@field public Rare integer @稀有
     ---@field public Legend integer @传说
     ---@field public Special integer @专属
    ['EquipQuality'] = {   None=-1,  Normal=1,  Rare=2,  Legend=3,  Special=4,  };
    ---@class EquipType @装备类型
     ---@field public Weapon integer @武器
     ---@field public Defense integer @防具
     ---@field public Horse integer @坐骑
    ['EquipType'] = {   Weapon=1,  Defense=2,  Horse=3,  };
    ---@class FlyStatusChange @飞行状态改变
     ---@field public ReachTop integer @最高点
     ---@field public LandGround integer @落地
    ['FlyStatusChange'] = {   ReachTop=1,  LandGround=2,  };
    ---@class FuncOpenTrigger @功能开启触发类型枚举
     ---@field public MainCity integer @城建
     ---@field public Test2 integer @测试2
     ---@field public Max integer @最大
    ['FuncOpenTrigger'] = {   MainCity=1,  Test2=2,  Max=3,  };
    ---@class GachaCntType @抽卡次数类型
     ---@field public Single integer @单抽
     ---@field public Five integer @五连抽
     ---@field public Twenty integer @二十连抽
    ['GachaCntType'] = {   Single=1,  Five=2,  Twenty=3,  };
    ---@class GeneralArmyType
     ---@field public Shield integer @盾兵
     ---@field public Cavalry integer @骑兵
     ---@field public Pikemen integer @枪兵
     ---@field public Archers integer @弓兵
     ---@field public None integer @无高级兵种
     ---@field public TongQueTaiLing integer @铜雀台伶
     ---@field public YuanRongNuBing integer @元戎弩兵
    ['GeneralArmyType'] = {   Shield=1,  Cavalry=2,  Pikemen=3,  Archers=4,  None=0,  TongQueTaiLing=41,  YuanRongNuBing=42,  };
    ---@class GeneralAttrType @武将属性
     ---@field public WuLi integer @武力
     ---@field public ZhiLi integer @智力
     ---@field public TongShuai integer @统率
     ---@field public XianGong integer @速度
    ['GeneralAttrType'] = {   WuLi=1,  ZhiLi=2,  TongShuai=4,  XianGong=5,  };
    ---@class GeneralDynasty
     ---@field public Wei integer @魏国
     ---@field public Shu integer @蜀国
     ---@field public Wu integer @吴国
     ---@field public Qun integer @群雄
    ['GeneralDynasty'] = {   Wei=1,  Shu=2,  Wu=3,  Qun=4,  };
    ---@class GeneralRoleType @武将定位
     ---@field public BingRen integer @兵刃
     ---@field public MouLue integer @谋略
     ---@field public WenWu integer @文武
     ---@field public FuZhu integer @辅助
     ---@field public ZhiLiao integer @治疗
     ---@field public FangYu integer @防御
    ['GeneralRoleType'] = {   BingRen=1,  MouLue=2,  WenWu=3,  FuZhu=4,  ZhiLiao=5,  FangYu=6,  };
    ---@class GMEvent @GM事件
     ---@field public ApplyClan integer @申请世族
     ---@field public LeaveClan integer @离开世族
     ---@field public OccupyLand integer @占领地块
     ---@field public TransferArmy integer @转移部队
     ---@field public SetAnnounceTimeout integer @设置宣战倒计时
    ['GMEvent'] = {   ApplyClan=1,  LeaveClan=2,  OccupyLand=3,  TransferArmy=4,  SetAnnounceTimeout=5,  };
    ---@class HealFormulaArgs @治疗公式参数
     ---@field public A integer @施法者智力值
     ---@field public B integer @战法恢复系数
     ---@field public C integer @恢复效果提升
     ---@field public D integer @兵力系数
     ---@field public D1 integer @常量&lt;D1&gt;
     ---@field public M integer @常量&lt;M&gt;
    ['HealFormulaArgs'] = {   A=1,  B=2,  C=3,  D=4,  D1=5,  M=6,  };
    ---@class HudLayer @Hud层级
     ---@field public GeographySecondary integer @次要地理信息
     ---@field public Geography integer @地理信息
     ---@field public Building integer @建筑
     ---@field public Army integer @军队
     ---@field public SelfArmy integer @玩家军队
     ---@field public Battle integer @战斗
     ---@field public Interactive integer @交互界面
     ---@field public Always integer @常显
    ['HudLayer'] = {   GeographySecondary=0,  Geography=1,  Building=2,  Army=3,  SelfArmy=4,  Battle=5,  Interactive=6,  Always=7,  };
    ---@class IdSegType @id段类型
     ---@field public Currency integer @货币
     ---@field public Item integer @道具
     ---@field public Equip integer @装备
     ---@field public Horse integer @坐骑
     ---@field public Token integer @信物
     ---@field public Material integer @材料
     ---@field public EffectItem integer @立刻生效道具
     ---@field public Hero integer @武将
     ---@field public BattleTactic integer @战法
    ['IdSegType'] = {   Currency=1,  Item=2,  Equip=3,  Horse=4,  Token=5,  Material=6,  EffectItem=7,  Hero=8,  BattleTactic=9,  };
    ---@class InConType @和容器关系
     ---@field public InBase integer @部队在回城点
     ---@field public InDefend integer @驻守容器中
     ---@field public InVehicle integer @部队操控器械中
     ---@field public Annex integer @城池的附属建筑
    ['InConType'] = {   InBase=101,  InDefend=102,  InVehicle=103,  Annex=104,  };
    ---@class InstantTacticAttributeType
     ---@field public LeftCD integer @剩余CD
     ---@field public PrepareStep integer @准备回合
     ---@field public ExtraProb integer @额外发动率
    ['InstantTacticAttributeType'] = {   LeftCD=1,  PrepareStep=2,  ExtraProb=3,  };
    ---@class InteractiveBehavior @多个体交互行为
     ---@field public Attack integer @A攻击B
     ---@field public Defend integer @A驻守B
     ---@field public Occupy integer @A攻占B地块
     ---@field public Raid integer @A扫荡B地块
     ---@field public Exploit integer @A开发B地块
     ---@field public ThrowStone integer @A向B投石
    ['InteractiveBehavior'] = {   Attack=1,  Defend=2,  Occupy=3,  Raid=4,  Exploit=5,  ThrowStone=6,  };
    ---@class JumpTrigger @跳转
     ---@field public OpenUpBuildingLevelWindow integer @打开建筑升级界面
     ---@field public OpenTechWindow integer @打开太学界面
     ---@field public JumpToBuild integer @定位到指定建筑
     ---@field public UISmithyMainMakeNew integer @铁匠铺打造界面
     ---@field public UISmithyMainMakeBetter integer @铁匠铺锻造界面
     ---@field public UISmithyMainReforge integer @铁匠铺重塑界面
     ---@field public UITrainingHorse integer @御马苑界面
    ['JumpTrigger'] = {   OpenUpBuildingLevelWindow=1,  OpenTechWindow=2,  JumpToBuild=3,  UISmithyMainMakeNew=4,  UISmithyMainMakeBetter=5,  UISmithyMainReforge=6,  UITrainingHorse=7,  };
    ---@class LandBuffType @地块Buff类型
     ---@field public AddProduct integer @增加产量
    ['LandBuffType'] = {   AddProduct=101,  };
    ---@class LandMgrSortType @领土分类
     ---@field public ResourceLand integer @资源地
     ---@field public RoadLand integer @道路地
     ---@field public Building integer @建筑地
     ---@field public Favorite integer @收藏地
    ['LandMgrSortType'] = {   ResourceLand=1,  RoadLand=2,  Building=3,  Favorite=4,  };
    ---@class MailType @邮件分类
     ---@field public AllyOrder integer @法令
     ---@field public Ally integer @同盟
     ---@field public AllyGroup integer @分组
     ---@field public System integer @系统
     ---@field public Announce integer @公告
    ['MailType'] = {   AllyOrder=1,  Ally=2,  AllyGroup=3,  System=4,  Announce=5,  };
    ---@class MapViewLevel @沙盘视图层级
     ---@field public None integer @无
     ---@field public Detail integer @最详细的视图层级，显示城池模型、兵模树木等
     ---@field public Simplified integer @显示大城模型，隐藏树木、兵模等细节，其他建筑以缩略图显示
     ---@field public Brief integer @显示大城与玩家主城的缩略模型，其他信息以更简略的形式(譬如小圆点)展示
     ---@field public Strategic integer @大城模型进一步缩略，同时展示沙盘染色
     ---@field public Overview integer @最简略的视图层级，完全隐藏分块地形，使用平面3d沙盘代替
    ['MapViewLevel'] = {   None=0,  Detail=1,  Simplified=2,  Brief=4,  Strategic=8,  Overview=16,  };
    ---@class MoveType @移动类型
     ---@field public Default integer @默认
     ---@field public Return integer @主动回城
     ---@field public Escape integer @战败溃逃
     ---@field public Deploy integer @调动
    ['MoveType'] = {   Default=1,  Return=2,  Escape=3,  Deploy=4,  };
    ---@class MsgType @消息类型
     ---@field public Normal integer @默认
     ---@field public Invalid integer @无效提示
    ['MsgType'] = {   Normal=1,  Invalid=2,  };
    ---@class NewbeeGuideEventType @新手引导事件类型
     ---@field public PV integer @PV内容
     ---@field public Dialogue integer @剧情对话
     ---@field public CreateId integer @创建ID
     ---@field public ClickOp integer @点击操作指引
     ---@field public ClickCheck integer @点击查看指引
     ---@field public MoveCheck integer @拖动查看指引
    ['NewbeeGuideEventType'] = {   PV=1,  Dialogue=2,  CreateId=3,  ClickOp=4,  ClickCheck=5,  MoveCheck=6,  };
    ---@class NodeTaskStatus @节点的执行状态
     ---@field public Default integer @默认
     ---@field public Running integer @运行时
    ['NodeTaskStatus'] = {   Default=1,  Running=2,  };
    ---@class NPCRandomPool @NPC随机池规则
     ---@field public WorldLand integer @第一个按当前日期计算出，后续纯随机
     ---@field public FullRandom integer @完全随机
    ['NPCRandomPool'] = {   WorldLand=1,  FullRandom=2,  };
    ---@class ObstacleType @阻挡类型
     ---@field public Land integer @空地
     ---@field public Obstacle integer @阻挡
     ---@field public DynamicObstacle integer @动态阻挡
    ['ObstacleType'] = {   Land=0,  Obstacle=1,  DynamicObstacle=2,  };
    ---@class OwnedStatus @收集状态
     ---@field public Owned integer @已拥有
     ---@field public NotOwned integer @未拥有
    ['OwnedStatus'] = {   Owned=1,  NotOwned=2,  };
    ---@class PeriodicType @周期类型
     ---@field public Total integer @总共
     ---@field public Daily integer @每日
     ---@field public Weekly integer @每周
    ['PeriodicType'] = {   Total=1,  Daily=2,  Weekly=3,  };
    ---@class Position @军职
     ---@field public Captain integer @1号位是主公
     ---@field public Pioneer integer @2号位是先锋
     ---@field public Adviser integer @3号位是军师
    ['Position'] = {   Captain=1,  Pioneer=2,  Adviser=3,  };
    ---@class PositiveEffectType
     ---@field public Insight integer @洞察
     ---@field public Fast integer @先机
     ---@field public Inevitable integer @必中
     ---@field public Agile integer @机敏
     ---@field public Healer integer @神医
     ---@field public AvoidDeath integer @免死
    ['PositiveEffectType'] = {   Insight=1,  Fast=2,  Inevitable=3,  Agile=4,  Healer=5,  AvoidDeath=6,  };
    ---@class ProduceSrcType @资源产出来源
     ---@field public WorldLand integer @大世界地块
     ---@field public HomeBuilding integer @内城建筑
     ---@field public CityLand integer @城池开垦地
     ---@field public Farmer integer @农民
     ---@field public Else integer @其他
     ---@field public ClanBuff integer @世族加成
     ---@field public City integer @城池
    ['ProduceSrcType'] = {   WorldLand=1,  HomeBuilding=2,  CityLand=3,  Farmer=4,  Else=5,  ClanBuff=6,  City=7,  };
    ---@class ProsperitySrc @繁荣度来源
     ---@field public HomeBuilding integer @内城建筑
     ---@field public HomeTech integer @内城科技
     ---@field public WorldLand integer @沙盘地块
     ---@field public WorldBuilding integer @沙盘建筑
    ['ProsperitySrc'] = {   HomeBuilding=1,  HomeTech=2,  WorldLand=3,  WorldBuilding=4,  };
    ---@class PurseType @战法子类型
     ---@field public Normal integer @普通
     ---@field public Extra integer @启动
    ['PurseType'] = {   Normal=0,  Extra=1,  };
    ---@class QualificationRank @资质评级
     ---@field public C integer @拙
     ---@field public B integer @常
     ---@field public A integer @精
     ---@field public S integer @极
    ['QualificationRank'] = {   C=0,  B=1,  A=2,  S=3,  };
    ---@class Quality @品质等级
     ---@field public Blue integer @蓝
     ---@field public Violet integer @紫
     ---@field public Gold integer @金
     ---@field public Red integer @红
    ['Quality'] = {   Blue=1,  Violet=2,  Gold=3,  Red=4,  };
    ---@class RankType @排行榜
     ---@field public Personal integer @个人霸业积分
     ---@field public Ally integer @联盟霸业积分
     ---@field public Prosperity integer @个人繁荣榜
     ---@field public Merit integer @个人武勋榜
     ---@field public MeritWeek integer @个人武勋周榜
     ---@field public FirstOccupy integer @个人首占榜
     ---@field public ClanProsperity integer @世族繁荣榜
     ---@field public ClanMerit integer @世族武勋榜
     ---@field public ClanMeritWeek integer @世族武勋周榜
    ['RankType'] = {   Personal=1,  Ally=2,  Prosperity=3,  Merit=4,  MeritWeek=5,  FirstOccupy=6,  ClanProsperity=7,  ClanMerit=8,  ClanMeritWeek=9,  };
    ---@class Reason @变更原因
     ---@field public GM integer @管理员
     ---@field public MODIFY_ARMY integer @调整部队
     ---@field public RESET_TACTIC integer @重置战法
     ---@field public UPGRADE_TACTIC integer @升级战法
     ---@field public UPGRADE_HERO_STAR integer @武将升星
     ---@field public UPGRADE_TACTIC_STAR integer @战法升星
     ---@field public CHOOSE_HERO_IDENTITY integer @武将身份演化选择
     ---@field public DECOMPOSE_TOKEN integer @分解信物
     ---@field public DECOMPOSE_EQUIP integer @分解装备
     ---@field public FORGE_EQUIP integer @锻造装备
     ---@field public REBUILD_EQUIP integer @重塑装备
     ---@field public BUILD_EQUIP integer @打造装备
     ---@field public TRAIN_HORSE integer @驯马
     ---@field public SOLD_HORSE integer @出售坐骑
     ---@field public JI_DIAN_REWARD integer @稷神祭典奖励
     ---@field public WO_ZHONG_SHENG_YOU integer @无中生有
     ---@field public USE_ITEM integer @使用道具
     ---@field public MAIL_REWARD integer @领取邮件
     ---@field public REFRESH_SHOP integer @刷新商店
     ---@field public BUY_GOODS integer @购买货物
     ---@field public RES_TRADE integer @资源贸易
     ---@field public TASK_REWARD integer @任务奖励
     ---@field public HOME_BUILDING integer @内城建造
     ---@field public UPGRADE_HOME_TECH integer @升级内城科技
     ---@field public PRODUCE integer @产出
     ---@field public AMBITIONS_REWARD integer @霸业阶段奖励
     ---@field public DRILL_LEVEL_REWARD integer @军演关卡奖励
     ---@field public DRILL_CHAPTER_REWARD integer @军演章节奖励
     ---@field public DRILL_HANGING_REWARD integer @军演挂机奖励
     ---@field public GACHA integer @抽卡
     ---@field public CLAN_DONATE integer @世族捐赠
     ---@field public CLAN_CREATE integer @创建世族
     ---@field public PAY integer @支付
     ---@field public NEWBEE integer @新手
     ---@field public BUILD_WORLD_BUILDING integer @建造大世界建筑
     ---@field public UPGRADE_WORLD_BUILDING integer @升级大世界建筑
     ---@field public BUILD_ALLY_WORLD_BUILDING integer @建造联盟大世界建筑
     ---@field public UPGRADE_ALLY_WORLD_BUILDING integer @升级联盟大世界建筑
    ['Reason'] = {   GM=0,  MODIFY_ARMY=101,  RESET_TACTIC=102,  UPGRADE_TACTIC=103,  UPGRADE_HERO_STAR=104,  UPGRADE_TACTIC_STAR=105,  CHOOSE_HERO_IDENTITY=106,  DECOMPOSE_TOKEN=201,  DECOMPOSE_EQUIP=202,  FORGE_EQUIP=203,  REBUILD_EQUIP=204,  BUILD_EQUIP=205,  TRAIN_HORSE=206,  SOLD_HORSE=207,  JI_DIAN_REWARD=301,  WO_ZHONG_SHENG_YOU=302,  USE_ITEM=401,  MAIL_REWARD=402,  REFRESH_SHOP=403,  BUY_GOODS=404,  RES_TRADE=405,  TASK_REWARD=501,  HOME_BUILDING=601,  UPGRADE_HOME_TECH=602,  PRODUCE=603,  AMBITIONS_REWARD=701,  DRILL_LEVEL_REWARD=801,  DRILL_CHAPTER_REWARD=802,  DRILL_HANGING_REWARD=803,  GACHA=901,  CLAN_DONATE=1001,  CLAN_CREATE=1002,  PAY=1101,  NEWBEE=1201,  BUILD_WORLD_BUILDING=1300,  UPGRADE_WORLD_BUILDING=1301,  BUILD_ALLY_WORLD_BUILDING=1302,  UPGRADE_ALLY_WORLD_BUILDING=1303,  };
    ---@class RelationShip @关系
     ---@field public Self integer @自己
     ---@field public SameAlly integer @盟友
     ---@field public FreeMan integer @散人
     ---@field public DiffAlly integer @不同联盟
     ---@field public Monster integer @野怪
     ---@field public SameClan integer @族友
    ['RelationShip'] = {   Self=0,  SameAlly=1,  FreeMan=2,  DiffAlly=3,  Monster=4,  SameClan=5,  };
    ---@class ReportReason @举报类型
     ---@field public spam integer @垃圾信息
     ---@field public cheating integer @举报外挂
     ---@field public inappropriate integer @不良发言
     ---@field public illegal integer @不法行为
     ---@field public nickname integer @不雅昵称
     ---@field public advertising integer @广告拉人
     ---@field public others integer @举报其他
    ['ReportReason'] = {   spam=1,  cheating=2,  inappropriate=3,  illegal=4,  nickname=5,  advertising=6,  others=7,  };
    ---@class Role @角色
     ---@field public None integer @无
     ---@field public Lord integer @主公
     ---@field public Loyalist integer @忠臣
     ---@field public Rebel integer @反贼
     ---@field public Traitor integer @内奸
    ['Role'] = {   None=0,  Lord=1,  Loyalist=2,  Rebel=3,  Traitor=4,  };
    ---@class S1Camp @S1阵营
     ---@field public None integer @无
     ---@field public Lord integer @主公阵营
     ---@field public Rebel integer @反贼阵营
     ---@field public Traitor integer @内奸阵营
    ['S1Camp'] = {   None=0,  Lord=1,  Rebel=2,  Traitor=3,  };
    ---@class SAvatarEvent @Avatar事件枚举
     ---@field public DEFAULT integer @空
     ---@field public PROSPERITY_CHANGE integer @繁荣度变化
     ---@field public HOME_BUILDING_UPGRADE integer @建筑升级建造
     ---@field public RES_PRODUCE_SPEED_CHANGE integer @资源产量变化
     ---@field public HOME_TECH_UPGRADE integer @内城科技升级解锁
     ---@field public HERO_LEVE_UPGRADE integer @武将升级
     ---@field public ARMY_SOLDIER_CHANGE integer @部队兵力变化
     ---@field public GET_NEW_TACTIC integer @获得战法
     ---@field public HOME_FARM_UPGRADE integer @开垦地升级
     ---@field public NEW_RES_LAND integer @获得资源地
     ---@field public UNLOCK_TALENT integer @解锁天赋
     ---@field public ADD_COIN integer @获得货币
     ---@field public DEC_COIN integer @减少货币
     ---@field public TASK_COMPLETE integer @任务完成
     ---@field public COIN_LIMIT_CHANGE integer @货币上限变动
     ---@field public FARMER_WORK integer @分配居民
     ---@field public OCCUPY_LAND integer @占领城外土地
     ---@field public GACHA_DRAW integer @寻访
     ---@field public CHALLENGE_DRILL integer @军演挑战
     ---@field public HERO_EQUIP_TACTIC integer @武将学习战法
     ---@field public BORN_WITH_TACTIC_LEVEL_UP integer @自带战法升级
     ---@field public SWITCH_ARMY_UNIT integer @交换武将
     ---@field public DEL_ARMY_UNIT integer @下阵武将
     ---@field public ADD_ARMY_UNIT integer @上阵武将
     ---@field public ARMY_SOLDIER_MAX_CHANGE integer @部队兵力上限变化
    ['SAvatarEvent'] = {   DEFAULT=0,  PROSPERITY_CHANGE=10001,  HOME_BUILDING_UPGRADE=10002,  RES_PRODUCE_SPEED_CHANGE=10003,  HOME_TECH_UPGRADE=10004,  HERO_LEVE_UPGRADE=10005,  ARMY_SOLDIER_CHANGE=10006,  GET_NEW_TACTIC=10007,  HOME_FARM_UPGRADE=10008,  NEW_RES_LAND=10009,  UNLOCK_TALENT=10010,  ADD_COIN=10011,  DEC_COIN=10012,  TASK_COMPLETE=10013,  COIN_LIMIT_CHANGE=10014,  FARMER_WORK=10015,  OCCUPY_LAND=10016,  GACHA_DRAW=10017,  CHALLENGE_DRILL=10018,  HERO_EQUIP_TACTIC=10019,  BORN_WITH_TACTIC_LEVEL_UP=10020,  SWITCH_ARMY_UNIT=10021,  DEL_ARMY_UNIT=10022,  ADD_ARMY_UNIT=10023,  ARMY_SOLDIER_MAX_CHANGE=10024,  };
    ---@class ScheduleType @排期类型
     ---@field public CommonDaily integer @日常刷天
     ---@field public AmbitionsDaily integer @霸业刷天
     ---@field public Nigth integer @夜间
     ---@field public CommonWeek integer @日常刷周
     ---@field public SeasonTest integer @测试赛季
     ---@field public Newbie integer @玩家新手活动示例
    ['ScheduleType'] = {   CommonDaily=1,  AmbitionsDaily=2,  Nigth=3,  CommonWeek=4,  SeasonTest=5,  Newbie=6,  };
    ---@class SelectChangeType @选中的样式
     ---@field public SELECT_ALL integer @全选
     ---@field public CANCEL_ALL integer @重置
     ---@field public NO_CHANGE integer @不变
    ['SelectChangeType'] = {   SELECT_ALL=-2,  CANCEL_ALL=-1,  NO_CHANGE=-4,  };
    ---@class Settings @设置
     ---@field public Null integer @无
     ---@field public GlobalVolumn integer @总音量
     ---@field public BackgroundVolumn integer @背景音量
     ---@field public SoundFXVolumn integer @音效音量
     ---@field public CharacterVolumn integer @角色配音
     ---@field public ImageQuality integer @画面质量
     ---@field public FrameRate integer @帧率
     ---@field public Weather integer @天气与昼夜效果
     ---@field public GridOutLine integer @地块描边
     ---@field public ColorAssist integer @色彩辅助模式
     ---@field public SandboxColorFill integer @沙盘染色
     ---@field public ColorAtmosphere integer @色彩氛围
     ---@field public Layout integer @横竖屏
     ---@field public GridVisualization integer @地块标识
     ---@field public ScreenFit integer @屏幕自适应
     ---@field public AddStranger integer @允许陌生人加好友
     ---@field public LocationRight integer @定位权限
     ---@field public PushRight integer @推送权限
     ---@field public tempRight integer @权限名称六字
    ['Settings'] = {   Null=1,  GlobalVolumn=101,  BackgroundVolumn=102,  SoundFXVolumn=103,  CharacterVolumn=104,  ImageQuality=201,  FrameRate=202,  Weather=203,  GridOutLine=211,  ColorAssist=212,  SandboxColorFill=213,  ColorAtmosphere=214,  Layout=221,  GridVisualization=301,  ScreenFit=302,  AddStranger=401,  LocationRight=411,  PushRight=412,  tempRight=413,  };
    ---@class SettingType @设置分类
     ---@field public Toggle integer @单个开关
     ---@field public EnumList integer @枚举列表
     ---@field public Slider integer @滑动条
     ---@field public ImgEnumList integer @图像枚举
     ---@field public Button integer @按钮
     ---@field public ToggleAndSlider integer @开关与滑动条
    ['SettingType'] = {   Toggle=1,  EnumList=2,  Slider=3,  ImgEnumList=4,  Button=5,  ToggleAndSlider=12,  };
    ---@class ShopType @商店分类
     ---@field public DailyShop integer @日常商店
     ---@field public MeritShop integer @武勋商店
    ['ShopType'] = {   DailyShop=1,  MeritShop=2,  };
    ---@class slg.AdministrativeHierarchy
     ---@field public County integer @县
     ---@field public Commandery integer @郡
     ---@field public Prefecture integer @州
    ['slg.AdministrativeHierarchy'] = {   County=1,  Commandery=2,  Prefecture=3,  };
    ---@class slg.MapElementLevelMask @Filter Level
     ---@field public L0 integer @等级0
     ---@field public L1 integer @等级1
     ---@field public L2 integer @等级2
     ---@field public L3 integer @等级3
     ---@field public L4 integer @等级4
     ---@field public L5 integer @等级5
     ---@field public L6 integer @等级6
     ---@field public L7 integer @等级7
     ---@field public L8 integer @等级8
     ---@field public L9 integer @等级9
     ---@field public L10 integer @等级10
     ---@field public L11 integer @等级11
     ---@field public L12 integer @等级12
    ['slg.MapElementLevelMask'] = {   L0=1,  L1=2,  L2=4,  L3=8,  L4=16,  L5=32,  L6=64,  L7=128,  L8=256,  L9=512,  L10=1024,  L11=2048,  L12=4096,  };
    ---@class slg.MapElementType
     ---@field public Empty integer @空地
     ---@field public Mountain integer @山
     ---@field public River integer @河
     ---@field public Food integer @粮食
     ---@field public Stone integer @石料
     ---@field public Wood integer @木材
     ---@field public Iron integer @铁矿
     ---@field public Copper integer @铜矿
     ---@field public DevelopableResourceNode integer @开垦地
     ---@field public FortifiedCity integer @主城府
     ---@field public Riverway integer @河道
     ---@field public Obstacle integer @障碍
     ---@field public CityWall integer @城墙
     ---@field public InnerRoad integer @城内道路
     ---@field public CityGate integer @城门
     ---@field public InnerCityGate integer @内城门
     ---@field public CityBarracks integer @城内兵营
     ---@field public SiegeCampFoundation integer @攻城地基
     ---@field public SiegeCampFoundationCenter integer @攻城地基中心
     ---@field public Ferry integer @渡口
     ---@field public FerryyPavilion integer @渡亭
     ---@field public FrontierPass integer @关堡
     ---@field public FrontierPassWall integer @关墙
     ---@field public Camp integer @营帐
     ---@field public MainCity integer @玩家主城
     ---@field public SiegeCamp integer @攻城大营
    ['slg.MapElementType'] = {   Empty=0,  Mountain=1,  River=2,  Food=3,  Stone=4,  Wood=5,  Iron=6,  Copper=7,  DevelopableResourceNode=8,  FortifiedCity=9,  Riverway=26,  Obstacle=27,  CityWall=100,  InnerRoad=101,  CityGate=102,  InnerCityGate=103,  CityBarracks=104,  SiegeCampFoundation=114,  SiegeCampFoundationCenter=115,  Ferry=150,  FerryyPavilion=151,  FrontierPass=200,  FrontierPassWall=201,  Camp=5000,  MainCity=5010,  SiegeCamp=5020,  };
    ---@class slg.MapElementTypeMask @Filter Type
     ---@field public Mountain integer @山
     ---@field public River integer @河
     ---@field public Food integer @粮食
     ---@field public Stone integer @石料
     ---@field public Wood integer @木材
     ---@field public Iron integer @铁矿
     ---@field public Copper integer @铜矿
     ---@field public DevelopableResourceNode integer @开垦地
     ---@field public FortifiedCity integer @主城府
    ['slg.MapElementTypeMask'] = {   Mountain=1,  River=2,  Food=4,  Stone=8,  Wood=16,  Iron=32,  Copper=64,  DevelopableResourceNode=128,  FortifiedCity=256,  };
    ---@class StrategyCardType @职业策牌枚举
     ---@field public NONE integer @空
     ---@field public SHA integer @杀
     ---@field public SHAN integer @闪
     ---@field public WU_ZHONG_SHENG_YOU integer @无中生有
     ---@field public WU_GU_FENG_DENG integer @五谷丰登
     ---@field public KAI_KEN integer @开垦
     ---@field public JI_SHEN_JI_DIAN integer @稷神祭典
    ['StrategyCardType'] = {   NONE=0,  SHA=2000101,  SHAN=2000102,  WU_ZHONG_SHENG_YOU=2000103,  WU_GU_FENG_DENG=2000201,  KAI_KEN=2000202,  JI_SHEN_JI_DIAN=2000203,  };
    ---@class TacticAttributeType
     ---@field public TotalCD integer @总CD
     ---@field public IsEnabled integer @是否可用
     ---@field public Probability integer @成功率
     ---@field public MaxPrepareStep integer @最大准备回合
    ['TacticAttributeType'] = {   TotalCD=1,  IsEnabled=2,  Probability=3,  MaxPrepareStep=4,  };
    ---@class TacticFeature
     ---@field public Assistance integer @辅助
     ---@field public CivilAndMilitary integer @文武
     ---@field public Pursue integer @治疗
     ---@field public Defend integer @防御
     ---@field public Strategy integer @谋略
     ---@field public Weapons integer @兵刃
    ['TacticFeature'] = {   Assistance=1,  CivilAndMilitary=2,  Pursue=3,  Defend=4,  Strategy=5,  Weapons=6,  };
    ---@class TacticType
     ---@field public Active integer @主动技
     ---@field public Passive integer @锁定技
     ---@field public Pursue integer @追击技
     ---@field public Awakening integer @觉醒技
     ---@field public PlainAttack integer @普攻
     ---@field public ArmyType integer @兵种
     ---@field public Formation integer @阵法
    ['TacticType'] = {   Active=1,  Passive=2,  Pursue=3,  Awakening=4,  PlainAttack=5,  ArmyType=6,  Formation=7,  };
    ---@class TriggerTiming
     ---@field public RoundStart integer @回合开始
     ---@field public ConsumeCard integer @消耗卡牌
    ['TriggerTiming'] = {   RoundStart=1,  ConsumeCard=2,  };
    ---@class ValueTipsColor @数值提示色
     ---@field public Enough integer @绿
     ---@field public Lack integer @红
     ---@field public Limit integer @白
     ---@field public Normal integer @橙
     ---@field public Positive integer @绿
     ---@field public Negative integer @红
    ['ValueTipsColor'] = {   Enough=1,  Lack=2,  Limit=3,  Normal=4,  Positive=5,  Negative=6,  };
    ---@class WorldBuffType @沙盘Buff类型
     ---@field public AddMoveSpeed integer @增加移速
     ---@field public AddDefendRange integer @增加驻守范围
    ['WorldBuffType'] = {   AddMoveSpeed=101,  AddDefendRange=102,  };
    ---@class ZoomDisableFlag @屏蔽缩放操作的类型
     ---@field public None integer @无
     ---@field public Paving integer @铺路时屏蔽缩放功能
    ['ZoomDisableFlag'] = {   None=0,  Paving=1,  };
    ---@class BattleAttributeType
     ---@field public Attack integer @武力
     ---@field public Intelligence integer @智力
     ---@field public RecoverableHealth integer @伤兵
     ---@field public Defense integer @统率
     ---@field public Speed integer @速度
     ---@field public AttackCritRate integer @会心几率
     ---@field public AttackCritDamage integer @会心伤害率
     ---@field public AttackDamageAdjustment integer @造成兵刃伤害
     ---@field public AttackDamageFinalAdjustment integer @造成兵刃伤害结果
     ---@field public HurtAttackAdjustment integer @受到兵刃伤害
     ---@field public HurtAttackFinalAdjustment integer @受到兵刃伤害结果
     ---@field public IntelligenceCritRate integer @奇谋几率
     ---@field public IntelligenceCritDamage integer @奇谋伤害率
     ---@field public IntelligenceDamageAdjustment integer @造成谋略伤害
     ---@field public IntelligenceDamageFinalAdjustment integer @造成谋略伤害结果
     ---@field public HurtIntelligenceAdjustment integer @受到谋略伤害
     ---@field public HurtIntelligenceFinalAdjustment integer @受到谋略伤害结果
     ---@field public HealAdjustment integer @造成治疗
     ---@field public BeHealedAdjustment integer @受到治疗
     ---@field public Health integer @兵力
     ---@field public ComboRate integer @连击率
     ---@field public AttackIgnoreDefense integer @破甲
     ---@field public IntelligenceIgnoreDefense integer @看破
     ---@field public FinalDamageCoefficient integer @格挡
     ---@field public ArmyTypeQualification integer @兵种适应性
     ---@field public ActiveTacticProbability integer @主动战法发动率
     ---@field public PursueTacticProbability integer @追击战法发动率
     ---@field public SelfActiveTacticProbability integer @自带主动战法发动率
     ---@field public SelfPursueTacticProbability integer @自带追击战法发动率
     ---@field public DodgeRate integer @闪避率
     ---@field public ExtraAttackDamageAdjustment integer @最终造成兵刃伤害
     ---@field public HurtExtraAttackDamageAdjustment integer @最终受到兵刃伤害
     ---@field public ExtraIntelligenceDamageAdjustment integer @最终造成谋略伤害
     ---@field public HurtExtraIntelligenceDamageAdjustment integer @最终受到谋略伤害
     ---@field public AttackVamp integer @倒戈
     ---@field public IntelligenceVamp integer @攻心
     ---@field public ArmyTypeCounterAdjustment integer @对克制兵种造成伤害
     ---@field public BeArmyTypeCounterAdjustment integer @受到克制兵种伤害
     ---@field public ActiveTacticDamageAdjustment integer @造成主动伤害
     ---@field public ActiveTacticHurtAdjustment integer @受到主动伤害
     ---@field public PursueTacticDamageAdjustment integer @造成追击伤害
     ---@field public PursueTacticHurtAdjustment integer @受到追击伤害
     ---@field public PlainAttackDamageAdjustment integer @造成普攻伤害
     ---@field public PlainAttackHurtAdjustment integer @受到普攻伤害
    ['BattleAttributeType'] = {   Attack=1,  Intelligence=2,  RecoverableHealth=3,  Defense=4,  Speed=5,  AttackCritRate=6,  AttackCritDamage=7,  AttackDamageAdjustment=8,  AttackDamageFinalAdjustment=9,  HurtAttackAdjustment=10,  HurtAttackFinalAdjustment=11,  IntelligenceCritRate=12,  IntelligenceCritDamage=13,  IntelligenceDamageAdjustment=14,  IntelligenceDamageFinalAdjustment=15,  HurtIntelligenceAdjustment=16,  HurtIntelligenceFinalAdjustment=17,  HealAdjustment=18,  BeHealedAdjustment=19,  Health=20,  ComboRate=21,  AttackIgnoreDefense=22,  IntelligenceIgnoreDefense=23,  FinalDamageCoefficient=24,  ArmyTypeQualification=25,  ActiveTacticProbability=26,  PursueTacticProbability=27,  SelfActiveTacticProbability=28,  SelfPursueTacticProbability=29,  DodgeRate=30,  ExtraAttackDamageAdjustment=31,  HurtExtraAttackDamageAdjustment=32,  ExtraIntelligenceDamageAdjustment=33,  HurtExtraIntelligenceDamageAdjustment=34,  AttackVamp=35,  IntelligenceVamp=36,  ArmyTypeCounterAdjustment=37,  BeArmyTypeCounterAdjustment=38,  ActiveTacticDamageAdjustment=39,  ActiveTacticHurtAdjustment=40,  PursueTacticDamageAdjustment=41,  PursueTacticHurtAdjustment=42,  PlainAttackDamageAdjustment=43,  PlainAttackHurtAdjustment=44,  };
}

local beans = {}
    do
    ---@class battle.NpcDifficultyConfig  @npc难度配置
     ---@field public min_level integer @最低等级
     ---@field public total_health integer @总兵力
        local class = {
            { name='min_level', type='integer'},
            { name='total_health', type='integer'},
        }
        beans['battle.NpcDifficultyConfig'] = class
    end
    do
    ---@class battle.NPCHeroUnit 
     ---@field public HeroId integer
     ---@field public Level integer
     ---@field public Health integer
     ---@field public SelfTactics_Lv integer
     ---@field public Tactics1 integer
     ---@field public Tactics1_Lv integer
     ---@field public Tactics2 integer
     ---@field public Tactics2_Lv integer
        local class = {
            { name='HeroId', type='integer'},
            { name='Level', type='integer'},
            { name='Health', type='integer'},
            { name='SelfTactics_Lv', type='integer'},
            { name='Tactics1', type='integer'},
            { name='Tactics1_Lv', type='integer'},
            { name='Tactics2', type='integer'},
            { name='Tactics2_Lv', type='integer'},
        }
        beans['battle.NPCHeroUnit'] = class
    end
    do
    ---@class battle.STAMINA_COLOR 
     ---@field public minValue integer @下限
     ---@field public maxValue integer @上限
     ---@field public color integer @颜色
        local class = {
            { name='minValue', type='integer'},
            { name='maxValue', type='integer'},
            { name='color', type='integer'},
        }
        beans['battle.STAMINA_COLOR'] = class
    end
    do
    ---@class battle.STAMINA_COST 
     ---@field public self_level integer
     ---@field public enemy_level_to_cost integer[]
        local class = {
            { name='self_level', type='integer'},
            { name='enemy_level_to_cost', type='integer[]'},
        }
        beans['battle.STAMINA_COST'] = class
    end
    do
    ---@class battle.TableArmyTypeQualification 
     ---@field public id integer @索引
     ---@field public fix boolean @固定
     ---@field public target integer @修改目标
     ---@field public modify_map table<integer,integer> @修改字典
        local class = {
            { name='id', type='integer'},
            { name='fix', type='boolean'},
            { name='target', type='integer'},
            { name='modify_map', type='table<integer,integer>'},
        }
        beans['battle.TableArmyTypeQualification'] = class
    end
    do
    ---@class battle.TableBattleAttribute 
     ---@field public ENUMS string[] @枚举名,注释
     ---@field public default_value number @默认值
     ---@field public min_value number @最小值
     ---@field public max_value number @最大值
     ---@field public sprite_path string @图标
     ---@field public show_percent boolean @以百分比显示
        local class = {
            { name='ENUMS', type='string[]'},
            { name='default_value', type='number'},
            { name='min_value', type='number'},
            { name='max_value', type='number'},
            { name='sprite_path', type='string'},
            { name='show_percent', type='boolean'},
        }
        beans['battle.TableBattleAttribute'] = class
    end
    do
    ---@class battle.TableBattleBuff 
     ---@field public id integer @这是id
     ---@field public name string @名字
     ---@field public desc string @描述
     ---@field public show boolean @是否显示
     ---@field public record boolean @是否记录战报
     ---@field public json_file string @技能图
     ---@field public type integer @类型
     ---@field public total_round integer @持续回合数
     ---@field public can_stack boolean @是否叠层
     ---@field public stack_rule integer @叠层规则
     ---@field public max_stack_count integer @最大堆叠层数
     ---@field public overlay_rule integer @叠加规则
     ---@field public icon_path string @图标
     ---@field public add_timing integer @自然添加时机
     ---@field public remove_timing integer @自然移除时机
     ---@field public dispellable boolean @是否可驱散
     ---@field public groupTypeList integer[] @buff组序号
     ---@field public sourceDeadClear boolean @来源死亡时是否清除
     ---@field public effect_id integer @buff特效
     ---@field public effect_socket string @特效挂接点
     ---@field public jump_on_add string @添加时跳字
     ---@field public jump_on_remove string @移除时跳字
     ---@field public max_combat integer @最大战斗次数
     ---@field public buff_source integer @Buff来源
     ---@field public battle_white_list integer[] @仅在以下类型战斗内生效
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='show', type='boolean'},
            { name='record', type='boolean'},
            { name='json_file', type='string'},
            { name='type', type='integer'},
            { name='total_round', type='integer'},
            { name='can_stack', type='boolean'},
            { name='stack_rule', type='integer'},
            { name='max_stack_count', type='integer'},
            { name='overlay_rule', type='integer'},
            { name='icon_path', type='string'},
            { name='add_timing', type='integer'},
            { name='remove_timing', type='integer'},
            { name='dispellable', type='boolean'},
            { name='groupTypeList', type='integer[]'},
            { name='sourceDeadClear', type='boolean'},
            { name='effect_id', type='integer'},
            { name='effect_socket', type='string'},
            { name='jump_on_add', type='string'},
            { name='jump_on_remove', type='string'},
            { name='max_combat', type='integer'},
            { name='buff_source', type='integer'},
            { name='battle_white_list', type='integer[]'},
        }
        beans['battle.TableBattleBuff'] = class
    end
    do
    ---@class battle.TableBattleBuffGroup 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public conflict_rule integer @冲突规则
     ---@field public attribute_association integer @属性关联
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='conflict_rule', type='integer'},
            { name='attribute_association', type='integer'},
        }
        beans['battle.TableBattleBuffGroup'] = class
    end
    do
    ---@class battle.TableBattleBuffValid 
     ---@field public battle_type integer @战斗类型
     ---@field public ban_buff_source integer[] @禁用的Buff来源列表
        local class = {
            { name='battle_type', type='integer'},
            { name='ban_buff_source', type='integer[]'},
        }
        beans['battle.TableBattleBuffValid'] = class
    end
    do
    ---@class battle.TableBattleEffectSound 
     ---@field public id integer
     ---@field public clip_asset table<integer,string> @资源路径
        local class = {
            { name='id', type='integer'},
            { name='clip_asset', type='table<integer,string>'},
        }
        beans['battle.TableBattleEffectSound'] = class
    end
    do
    ---@class battle.TableBattleFormation 
     ---@field public army_type integer @兵种
     ---@field public combat_status integer @战斗状态
     ---@field public main_status integer @主要状态
     ---@field public formation_name string @阵型名
     ---@field public direction_map integer[][] @六方向映射
     ---@field public animation_start_frame integer[] @动画从第n帧开始
        local class = {
            { name='army_type', type='integer'},
            { name='combat_status', type='integer'},
            { name='main_status', type='integer'},
            { name='formation_name', type='string'},
            { name='direction_map', type='integer[][]'},
            { name='animation_start_frame', type='integer[]'},
        }
        beans['battle.TableBattleFormation'] = class
    end
    do
    ---@class battle.TableBattleFormulas 
     ---@field public Bparameter1 number @兵力/((10000-1)/(90-9))+9（兵刃谋略）
     ---@field public Bparameter2 number
     ---@field public Bparameter3 number
     ---@field public Bparameter4 number
     ---@field public Bparameter5 number
     ---@field public Bparameter6 number @if {兵力&lt;=2000,(兵力/11.5)},else{(15*兵力)^0.5}（兵刃谋略）
     ---@field public Bparameter7 number
     ---@field public Bparameter8 number
     ---@field public Bparameter9 number
     ---@field public Healparameter1 number @if {兵力&lt;=2000,(兵力/11.5)},else{(15*兵力)^1/2}
     ---@field public Healparameter2 number
     ---@field public Healparameter3 number
     ---@field public Healparameter4 number
     ---@field public Healparameter5 number
     ---@field public Healparameter6 number
     ---@field public Healparameter7 number
     ---@field public Healparameter8 number @属性修正系数
     ---@field public Healparameter9 number @兵力修正系数
     ---@field public Cparameter1 number @(进攻方武力值C&#39;-防守方统帅值C&#39;&#39;*进攻方破甲百分比C&#39;&#39;)*1.3
     ---@field public Cparameter2 number @(进攻方智力值C&#39;-（防守方统帅值C&#39;&#39;+防守方智力值C&#39;&#39;&#39;)/2*进攻方看破百分比C&#39;&#39;&#39;&#39;)*1.3
     ---@field public E2parameter1 number @红度增伤,武将红度*0
     ---@field public E2parameter2 number @红度减伤,武将红度*0
     ---@field public Mparameter number @指定随机数，填0表示不指定
     ---@field public Mparameter1 number @随机数范围{0.95,1.05}
     ---@field public Mparameter2 number
     ---@field public G5parameter number @补给增减伤系数
     ---@field public G6parameter number @伤害修正系数
        local class = {
            { name='Bparameter1', type='number'},
            { name='Bparameter2', type='number'},
            { name='Bparameter3', type='number'},
            { name='Bparameter4', type='number'},
            { name='Bparameter5', type='number'},
            { name='Bparameter6', type='number'},
            { name='Bparameter7', type='number'},
            { name='Bparameter8', type='number'},
            { name='Bparameter9', type='number'},
            { name='Healparameter1', type='number'},
            { name='Healparameter2', type='number'},
            { name='Healparameter3', type='number'},
            { name='Healparameter4', type='number'},
            { name='Healparameter5', type='number'},
            { name='Healparameter6', type='number'},
            { name='Healparameter7', type='number'},
            { name='Healparameter8', type='number'},
            { name='Healparameter9', type='number'},
            { name='Cparameter1', type='number'},
            { name='Cparameter2', type='number'},
            { name='E2parameter1', type='number'},
            { name='E2parameter2', type='number'},
            { name='Mparameter', type='number'},
            { name='Mparameter1', type='number'},
            { name='Mparameter2', type='number'},
            { name='G5parameter', type='number'},
            { name='G6parameter', type='number'},
        }
        beans['battle.TableBattleFormulas'] = class
    end
    do
    ---@class battle.TableBattleRecordCrowdControl 
     ---@field public crowdControlType integer @战报类型
     ---@field public raw_txt string @文本，参数用%s代替，包含缩进
        local class = {
            { name='crowdControlType', type='integer'},
            { name='raw_txt', type='string'},
        }
        beans['battle.TableBattleRecordCrowdControl'] = class
    end
    do
    ---@class battle.TableBattleRecordText 
     ---@field public detailRecordType integer @战报类型
     ---@field public raw_txt string[] @文本，参数用%s代替，包含缩进
        local class = {
            { name='detailRecordType', type='integer'},
            { name='raw_txt', type='string[]'},
        }
        beans['battle.TableBattleRecordText'] = class
    end
    do
    ---@class battle.TableBattleTactic 
     ---@field public id integer @这是id
     ---@field public name string @名字
     ---@field public tactic_type integer @卡牌类型
     ---@field public probability string @概率公式
     ---@field public prepare_step integer @准备回合数
     ---@field public cd integer @cd回合
     ---@field public json_file string @json文件名
     ---@field public description string @描述
     ---@field public brief_description string @简短描述
     ---@field public sprite_path string @技能展示图
     ---@field public icon_path string @卡牌图
     ---@field public tactic_grade integer @品级
     ---@field public tactic_feature integer @战法特性
     ---@field public born_with boolean @是否武将自带战法
     ---@field public learnable boolean @是否武将可学习战法
     ---@field public TAGS string[] @标签
     ---@field public army_type_qualification_id integer @兵种适应性修改(仅兵种和阵法生效）
     ---@field public slg_effect integer @大世界特效
     ---@field public unlock_season integer @解锁赛季
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='tactic_type', type='integer'},
            { name='probability', type='string'},
            { name='prepare_step', type='integer'},
            { name='cd', type='integer'},
            { name='json_file', type='string'},
            { name='description', type='string'},
            { name='brief_description', type='string'},
            { name='sprite_path', type='string'},
            { name='icon_path', type='string'},
            { name='tactic_grade', type='integer'},
            { name='tactic_feature', type='integer'},
            { name='born_with', type='boolean'},
            { name='learnable', type='boolean'},
            { name='TAGS', type='string[]'},
            { name='army_type_qualification_id', type='integer'},
            { name='slg_effect', type='integer'},
            { name='unlock_season', type='integer'},
        }
        beans['battle.TableBattleTactic'] = class
    end
    do
    ---@class battle.TableEventPriority 
     ---@field public battle_event integer @事件
     ---@field public name string @技能图名
     ---@field public priority integer @优先级
        local class = {
            { name='battle_event', type='integer'},
            { name='name', type='string'},
            { name='priority', type='integer'},
        }
        beans['battle.TableEventPriority'] = class
    end
    do
    ---@class battle.TableFormulaIndex 
     ---@field public name string @技能图名
     ---@field public key string @索引键值
     ---@field public var_name string @分段参数名
     ---@field public final_min number @值域下界
     ---@field public final_max number @值域上界
     ---@field public formulas string[] @公式（武力att,智力int,统率def,速度spe，satt，sint，sdef，sspe等表示来源id，注意skill里不能有satt）
        local class = {
            { name='name', type='string'},
            { name='key', type='string'},
            { name='var_name', type='string'},
            { name='final_min', type='number'},
            { name='final_max', type='number'},
            { name='formulas', type='string[]'},
        }
        beans['battle.TableFormulaIndex'] = class
    end
    do
    ---@class battle.TableFormulaIndexName 
     ---@field public keys string[] @伤害率
        local class = {
            { name='keys', type='string[]'},
        }
        beans['battle.TableFormulaIndexName'] = class
    end
    do
    ---@class battle.TableHeroArmyType 
     ---@field public id integer @英雄兵种
     ---@field public name string @名字
     ---@field public full_name string @全名
     ---@field public counter integer @克制兵种
     ---@field public attack_counter_factor number @攻击克制系数
     ---@field public defence_counter_factor number @防御克制系数
     ---@field public army_type_category integer @所属兵种系
     ---@field public former_army_type integer @前置兵种
     ---@field public sprite_path string @图标
     ---@field public sprite2_path string @图标2
     ---@field public sprite_L string
     ---@field public sprite_hud string
     ---@field public armyTypeName string
     ---@field public scale number @模型缩放
     ---@field public color string @颜色
     ---@field public desc string @描述
     ---@field public termId integer @词条说明
     ---@field public termId2 integer @词条说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='full_name', type='string'},
            { name='counter', type='integer'},
            { name='attack_counter_factor', type='number'},
            { name='defence_counter_factor', type='number'},
            { name='army_type_category', type='integer'},
            { name='former_army_type', type='integer'},
            { name='sprite_path', type='string'},
            { name='sprite2_path', type='string'},
            { name='sprite_L', type='string'},
            { name='sprite_hud', type='string'},
            { name='armyTypeName', type='string'},
            { name='scale', type='number'},
            { name='color', type='string'},
            { name='desc', type='string'},
            { name='termId', type='integer'},
            { name='termId2', type='integer'},
        }
        beans['battle.TableHeroArmyType'] = class
    end
    do
    ---@class battle.TableHeroConst 
     ---@field public HeroMaxStars integer @武将最大红度
     ---@field public AddPointByLevel integer @武将每x级可加点
     ---@field public AddPointByStars integer @武将每x红度可加点
     ---@field public AddPointStep integer @单次增加的加点数值
     ---@field public HeroMinLevel integer @武将最低等级
     ---@field public HeroMaxLevel integer @武将最高等级
     ---@field public SoldierMaxInit integer @武将初始带兵数量
     ---@field public SoldierMaxAddPerLevel integer @武将初始带兵数量
     ---@field public EXP_ARG number @基础经验值系数
     ---@field public STAR_EXP_ARG number @敌方红度加成
     ---@field public LOST_EXP_ARG number @经验补偿比例
     ---@field public MERIT_ARG number @战功计算系数
        local class = {
            { name='HeroMaxStars', type='integer'},
            { name='AddPointByLevel', type='integer'},
            { name='AddPointByStars', type='integer'},
            { name='AddPointStep', type='integer'},
            { name='HeroMinLevel', type='integer'},
            { name='HeroMaxLevel', type='integer'},
            { name='SoldierMaxInit', type='integer'},
            { name='SoldierMaxAddPerLevel', type='integer'},
            { name='EXP_ARG', type='number'},
            { name='STAR_EXP_ARG', type='number'},
            { name='LOST_EXP_ARG', type='number'},
            { name='MERIT_ARG', type='number'},
        }
        beans['battle.TableHeroConst'] = class
    end
    do
    ---@class battle.TableHeroDynasty 
     ---@field public id integer @英雄阵营
     ---@field public name string @名字
     ---@field public full_name string @全名
     ---@field public sprite_path string @图标
     ---@field public color_sprite_path string
     ---@field public color string @颜色
     ---@field public desc string @描述
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='full_name', type='string'},
            { name='sprite_path', type='string'},
            { name='color_sprite_path', type='string'},
            { name='color', type='string'},
            { name='desc', type='string'},
        }
        beans['battle.TableHeroDynasty'] = class
    end
    do
    ---@class battle.TableNPCDifficulty 
     ---@field public level integer @土地等级
     ---@field public difficulty table<integer,battle.NpcDifficultyConfig> @难度
        local class = {
            { name='level', type='integer'},
            { name='difficulty', type='table<integer,battle.NpcDifficultyConfig>'},
        }
        beans['battle.TableNPCDifficulty'] = class
    end
    do
    ---@class battle.TableNPCPool 
     ---@field public Id integer @Id
     ---@field public num integer @部队数量
     ---@field public pool integer[] @阵容池
     ---@field public rule integer @刷新规则
     ---@field public Exp integer @经验值
        local class = {
            { name='Id', type='integer'},
            { name='num', type='integer'},
            { name='pool', type='integer[]'},
            { name='rule', type='integer'},
            { name='Exp', type='integer'},
        }
        beans['battle.TableNPCPool'] = class
    end
    do
    ---@class battle.TableNPCTeam 
     ---@field public Id integer @Id
     ---@field public name string @名字
     ---@field public ArmyType integer @兵种
     ---@field public HeroUnits table<integer,battle.NPCHeroUnit> @武将位置
     ---@field public SuggestLevel integer @推荐等级
     ---@field public SuggestHealth integer @推荐兵力
        local class = {
            { name='Id', type='integer'},
            { name='name', type='string'},
            { name='ArmyType', type='integer'},
            { name='HeroUnits', type='table<integer,battle.NPCHeroUnit>'},
            { name='SuggestLevel', type='integer'},
            { name='SuggestHealth', type='integer'},
        }
        beans['battle.TableNPCTeam'] = class
    end
    do
    ---@class battle.TableTacticConst 
     ---@field public TacticMaxStars integer @战法最大红度
     ---@field public TacticMaxLevel integer @战法最大等级
     ---@field public TacticUpgradeCost table<integer,integer> @战法升级消耗
     ---@field public TacticUpgradeCostId integer @战法升级消耗货币
     ---@field public TacticRenewDesc string @重置战法界面提示
     ---@field public TacticRenewConfirmTopTip string @重置战法界面提示
     ---@field public TacticRenewConfirmBottomTip string @重置战法界面提示
     ---@field public TacticRenewCost integer @重置战法资源花费
        local class = {
            { name='TacticMaxStars', type='integer'},
            { name='TacticMaxLevel', type='integer'},
            { name='TacticUpgradeCost', type='table<integer,integer>'},
            { name='TacticUpgradeCostId', type='integer'},
            { name='TacticRenewDesc', type='string'},
            { name='TacticRenewConfirmTopTip', type='string'},
            { name='TacticRenewConfirmBottomTip', type='string'},
            { name='TacticRenewCost', type='integer'},
        }
        beans['battle.TableTacticConst'] = class
    end
    do
    ---@class battle.TableTemplateHero 
     ---@field public id integer @英雄id
     ---@field public name string @名字
     ---@field public rarity integer @稀有度
     ---@field public dynasty integer @阵营
     ---@field public armyType_qualification table<integer,integer> @兵种适应性
     ---@field public role integer[] @定位
     ---@field public recommended_tactics integer[] @推荐战法Id
     ---@field public primary_attr integer @主属性
     ---@field public Attack integer @初始武力
     ---@field public Intelligence integer @初始智力
     ---@field public Defense integer @初始统帅
     ---@field public Speed integer @初始速度
     ---@field public attack_lv number @每级成长武力
     ---@field public intelligence_lv number @每级成长智力
     ---@field public defense_lv number @每级成长统帅
     ---@field public speed_lv number @每级成长速度
     ---@field public AttackCritRate number @兵刃暴击率
     ---@field public AttackCritDamage number @兵刃暴击伤害倍率
     ---@field public IntelligenceCritRate number @谋略暴击率
     ---@field public IntelligenceCritDamage number @谋略暴击伤害倍率
     ---@field public selfTacticId integer @自身战法Id
     ---@field public prefab_path string @模型路径
     ---@field public avatar_path string @头像图片
     ---@field public avatar_sq_path string @头像图片(方)
     ---@field public sprite_path string @展示图片路径
     ---@field public sprite_L_path string @全身像路径
     ---@field public audio_group integer @音效组
     ---@field public is_female boolean @性别（女True男False）
     ---@field public sub_identity integer[][] @身份演化-特殊子身份
     ---@field public identity_attribute integer[][] @身份演化-特殊特质
     ---@field public unlock_season integer @解锁赛季
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='rarity', type='integer'},
            { name='dynasty', type='integer'},
            { name='armyType_qualification', type='table<integer,integer>'},
            { name='role', type='integer[]'},
            { name='recommended_tactics', type='integer[]'},
            { name='primary_attr', type='integer'},
            { name='Attack', type='integer'},
            { name='Intelligence', type='integer'},
            { name='Defense', type='integer'},
            { name='Speed', type='integer'},
            { name='attack_lv', type='number'},
            { name='intelligence_lv', type='number'},
            { name='defense_lv', type='number'},
            { name='speed_lv', type='number'},
            { name='AttackCritRate', type='number'},
            { name='AttackCritDamage', type='number'},
            { name='IntelligenceCritRate', type='number'},
            { name='IntelligenceCritDamage', type='number'},
            { name='selfTacticId', type='integer'},
            { name='prefab_path', type='string'},
            { name='avatar_path', type='string'},
            { name='avatar_sq_path', type='string'},
            { name='sprite_path', type='string'},
            { name='sprite_L_path', type='string'},
            { name='audio_group', type='integer'},
            { name='is_female', type='boolean'},
            { name='sub_identity', type='integer[][]'},
            { name='identity_attribute', type='integer[][]'},
            { name='unlock_season', type='integer'},
        }
        beans['battle.TableTemplateHero'] = class
    end
    do
    ---@class battle.TacticTuple 
     ---@field public id integer @战斗战法
     ---@field public level integer @战法等级
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
        }
        beans['battle.TacticTuple'] = class
    end
    do
    ---@class color 
     ---@field public r number
     ---@field public g number
     ---@field public b number
     ---@field public a number
        local class = {
            { name='r', type='number'},
            { name='g', type='number'},
            { name='b', type='number'},
            { name='a', type='number'},
        }
        beans['color'] = class
    end
    do
    ---@class common.CameraShakeConfig 
     ---@field public startTime number @开始时间
     ---@field public duration number @时长
     ---@field public amplitude number @幅度
     ---@field public frequency number @频率
        local class = {
            { name='startTime', type='number'},
            { name='duration', type='number'},
            { name='amplitude', type='number'},
            { name='frequency', type='number'},
        }
        beans['common.CameraShakeConfig'] = class
    end
    do
    ---@class common.CardData 
     ---@field public id integer @ID
     ---@field public level integer @等级
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
        }
        beans['common.CardData'] = class
    end
    do
    ---@class common.Condition 
        local class = {
        }
        beans['common.Condition'] = class
    end
    do
    ---@class common.CheckBuild :common.Condition 
     ---@field public id integer
     ---@field public level integer
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
        }
        beans['common.CheckBuild'] = class
    end
    do
    ---@class common.EnumPair 
     ---@field public desc string @名称
     ---@field public value integer @枚举值
        local class = {
            { name='desc', type='string'},
            { name='value', type='integer'},
        }
        beans['common.EnumPair'] = class
    end
    do
    ---@class common.IntIntPair 
     ---@field public key integer
     ---@field public val integer
        local class = {
            { name='key', type='integer'},
            { name='val', type='integer'},
        }
        beans['common.IntIntPair'] = class
    end
    do
    ---@class common.Reward 
        local class = {
        }
        beans['common.Reward'] = class
    end
    do
    ---@class common.RewardCoin :common.Reward 
     ---@field public coin table<integer,integer> @货币奖励
        local class = {
            { name='coin', type='table<integer,integer>'},
        }
        beans['common.RewardCoin'] = class
    end
    do
    ---@class common.RewardCoinItem :common.Reward 
     ---@field public coin table<integer,integer> @货币奖励
     ---@field public item table<integer,integer> @道具奖励
        local class = {
            { name='coin', type='table<integer,integer>'},
            { name='item', type='table<integer,integer>'},
        }
        beans['common.RewardCoinItem'] = class
    end
    do
    ---@class common.RewardItem :common.Reward 
     ---@field public item table<integer,integer> @道具奖励
        local class = {
            { name='item', type='table<integer,integer>'},
        }
        beans['common.RewardItem'] = class
    end
    do
    ---@class common.RuleText 
     ---@field public tabName string @页签名称
     ---@field public rules common.RuleTextCell[] @内容单元
        local class = {
            { name='tabName', type='string'},
            { name='rules', type='common.RuleTextCell[]'},
        }
        beans['common.RuleText'] = class
    end
    do
    ---@class common.RuleTextCell 
     ---@field public title string @标题
     ---@field public content string @内容
        local class = {
            { name='title', type='string'},
            { name='content', type='string'},
        }
        beans['common.RuleTextCell'] = class
    end
    do
    ---@class common.ScheduleStageBeginDay 
     ---@field public day integer @天
        local class = {
            { name='day', type='integer'},
        }
        beans['common.ScheduleStageBeginDay'] = class
    end
    do
    ---@class common.ScheduleStages 
     ---@field public StageId string @阶段id
     ---@field public BeginDay table<string,integer> @周期开始天数
     ---@field public BeginTime integer[] @周期开始时间
        local class = {
            { name='StageId', type='string'},
            { name='BeginDay', type='table<string,integer>'},
            { name='BeginTime', type='integer[]'},
        }
        beans['common.ScheduleStages'] = class
    end
    do
    ---@class common.TableActorContainer 
     ---@field public id integer
     ---@field public first integer[] @主动方类型
     ---@field public second integer[] @被动方类型
     ---@field public relation integer[] @first看来和second的关系
     ---@field public valid integer @能建立的容器关系
     ---@field public enter_same_pos boolean @进入时需要在相同位置
        local class = {
            { name='id', type='integer'},
            { name='first', type='integer[]'},
            { name='second', type='integer[]'},
            { name='relation', type='integer[]'},
            { name='valid', type='integer'},
            { name='enter_same_pos', type='boolean'},
        }
        beans['common.TableActorContainer'] = class
    end
    do
    ---@class common.TableActorContainerConfig 
     ---@field public id integer @能建立的容器关系
     ---@field public recover_priority integer @恢复优先级-小的优先
     ---@field public follow_container boolean @跟随容器移动
     ---@field public follow_content boolean @跟随内容物移动
        local class = {
            { name='id', type='integer'},
            { name='recover_priority', type='integer'},
            { name='follow_container', type='boolean'},
            { name='follow_content', type='boolean'},
        }
        beans['common.TableActorContainerConfig'] = class
    end
    do
    ---@class common.TableActorMove 
     ---@field public id integer @移动类型
     ---@field public add_ratio integer @移动速度加成x%
        local class = {
            { name='id', type='integer'},
            { name='add_ratio', type='integer'},
        }
        beans['common.TableActorMove'] = class
    end
    do
    ---@class common.TableActorType 
     ---@field public id integer @Actor类型
        local class = {
            { name='id', type='integer'},
        }
        beans['common.TableActorType'] = class
    end
    do
    ---@class common.TableAnimLength 
     ---@field public Ani_UICardDrawOneGroup_One number @抽卡动画单抽动画时长
     ---@field public Ani_UICardDrawOneGroup_Five number @抽卡动画五抽单个动画时长
        local class = {
            { name='Ani_UICardDrawOneGroup_One', type='number'},
            { name='Ani_UICardDrawOneGroup_Five', type='number'},
        }
        beans['common.TableAnimLength'] = class
    end
    do
    ---@class common.TableBahaviorConfig 
     ---@field public id integer @索引
     ---@field public main integer[] @主状态
     ---@field public combat integer[] @战斗状态
     ---@field public deploy integer[] @调动状态
     ---@field public vehicle integer[] @操控器械状态
     ---@field public name string @显示名
     ---@field public count_down boolean @显示倒计时
     ---@field public ui_state_index integer @UI状态机序号
     ---@field public valid_behaviors integer[] @合法行为（目前客户端用）
     ---@field public logic_state_name string @逻辑状态机名
        local class = {
            { name='id', type='integer'},
            { name='main', type='integer[]'},
            { name='combat', type='integer[]'},
            { name='deploy', type='integer[]'},
            { name='vehicle', type='integer[]'},
            { name='name', type='string'},
            { name='count_down', type='boolean'},
            { name='ui_state_index', type='integer'},
            { name='valid_behaviors', type='integer[]'},
            { name='logic_state_name', type='string'},
        }
        beans['common.TableBahaviorConfig'] = class
    end
    do
    ---@class common.TableBattleConfig 
     ---@field public MaxRound integer @单局战斗最大回合数
     ---@field public MaxContinousDraw integer @最大连续平局次数
     ---@field public MaxBattleFormations integer @玩家最大编队数量
     ---@field public PlainAttackTacticId integer @普攻战法ID
     ---@field public BattleFormationPositionMap table<integer,integer[]> @阵型位置
     ---@field public InsightEffectBuffTypes table<integer,boolean> @洞察影响的buff类型
     ---@field public IMPASSE_DURATION integer @僵持状态持续时长/秒
     ---@field public FAST_BATTLE_THRESHOLD integer @触发快速战斗的格子上部队阈值
     ---@field public BATTLE_BANNER_MERIT_THRESHOLD integer @战斗横幅出现的武勋阈值
     ---@field public INJURED_DURATION integer @重伤持续时间/秒
     ---@field public BattleDefaultFormationName string @默认阵型名
     ---@field public PositionMap vector3[][] @角色位置
     ---@field public DirectionMap vector3[][] @角色位置
     ---@field public CharacterScale number @角色缩放
     ---@field public BackToPositionDurationRoundEnd number @大轮次结束后角色归位的时间
     ---@field public BackToPositionDurationActionEnd number @回合结束后角色归位的时间
     ---@field public DefaultColor color @英雄默认颜色
     ---@field public EffectFadeDuration number @特效淡出时间
     ---@field public CharacterAppearEffectDuration number @角色出现的特效时长
     ---@field public CharacterDieAnimationDuration number @角色死亡的动画时长
     ---@field public WaitTimeWhenFirstRoundStart number @第一回合开始的等待时间
     ---@field public HeroTurnStartWaitTime number @英雄轮次开始时，等待的时间
     ---@field public HeroTurnEndWaitTime number @英雄轮次结束时，等待的时间
     ---@field public CameraFullView table<string,number> @全景相机参数
     ---@field public CameraFocusView table<string,number> @聚焦相机参数
     ---@field public CameraTransitionTime table<string,number> @相机变化时间
     ---@field public RoundStartSoundId integer @回合开始音效id
     ---@field public SkillComboTimeScale number @连击时停时间缩放
     ---@field public AnimationTransition number @动画过渡时长
     ---@field public AngleOffsetWhenOccupied number[] @移动时目标位置如果被占用，偏移角顺序
     ---@field public CameraFollowNode string @Follow相机绑定的节点（空为根节点）
     ---@field public AutoCullFarthestDistanceFactor number @切镜自动裁剪角色的最大距离参数
     ---@field public SlgBattleRoundInterval number @沙盘战斗每回合时间
     ---@field public UISlgBattleTacticInterval number @沙盘战斗UI释放战法时间间隔
     ---@field public UISlgBattleModifyHealthInterval number @沙盘战斗UI修改血量时间间隔
     ---@field public SlgBattleResultDuration number @沙盘战斗展示结果时长
     ---@field public SlgBattlePrepareDuration number @沙盘战斗准备时间
     ---@field public UIAllyHpBarColor color @我方血条颜色
     ---@field public UIAllyHpTxtColor color @我方血量数值颜色
     ---@field public UICardActionSpeed number @出牌动画的速度，原速是1.0
     ---@field public UICardActionDuration number @卡牌自身的显示时长
     ---@field public UIAttackDamageStringColor color @兵刃伤害跳字颜色
     ---@field public UIIntelligenceDamageStringColor color @谋略伤害跳字颜色
     ---@field public UIRealityDamageStringColor color @真实伤害跳字颜色
     ---@field public UIChainDamageStringColor color @连锁伤害跳字颜瑟
     ---@field public UIHealStringColor color @治疗跳字颜色
     ---@field public UIDamageStringScaleMap number[][] @伤害跳字大小映射
     ---@field public UIHealStringScaleMap number[][] @治疗跳字大小映射
     ---@field public UIBattleToastShowDuration number @战斗信息提示（开始/胜利/失败等）显示时长
     ---@field public UIBattleToastFadeOutDuration number @战斗信息提示淡出时长
     ---@field public UIBattleToastBgPath table<string,string> @战斗信息提示图片路径
     ---@field public UIBattleToastText table<string,string> @战斗信息提示文本
     ---@field public UIBattleToastColor table<string,color> @战斗信息提示颜色
     ---@field public UIAllySpeedBarFrameColor color @我方速度条头像框颜色
     ---@field public UIEnemySpeedBarFrameColor color @敌方速度条头像框颜色
     ---@field public UITacticGradeBgPath table<integer,string> @卡牌品级图标路径
     ---@field public UITacticTypeName table<integer,string> @卡牌类型名
     ---@field public UIShowCardsAnimationDuration number @卡牌出现动画时长
     ---@field public UIHideCardsAnimationDuration number @卡牌小时动画时长
     ---@field public UICardUpAnimationDuration number @使用卡牌时上浮动画时长
     ---@field public UICardDownAnimationDuration number @收回卡牌时下沉动画时长
     ---@field public UICardUpDistance number @使用卡牌时上浮的距离
     ---@field public UINormalCardBg string @正常卡牌背景
     ---@field public UITempCardBg string @临时卡牌背景
     ---@field public UIAddCardSourceOffset vector3 @添加卡牌动画起点距角色的偏移
     ---@field public UIAddCardTargetOffset vector3 @添加卡牌动画终点距角色的偏移
     ---@field public UIAddCardAnimationShowDuration number @添加卡牌动画展示卡牌时间
     ---@field public UIAddCardAnimationFlyDuration number @添加卡牌动画卡牌飞行时间
     ---@field public UITempCardFadeOutDuration number @临时卡牌打出时渐隐的时长
     ---@field public UIShowCardFadeInDuration number @卡牌展示出现动画时长
     ---@field public UIShowCardFadeOutDuration number @卡牌展示消失动画时长
     ---@field public HeroIntHealth number @1级武将最大可装配兵力
     ---@field public HeroHealthGrowth number @武将每级最大可装配兵力成长值
     ---@field public DamageChangeToRecoverableHealthScale number @受到伤害时，受到伤害的90%立刻转换为伤兵
     ---@field public RoundEndChangeToRecoverableHealthScale number @每个大回合结束时，单个武将的所有伤兵 会转换10%为死兵
     ---@field public BattleEndChangeToRecoverableHealthScale number @单场战斗结束后，单个武将的所有伤兵 会转换30%为死兵
     ---@field public CaptainDeadChangeToRecoverableHealthScale number @主将死亡后，先锋和军师若存活，则当前兵力立刻转换10%为死兵
     ---@field public BattleRecordDamageColorString string @战报伤害颜色
     ---@field public BattleRecordDamageCirtColorString string @战报暴击伤害颜色
     ---@field public BattleRecordHealColorString string @战报治疗颜色
     ---@field public BattleRecordIconWin string @战报胜利图标
     ---@field public BattleRecordIconLose string @战报失败图标
     ---@field public BattleRecordIconDraw string @战报平局图标
     ---@field public BattleRecordIconMorale string @战报阶段：补给
     ---@field public BattleRecordIconArmyType string @战报阶段：兵种
     ---@field public BattleRecordIconTechnology string @战报阶段：科技
     ---@field public BattleRecordIconEquipment string @战报阶段：装备
     ---@field public BattleRecordIconHeroIdentity string @战报阶段：身份
     ---@field public BattleRecordIconTactics string @战报阶段：战法
     ---@field public BattleRecordExtraHeroColorString string @战报获得经验值信息里，英雄的颜色
     ---@field public BattleRecordExtraValueColorString string @战报获得经验值/武勋信息里，数值的颜色
     ---@field public SlgBattleDamageJumpTextColor color @沙盘战斗伤害跳字颜色
     ---@field public SlgBattleHealJumpTextColor color @沙盘战斗治疗跳字颜色
     ---@field public SlgBattleCrowdControlPriority integer[] @沙盘战斗跳字控制状态优先级
     ---@field public SlgBattleCrowdControlScore table<integer,number> @沙盘战斗跳字控制状态换算分
     ---@field public SlgBattleJumpTextNumberPerRound integer @沙盘战斗跳字每回合每批次个数
     ---@field public SlgBattleJumpTextScoreToScale number[][] @沙盘战斗跳字分数到字号的映射
     ---@field public SlgBattleEffectCountPerRound integer @沙盘战斗每方每回合播特效数量
     ---@field public SlgBattleEffectInterval number @沙盘战斗播特效间隔
     ---@field public SlgBattleJumpTextInterval number @沙盘战斗跳字每回合每批次中跳字间隔
     ---@field public SlgBattleJumpTextLifeTime number @沙盘战斗跳字生命周期
     ---@field public SlgBattleJumpTextHealthChangeInterval number @沙盘战斗跳字生命值修改间隔
     ---@field public SlgBattleJumpTextOffset number @沙盘战斗跳字HUD竖直偏移
     ---@field public SlgBattleResultIconWin string @沙盘战斗胜利图标
     ---@field public SlgBattleResultIconLose string @沙盘战斗失败图标
     ---@field public SlgBattleResultIconDraw string @沙盘战斗平局图标
        local class = {
            { name='MaxRound', type='integer'},
            { name='MaxContinousDraw', type='integer'},
            { name='MaxBattleFormations', type='integer'},
            { name='PlainAttackTacticId', type='integer'},
            { name='BattleFormationPositionMap', type='table<integer,integer[]>'},
            { name='InsightEffectBuffTypes', type='table<integer,boolean>'},
            { name='IMPASSE_DURATION', type='integer'},
            { name='FAST_BATTLE_THRESHOLD', type='integer'},
            { name='BATTLE_BANNER_MERIT_THRESHOLD', type='integer'},
            { name='INJURED_DURATION', type='integer'},
            { name='BattleDefaultFormationName', type='string'},
            { name='PositionMap', type='vector3[][]'},
            { name='DirectionMap', type='vector3[][]'},
            { name='CharacterScale', type='number'},
            { name='BackToPositionDurationRoundEnd', type='number'},
            { name='BackToPositionDurationActionEnd', type='number'},
            { name='DefaultColor', type='color'},
            { name='EffectFadeDuration', type='number'},
            { name='CharacterAppearEffectDuration', type='number'},
            { name='CharacterDieAnimationDuration', type='number'},
            { name='WaitTimeWhenFirstRoundStart', type='number'},
            { name='HeroTurnStartWaitTime', type='number'},
            { name='HeroTurnEndWaitTime', type='number'},
            { name='CameraFullView', type='table<string,number>'},
            { name='CameraFocusView', type='table<string,number>'},
            { name='CameraTransitionTime', type='table<string,number>'},
            { name='RoundStartSoundId', type='integer'},
            { name='SkillComboTimeScale', type='number'},
            { name='AnimationTransition', type='number'},
            { name='AngleOffsetWhenOccupied', type='number[]'},
            { name='CameraFollowNode', type='string'},
            { name='AutoCullFarthestDistanceFactor', type='number'},
            { name='SlgBattleRoundInterval', type='number'},
            { name='UISlgBattleTacticInterval', type='number'},
            { name='UISlgBattleModifyHealthInterval', type='number'},
            { name='SlgBattleResultDuration', type='number'},
            { name='SlgBattlePrepareDuration', type='number'},
            { name='UIAllyHpBarColor', type='color'},
            { name='UIAllyHpTxtColor', type='color'},
            { name='UICardActionSpeed', type='number'},
            { name='UICardActionDuration', type='number'},
            { name='UIAttackDamageStringColor', type='color'},
            { name='UIIntelligenceDamageStringColor', type='color'},
            { name='UIRealityDamageStringColor', type='color'},
            { name='UIChainDamageStringColor', type='color'},
            { name='UIHealStringColor', type='color'},
            { name='UIDamageStringScaleMap', type='number[][]'},
            { name='UIHealStringScaleMap', type='number[][]'},
            { name='UIBattleToastShowDuration', type='number'},
            { name='UIBattleToastFadeOutDuration', type='number'},
            { name='UIBattleToastBgPath', type='table<string,string>'},
            { name='UIBattleToastText', type='table<string,string>'},
            { name='UIBattleToastColor', type='table<string,color>'},
            { name='UIAllySpeedBarFrameColor', type='color'},
            { name='UIEnemySpeedBarFrameColor', type='color'},
            { name='UITacticGradeBgPath', type='table<integer,string>'},
            { name='UITacticTypeName', type='table<integer,string>'},
            { name='UIShowCardsAnimationDuration', type='number'},
            { name='UIHideCardsAnimationDuration', type='number'},
            { name='UICardUpAnimationDuration', type='number'},
            { name='UICardDownAnimationDuration', type='number'},
            { name='UICardUpDistance', type='number'},
            { name='UINormalCardBg', type='string'},
            { name='UITempCardBg', type='string'},
            { name='UIAddCardSourceOffset', type='vector3'},
            { name='UIAddCardTargetOffset', type='vector3'},
            { name='UIAddCardAnimationShowDuration', type='number'},
            { name='UIAddCardAnimationFlyDuration', type='number'},
            { name='UITempCardFadeOutDuration', type='number'},
            { name='UIShowCardFadeInDuration', type='number'},
            { name='UIShowCardFadeOutDuration', type='number'},
            { name='HeroIntHealth', type='number'},
            { name='HeroHealthGrowth', type='number'},
            { name='DamageChangeToRecoverableHealthScale', type='number'},
            { name='RoundEndChangeToRecoverableHealthScale', type='number'},
            { name='BattleEndChangeToRecoverableHealthScale', type='number'},
            { name='CaptainDeadChangeToRecoverableHealthScale', type='number'},
            { name='BattleRecordDamageColorString', type='string'},
            { name='BattleRecordDamageCirtColorString', type='string'},
            { name='BattleRecordHealColorString', type='string'},
            { name='BattleRecordIconWin', type='string'},
            { name='BattleRecordIconLose', type='string'},
            { name='BattleRecordIconDraw', type='string'},
            { name='BattleRecordIconMorale', type='string'},
            { name='BattleRecordIconArmyType', type='string'},
            { name='BattleRecordIconTechnology', type='string'},
            { name='BattleRecordIconEquipment', type='string'},
            { name='BattleRecordIconHeroIdentity', type='string'},
            { name='BattleRecordIconTactics', type='string'},
            { name='BattleRecordExtraHeroColorString', type='string'},
            { name='BattleRecordExtraValueColorString', type='string'},
            { name='SlgBattleDamageJumpTextColor', type='color'},
            { name='SlgBattleHealJumpTextColor', type='color'},
            { name='SlgBattleCrowdControlPriority', type='integer[]'},
            { name='SlgBattleCrowdControlScore', type='table<integer,number>'},
            { name='SlgBattleJumpTextNumberPerRound', type='integer'},
            { name='SlgBattleJumpTextScoreToScale', type='number[][]'},
            { name='SlgBattleEffectCountPerRound', type='integer'},
            { name='SlgBattleEffectInterval', type='number'},
            { name='SlgBattleJumpTextInterval', type='number'},
            { name='SlgBattleJumpTextLifeTime', type='number'},
            { name='SlgBattleJumpTextHealthChangeInterval', type='number'},
            { name='SlgBattleJumpTextOffset', type='number'},
            { name='SlgBattleResultIconWin', type='string'},
            { name='SlgBattleResultIconLose', type='string'},
            { name='SlgBattleResultIconDraw', type='string'},
        }
        beans['common.TableBattleConfig'] = class
    end
    do
    ---@class common.TableBattleTacticItem 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public quality integer @道具品质
     ---@field public tactic integer @战法
     ---@field public auto_use boolean @是否自动使用
     ---@field public use_func item.UseFunc
     ---@field public extra_rewards common.Reward @额外奖励
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='quality', type='integer'},
            { name='tactic', type='integer'},
            { name='auto_use', type='boolean'},
            { name='use_func', type='item.UseFunc'},
            { name='extra_rewards', type='common.Reward'},
        }
        beans['common.TableBattleTacticItem'] = class
    end
    do
    ---@class common.TableCareerConst 
     ---@field public STRATEGY_POINT_RECOVER_TIME integer @30分钟一点
     ---@field public STRATEGY_POINT_MAX integer @最大策点数量
     ---@field public WU_GU_MAX_LEVEL_UP integer @五谷丰登最大可提升至等级
     ---@field public WU_GU_MAX_LEVEL_UP_GRID integer @单玩家可受五谷丰登提升等级的最多地块数量
        local class = {
            { name='STRATEGY_POINT_RECOVER_TIME', type='integer'},
            { name='STRATEGY_POINT_MAX', type='integer'},
            { name='WU_GU_MAX_LEVEL_UP', type='integer'},
            { name='WU_GU_MAX_LEVEL_UP_GRID', type='integer'},
        }
        beans['common.TableCareerConst'] = class
    end
    do
    ---@class common.TableCareerTelent 
     ---@field public id integer @天赋id
     ---@field public name string @天赋名称
     ---@field public desc string @天赋描述
     ---@field public Strategy integer @关联策牌
     ---@field public TallentEffect Talent.BaseTalent
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='Strategy', type='integer'},
            { name='TallentEffect', type='Talent.BaseTalent'},
        }
        beans['common.TableCareerTelent'] = class
    end
    do
    ---@class common.TableCareerTree 
     ---@field public id integer @树节点id
     ---@field public front_node integer[] @前置节点id
     ---@field public FIELD_MASTER integer @田师
     ---@field public name string @天赋名称
     ---@field public desc string[] @天赋描述
     ---@field public sprite_path string @展示图片路径
        local class = {
            { name='id', type='integer'},
            { name='front_node', type='integer[]'},
            { name='FIELD_MASTER', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string[]'},
            { name='sprite_path', type='string'},
        }
        beans['common.TableCareerTree'] = class
    end
    do
    ---@class common.TableChatConst 
     ---@field public ContactsMax integer @联系人最大数量
     ---@field public FriendsMax integer @好友最大数量
     ---@field public BlackMax integer @黑名单最大数量
     ---@field public PinnedMax integer @置顶最大数量
        local class = {
            { name='ContactsMax', type='integer'},
            { name='FriendsMax', type='integer'},
            { name='BlackMax', type='integer'},
            { name='PinnedMax', type='integer'},
        }
        beans['common.TableChatConst'] = class
    end
    do
    ---@class common.TableClanConst 
     ---@field public CLAN_ENTER_CD integer[] @进入世族的CD
     ---@field public CLAN_NAME_LENGTH integer @世族名称长度限制
     ---@field public CLAN_MOTTO_LENGTH integer @世族族训长度限制
     ---@field public CLAN_BADGE_LENGTH integer @世族族徽长度限制
     ---@field public CLAN_CREATE_COST table<integer,integer> @创建世族花费
     ---@field public CLAN_VICE_LEADER_LIMIT integer @副族长人数上限
     ---@field public CLAN_DONATE_CURRENCY integer @捐赠货币
     ---@field public CLAN_OCCUPY_LAND_CONTRIBUTION_RATE integer @占领地块贡献 X*地块等级
     ---@field public CLAN_BATTLE_CONTRIBUTION_RATE integer @战斗贡献 X*武勋
     ---@field public CLAN_LEAVE_CONTRIBUTION_REDUCE_RATE integer @成员离开贡献减少比例
     ---@field public CLAN_RESOURCE_BUFF table<integer,integer> @世族对资源地的加成/小时
     ---@field public CLAN_APPLY_LENGTH integer @世族申请长度限制
        local class = {
            { name='CLAN_ENTER_CD', type='integer[]'},
            { name='CLAN_NAME_LENGTH', type='integer'},
            { name='CLAN_MOTTO_LENGTH', type='integer'},
            { name='CLAN_BADGE_LENGTH', type='integer'},
            { name='CLAN_CREATE_COST', type='table<integer,integer>'},
            { name='CLAN_VICE_LEADER_LIMIT', type='integer'},
            { name='CLAN_DONATE_CURRENCY', type='integer'},
            { name='CLAN_OCCUPY_LAND_CONTRIBUTION_RATE', type='integer'},
            { name='CLAN_BATTLE_CONTRIBUTION_RATE', type='integer'},
            { name='CLAN_LEAVE_CONTRIBUTION_REDUCE_RATE', type='integer'},
            { name='CLAN_RESOURCE_BUFF', type='table<integer,integer>'},
            { name='CLAN_APPLY_LENGTH', type='integer'},
        }
        beans['common.TableClanConst'] = class
    end
    do
    ---@class common.TableClanLevel 
     ---@field public level integer @等级
     ---@field public needExp integer @所需经验值
     ---@field public memberLimit integer @人数上限
     ---@field public landLimit integer @资源地上限
        local class = {
            { name='level', type='integer'},
            { name='needExp', type='integer'},
            { name='memberLimit', type='integer'},
            { name='landLimit', type='integer'},
        }
        beans['common.TableClanLevel'] = class
    end
    do
    ---@class common.TableCommonConfig 
     ---@field public ToastShowTime number @提示显示时间
     ---@field public DynastyIconMap table<integer,string> @王朝(魏蜀吴群)图标
     ---@field public WorldChannelRpcLimit integer @世界聊天消息间隔
     ---@field public ValueTipsColorMap table<integer,string> @数值提示色
     ---@field public TacticGradeColorMap table<integer,color> @武将战法品级色
        local class = {
            { name='ToastShowTime', type='number'},
            { name='DynastyIconMap', type='table<integer,string>'},
            { name='WorldChannelRpcLimit', type='integer'},
            { name='ValueTipsColorMap', type='table<integer,string>'},
            { name='TacticGradeColorMap', type='table<integer,color>'},
        }
        beans['common.TableCommonConfig'] = class
    end
    do
    ---@class common.TableCurrency 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public desc string @描述
     ---@field public init_value integer @货币初始值
     ---@field public init_limit integer @货币上限
     ---@field public icon_path string @icon路径
     ---@field public icon_path_256 string @icon路径256
     ---@field public icon_min_path string @icon_min_路径
     ---@field public using_Data string[] @恭喜获得界面使用提示
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='init_value', type='integer'},
            { name='init_limit', type='integer'},
            { name='icon_path', type='string'},
            { name='icon_path_256', type='string'},
            { name='icon_min_path', type='string'},
            { name='using_Data', type='string[]'},
        }
        beans['common.TableCurrency'] = class
    end
    do
    ---@class common.TableEffect 
     ---@field public id integer
     ---@field public asset_path string @资源路径
     ---@field public is_loop boolean @是否循环
     ---@field public is_pic boolean @是否是图片特效
     ---@field public duration number @持续时间
     ---@field public scale number @缩放
     ---@field public is_hero_effect boolean @是否英雄特效
     ---@field public auto_destroy boolean @自动销毁
     ---@field public camera_shake_configs common.CameraShakeConfig[] @相机震动配置
        local class = {
            { name='id', type='integer'},
            { name='asset_path', type='string'},
            { name='is_loop', type='boolean'},
            { name='is_pic', type='boolean'},
            { name='duration', type='number'},
            { name='scale', type='number'},
            { name='is_hero_effect', type='boolean'},
            { name='auto_destroy', type='boolean'},
            { name='camera_shake_configs', type='common.CameraShakeConfig[]'},
        }
        beans['common.TableEffect'] = class
    end
    do
    ---@class common.TableEffectItem 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public cap integer @持有上限
     ---@field public active_use boolean @能否主动使用
     ---@field public description string @描述
     ---@field public access string[]
     ---@field public use_func item.UseFunc
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='cap', type='integer'},
            { name='active_use', type='boolean'},
            { name='description', type='string'},
            { name='access', type='string[]'},
            { name='use_func', type='item.UseFunc'},
        }
        beans['common.TableEffectItem'] = class
    end
    do
    ---@class common.TableEquip 
     ---@field public id integer @装备id
     ---@field public name string @装备名
     ---@field public equip_type integer @装备类型
     ---@field public quality integer @坐骑品质
     ---@field public main_prop integer @固定主属性
     ---@field public icon_path string @icon路径
     ---@field public description string @描述
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='equip_type', type='integer'},
            { name='quality', type='integer'},
            { name='main_prop', type='integer'},
            { name='icon_path', type='string'},
            { name='description', type='string'},
        }
        beans['common.TableEquip'] = class
    end
    do
    ---@class common.TableEquipConst 
     ---@field public EQUIP_PROPS_TYPE integer[] @装备基础属性类型
     ---@field public BUILD_EQUIP_COST_CURRENCY_TYPE integer @打造装备消耗货币种类
     ---@field public BUILD_EQUIP_COST_CURRENCY_NUM integer @打造装备消耗货币数量
     ---@field public BUILD_EQUIP_COST_ITEM_ID integer @打造装备消耗道具Id
     ---@field public BUILD_EQUIP_COST_ITEM_NUM integer @打造装备消耗道具数量
     ---@field public MAX_CONTINUOUS_BUILD integer @最大连续打造次数
     ---@field public BUILD_EQUIP_BAR_PROBABILITY_SHOW integer[] @打造概率条显示（所占宽度像素大小）
     ---@field public FORGE_COST_ITEM_ID integer @锻造消耗道具ID
     ---@field public FORGE_COST_ITEM_NUM integer @锻造消耗道具数量
     ---@field public FORGE_GUARANTEE_TIME integer @锻造保底次数
     ---@field public FORGE_GUARANTEE_UP_RANGE integer[] @锻造保底触发数值提升范围
     ---@field public FORGE_NORMAL_UP_RANGE integer @单次锻造最大可能提升范围
     ---@field public FORGE_DOUBLE_MAX_DIST integer @锻造时双词条最大差距
     ---@field public REBUILD_COST_ITEM_ID integer @重塑消耗道具ID
     ---@field public REBUILD_COST_ITEM_NUM integer @重塑消耗道具数量
     ---@field public TRAIN_HORSE_COST_CURRENCY table<integer,integer> @驯马消耗货币
     ---@field public TRAIN_HORSE_COST_TIME integer @驯马消耗时间
     ---@field public EQUIP_SLOTS integer[] @装备类型
     ---@field public ENTRIES_BASE_VALUE table<integer,integer> @装备词条基础值
     ---@field public DOUBLE_REVERSE_POSITIVE_ADD integer @正负词条正词条加成(%)
     ---@field public DOUBLE_REVERSE_NEGATIVE_ADD integer @正负词条负词条加成(%)
     ---@field public INIT_ENTRY_VALUE_RATIO integer[] @初始词条进度(%)
        local class = {
            { name='EQUIP_PROPS_TYPE', type='integer[]'},
            { name='BUILD_EQUIP_COST_CURRENCY_TYPE', type='integer'},
            { name='BUILD_EQUIP_COST_CURRENCY_NUM', type='integer'},
            { name='BUILD_EQUIP_COST_ITEM_ID', type='integer'},
            { name='BUILD_EQUIP_COST_ITEM_NUM', type='integer'},
            { name='MAX_CONTINUOUS_BUILD', type='integer'},
            { name='BUILD_EQUIP_BAR_PROBABILITY_SHOW', type='integer[]'},
            { name='FORGE_COST_ITEM_ID', type='integer'},
            { name='FORGE_COST_ITEM_NUM', type='integer'},
            { name='FORGE_GUARANTEE_TIME', type='integer'},
            { name='FORGE_GUARANTEE_UP_RANGE', type='integer[]'},
            { name='FORGE_NORMAL_UP_RANGE', type='integer'},
            { name='FORGE_DOUBLE_MAX_DIST', type='integer'},
            { name='REBUILD_COST_ITEM_ID', type='integer'},
            { name='REBUILD_COST_ITEM_NUM', type='integer'},
            { name='TRAIN_HORSE_COST_CURRENCY', type='table<integer,integer>'},
            { name='TRAIN_HORSE_COST_TIME', type='integer'},
            { name='EQUIP_SLOTS', type='integer[]'},
            { name='ENTRIES_BASE_VALUE', type='table<integer,integer>'},
            { name='DOUBLE_REVERSE_POSITIVE_ADD', type='integer'},
            { name='DOUBLE_REVERSE_NEGATIVE_ADD', type='integer'},
            { name='INIT_ENTRY_VALUE_RATIO', type='integer[]'},
        }
        beans['common.TableEquipConst'] = class
    end
    do
    ---@class common.TableEquipEffect 
     ---@field public id integer @特效id
     ---@field public name string @特效名
     ---@field public quality integer @特效品质
     ---@field public description string @描述
     ---@field public equip integer @专属装备
     ---@field public hero integer @专属武将
     ---@field public special_effect equip.Effect @特殊效果
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='quality', type='integer'},
            { name='description', type='string'},
            { name='equip', type='integer'},
            { name='hero', type='integer'},
            { name='special_effect', type='equip.Effect'},
        }
        beans['common.TableEquipEffect'] = class
    end
    do
    ---@class common.TableEquipQualityRatio 
     ---@field public id integer @特效品质
     ---@field public base_build_ratio integer @基础打造品质概率
     ---@field public build_entry_ratio table<integer,integer> @打造出词条概率
     ---@field public build_effect_ratio table<integer,integer> @打造出品质特效概率
     ---@field public rebuild_effect_ratio table<integer,integer> @重塑特效品质概率
     ---@field public props_add integer @装备稀有度对应基础属性增幅
     ---@field public decompose_equip_reward table<integer,integer> @装备分解奖励
     ---@field public sold_horse_reward table<integer,integer> @出售马匹奖励
        local class = {
            { name='id', type='integer'},
            { name='base_build_ratio', type='integer'},
            { name='build_entry_ratio', type='table<integer,integer>'},
            { name='build_effect_ratio', type='table<integer,integer>'},
            { name='rebuild_effect_ratio', type='table<integer,integer>'},
            { name='props_add', type='integer'},
            { name='decompose_equip_reward', type='table<integer,integer>'},
            { name='sold_horse_reward', type='table<integer,integer>'},
        }
        beans['common.TableEquipQualityRatio'] = class
    end
    do
    ---@class common.TableGachaSummary 
     ---@field public id integer @卡池id
     ---@field public name string @卡池名称
     ---@field public open_time integer @开始时间
     ---@field public end_time integer @结束时间
     ---@field public img_BG_url string @背景图形资源路径
     ---@field public img_tab_BG_url string @页签背景资源路径
     ---@field public img_tab_hero_url string @页签英雄标签路径
     ---@field public draw_cost_ticket_1 table<integer,integer> @单抽消耗招募令
     ---@field public draw_cost_currency_1 table<integer,integer> @单抽消耗货币
     ---@field public draw_cost_ticket_2 table<integer,integer> @五连抽消耗招募令
     ---@field public draw_cost_currency_2 table<integer,integer> @五连抽消耗货币
     ---@field public draw_cost_ticket_3 table<integer,integer> @二十连抽消耗招募令
     ---@field public draw_cost_currency_3 table<integer,integer> @二十连抽消耗货币
     ---@field public guarantee integer @保底次数
     ---@field public rand_pool_id integer @随机池id
     ---@field public draw_extra_rewards common.Reward @每抽额外奖励
     ---@field public daily_limit integer @每日抽卡上限
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='open_time', type='integer'},
            { name='end_time', type='integer'},
            { name='img_BG_url', type='string'},
            { name='img_tab_BG_url', type='string'},
            { name='img_tab_hero_url', type='string'},
            { name='draw_cost_ticket_1', type='table<integer,integer>'},
            { name='draw_cost_currency_1', type='table<integer,integer>'},
            { name='draw_cost_ticket_2', type='table<integer,integer>'},
            { name='draw_cost_currency_2', type='table<integer,integer>'},
            { name='draw_cost_ticket_3', type='table<integer,integer>'},
            { name='draw_cost_currency_3', type='table<integer,integer>'},
            { name='guarantee', type='integer'},
            { name='rand_pool_id', type='integer'},
            { name='draw_extra_rewards', type='common.Reward'},
            { name='daily_limit', type='integer'},
        }
        beans['common.TableGachaSummary'] = class
    end
    do
    ---@class common.TableHeroAttrType 
     ---@field public id integer @英雄属性
     ---@field public name string @名字
     ---@field public full_name string @全名
     ---@field public termId integer @词条说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='full_name', type='string'},
            { name='termId', type='integer'},
        }
        beans['common.TableHeroAttrType'] = class
    end
    do
    ---@class common.TableHeroEffect 
     ---@field public id integer
     ---@field public hero_id integer @英雄id
     ---@field public asset_path string @资源路径
     ---@field public is_loop boolean @是否循环
     ---@field public is_pic boolean @是否是图片特效
     ---@field public duration number @持续时间
     ---@field public scale number @缩放
        local class = {
            { name='id', type='integer'},
            { name='hero_id', type='integer'},
            { name='asset_path', type='string'},
            { name='is_loop', type='boolean'},
            { name='is_pic', type='boolean'},
            { name='duration', type='number'},
            { name='scale', type='number'},
        }
        beans['common.TableHeroEffect'] = class
    end
    do
    ---@class common.TableHeroExp 
     ---@field public level integer @武将等级
     ---@field public next_need_exp integer @升级经验
        local class = {
            { name='level', type='integer'},
            { name='next_need_exp', type='integer'},
        }
        beans['common.TableHeroExp'] = class
    end
    do
    ---@class common.TableHeroIdentityAttribute 
     ---@field public id integer @id
     ---@field public name string @名字
     ---@field public main_identity integer @主身份
     ---@field public buffId integer @对应战斗buff
     ---@field public desc string @描述
     ---@field public special_desc string @特殊特质描述
     ---@field public icon string @图标路径
     ---@field public special boolean @特殊
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='main_identity', type='integer'},
            { name='buffId', type='integer'},
            { name='desc', type='string'},
            { name='special_desc', type='string'},
            { name='icon', type='string'},
            { name='special', type='boolean'},
        }
        beans['common.TableHeroIdentityAttribute'] = class
    end
    do
    ---@class common.TableHeroIdentityConst 
     ---@field public HERO_IDENTITY_SLOT_UNLOCK integer[] @武将身份槽位解锁等级
     ---@field public HERO_IDENTITY_SLOT_UNLOCK_COST_COIN integer @解锁武将身份槽位消耗货币类型
     ---@field public HERO_IDENTITY_SLOT_UNLOCK_COST_NUM integer[] @解锁武将身份槽位对应消耗货币数量
     ---@field public HERO_IDENTITY_DEFAULT_BG_ICON string @英雄界面默认身份背景
        local class = {
            { name='HERO_IDENTITY_SLOT_UNLOCK', type='integer[]'},
            { name='HERO_IDENTITY_SLOT_UNLOCK_COST_COIN', type='integer'},
            { name='HERO_IDENTITY_SLOT_UNLOCK_COST_NUM', type='integer[]'},
            { name='HERO_IDENTITY_DEFAULT_BG_ICON', type='string'},
        }
        beans['common.TableHeroIdentityConst'] = class
    end
    do
    ---@class common.TableHeroItem 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public quality integer @道具品质
     ---@field public hero integer @武将
     ---@field public auto_use boolean @是否自动使用
     ---@field public use_func item.UseFunc
     ---@field public extra_rewards common.Reward @额外奖励
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='quality', type='integer'},
            { name='hero', type='integer'},
            { name='auto_use', type='boolean'},
            { name='use_func', type='item.UseFunc'},
            { name='extra_rewards', type='common.Reward'},
        }
        beans['common.TableHeroItem'] = class
    end
    do
    ---@class common.TableHeroMainIdentity 
     ---@field public id integer @id
     ---@field public name string @名字
     ---@field public color color @颜色
     ---@field public bg_icon string @背景底板路径
     ---@field public icon string @子身份界面图标路径
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='color', type='color'},
            { name='bg_icon', type='string'},
            { name='icon', type='string'},
        }
        beans['common.TableHeroMainIdentity'] = class
    end
    do
    ---@class common.TableHeroRoleType 
     ---@field public id integer @英雄定位
     ---@field public name string @名字
     ---@field public sprite_path string @图标
     ---@field public termId integer @词条说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='sprite_path', type='string'},
            { name='termId', type='integer'},
        }
        beans['common.TableHeroRoleType'] = class
    end
    do
    ---@class common.TableHeroSubIdentity 
     ---@field public id integer @id
     ---@field public name string @名字
     ---@field public main_identity integer @主身份
     ---@field public buffId integer @对应战斗buff
     ---@field public desc string @描述
     ---@field public icon string @图标路径
     ---@field public special boolean @特殊
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='main_identity', type='integer'},
            { name='buffId', type='integer'},
            { name='desc', type='string'},
            { name='icon', type='string'},
            { name='special', type='boolean'},
        }
        beans['common.TableHeroSubIdentity'] = class
    end
    do
    ---@class common.TableHeroSuitRank 
     ---@field public id integer @英雄适性
     ---@field public name string @名字
     ---@field public content string @文字
     ---@field public advise_value integer @推荐值
     ---@field public format string @文字格式
     ---@field public format_full string @文字格式(全)
     ---@field public bg_path string @底框图片
     ---@field public color_top color @颜色
     ---@field public color_bottom color @颜色
     ---@field public termId integer
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='content', type='string'},
            { name='advise_value', type='integer'},
            { name='format', type='string'},
            { name='format_full', type='string'},
            { name='bg_path', type='string'},
            { name='color_top', type='color'},
            { name='color_bottom', type='color'},
            { name='termId', type='integer'},
        }
        beans['common.TableHeroSuitRank'] = class
    end
    do
    ---@class common.TableHeroTitleType 
     ---@field public id integer @英雄身份
     ---@field public name string @名字
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
        }
        beans['common.TableHeroTitleType'] = class
    end
    do
    ---@class common.TableHomeBuildFuncBtn 
     ---@field public id integer @功能id
     ---@field public name string @名称(最终最好从Text读取)
     ---@field public icon string @图标
     ---@field public directlyOpenWhenOnlyOne boolean @只有该按钮时直接执行
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon', type='string'},
            { name='directlyOpenWhenOnlyOne', type='boolean'},
        }
        beans['common.TableHomeBuildFuncBtn'] = class
    end
    do
    ---@class common.TableHomeBuilding 
     ---@field public id integer @建筑id
     ---@field public level integer @等级
     ---@field public func home.BuildingFunc @建筑功能
     ---@field public cost table<integer,integer> @建造&amp;升级消耗
     ---@field public prosperity integer @繁荣度
     ---@field public need_pre_level integer @需要前置达成君王殿等级
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
            { name='func', type='home.BuildingFunc'},
            { name='cost', type='table<integer,integer>'},
            { name='prosperity', type='integer'},
            { name='need_pre_level', type='integer'},
        }
        beans['common.TableHomeBuilding'] = class
    end
    do
    ---@class common.TableHomeBuildingConst 
     ---@field public InitBuilding integer[] @初始建筑
     ---@field public RecruitmentInstituteRate integer @征兵所速度倍率
     ---@field public StorageBuildingResourceIds integer[] @仓库影响的资源
     ---@field public FarmerWorkBuilding integer[] @农民可以工作的资源建筑
     ---@field public SameBuilding table<integer,integer[]> @同类建筑
     ---@field public CoinOfBuilding table<integer,integer> @资源与建筑映射
        local class = {
            { name='InitBuilding', type='integer[]'},
            { name='RecruitmentInstituteRate', type='integer'},
            { name='StorageBuildingResourceIds', type='integer[]'},
            { name='FarmerWorkBuilding', type='integer[]'},
            { name='SameBuilding', type='table<integer,integer[]>'},
            { name='CoinOfBuilding', type='table<integer,integer>'},
        }
        beans['common.TableHomeBuildingConst'] = class
    end
    do
    ---@class common.TableHomeBuildingType 
     ---@field public id integer @建筑类型
     ---@field public name string @名称
     ---@field public maxLevel integer @最高等级
     ---@field public prefab_path string @模型路径
     ---@field public UI_path string @2DUI路径
     ---@field public brief_desc string @简短描述
     ---@field public desc string @描述
     ---@field public add_effect string[] @升级加成
     ---@field public extraButton homeBuilding.Button @name
     ---@field public funcBtn integer[] @功能按钮
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='maxLevel', type='integer'},
            { name='prefab_path', type='string'},
            { name='UI_path', type='string'},
            { name='brief_desc', type='string'},
            { name='desc', type='string'},
            { name='add_effect', type='string[]'},
            { name='extraButton', type='homeBuilding.Button'},
            { name='funcBtn', type='integer[]'},
        }
        beans['common.TableHomeBuildingType'] = class
    end
    do
    ---@class common.TableHomeTech 
     ---@field public id integer @科技id
     ---@field public maxLevel integer @最高等级
     ---@field public name string @科技名
     ---@field public func homeTech.TechFunc @建筑功能
     ---@field public pos integer[] @位于科技树的位置（列，行）
     ---@field public cost table<integer,integer[]> @建造&amp;升级消耗
     ---@field public prosperity integer[] @繁荣度
     ---@field public unlock_need_lv integer @君王殿等级限制
     ---@field public icon_path string @icon资源路径
     ---@field public desc1 string[] @太学加成描述1
     ---@field public desc2 string[] @太学加成描述2
     ---@field public specialBonus homeTech.SpecialBonus @描述
        local class = {
            { name='id', type='integer'},
            { name='maxLevel', type='integer'},
            { name='name', type='string'},
            { name='func', type='homeTech.TechFunc'},
            { name='pos', type='integer[]'},
            { name='cost', type='table<integer,integer[]>'},
            { name='prosperity', type='integer[]'},
            { name='unlock_need_lv', type='integer'},
            { name='icon_path', type='string'},
            { name='desc1', type='string[]'},
            { name='desc2', type='string[]'},
            { name='specialBonus', type='homeTech.SpecialBonus'},
        }
        beans['common.TableHomeTech'] = class
    end
    do
    ---@class common.TableHorse 
     ---@field public id integer @坐骑id
     ---@field public name string @坐骑名
     ---@field public equip_type integer @装备类型
     ---@field public quality integer @坐骑品质
     ---@field public icon_path string @icon路径
     ---@field public description string @描述
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='equip_type', type='integer'},
            { name='quality', type='integer'},
            { name='icon_path', type='string'},
            { name='description', type='string'},
        }
        beans['common.TableHorse'] = class
    end
    do
    ---@class common.TableIdSegment 
     ---@field public id integer @id段枚举
     ---@field public id_start integer @id段起始
     ---@field public id_end integer @id段结束
     ---@field public table_name string @导出表名
        local class = {
            { name='id', type='integer'},
            { name='id_start', type='integer'},
            { name='id_end', type='integer'},
            { name='table_name', type='string'},
        }
        beans['common.TableIdSegment'] = class
    end
    do
    ---@class common.TableItem 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public cap integer @持有上限
     ---@field public expire integer @过期时长/秒
     ---@field public red_expire integer @红显时间/秒
     ---@field public active_use boolean @能否主动使用
     ---@field public description string @描述
     ---@field public access string[]
     ---@field public use_func item.UseFunc
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='cap', type='integer'},
            { name='expire', type='integer'},
            { name='red_expire', type='integer'},
            { name='active_use', type='boolean'},
            { name='description', type='string'},
            { name='access', type='string[]'},
            { name='use_func', type='item.UseFunc'},
        }
        beans['common.TableItem'] = class
    end
    do
    ---@class common.TableItemConst 
     ---@field public SINGLE_USE_LIMIT integer @单次使用道具的上限
     ---@field public ITEM_BAG_RED_SHOW integer @背包格子红显数量
     ---@field public HERO_TOKEN_COST integer[] @武将升红需要信物
     ---@field public TACTIC_TOKEN_COST integer[] @战法升红需要信物
     ---@field public ITEM_BAG_CAP integer @背包格子数量上限
     ---@field public EQUIP_BAG_CAP integer @装备背包格子数量上限
     ---@field public HORSE_BAG_CAP integer @坐骑背包格子数量上限
     ---@field public TOKEN_BAG_CAP integer @信物背包格子数量上限
     ---@field public MATERIAL_BAG_CAP integer @材料背包格子数量上限
     ---@field public BAG_ITEM_COL_ROW_MIN_SIZE vector2 @背包格子最少列行数量
     ---@field public TACTIC_TOKEN_NAME_WRAP string @信物名称装饰
     ---@field public HERO_TOKEN_NAME_WRAP string @将魂名称装饰
     ---@field public CONGRATULATIONS_COL_ROW_DATA vector2 @恭喜获得界面格子列数量，滑动启用最小行数量
     ---@field public TOKEN_INSUFFICIENT_MESSAGE string @信物不足提示
        local class = {
            { name='SINGLE_USE_LIMIT', type='integer'},
            { name='ITEM_BAG_RED_SHOW', type='integer'},
            { name='HERO_TOKEN_COST', type='integer[]'},
            { name='TACTIC_TOKEN_COST', type='integer[]'},
            { name='ITEM_BAG_CAP', type='integer'},
            { name='EQUIP_BAG_CAP', type='integer'},
            { name='HORSE_BAG_CAP', type='integer'},
            { name='TOKEN_BAG_CAP', type='integer'},
            { name='MATERIAL_BAG_CAP', type='integer'},
            { name='BAG_ITEM_COL_ROW_MIN_SIZE', type='vector2'},
            { name='TACTIC_TOKEN_NAME_WRAP', type='string'},
            { name='HERO_TOKEN_NAME_WRAP', type='string'},
            { name='CONGRATULATIONS_COL_ROW_DATA', type='vector2'},
            { name='TOKEN_INSUFFICIENT_MESSAGE', type='string'},
        }
        beans['common.TableItemConst'] = class
    end
    do
    ---@class common.TableJudgeBehaviorTo 
     ---@field public id integer
     ---@field public first integer[] @主动方类型
     ---@field public second integer[] @被动方类型
     ---@field public relation integer[] @first看来和second的关系
     ---@field public valid integer @可以做的行为
        local class = {
            { name='id', type='integer'},
            { name='first', type='integer[]'},
            { name='second', type='integer[]'},
            { name='relation', type='integer[]'},
            { name='valid', type='integer'},
        }
        beans['common.TableJudgeBehaviorTo'] = class
    end
    do
    ---@class common.TableLandBuff 
     ---@field public id integer @Buff id
     ---@field public desc string @描述
     ---@field public type integer @地块Buff类型
     ---@field public arg integer @参数（目前一个够用，后续扩展为bean）
     ---@field public VX_effect string @vx表现
        local class = {
            { name='id', type='integer'},
            { name='desc', type='string'},
            { name='type', type='integer'},
            { name='arg', type='integer'},
            { name='VX_effect', type='string'},
        }
        beans['common.TableLandBuff'] = class
    end
    do
    ---@class common.TableLandConst 
     ---@field public SINGLE_RES_LAND_LIMIT integer @单类资源领地上限
     ---@field public INIT_RES_LAND_LIMIT integer @初始总领地上限
     ---@field public INIT_ROAD_LAND_LIMIT integer @初始总道路上限
     ---@field public ROAD_LAND_LOCK_LIMIT integer @最大可锁定的道路地上限(相对于总道路地百分比)
     ---@field public FAVORITE_LIMIT integer @收藏数量上限
     ---@field public FAVORITE_NAME_LIMIT integer @收藏自定义名字长度上限
     ---@field public GIVE_UP_LAND_DELAY integer @手动放弃地块耗时
     ---@field public MAX_SINGLE_RES_PROCESS integer @单类资源产量上线
     ---@field public RESLAND string @领土
     ---@field public ROAD string @道路
     ---@field public BUILDING string @建筑
     ---@field public FAVORITE string @收藏
     ---@field public NAME string @名称
     ---@field public TYPE string @类型
     ---@field public POSITION string @位置
     ---@field public STATE string @状态
     ---@field public CANCEL_ALL string @取消
     ---@field public SELECT_ALL string @全选
     ---@field public LOCK_GIVE_UP_DESC string @上锁放弃描述
     ---@field public CANCEL_FAVORITE_SUCESS string @取消收藏描述
     ---@field public SELECT_FAVORITE_SUCESS string @标记收藏描述
     ---@field public LAND_TIP_TITLE string @提示标题
     ---@field public LAND_TIP_COUNT integer @提示数量
     ---@field public TIP_1 string @提示一
     ---@field public TIP_2 string @提示二
     ---@field public TIP_3 string @提示三
     ---@field public TIP_4 string @提示四
     ---@field public COUNT string @文本配表
     ---@field public LEVEL string @文本配表
     ---@field public STATE_GIVING_UP string @文本配表
        local class = {
            { name='SINGLE_RES_LAND_LIMIT', type='integer'},
            { name='INIT_RES_LAND_LIMIT', type='integer'},
            { name='INIT_ROAD_LAND_LIMIT', type='integer'},
            { name='ROAD_LAND_LOCK_LIMIT', type='integer'},
            { name='FAVORITE_LIMIT', type='integer'},
            { name='FAVORITE_NAME_LIMIT', type='integer'},
            { name='GIVE_UP_LAND_DELAY', type='integer'},
            { name='MAX_SINGLE_RES_PROCESS', type='integer'},
            { name='RESLAND', type='string'},
            { name='ROAD', type='string'},
            { name='BUILDING', type='string'},
            { name='FAVORITE', type='string'},
            { name='NAME', type='string'},
            { name='TYPE', type='string'},
            { name='POSITION', type='string'},
            { name='STATE', type='string'},
            { name='CANCEL_ALL', type='string'},
            { name='SELECT_ALL', type='string'},
            { name='LOCK_GIVE_UP_DESC', type='string'},
            { name='CANCEL_FAVORITE_SUCESS', type='string'},
            { name='SELECT_FAVORITE_SUCESS', type='string'},
            { name='LAND_TIP_TITLE', type='string'},
            { name='LAND_TIP_COUNT', type='integer'},
            { name='TIP_1', type='string'},
            { name='TIP_2', type='string'},
            { name='TIP_3', type='string'},
            { name='TIP_4', type='string'},
            { name='COUNT', type='string'},
            { name='LEVEL', type='string'},
            { name='STATE_GIVING_UP', type='string'},
        }
        beans['common.TableLandConst'] = class
    end
    do
    ---@class common.TableLandFavoriteName 
     ---@field public id integer @ActorId或者是MapElementType
     ---@field public desc string @类型名称
     ---@field public showName string @收藏中显示的名称
        local class = {
            { name='id', type='integer'},
            { name='desc', type='string'},
            { name='showName', type='string'},
        }
        beans['common.TableLandFavoriteName'] = class
    end
    do
    ---@class common.TableLotteryPack 
     ---@field public id integer @卡包id
     ---@field public name string @卡包名
     ---@field public heroes integer[] @武将ID
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='heroes', type='integer[]'},
        }
        beans['common.TableLotteryPack'] = class
    end
    do
    ---@class common.TableMailConst 
     ---@field public TITLE_LENGTH_LIMIT integer @标题字数上限
     ---@field public CONTENT_LENGTH_LIMIT integer @内容字数上限
     ---@field public TIP_COUNT integer @模版tip数量
     ---@field public TEMPLATE_TIP_TITLE string @模版tip标题
     ---@field public TIP_1 string @Tip文字1，公共前缀为TIP_
     ---@field public TIP_2 string @Tip文字2
     ---@field public TIP_3 string @Tip文字3
     ---@field public SYSTEM_MAIL string @系统邮件默认作者名
     ---@field public DELETE_CONFIRM string @删除邮件提醒
     ---@field public DELETE_WARNING string @一键删除提示
     ---@field public FAVORITE_REWARD_WARNING string @奖励未领取收藏提示
     ---@field public DELETE_REWARD_WARNING string @奖励未领取删除提示
     ---@field public EMPTY_TITLE_WARNING string @空标题提示文本
     ---@field public EMPTY_CONTENT_WARNING string @空内容提示文本
     ---@field public SEND_SUCCESS_TEXT string @成功发送文本
     ---@field public SAVE_HINT string @内容保存提示文本
     ---@field public OPRATE_SUCCESS string @操作成功文本
     ---@field public TAB_TITLE_1 string @页签的标题名称  对应enums中的MailType
     ---@field public TAB_TITLE_2 string
     ---@field public TAB_TITLE_3 string
     ---@field public TAB_TITLE_4 string
     ---@field public TAB_TITLE_5 string
     ---@field public TAB_FAVORITE string @收藏的页签名称  不同于Enum
     ---@field public TIME_FORMAT string @时间格式
        local class = {
            { name='TITLE_LENGTH_LIMIT', type='integer'},
            { name='CONTENT_LENGTH_LIMIT', type='integer'},
            { name='TIP_COUNT', type='integer'},
            { name='TEMPLATE_TIP_TITLE', type='string'},
            { name='TIP_1', type='string'},
            { name='TIP_2', type='string'},
            { name='TIP_3', type='string'},
            { name='SYSTEM_MAIL', type='string'},
            { name='DELETE_CONFIRM', type='string'},
            { name='DELETE_WARNING', type='string'},
            { name='FAVORITE_REWARD_WARNING', type='string'},
            { name='DELETE_REWARD_WARNING', type='string'},
            { name='EMPTY_TITLE_WARNING', type='string'},
            { name='EMPTY_CONTENT_WARNING', type='string'},
            { name='SEND_SUCCESS_TEXT', type='string'},
            { name='SAVE_HINT', type='string'},
            { name='OPRATE_SUCCESS', type='string'},
            { name='TAB_TITLE_1', type='string'},
            { name='TAB_TITLE_2', type='string'},
            { name='TAB_TITLE_3', type='string'},
            { name='TAB_TITLE_4', type='string'},
            { name='TAB_TITLE_5', type='string'},
            { name='TAB_FAVORITE', type='string'},
            { name='TIME_FORMAT', type='string'},
        }
        beans['common.TableMailConst'] = class
    end
    do
    ---@class common.TableMailTemplate 
     ---@field public id integer @邮件id
     ---@field public alias string @程序别名(别重复)
     ---@field public mail_type integer @邮件分类
     ---@field public title string @标题
     ---@field public content string @邮件内容
        local class = {
            { name='id', type='integer'},
            { name='alias', type='string'},
            { name='mail_type', type='integer'},
            { name='title', type='string'},
            { name='content', type='string'},
        }
        beans['common.TableMailTemplate'] = class
    end
    do
    ---@class common.TableMaterial 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public cap integer @持有上限
     ---@field public expire integer @过期时长/秒
     ---@field public red_expire integer @红显时间/秒
     ---@field public active_use boolean @能否主动使用
     ---@field public description string @描述
     ---@field public access string[]
     ---@field public use_func item.UseFunc
     ---@field public jumpTrigger integer @点击使用按钮跳转的UI
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='cap', type='integer'},
            { name='expire', type='integer'},
            { name='red_expire', type='integer'},
            { name='active_use', type='boolean'},
            { name='description', type='string'},
            { name='access', type='string[]'},
            { name='use_func', type='item.UseFunc'},
            { name='jumpTrigger', type='integer'},
        }
        beans['common.TableMaterial'] = class
    end
    do
    ---@class common.TableNewbeeConfig 
     ---@field public NewbeeGuideFlowStart integer @引导流程起点
     ---@field public NewbeeGuideGacha common.Reward[] @新手抽卡配置
     ---@field public NewbeeGachaPoolId integer @新手引导抽卡卡池Id
     ---@field public NewbeeInitBag common.Reward @新手初始化道具
     ---@field public NewbeeRandomNameOne string[] @随机名字分词1
     ---@field public NewbeeRandomNameTwo string[] @随机名字分词2
     ---@field public NewbeeRandomNameThree string[] @随机名字分词3
     ---@field public NewbeeRandomNameFour string[] @随机名字分词4
        local class = {
            { name='NewbeeGuideFlowStart', type='integer'},
            { name='NewbeeGuideGacha', type='common.Reward[]'},
            { name='NewbeeGachaPoolId', type='integer'},
            { name='NewbeeInitBag', type='common.Reward'},
            { name='NewbeeRandomNameOne', type='string[]'},
            { name='NewbeeRandomNameTwo', type='string[]'},
            { name='NewbeeRandomNameThree', type='string[]'},
            { name='NewbeeRandomNameFour', type='string[]'},
        }
        beans['common.TableNewbeeConfig'] = class
    end
    do
    ---@class common.TableNewbeeDialogue 
     ---@field public id integer @流程id
     ---@field public type integer @流程类型
     ---@field public Special integer @特殊情况
     ---@field public Binder string @绑定的组件
     ---@field public buildingID integer @建筑ID
     ---@field public highlightShape integer @高亮形状
     ---@field public highlightSpot number[] @高亮位置
     ---@field public highlightScale number[] @圆形scale
     ---@field public highlightWidth number[] @方形width
     ---@field public startSpot number[] @拖动起始位
     ---@field public endSpot number[] @拖动终止位
     ---@field public personId integer @人物ID
     ---@field public dialogueContent string @对话内容
     ---@field public guideContent string @引导框内容
     ---@field public resourceSpot string @资源位置
        local class = {
            { name='id', type='integer'},
            { name='type', type='integer'},
            { name='Special', type='integer'},
            { name='Binder', type='string'},
            { name='buildingID', type='integer'},
            { name='highlightShape', type='integer'},
            { name='highlightSpot', type='number[]'},
            { name='highlightScale', type='number[]'},
            { name='highlightWidth', type='number[]'},
            { name='startSpot', type='number[]'},
            { name='endSpot', type='number[]'},
            { name='personId', type='integer'},
            { name='dialogueContent', type='string'},
            { name='guideContent', type='string'},
            { name='resourceSpot', type='string'},
        }
        beans['common.TableNewbeeDialogue'] = class
    end
    do
    ---@class common.TableNewbeeGuidePerson 
     ---@field public id integer @人物ID
     ---@field public name string @人物名称
     ---@field public resourceSpot string @资源位置
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='resourceSpot', type='string'},
        }
        beans['common.TableNewbeeGuidePerson'] = class
    end
    do
    ---@class common.TableRandomItem 
     ---@field public pool_id integer @随机池id
     ---@field public auto_id integer @随机道具id
     ---@field public item_list common.Reward @ 道具列表
     ---@field public sub_pool_id integer @子池id
     ---@field public weight integer @权重
     ---@field public guarantee integer @保底次数
        local class = {
            { name='pool_id', type='integer'},
            { name='auto_id', type='integer'},
            { name='item_list', type='common.Reward'},
            { name='sub_pool_id', type='integer'},
            { name='weight', type='integer'},
            { name='guarantee', type='integer'},
        }
        beans['common.TableRandomItem'] = class
    end
    do
    ---@class common.TableRank 
     ---@field public id integer @排行榜类型
     ---@field public all_top_n integer @最大前几排名
     ---@field public career_top_n integer @职业内<br/>最大前几排名
     ---@field public level_top_n integer @等级内<br/>最大前几名
     ---@field public client_filter string @支持筛选<br/>(客户端基于全服筛选)
     ---@field public host string @排行榜持有者
     ---@field public is_personal boolean @是否为个人排行榜
     ---@field public week_cycle boolean @周循环排行榜
        local class = {
            { name='id', type='integer'},
            { name='all_top_n', type='integer'},
            { name='career_top_n', type='integer'},
            { name='level_top_n', type='integer'},
            { name='client_filter', type='string'},
            { name='host', type='string'},
            { name='is_personal', type='boolean'},
            { name='week_cycle', type='boolean'},
        }
        beans['common.TableRank'] = class
    end
    do
    ---@class common.TableRankConst 
     ---@field public FIRST_OCCUPY_RANK_LEVEL_RANGE integer[] @首占排行榜等级范围
     ---@field public PANEL_TITLE string @排行榜界面标题
     ---@field public INDIVIDUAL_PROSPERITY_ALL_STRING string @个人繁荣榜列表全部选项文本
     ---@field public INDIVIDUAL_MILITARY_MERIT_AWARDS_LIST_STRING string[] @个人武勋榜文本列表
        local class = {
            { name='FIRST_OCCUPY_RANK_LEVEL_RANGE', type='integer[]'},
            { name='PANEL_TITLE', type='string'},
            { name='INDIVIDUAL_PROSPERITY_ALL_STRING', type='string'},
            { name='INDIVIDUAL_MILITARY_MERIT_AWARDS_LIST_STRING', type='string[]'},
        }
        beans['common.TableRankConst'] = class
    end
    do
    ---@class common.TableRarityRank 
     ---@field public id integer @稀有度等级
     ---@field public name string @名字
     ---@field public sprite_headitem4 string @列表头像边框
     ---@field public sprite_headitem string @列表头像边框
     ---@field public sprite_tacticItem string @战法品质边框
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='sprite_headitem4', type='string'},
            { name='sprite_headitem', type='string'},
            { name='sprite_tacticItem', type='string'},
        }
        beans['common.TableRarityRank'] = class
    end
    do
    ---@class common.TableRechargeJade 
     ---@field public id integer @id
     ---@field public sdkId string @对应的SDK商品Id
     ---@field public name string @商品名称(必填，在sdk界面需要显示)
     ---@field public desc string @商品描述(必填，在渠道包界面需要显示)
     ---@field public isFirstDouble boolean @是否首充翻倍
     ---@field public num integer @获得的魂玉数量
     ---@field public additionalNum integer @加赠数量
     ---@field public price number @价格
        local class = {
            { name='id', type='integer'},
            { name='sdkId', type='string'},
            { name='name', type='string'},
            { name='desc', type='string'},
            { name='isFirstDouble', type='boolean'},
            { name='num', type='integer'},
            { name='additionalNum', type='integer'},
            { name='price', type='number'},
        }
        beans['common.TableRechargeJade'] = class
    end
    do
    ---@class common.TableRetCode 
     ---@field public id integer @id
     ---@field public name string @名字
     ---@field public text string @文本
     ---@field public pop_notify boolean @是否弹出消息
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='text', type='string'},
            { name='pop_notify', type='boolean'},
        }
        beans['common.TableRetCode'] = class
    end
    do
    ---@class common.TableRuleText 
     ---@field public id integer @页面id
     ---@field public name string @页面名称
     ---@field public ruleText common.RuleText[] @页签名称
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='ruleText', type='common.RuleText[]'},
        }
        beans['common.TableRuleText'] = class
    end
    do
    ---@class common.TableSchedule 
     ---@field public ScheduleId integer @排期id
     ---@field public TimeZone string @时区（缺省用服务器时区）
     ---@field public BeginDatetime integer[] @开始时间
     ---@field public SpecialDate string @指定起始日期方法
     ---@field public CycleType string @周期类型
     ---@field public CycleSpan integer @周期跨度
     ---@field public CycleLoop integer @循环次数
     ---@field public Version integer @版本
     ---@field public Stages common.ScheduleStages[] @阶段
        local class = {
            { name='ScheduleId', type='integer'},
            { name='TimeZone', type='string'},
            { name='BeginDatetime', type='integer[]'},
            { name='SpecialDate', type='string'},
            { name='CycleType', type='string'},
            { name='CycleSpan', type='integer'},
            { name='CycleLoop', type='integer'},
            { name='Version', type='integer'},
            { name='Stages', type='common.ScheduleStages[]'},
        }
        beans['common.TableSchedule'] = class
    end
    do
    ---@class common.TableSetting 
     ---@field public id integer @id
     ---@field public name string @设置名称
     ---@field public groupName string[] @包含的设置Group
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='groupName', type='string[]'},
        }
        beans['common.TableSetting'] = class
    end
    do
    ---@class common.TableSettingConst 
     ---@field public TEXT_LOGOUT string @退出账号文本
     ---@field public TEXT_COPYBOARD string @剪贴板提示
     ---@field public NAME_TEXT string @文本
     ---@field public CHARACTER_ID_TEXT string @文本
     ---@field public SERVER_TEXT string @文本
     ---@field public TEXT_EXIT_GAME string @退出游戏文本
        local class = {
            { name='TEXT_LOGOUT', type='string'},
            { name='TEXT_COPYBOARD', type='string'},
            { name='NAME_TEXT', type='string'},
            { name='CHARACTER_ID_TEXT', type='string'},
            { name='SERVER_TEXT', type='string'},
            { name='TEXT_EXIT_GAME', type='string'},
        }
        beans['common.TableSettingConst'] = class
    end
    do
    ---@class common.TableSettingGroup 
     ---@field public id integer @设置细则id
     ---@field public name string @设置名称
     ---@field public showTip boolean @是否显示tip
     ---@field public codeEnum integer @enums表中Settings，无对应则填无
     ---@field public type integer @设置的组件类型
     ---@field public layout integer[] @坐标布局(x,y)
     ---@field public defaultValue integer @默认值
     ---@field public maxValue integer @最大值
     ---@field public minValue integer @最小值
     ---@field public tipTextId integer @提示文本Id，对应字典表
     ---@field public enumValues common.EnumPair[] @枚举值一览，枚举名称与值
     ---@field public enumImageUrl string[] @图像URL，与枚举一一对应
     ---@field public hideName boolean @是否隐藏名称
     ---@field public enumImageHint string @图像枚举的提示文本
     ---@field public buttonImgUrl string[] @第一项是背景  第二项为Icon，可只保留一项
     ---@field public clickFunc string @跳转函数
     ---@field public btnStateShow boolean @显示状态与否
     ---@field public childSettings integer[] @子属性
     ---@field public infoText table<integer,string> @操作后的提示文本
     ---@field public textToast string @操作时的Toast文本
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='showTip', type='boolean'},
            { name='codeEnum', type='integer'},
            { name='type', type='integer'},
            { name='layout', type='integer[]'},
            { name='defaultValue', type='integer'},
            { name='maxValue', type='integer'},
            { name='minValue', type='integer'},
            { name='tipTextId', type='integer'},
            { name='enumValues', type='common.EnumPair[]'},
            { name='enumImageUrl', type='string[]'},
            { name='hideName', type='boolean'},
            { name='enumImageHint', type='string'},
            { name='buttonImgUrl', type='string[]'},
            { name='clickFunc', type='string'},
            { name='btnStateShow', type='boolean'},
            { name='childSettings', type='integer[]'},
            { name='infoText', type='table<integer,string>'},
            { name='textToast', type='string'},
        }
        beans['common.TableSettingGroup'] = class
    end
    do
    ---@class common.TableSettingRelation 
     ---@field public id string @设置组名
     ---@field public list integer[] @设置子列表
     ---@field public height integer @高度
     ---@field public controlChild boolean @能否控制子项
        local class = {
            { name='id', type='string'},
            { name='list', type='integer[]'},
            { name='height', type='integer'},
            { name='controlChild', type='boolean'},
        }
        beans['common.TableSettingRelation'] = class
    end
    do
    ---@class common.TableShop 
     ---@field public id integer
     ---@field public schedule integer @刷新排期
     ---@field public refresh_cost_coin integer @立刻刷新<br/>消耗货币类型
     ---@field public refresh_cost_num integer @立刻刷新<br/>消耗货币数量
     ---@field public showResource integer[] @展示的资源
     ---@field public showCoin integer[] @展示的货币
        local class = {
            { name='id', type='integer'},
            { name='schedule', type='integer'},
            { name='refresh_cost_coin', type='integer'},
            { name='refresh_cost_num', type='integer'},
            { name='showResource', type='integer[]'},
            { name='showCoin', type='integer[]'},
        }
        beans['common.TableShop'] = class
    end
    do
    ---@class common.TableShopConst 
     ---@field public RESOURCE_TRADE_RATIO integer @贸易比例
     ---@field public RESOURCE_TRADE_TYPES integer[] @贸易的资源
     ---@field public RESOURCE_TRADE_ONECHANGE integer[][] @贸易单次加减的数量级
     ---@field public RESOURCE_TRADE_COST_MIN_DIVISOR integer @贸易消耗资源最小约数
        local class = {
            { name='RESOURCE_TRADE_RATIO', type='integer'},
            { name='RESOURCE_TRADE_TYPES', type='integer[]'},
            { name='RESOURCE_TRADE_ONECHANGE', type='integer[][]'},
            { name='RESOURCE_TRADE_COST_MIN_DIVISOR', type='integer'},
        }
        beans['common.TableShopConst'] = class
    end
    do
    ---@class common.TableShopGoods 
     ---@field public id integer @货物id<br/>小id的摆前面
     ---@field public shop integer @所属商店
     ---@field public goods item.SingleItemBase
     ---@field public limit integer @购买上限
     ---@field public coin integer @货币
     ---@field public cost integer @折扣后价格
     ---@field public rebate integer @折扣0-100
     ---@field public order integer @摆放顺序
     ---@field public stage_start integer @霸业阶段后<br/>可购买
     ---@field public stage_end integer @霸业阶段后<br/>不可购买
        local class = {
            { name='id', type='integer'},
            { name='shop', type='integer'},
            { name='goods', type='item.SingleItemBase'},
            { name='limit', type='integer'},
            { name='coin', type='integer'},
            { name='cost', type='integer'},
            { name='rebate', type='integer'},
            { name='order', type='integer'},
            { name='stage_start', type='integer'},
            { name='stage_end', type='integer'},
        }
        beans['common.TableShopGoods'] = class
    end
    do
    ---@class common.TableSingleBehaviorConfig 
     ---@field public behavior_type integer @单体行为类型
     ---@field public need_stamina boolean @是否要求体力大于0
        local class = {
            { name='behavior_type', type='integer'},
            { name='need_stamina', type='boolean'},
        }
        beans['common.TableSingleBehaviorConfig'] = class
    end
    do
    ---@class common.TableSlgConfig 
     ---@field public ResourceIconPathDict table<integer,string> @资源图标
     ---@field public GridMarkColorMap table<integer,color> @地块归属颜色
     ---@field public HudNameColorMap table<integer,color> @HUD名字文字颜色
     ---@field public SelectGridMarkColor color @地块选中颜色
     ---@field public FormationScaleFactor number @阵型缩放参数
     ---@field public ArmySelectPriority table<integer,integer> @军队选择界面的优先级
     ---@field public ArmyHotAreaRadius number @军队热区半径
     ---@field public HudLayerHideMap table<string,integer[]> @操作对应隐藏的Hud层级列表
     ---@field public UIArmyTypeCounterTipImage string @兵种克制说明图
     ---@field public MOVE_SPEED number @每格移动消耗时间/秒
     ---@field public INIT_SUPPLY integer @初始补给值
     ---@field public SUPPLY_RECOVER_INTERVAL_SECOND integer @耐久每点恢复耗时（秒）
     ---@field public DEFEND_CMD_COST_SUPPLY integer @驻守指令消耗耐久
     ---@field public MOVE_SUPPLY_COST_PER_GRID integer @移动每格消耗的补给
     ---@field public INSUFFICIENT_SUPPLY integer @补给不足提示阈值
     ---@field public ARMY_ANIMATION_TRANSITION_TIME number @兵种动画通用过渡时间
     ---@field public ARMY_MOVE_ANIMATION_SWITCH_SPEED number @当速度是普通行军的2倍时，<br/>切换为run动画，否则播放walk动画
     ---@field public STAMINA_RECOVER_TIME number @耐久恢复速度，每恢复1点体力需要x秒
     ---@field public STAMINA_COST integer @自动铺路体力消耗代价系数
     ---@field public GRID_COST integer @自动铺路每移动一格消耗系数
     ---@field public TURN_COST integer @自动铺路转弯消耗系数
     ---@field public BASE_MAX_AUTO_PATH_NUM integer @基础的最大自动铺路数量
     ---@field public GRID_DEFENDER_RECOVER_DELAY integer @土地守军恢复延迟
     ---@field public PANW_MOVE_SPEED number @小兵移动速度
     ---@field public PANW_ROTATE_SPEED number @小兵转向角速度
     ---@field public MARCH_LINE_NORMAL_WIDTH number @普通行军线宽度（数值无绝对意义）
     ---@field public MARCH_LINE_DETAIL_WIDTH number @详细行军线宽度（数值无绝对意义）
     ---@field public ARMY_CAMERA_FOCUS_HEIGHT number @相机锁定军队时，相机的高度
     ---@field public BATTLE_CAMERA_SHAKE_RANGE number[] @战斗可震屏的视口范围，最小x、最大x、最小y，最大y（0-1）
     ---@field public CAMERA_FOCUS_RANGE number[] @选中的军队在视口范围外时，相机会跳转锁定，而不打开队伍总览
        local class = {
            { name='ResourceIconPathDict', type='table<integer,string>'},
            { name='GridMarkColorMap', type='table<integer,color>'},
            { name='HudNameColorMap', type='table<integer,color>'},
            { name='SelectGridMarkColor', type='color'},
            { name='FormationScaleFactor', type='number'},
            { name='ArmySelectPriority', type='table<integer,integer>'},
            { name='ArmyHotAreaRadius', type='number'},
            { name='HudLayerHideMap', type='table<string,integer[]>'},
            { name='UIArmyTypeCounterTipImage', type='string'},
            { name='MOVE_SPEED', type='number'},
            { name='INIT_SUPPLY', type='integer'},
            { name='SUPPLY_RECOVER_INTERVAL_SECOND', type='integer'},
            { name='DEFEND_CMD_COST_SUPPLY', type='integer'},
            { name='MOVE_SUPPLY_COST_PER_GRID', type='integer'},
            { name='INSUFFICIENT_SUPPLY', type='integer'},
            { name='ARMY_ANIMATION_TRANSITION_TIME', type='number'},
            { name='ARMY_MOVE_ANIMATION_SWITCH_SPEED', type='number'},
            { name='STAMINA_RECOVER_TIME', type='number'},
            { name='STAMINA_COST', type='integer'},
            { name='GRID_COST', type='integer'},
            { name='TURN_COST', type='integer'},
            { name='BASE_MAX_AUTO_PATH_NUM', type='integer'},
            { name='GRID_DEFENDER_RECOVER_DELAY', type='integer'},
            { name='PANW_MOVE_SPEED', type='number'},
            { name='PANW_ROTATE_SPEED', type='number'},
            { name='MARCH_LINE_NORMAL_WIDTH', type='number'},
            { name='MARCH_LINE_DETAIL_WIDTH', type='number'},
            { name='ARMY_CAMERA_FOCUS_HEIGHT', type='number'},
            { name='BATTLE_CAMERA_SHAKE_RANGE', type='number[]'},
            { name='CAMERA_FOCUS_RANGE', type='number[]'},
        }
        beans['common.TableSlgConfig'] = class
    end
    do
    ---@class common.TableSLGRuleText 
     ---@field public id integer @对应的沙盘元素id
     ---@field public name string @页面名称
     ---@field public ruleText common.RuleTextCell[] @内容单元列表
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='ruleText', type='common.RuleTextCell[]'},
        }
        beans['common.TableSLGRuleText'] = class
    end
    do
    ---@class common.TableStrategy 
     ---@field public id integer @策牌id
     ---@field public name string @策牌名称
     ---@field public name_display string @策牌名称(展示)
     ---@field public is_base boolean @是否基础策牌
     ---@field public day_limit integer @日次数限制
     ---@field public cost integer @消耗策点数量
     ---@field public daily_recover integer @每日回复数量
     ---@field public storage_limit integer @存储上限
     ---@field public sprite_path_card string @展示卡牌
     ---@field public sing_process_view strategy.SingView @吟唱过程表现
     ---@field public sing_end_view strategy.SingView @吟唱结束表现
     ---@field public strategy_func strategy.BaseStrategy @策牌类型
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='name_display', type='string'},
            { name='is_base', type='boolean'},
            { name='day_limit', type='integer'},
            { name='cost', type='integer'},
            { name='daily_recover', type='integer'},
            { name='storage_limit', type='integer'},
            { name='sprite_path_card', type='string'},
            { name='sing_process_view', type='strategy.SingView'},
            { name='sing_end_view', type='strategy.SingView'},
            { name='strategy_func', type='strategy.BaseStrategy'},
        }
        beans['common.TableStrategy'] = class
    end
    do
    ---@class common.TableSwitch 
     ---@field public name string @名称
     ---@field public value boolean @开发
     ---@field public release_value boolean @外放
     ---@field public text string @文本显示
        local class = {
            { name='name', type='string'},
            { name='value', type='boolean'},
            { name='release_value', type='boolean'},
            { name='text', type='string'},
        }
        beans['common.TableSwitch'] = class
    end
    do
    ---@class common.TableTags 
     ---@field public name string @名字
     ---@field public content integer[] @id列表
        local class = {
            { name='name', type='string'},
            { name='content', type='integer[]'},
        }
        beans['common.TableTags'] = class
    end
    do
    ---@class common.TableTask 
     ---@field public id integer @任务id
     ---@field public title string @任务标题
     ---@field public chapters integer @主线章节
     ---@field public condition table<string,integer>[] @任务条件:id,level,num,value
     ---@field public triggerEvent integer[] @关联的触发事件
     ---@field public preCondition task._preCondition @前置条件类型
     ---@field public collectFunc task.collectFunc @搜集进度方法
     ---@field public rewards common.Reward @奖励类型
        local class = {
            { name='id', type='integer'},
            { name='title', type='string'},
            { name='chapters', type='integer'},
            { name='condition', type='table<string,integer>[]'},
            { name='triggerEvent', type='integer[]'},
            { name='preCondition', type='task._preCondition'},
            { name='collectFunc', type='task.collectFunc'},
            { name='rewards', type='common.Reward'},
        }
        beans['common.TableTask'] = class
    end
    do
    ---@class common.TableTaskChapterInterlude 
     ---@field public taskId integer @对应的任务id
     ---@field public chapter string @章节名称
     ---@field public title string @章节标题
     ---@field public content string @文字内容
     ---@field public BGPath string @背景图路径
     ---@field public mainRightBigPicPath string @任务主界面右侧大图路径
        local class = {
            { name='taskId', type='integer'},
            { name='chapter', type='string'},
            { name='title', type='string'},
            { name='content', type='string'},
            { name='BGPath', type='string'},
            { name='mainRightBigPicPath', type='string'},
        }
        beans['common.TableTaskChapterInterlude'] = class
    end
    do
    ---@class common.TableTaskConst 
     ---@field public InitTask integer @初始任务
     ---@field public SearchRadius integer @点击占领城外土地任务跳转的半径上限
     ---@field public SearchResourceLandMaxLevel integer @点击占领城外土地任务跳转的土地等级上限
        local class = {
            { name='InitTask', type='integer'},
            { name='SearchRadius', type='integer'},
            { name='SearchResourceLandMaxLevel', type='integer'},
        }
        beans['common.TableTaskConst'] = class
    end
    do
    ---@class common.TableTermEntry 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public desc string @说明
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='desc', type='string'},
        }
        beans['common.TableTermEntry'] = class
    end
    do
    ---@class common.TableTermEntry1 
     ---@field public name string @名称
     ---@field public desc string @说明
        local class = {
            { name='name', type='string'},
            { name='desc', type='string'},
        }
        beans['common.TableTermEntry1'] = class
    end
    do
    ---@class common.TableText 
     ---@field public id integer @id
     ---@field public name string @名字
     ---@field public text string @文本
     ---@field public flag integer @扩展标识(1:错误提示)
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='text', type='string'},
            { name='flag', type='integer'},
        }
        beans['common.TableText'] = class
    end
    do
    ---@class common.TableToken 
     ---@field public id integer @道具id
     ---@field public name string @道具名
     ---@field public icon_path string @icon路径
     ---@field public quality integer @道具品质
     ---@field public hero integer @武将
     ---@field public tactic integer @战法
     ---@field public decompose_coin integer @分解得到货币类型
     ---@field public decompose_num integer @分解得到货币数
     ---@field public cap integer @持有上限
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='icon_path', type='string'},
            { name='quality', type='integer'},
            { name='hero', type='integer'},
            { name='tactic', type='integer'},
            { name='decompose_coin', type='integer'},
            { name='decompose_num', type='integer'},
            { name='cap', type='integer'},
        }
        beans['common.TableToken'] = class
    end
    do
    ---@class common.TableValidBehavior 
     ---@field public status integer @状态
     ---@field public March boolean @行军
     ---@field public OccupyGrid boolean @攻占
     ---@field public AttackBuilding boolean @攻城
     ---@field public Defend boolean @前往驻守格子或建筑
     ---@field public Deploy boolean @调动
     ---@field public Siege boolean @集结
     ---@field public Return boolean @回城
     ---@field public UseStrategy boolean @使用策牌
     ---@field public OperateVehicle boolean @前往操控器械
     ---@field public ThrowStone boolean @投石
     ---@field public Halt boolean @中断
     ---@field public EnterSpace boolean @进入场景，可被玩家看见
     ---@field public ModifyArmy boolean @编队
     ---@field public Supply boolean @定时恢复补给
     ---@field public Interactable boolean @进入格子交互(仅主状态)
        local class = {
            { name='status', type='integer'},
            { name='March', type='boolean'},
            { name='OccupyGrid', type='boolean'},
            { name='AttackBuilding', type='boolean'},
            { name='Defend', type='boolean'},
            { name='Deploy', type='boolean'},
            { name='Siege', type='boolean'},
            { name='Return', type='boolean'},
            { name='UseStrategy', type='boolean'},
            { name='OperateVehicle', type='boolean'},
            { name='ThrowStone', type='boolean'},
            { name='Halt', type='boolean'},
            { name='EnterSpace', type='boolean'},
            { name='ModifyArmy', type='boolean'},
            { name='Supply', type='boolean'},
            { name='Interactable', type='boolean'},
        }
        beans['common.TableValidBehavior'] = class
    end
    do
    ---@class common.TableWorldBuff 
     ---@field public id integer @Buff id
     ---@field public type integer @沙盘Buff类型
     ---@field public arg integer @参数（目前一个够用，后续扩展为bean）
     ---@field public VX_effect string @vx表现
     ---@field public effect_id integer @特效表id
        local class = {
            { name='id', type='integer'},
            { name='type', type='integer'},
            { name='arg', type='integer'},
            { name='VX_effect', type='string'},
            { name='effect_id', type='integer'},
        }
        beans['common.TableWorldBuff'] = class
    end
    do
    ---@class drill.FixReward 
     ---@field public time integer @时间（秒）
     ---@field public itemId integer @道具id
     ---@field public num integer @数量
        local class = {
            { name='time', type='integer'},
            { name='itemId', type='integer'},
            { name='num', type='integer'},
        }
        beans['drill.FixReward'] = class
    end
    do
    ---@class drill.TableDrillChapter 
     ---@field public id integer @章节
     ---@field public name string @章节名
     ---@field public level_list integer[] @关卡列表
     ---@field public desc string @章节介绍
     ---@field public rewards common.Reward[]
     ---@field public unlock_day integer @开服后第几天解锁章节
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='level_list', type='integer[]'},
            { name='desc', type='string'},
            { name='rewards', type='common.Reward[]'},
            { name='unlock_day', type='integer'},
        }
        beans['drill.TableDrillChapter'] = class
    end
    do
    ---@class drill.TableDrillConst 
     ---@field public CHAPTER_REWARD_UNLOCK integer[] @章节宝箱解锁关卡
     ---@field public GET_REWARD_MIN_TIME integer @领取宝箱奖励最小时间
     ---@field public NOTIFY_REWARD_MIN_TIME integer @最小提醒领取宝箱奖励时间
     ---@field public MAX_STORAGE_REWARD_TIME integer @宝箱奖励持续累积最大时间
     ---@field public FIXED_REWARD drill.FixReward[] @军演固定挂机奖励
     ---@field public DAILY_CHANCE integer @日常挑战机会次数
     ---@field public TRANSLATE_ANIM_LENGTH number @位移动画时长
     ---@field public SHAKE_ANIM_LENGTH number @震动动画时长
     ---@field public RETURN_ANIM_LENGTH number @复位动画时长
     ---@field public FADE_ANIM_LENGTH number @渐隐动画时长
     ---@field public FORMATION_RULE_TITLE string @规则标题文字
     ---@field public FORMATION_RULE_LEN integer @规则数量 文本需要索引为RULE_开头  顺序连续
     ---@field public RULE_1 string @规则一
     ---@field public RULE_2 string @规则二
     ---@field public HANGREWARD_TIP_TITLE string @挂机奖励标题
     ---@field public HANGREWARD_TIP_RULE integer @挂机奖励的文本数量
     ---@field public TIP_1 string
     ---@field public TIP_2 string
     ---@field public TIP_3 string
     ---@field public TIP_4 string
     ---@field public HANG_INFO string @挂机上限提醒
     ---@field public FAIL_TIP_TEXT_1 string @失败Tip提示文字1
     ---@field public FAIL_TIP_TEXT_2 string @失败Tip提示文字2
     ---@field public FAIL_TIP_TEXT_3 string @失败Tip提示文字3
     ---@field public DEFENDER_BRIEF string @文本
     ---@field public PLAYER_FORMATION string @文本
     ---@field public DRILL_TITLE string @文本
     ---@field public TEAM_TEXT string @文本
        local class = {
            { name='CHAPTER_REWARD_UNLOCK', type='integer[]'},
            { name='GET_REWARD_MIN_TIME', type='integer'},
            { name='NOTIFY_REWARD_MIN_TIME', type='integer'},
            { name='MAX_STORAGE_REWARD_TIME', type='integer'},
            { name='FIXED_REWARD', type='drill.FixReward[]'},
            { name='DAILY_CHANCE', type='integer'},
            { name='TRANSLATE_ANIM_LENGTH', type='number'},
            { name='SHAKE_ANIM_LENGTH', type='number'},
            { name='RETURN_ANIM_LENGTH', type='number'},
            { name='FADE_ANIM_LENGTH', type='number'},
            { name='FORMATION_RULE_TITLE', type='string'},
            { name='FORMATION_RULE_LEN', type='integer'},
            { name='RULE_1', type='string'},
            { name='RULE_2', type='string'},
            { name='HANGREWARD_TIP_TITLE', type='string'},
            { name='HANGREWARD_TIP_RULE', type='integer'},
            { name='TIP_1', type='string'},
            { name='TIP_2', type='string'},
            { name='TIP_3', type='string'},
            { name='TIP_4', type='string'},
            { name='HANG_INFO', type='string'},
            { name='FAIL_TIP_TEXT_1', type='string'},
            { name='FAIL_TIP_TEXT_2', type='string'},
            { name='FAIL_TIP_TEXT_3', type='string'},
            { name='DEFENDER_BRIEF', type='string'},
            { name='PLAYER_FORMATION', type='string'},
            { name='DRILL_TITLE', type='string'},
            { name='TEAM_TEXT', type='string'},
        }
        beans['drill.TableDrillConst'] = class
    end
    do
    ---@class drill.TableDrillLevel 
     ---@field public id integer @关卡id
     ---@field public name string @关卡名
     ---@field public teamCount integer @玩家上阵队伍数
     ---@field public teamId integer[] @关卡阵容（NPC阵容）
     ---@field public rewards common.Reward @资源奖励
     ---@field public output table<integer,integer> @货币产出速度（每小时）（非累积）
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='teamCount', type='integer'},
            { name='teamId', type='integer[]'},
            { name='rewards', type='common.Reward'},
            { name='output', type='table<integer,integer>'},
        }
        beans['drill.TableDrillLevel'] = class
    end
    do
    ---@class equip.Effect  @装备特效
        local class = {
        }
        beans['equip.Effect'] = class
    end
    do
    ---@class equip.AddAttrEffect :equip.Effect 
     ---@field public attrType integer @属性类型
     ---@field public value integer @增加属性值
        local class = {
            { name='attrType', type='integer'},
            { name='value', type='integer'},
        }
        beans['equip.AddAttrEffect'] = class
    end
    do
    ---@class equip.CombatBuff :equip.Effect 
     ---@field public buffId integer @战斗buffId
        local class = {
            { name='buffId', type='integer'},
        }
        beans['equip.CombatBuff'] = class
    end
    do
    ---@class equip.ExpAddition :equip.Effect 
     ---@field public addPercent integer @增加经验百分比
        local class = {
            { name='addPercent', type='integer'},
        }
        beans['equip.ExpAddition'] = class
    end
    do
    ---@class funcopen.TableFuncOpen 
     ---@field public id integer @功能开启ID
     ---@field public condition common.Condition[] @功能开启条件
     ---@field public icon string @功能开放展示图片名称
     ---@field public title string @标题
     ---@field public dialogGroupId integer @对话组id
     ---@field public prompt boolean @功能开放时是否提示
     ---@field public disable boolean @是否禁用
        local class = {
            { name='id', type='integer'},
            { name='condition', type='common.Condition[]'},
            { name='icon', type='string'},
            { name='title', type='string'},
            { name='dialogGroupId', type='integer'},
            { name='prompt', type='boolean'},
            { name='disable', type='boolean'},
        }
        beans['funcopen.TableFuncOpen'] = class
    end
    do
    ---@class funcopen.TableFuncOpenTrigger 
     ---@field public id string @开启条件类型名
     ---@field public triggerType integer @触发类型归属
        local class = {
            { name='id', type='string'},
            { name='triggerType', type='integer'},
        }
        beans['funcopen.TableFuncOpenTrigger'] = class
    end
    do
    ---@class home.BuildingFunc  @建筑功能函数
        local class = {
        }
        beans['home.BuildingFunc'] = class
    end
    do
    ---@class home.EmptyFunc :home.BuildingFunc 
        local class = {
        }
        beans['home.EmptyFunc'] = class
    end
    do
    ---@class home.FarmerHome :home.BuildingFunc 
     ---@field public resOutputPerHour integer @增加的每小时资源产量
        local class = {
            { name='resOutputPerHour', type='integer'},
        }
        beans['home.FarmerHome'] = class
    end
    do
    ---@class home.JunYing :home.BuildingFunc 
     ---@field public soldierNumAdd integer @每名武将带兵上限增加
        local class = {
            { name='soldierNumAdd', type='integer'},
        }
        beans['home.JunYing'] = class
    end
    do
    ---@class home.MainBuilding :home.BuildingFunc 
     ---@field public duraLimit integer @主城耐久上限
     ---@field public armySlotLimit integer @部队数量上限
        local class = {
            { name='duraLimit', type='integer'},
            { name='armySlotLimit', type='integer'},
        }
        beans['home.MainBuilding'] = class
    end
    do
    ---@class home.ResBuilding :home.BuildingFunc 
     ---@field public resType integer @产出资源类型
     ---@field public resAddPerHour integer @每小时资源产出增加
     ---@field public resAddRatio4WuZhongShengYou integer @无中生有加成资源获得比例
        local class = {
            { name='resType', type='integer'},
            { name='resAddPerHour', type='integer'},
            { name='resAddRatio4WuZhongShengYou', type='integer'},
        }
        beans['home.ResBuilding'] = class
    end
    do
    ---@class home.StorageBuilding :home.BuildingFunc 
     ---@field public resLimit integer @资源上限
        local class = {
            { name='resLimit', type='integer'},
        }
        beans['home.StorageBuilding'] = class
    end
    do
    ---@class home.ZhengBingSuoFunc :home.BuildingFunc 
     ---@field public soldierLimitSet integer @预备兵上限最终值
     ---@field public soldierRecoverPerHour integer @每小时预备兵恢复数量
     ---@field public resCostPerHour integer @每小时资源消耗
        local class = {
            { name='soldierLimitSet', type='integer'},
            { name='soldierRecoverPerHour', type='integer'},
            { name='resCostPerHour', type='integer'},
        }
        beans['home.ZhengBingSuoFunc'] = class
    end
    do
    ---@class homeBuilding.Button 
     ---@field public name string @按钮名称
     ---@field public iconPath string @按钮icon资源路径
     ---@field public directlyOpenWhenOnlyOne boolean @只有该按钮时直接执行
        local class = {
            { name='name', type='string'},
            { name='iconPath', type='string'},
            { name='directlyOpenWhenOnlyOne', type='boolean'},
        }
        beans['homeBuilding.Button'] = class
    end
    do
    ---@class homeTech.TechFunc  @内城科技功能函数
        local class = {
        }
        beans['homeTech.TechFunc'] = class
    end
    do
    ---@class homeTech.AddResLand :homeTech.TechFunc 
     ---@field public addLimit integer[] @增加地块上限
        local class = {
            { name='addLimit', type='integer[]'},
        }
        beans['homeTech.AddResLand'] = class
    end
    do
    ---@class homeTech.AddRoadLand :homeTech.TechFunc 
     ---@field public addLimit integer[] @增加地块上限
        local class = {
            { name='addLimit', type='integer[]'},
        }
        beans['homeTech.AddRoadLand'] = class
    end
    do
    ---@class homeTech.AddSupplyMax :homeTech.TechFunc 
     ---@field public zeroValue integer @0级效果参数
     ---@field public value integer[] @参数
        local class = {
            { name='zeroValue', type='integer'},
            { name='value', type='integer[]'},
        }
        beans['homeTech.AddSupplyMax'] = class
    end
    do
    ---@class homeTech.ArmyBattleBuff :homeTech.TechFunc 
     ---@field public zeroValue number @0级效果参数
     ---@field public value number[] @参数
     ---@field public isPercentage boolean @百分比参数
     ---@field public buffId integer @对应的buffId
        local class = {
            { name='zeroValue', type='number'},
            { name='value', type='number[]'},
            { name='isPercentage', type='boolean'},
            { name='buffId', type='integer'},
        }
        beans['homeTech.ArmyBattleBuff'] = class
    end
    do
    ---@class homeTech.ArmyFateBuff :homeTech.TechFunc 
     ---@field public zeroValue number @0级效果参数
     ---@field public value number[] @参数
     ---@field public isPercentage boolean @百分比参数
     ---@field public buffId integer @对应的buffId
     ---@field public dynasty integer @国家
        local class = {
            { name='zeroValue', type='number'},
            { name='value', type='number[]'},
            { name='isPercentage', type='boolean'},
            { name='buffId', type='integer'},
            { name='dynasty', type='integer'},
        }
        beans['homeTech.ArmyFateBuff'] = class
    end
    do
    ---@class homeTech.SpecialBonus :homeTech.TechFunc 
     ---@field public desc string @描述
     ---@field public buffId integer @对应的战斗buffId
     ---@field public neededLevel integer @所需解锁等级
        local class = {
            { name='desc', type='string'},
            { name='buffId', type='integer'},
            { name='neededLevel', type='integer'},
        }
        beans['homeTech.SpecialBonus'] = class
    end
    do
    ---@class item.BattleTacticTransfer 
     ---@field public battle_tactic_id integer @战法
     ---@field public token_id integer @信物
        local class = {
            { name='battle_tactic_id', type='integer'},
            { name='token_id', type='integer'},
        }
        beans['item.BattleTacticTransfer'] = class
    end
    do
    ---@class item.HeroTransfer 
     ---@field public hero_id integer @武将
     ---@field public token_id integer @信物
        local class = {
            { name='hero_id', type='integer'},
            { name='token_id', type='integer'},
        }
        beans['item.HeroTransfer'] = class
    end
    do
    ---@class item.IdNumRatio 
     ---@field public id integer @道具id
     ---@field public num integer @数量
     ---@field public weight integer @概率
        local class = {
            { name='id', type='integer'},
            { name='num', type='integer'},
            { name='weight', type='integer'},
        }
        beans['item.IdNumRatio'] = class
    end
    do
    ---@class item.SingleItemBase  @单个道具配置
        local class = {
        }
        beans['item.SingleItemBase'] = class
    end
    do
    ---@class item.SingleCurrency :item.SingleItemBase 
     ---@field public id integer @货币id
     ---@field public num integer @数量
        local class = {
            { name='id', type='integer'},
            { name='num', type='integer'},
        }
        beans['item.SingleCurrency'] = class
    end
    do
    ---@class item.SingleItem :item.SingleItemBase 
     ---@field public id integer @道具id
     ---@field public num integer @数量
        local class = {
            { name='id', type='integer'},
            { name='num', type='integer'},
        }
        beans['item.SingleItem'] = class
    end
    do
    ---@class item.UseFunc  @道具主动使用方法
        local class = {
        }
        beans['item.UseFunc'] = class
    end
    do
    ---@class item.AddResLand :item.UseFunc 
        local class = {
        }
        beans['item.AddResLand'] = class
    end
    do
    ---@class item.AddRoadLand :item.UseFunc 
        local class = {
        }
        beans['item.AddRoadLand'] = class
    end
    do
    ---@class item.AddStamina :item.UseFunc 
     ---@field public value integer @恢复值
        local class = {
            { name='value', type='integer'},
        }
        beans['item.AddStamina'] = class
    end
    do
    ---@class item.BattleTacticItem :item.UseFunc 
     ---@field public battle_tactic_transfer item.BattleTacticTransfer
        local class = {
            { name='battle_tactic_transfer', type='item.BattleTacticTransfer'},
        }
        beans['item.BattleTacticItem'] = class
    end
    do
    ---@class item.ChooseItem :item.UseFunc 
     ---@field public options table<integer,integer> @道具id: 数量
        local class = {
            { name='options', type='table<integer,integer>'},
        }
        beans['item.ChooseItem'] = class
    end
    do
    ---@class item.ChooseRes :item.UseFunc 
     ---@field public options table<integer,integer> @资源货币id: 数量
        local class = {
            { name='options', type='table<integer,integer>'},
        }
        beans['item.ChooseRes'] = class
    end
    do
    ---@class item.EquipBlueprint :item.UseFunc 
     ---@field public equipId integer @装备id
        local class = {
            { name='equipId', type='integer'},
        }
        beans['item.EquipBlueprint'] = class
    end
    do
    ---@class item.HeroItem :item.UseFunc 
     ---@field public hero_transfer item.HeroTransfer
        local class = {
            { name='hero_transfer', type='item.HeroTransfer'},
        }
        beans['item.HeroItem'] = class
    end
    do
    ---@class item.HorseTrainItem :item.UseFunc 
        local class = {
        }
        beans['item.HorseTrainItem'] = class
    end
    do
    ---@class item.HorseTrainMaterial :item.UseFunc 
        local class = {
        }
        beans['item.HorseTrainMaterial'] = class
    end
    do
    ---@class item.RandomItem :item.UseFunc 
     ---@field public rand_list item.IdNumRatio[] @随机列表
        local class = {
            { name='rand_list', type='item.IdNumRatio[]'},
        }
        beans['item.RandomItem'] = class
    end
    do
    ---@class jump.TableJump 
     ---@field public id integer @跳转ID
     ---@field public func_openId integer @对应功能开启表(用于功能阶段性开启)
     ---@field public disable integer @禁用:1
     ---@field public desc string @描述(跳转栏文本显示)
        local class = {
            { name='id', type='integer'},
            { name='func_openId', type='integer'},
            { name='disable', type='integer'},
            { name='desc', type='string'},
        }
        beans['jump.TableJump'] = class
    end
    do
    ---@class map.TableMapLevel 
     ---@field public id integer @关卡id
     ---@field public name string @关卡名
     ---@field public visible boolean @是否可见
     ---@field public season_id integer @赛季id
     ---@field public season string @赛季
     ---@field public view_config table<integer,vector2>
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='visible', type='boolean'},
            { name='season_id', type='integer'},
            { name='season', type='string'},
            { name='view_config', type='table<integer,vector2>'},
        }
        beans['map.TableMapLevel'] = class
    end
    do
    ---@class redpoint.TableRedpoint 
     ---@field public id integer @节点ID
     ---@field public name string @导出名称
     ---@field public styleType integer @红点样式类型 (0:小圆点 1:数字显示)
     ---@field public displayName string @描述
     ---@field public parameter string @扩展参数
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='styleType', type='integer'},
            { name='displayName', type='string'},
            { name='parameter', type='string'},
        }
        beans['redpoint.TableRedpoint'] = class
    end
    do
    ---@class season.BaseFunc  @赛季功能开关
        local class = {
        }
        beans['season.BaseFunc'] = class
    end
    do
    ---@class season.AddResLandLimit :season.BaseFunc 
     ---@field public resLand integer @领地数量
     ---@field public roadLand integer @道路数量
        local class = {
            { name='resLand', type='integer'},
            { name='roadLand', type='integer'},
        }
        beans['season.AddResLandLimit'] = class
    end
    do
    ---@class season.Empty :season.BaseFunc 
        local class = {
        }
        beans['season.Empty'] = class
    end
    do
    ---@class season.LimitSwitchFunc :season.BaseFunc 
     ---@field public switch_name string @开关名
     ---@field public set_status boolean @开关
        local class = {
            { name='switch_name', type='string'},
            { name='set_status', type='boolean'},
        }
        beans['season.LimitSwitchFunc'] = class
    end
    do
    ---@class season.SwitchFunc :season.BaseFunc 
     ---@field public switch_name string @开关名
     ---@field public set_status boolean @开关
        local class = {
            { name='switch_name', type='string'},
            { name='set_status', type='boolean'},
        }
        beans['season.SwitchFunc'] = class
    end
    do
    ---@class season.BaseTarget  @赛季目标
        local class = {
        }
        beans['season.BaseTarget'] = class
    end
    do
    ---@class season.OccupyLand :season.BaseTarget 
     ---@field public level integer @地块等级
     ---@field public num integer @地块数量
     ---@field public desc string @内容描述
        local class = {
            { name='level', type='integer'},
            { name='num', type='integer'},
            { name='desc', type='string'},
        }
        beans['season.OccupyLand'] = class
    end
    do
    ---@class season.TableS1 
     ---@field public id integer @霸业阶段
     ---@field public name string @阶段名
     ---@field public begin integer @开始天数
     ---@field public limit_pack integer[] @限时卡包
     ---@field public switches season.BaseFunc[] @新功能开启
     ---@field public targets season.BaseTarget @达成条件
     ---@field public rewards common.Reward @达成奖励
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='begin', type='integer'},
            { name='limit_pack', type='integer[]'},
            { name='switches', type='season.BaseFunc[]'},
            { name='targets', type='season.BaseTarget'},
            { name='rewards', type='common.Reward'},
        }
        beans['season.TableS1'] = class
    end
    do
    ---@class season.TableS1CampInfo 
     ---@field public identity integer
     ---@field public color color @颜色
        local class = {
            { name='identity', type='integer'},
            { name='color', type='color'},
        }
        beans['season.TableS1CampInfo'] = class
    end
    do
    ---@class season.TableS1Const 
     ---@field public BIRTHPLACE_NUM integer @出生州数量
     ---@field public CHOOSE_ROLE_PRE_TIME integer @选角色前置时长（秒）
     ---@field public CHOOSE_ROLE_DURATION integer @单个州选角色持续时间（秒）
     ---@field public FORCE_CHOOSE_ROLE_STAGE integer @赛季强制选角色霸业阶段
     ---@field public ROLE_TO_NUM table<integer,integer> @角色数量
     ---@field public SHOW_CARD_NUM integer @角色卡牌明牌数量
     ---@field public AMBITIONS_MIDDLE_SETTLE_DAY integer @赛季中期结算日
     ---@field public AMBITIONS_FINAL_SETTLE_DAY integer @赛季末期结算日
     ---@field public AMBITIONS_MIDDLE_START_DAY integer @赛季中期开始结算日
     ---@field public BIRTH_PLACE_ORDER integer[] @出生州序号顺序
        local class = {
            { name='BIRTHPLACE_NUM', type='integer'},
            { name='CHOOSE_ROLE_PRE_TIME', type='integer'},
            { name='CHOOSE_ROLE_DURATION', type='integer'},
            { name='FORCE_CHOOSE_ROLE_STAGE', type='integer'},
            { name='ROLE_TO_NUM', type='table<integer,integer>'},
            { name='SHOW_CARD_NUM', type='integer'},
            { name='AMBITIONS_MIDDLE_SETTLE_DAY', type='integer'},
            { name='AMBITIONS_FINAL_SETTLE_DAY', type='integer'},
            { name='AMBITIONS_MIDDLE_START_DAY', type='integer'},
            { name='BIRTH_PLACE_ORDER', type='integer[]'},
        }
        beans['season.TableS1Const'] = class
    end
    do
    ---@class season.TableS1Count 
     ---@field public id integer
     ---@field public name string @名次
     ---@field public ambitionMidtermCount integer @霸业名额
     ---@field public scoreRank string @积分名次
     ---@field public allyTitle string @同盟称号
     ---@field public ambitionSeasonCount integer @霸业名额
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='ambitionMidtermCount', type='integer'},
            { name='scoreRank', type='string'},
            { name='allyTitle', type='string'},
            { name='ambitionSeasonCount', type='integer'},
        }
        beans['season.TableS1Count'] = class
    end
    do
    ---@class season.TableS1EmpireWords 
     ---@field public text string @对话
        local class = {
            { name='text', type='string'},
        }
        beans['season.TableS1EmpireWords'] = class
    end
    do
    ---@class season.TableS1FullscreenToast 
     ---@field public id integer
     ---@field public title string @标题
     ---@field public subtitle string @副标题
        local class = {
            { name='id', type='integer'},
            { name='title', type='string'},
            { name='subtitle', type='string'},
        }
        beans['season.TableS1FullscreenToast'] = class
    end
    do
    ---@class season.TableS1Gameplay 
     ---@field public id integer
     ---@field public name string @玩法标签
     ---@field public menus integer[] @菜单列表索引
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='menus', type='integer[]'},
        }
        beans['season.TableS1Gameplay'] = class
    end
    do
    ---@class season.TableS1GameplayInfo 
     ---@field public id integer
     ---@field public name string @选项名
     ---@field public title string[] @标题内容
     ---@field public tips string[] @提示内容
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='title', type='string[]'},
            { name='tips', type='string[]'},
        }
        beans['season.TableS1GameplayInfo'] = class
    end
    do
    ---@class season.TableS1IdentityInfo 
     ---@field public identity integer
     ---@field public s1camp integer @所属阵营
     ---@field public desc1 string @描述1
     ---@field public desc2 string @描述2
     ---@field public color color @颜色
     ---@field public card_icon string @身份卡图标
     ---@field public sketch_icon string @剪影图标
     ---@field public termId integer @术语表id
        local class = {
            { name='identity', type='integer'},
            { name='s1camp', type='integer'},
            { name='desc1', type='string'},
            { name='desc2', type='string'},
            { name='color', type='color'},
            { name='card_icon', type='string'},
            { name='sketch_icon', type='string'},
            { name='termId', type='integer'},
        }
        beans['season.TableS1IdentityInfo'] = class
    end
    do
    ---@class season.TableS1Reward 
     ---@field public id integer
     ---@field public rewardType string @奖励类型
     ---@field public rewards common.Reward @资源奖励
     ---@field public lotteryInfo string[] @卡包开启，超链接文本需要处理
     ---@field public seasonShopInfo string[] @赛季商店
        local class = {
            { name='id', type='integer'},
            { name='rewardType', type='string'},
            { name='rewards', type='common.Reward'},
            { name='lotteryInfo', type='string[]'},
            { name='seasonShopInfo', type='string[]'},
        }
        beans['season.TableS1Reward'] = class
    end
    do
    ---@class siege.TableSiegeConst 
     ---@field public ANNOUNCE_DELAY_HOUR integer[] @攻城宣战可选倒计时(小时)
     ---@field public ANNOUNCE_DELAY_MINUTE integer[] @攻城宣战可选倒计时(分钟)
     ---@field public PREORDER_GO_BEFORE_SEC integer @预约在宣战前1小时出发
     ---@field public PREORDER_GO_CHECK_DELAY integer @预约出发检查时间
     ---@field public PREORDER_GO_CHECK_CNT integer @预约出发检查次数
     ---@field public SIEGE_DURATION integer @攻城流程总时长
     ---@field public SIEGE_CODEDOWN integer @攻城失败冷却
     ---@field public SIEGE_MAIN_MOVE_PATCH integer @攻城主力出发批次人数
     ---@field public SIEGE_SUB_MOVE_PATCH integer @攻城拆迁出发批次人数
     ---@field public SIEGE_MOVE_PATCH_DELAY integer @攻城不对出发批次间隔秒
     ---@field public UISiegeAssembleColor color @集结中颜色
     ---@field public UISiegeAttackingColor color @攻城中颜色
     ---@field public UISiegeAssembleIcon string @集结中图标
     ---@field public UISiegeAttackingIcon string @攻城中图标
     ---@field public UISiegeHudAssembleBg string @集结中图标
     ---@field public UISiegeHudAttackingBg string @攻城中图标
     ---@field public UISiegeHudAssembleTextColor color @集结中文字颜色
     ---@field public UISiegeHudAttackingTextColor color @攻城中文字颜色
     ---@field public BarrackTempArmyStartTime number @兵营出兵开始时间
     ---@field public BarrackTempArmyDuration number @兵营出兵时长
     ---@field public BarrackTempArmyInterval1 number @出兵大间隔
     ---@field public BarrackTempArmyInterval2 number @出兵小间隔
     ---@field public BarrackTempArmyCount integer @单次出兵数量
     ---@field public BarrackTempArmySpeed number @兵营出兵军队速度
     ---@field public RankTopN integer @排行榜统计前N个
     ---@field public BREAK_DURA_COST_TIME integer @拆耐久消耗时间（秒）
     ---@field public BREAK_DURA_BASE_SIEGE integer @拆耐久基础攻城值
     ---@field public CLEAR_INSIDER_REPEAT_COUNT integer @攻城失败清理内部次数
     ---@field public CLEAR_INSIDER_REPEAT_INTERNAL integer @攻城失败清理内部间隔
        local class = {
            { name='ANNOUNCE_DELAY_HOUR', type='integer[]'},
            { name='ANNOUNCE_DELAY_MINUTE', type='integer[]'},
            { name='PREORDER_GO_BEFORE_SEC', type='integer'},
            { name='PREORDER_GO_CHECK_DELAY', type='integer'},
            { name='PREORDER_GO_CHECK_CNT', type='integer'},
            { name='SIEGE_DURATION', type='integer'},
            { name='SIEGE_CODEDOWN', type='integer'},
            { name='SIEGE_MAIN_MOVE_PATCH', type='integer'},
            { name='SIEGE_SUB_MOVE_PATCH', type='integer'},
            { name='SIEGE_MOVE_PATCH_DELAY', type='integer'},
            { name='UISiegeAssembleColor', type='color'},
            { name='UISiegeAttackingColor', type='color'},
            { name='UISiegeAssembleIcon', type='string'},
            { name='UISiegeAttackingIcon', type='string'},
            { name='UISiegeHudAssembleBg', type='string'},
            { name='UISiegeHudAttackingBg', type='string'},
            { name='UISiegeHudAssembleTextColor', type='color'},
            { name='UISiegeHudAttackingTextColor', type='color'},
            { name='BarrackTempArmyStartTime', type='number'},
            { name='BarrackTempArmyDuration', type='number'},
            { name='BarrackTempArmyInterval1', type='number'},
            { name='BarrackTempArmyInterval2', type='number'},
            { name='BarrackTempArmyCount', type='integer'},
            { name='BarrackTempArmySpeed', type='number'},
            { name='RankTopN', type='integer'},
            { name='BREAK_DURA_COST_TIME', type='integer'},
            { name='BREAK_DURA_BASE_SIEGE', type='integer'},
            { name='CLEAR_INSIDER_REPEAT_COUNT', type='integer'},
            { name='CLEAR_INSIDER_REPEAT_INTERNAL', type='integer'},
        }
        beans['siege.TableSiegeConst'] = class
    end
    do
    ---@class slg.TableBuilding 
     ---@field public id integer @类型
     ---@field public sub_id integer @子id
     ---@field public map_element_id integer @沙盘元素表id
     ---@field public name string @显示名
     ---@field public build_panel_desc string @建造界面描述
     ---@field public build_select_desc string @选择建筑界面描述
     ---@field public view_lod integer @视图显示规则
     ---@field public sprite string @图片资源路径
     ---@field public icon_sprite string @icon资源路径
     ---@field public obstacle integer @阻挡类型
     ---@field public prefab_path string
     ---@field public direct_build boolean @可直接建造
     ---@field public upgrade_next integer @升级下一级
     ---@field public build_cost_res table<integer,integer> @建造升级到当前消耗货币
     ---@field public ally_build_cost_res table<integer,table<integer,integer>> @建造升级到当前消耗联盟货币（城池等级_消耗货币Map）
     ---@field public auto_build boolean @自动开始升级
     ---@field public build_cost_time integer @升级到当前耗时
     ---@field public dismantle_cost_time integer @拆除所需时间
     ---@field public skip_build_cost_res table<integer,integer> @跳过升级到当前消耗资源
     ---@field public is_obstacle boolean @是否为阻挡
     ---@field public ally_build boolean @联盟建筑
     ---@field public valid_grid integer[] @限制在特定地块上建造
     ---@field public dura_zero_destroy boolean @是否耐久0被销毁
     ---@field public dura_zero_ally_occupy boolean @耐久0被联盟占领
     ---@field public valid_alias boolean @能否起别名
     ---@field public is_foundation boolean @是地基
     ---@field public prosperity integer @增加繁荣度
     ---@field public is_outpost boolean @据点（可被攻城）
     ---@field public is_defender_host boolean @可被驻守（作为建筑）
     ---@field public max_deploy integer @调动数量上限
     ---@field public is_deploy_host boolean @可被调动
     ---@field public is_siege_host boolean @可被集结调动
     ---@field public durability integer @基础耐久
     ---@field public dura_recover integer @耐久恢复/分
     ---@field public dura_defender integer[] @城防守军
     ---@field public sys_defender integer[] @驻城部队
     ---@field public sys_defender_num integer @驻城部队数量
     ---@field public barracks_defender_list table<integer,integer[]> @兵营驻城部队配置
     ---@field public barracks_defender integer @单个兵营提供驻城守军数量
     ---@field public is_vehicle boolean @能否被操控
        local class = {
            { name='id', type='integer'},
            { name='sub_id', type='integer'},
            { name='map_element_id', type='integer'},
            { name='name', type='string'},
            { name='build_panel_desc', type='string'},
            { name='build_select_desc', type='string'},
            { name='view_lod', type='integer'},
            { name='sprite', type='string'},
            { name='icon_sprite', type='string'},
            { name='obstacle', type='integer'},
            { name='prefab_path', type='string'},
            { name='direct_build', type='boolean'},
            { name='upgrade_next', type='integer'},
            { name='build_cost_res', type='table<integer,integer>'},
            { name='ally_build_cost_res', type='table<integer,table<integer,integer>>'},
            { name='auto_build', type='boolean'},
            { name='build_cost_time', type='integer'},
            { name='dismantle_cost_time', type='integer'},
            { name='skip_build_cost_res', type='table<integer,integer>'},
            { name='is_obstacle', type='boolean'},
            { name='ally_build', type='boolean'},
            { name='valid_grid', type='integer[]'},
            { name='dura_zero_destroy', type='boolean'},
            { name='dura_zero_ally_occupy', type='boolean'},
            { name='valid_alias', type='boolean'},
            { name='is_foundation', type='boolean'},
            { name='prosperity', type='integer'},
            { name='is_outpost', type='boolean'},
            { name='is_defender_host', type='boolean'},
            { name='max_deploy', type='integer'},
            { name='is_deploy_host', type='boolean'},
            { name='is_siege_host', type='boolean'},
            { name='durability', type='integer'},
            { name='dura_recover', type='integer'},
            { name='dura_defender', type='integer[]'},
            { name='sys_defender', type='integer[]'},
            { name='sys_defender_num', type='integer'},
            { name='barracks_defender_list', type='table<integer,integer[]>'},
            { name='barracks_defender', type='integer'},
            { name='is_vehicle', type='boolean'},
        }
        beans['slg.TableBuilding'] = class
    end
    do
    ---@class slg.TableBuildingConst 
     ---@field public PERSONAL_BUILDING_MAX table<integer,integer> @个人建筑上限
     ---@field public STONE_VEHICLE_ATTACK_RANGE integer @投石车攻击范围
     ---@field public STONE_VEHICLE_ATTACK_TIME integer @投石车攻击倒计时
     ---@field public STONE_VEHICLE_ATTACK_DAMAGE integer @投石车耐久伤害
     ---@field public STONE_VEHICLE_MAX_STONE_NUM integer @投石车石头上限
        local class = {
            { name='PERSONAL_BUILDING_MAX', type='table<integer,integer>'},
            { name='STONE_VEHICLE_ATTACK_RANGE', type='integer'},
            { name='STONE_VEHICLE_ATTACK_TIME', type='integer'},
            { name='STONE_VEHICLE_ATTACK_DAMAGE', type='integer'},
            { name='STONE_VEHICLE_MAX_STONE_NUM', type='integer'},
        }
        beans['slg.TableBuildingConst'] = class
    end
    do
    ---@class slg.TableHudViewLod 
     ---@field public id integer @id
     ---@field public view_lod integer @视图显示规则
        local class = {
            { name='id', type='integer'},
            { name='view_lod', type='integer'},
        }
        beans['slg.TableHudViewLod'] = class
    end
    do
    ---@class slg.TableLandColor 
     ---@field public id integer @id
     ---@field public color_txt color @颜色值
     ---@field public color_plane color @颜色值
        local class = {
            { name='id', type='integer'},
            { name='color_txt', type='color'},
            { name='color_plane', type='color'},
        }
        beans['slg.TableLandColor'] = class
    end
    do
    ---@class slg.TableMainCityDistScore 
     ---@field public id integer @id
     ---@field public score integer @积分
     ---@field public max_distance integer @最大影响距离
        local class = {
            { name='id', type='integer'},
            { name='score', type='integer'},
            { name='max_distance', type='integer'},
        }
        beans['slg.TableMainCityDistScore'] = class
    end
    do
    ---@class slg.TableMainCityRandomConfig 
     ---@field public MainCityRadius integer @主城半径（除中心格子外的半径）
     ---@field public MaxScore integer @地块最大、初始积分
     ---@field public ResScanRadius integer @资源地扫描半径
     ---@field public ScoreLvRange integer[] @积分等级，每级范围[start, end)
        local class = {
            { name='MainCityRadius', type='integer'},
            { name='MaxScore', type='integer'},
            { name='ResScanRadius', type='integer'},
            { name='ScoreLvRange', type='integer[]'},
        }
        beans['slg.TableMainCityRandomConfig'] = class
    end
    do
    ---@class slg.TableMainCityResScore 
     ---@field public id integer @资源等级
     ---@field public score integer @积分
     ---@field public max_count integer @最大影响数量
        local class = {
            { name='id', type='integer'},
            { name='score', type='integer'},
            { name='max_count', type='integer'},
        }
        beans['slg.TableMainCityResScore'] = class
    end
    do
    ---@class slg.TableMapElement 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public name_format string @名称(格式)
     ---@field public type integer @类型
     ---@field public level integer @等级
     ---@field public description string @描述
     ---@field public x_coords integer[] @占地坐标(x)
     ---@field public y_coords integer[] @占地坐标(y)
     ---@field public collect_children boolean @是否收集子节点
     ---@field public obstacle integer @阻挡类型
     ---@field public maincity_obstacle boolean @禁止建造玩家主城
     ---@field public prefab_path string
     ---@field public scale vector3 @缩放
     ---@field public color string @编辑器用
     ---@field public text_color string @编辑器用
     ---@field public tile_text string @编辑器用
     ---@field public occupy_time integer @占领时间（秒）
     ---@field public land_team integer @土地阵容随机表
     ---@field public production integer @产出货币类型
     ---@field public productivity integer @x/小时
     ---@field public prosperity integer @繁荣度
     ---@field public army_type integer @兵营对应的兵种
     ---@field public move_rt_check boolean @移动实时检测关系
     ---@field public view_lod integer @视图显示规则
     ---@field public use_prefab_mode boolean @是否使用prefab模式
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='name_format', type='string'},
            { name='type', type='integer'},
            { name='level', type='integer'},
            { name='description', type='string'},
            { name='x_coords', type='integer[]'},
            { name='y_coords', type='integer[]'},
            { name='collect_children', type='boolean'},
            { name='obstacle', type='integer'},
            { name='maincity_obstacle', type='boolean'},
            { name='prefab_path', type='string'},
            { name='scale', type='vector3'},
            { name='color', type='string'},
            { name='text_color', type='string'},
            { name='tile_text', type='string'},
            { name='occupy_time', type='integer'},
            { name='land_team', type='integer'},
            { name='production', type='integer'},
            { name='productivity', type='integer'},
            { name='prosperity', type='integer'},
            { name='army_type', type='integer'},
            { name='move_rt_check', type='boolean'},
            { name='view_lod', type='integer'},
            { name='use_prefab_mode', type='boolean'},
        }
        beans['slg.TableMapElement'] = class
    end
    do
    ---@class slg.TableMapElementInstance 
     ---@field public level_id integer @地图id
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public gis_id integer @地理id
     ---@field public element_id integer @类型id
     ---@field public type integer @类型【废弃】
     ---@field public description string @描述
     ---@field public prefab_path string @如有，会覆盖元素表里的默认模型
     ---@field public sprite_path string @图片路径
        local class = {
            { name='level_id', type='integer'},
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='gis_id', type='integer'},
            { name='element_id', type='integer'},
            { name='type', type='integer'},
            { name='description', type='string'},
            { name='prefab_path', type='string'},
            { name='sprite_path', type='string'},
        }
        beans['slg.TableMapElementInstance'] = class
    end
    do
    ---@class slg.TableMapGIS 
     ---@field public level_id integer @关卡id
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public parent_id integer @parent_id
     ---@field public administrative_class integer @行政划分
     ---@field public description string @描述
     ---@field public color string @编辑器用
     ---@field public birthplace boolean @出生州标记
     ---@field public old_id integer @映射之前的序号
        local class = {
            { name='level_id', type='integer'},
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='parent_id', type='integer'},
            { name='administrative_class', type='integer'},
            { name='description', type='string'},
            { name='color', type='string'},
            { name='birthplace', type='boolean'},
            { name='old_id', type='integer'},
        }
        beans['slg.TableMapGIS'] = class
    end
    do
    ---@class slg.TableMapViewLod 
     ---@field public id integer @id
     ---@field public name string @名称
     ---@field public LOD0 integer @3d模型视图LOD
     ---@field public icon1 string @小icon
     ---@field public is_icon1_auto boolean @锁定大小
     ---@field public LOD1 integer
     ---@field public use_lod2 boolean @是否强制启用第二套icon
     ---@field public icon2 string
     ---@field public is_icon2_auto boolean @锁定大小
     ---@field public LOD2 integer
        local class = {
            { name='id', type='integer'},
            { name='name', type='string'},
            { name='LOD0', type='integer'},
            { name='icon1', type='string'},
            { name='is_icon1_auto', type='boolean'},
            { name='LOD1', type='integer'},
            { name='use_lod2', type='boolean'},
            { name='icon2', type='string'},
            { name='is_icon2_auto', type='boolean'},
            { name='LOD2', type='integer'},
        }
        beans['slg.TableMapViewLod'] = class
    end
    do
    ---@class slg.TablePresetBuilding 
     ---@field public id integer
     ---@field public level integer
     ---@field public ally_resources_isShow boolean
     ---@field public ally_resources_name string
     ---@field public ally_resources integer
     ---@field public resident_resources_isShow boolean
     ---@field public resident_resources_name string
     ---@field public resident_resources integer
     ---@field public resident_count_isShow boolean
     ---@field public resident_count_name string
     ---@field public resident_count integer
     ---@field public deploy_count_isShow boolean
     ---@field public ally_additional_member_isShow boolean
     ---@field public ally_additional_member_name string
     ---@field public ally_additional_member integer
     ---@field public ambitions_points_isShow boolean
     ---@field public ambitions_points_name string
     ---@field public ambitions_points integer
     ---@field public first_occupy_rankList_isShow boolean
     ---@field public first_occupy_rewards_isShow boolean
     ---@field public first_occupy_rewards_name string
     ---@field public first_occupy_rewards common.RewardCoinItem @首占奖励
     ---@field public first_occupy_normal_reward common.RewardCoinItem @首占非参与奖励
     ---@field public sprite string
        local class = {
            { name='id', type='integer'},
            { name='level', type='integer'},
            { name='ally_resources_isShow', type='boolean'},
            { name='ally_resources_name', type='string'},
            { name='ally_resources', type='integer'},
            { name='resident_resources_isShow', type='boolean'},
            { name='resident_resources_name', type='string'},
            { name='resident_resources', type='integer'},
            { name='resident_count_isShow', type='boolean'},
            { name='resident_count_name', type='string'},
            { name='resident_count', type='integer'},
            { name='deploy_count_isShow', type='boolean'},
            { name='ally_additional_member_isShow', type='boolean'},
            { name='ally_additional_member_name', type='string'},
            { name='ally_additional_member', type='integer'},
            { name='ambitions_points_isShow', type='boolean'},
            { name='ambitions_points_name', type='string'},
            { name='ambitions_points', type='integer'},
            { name='first_occupy_rankList_isShow', type='boolean'},
            { name='first_occupy_rewards_isShow', type='boolean'},
            { name='first_occupy_rewards_name', type='string'},
            { name='first_occupy_rewards', type='common.RewardCoinItem'},
            { name='first_occupy_normal_reward', type='common.RewardCoinItem'},
            { name='sprite', type='string'},
        }
        beans['slg.TablePresetBuilding'] = class
    end
    do
    ---@class slg.TableStaminaConst 
     ---@field public REDUCE_BY_SOLDIER_NUM common.IntIntPair[] @受敌军兵力影响减少体力消耗
     ---@field public PVP_ONCE_COST_MAX integer @单次出征最大PVP体力消耗
     ---@field public GENERAL_COLOR battle.STAMINA_COLOR[] @通用体力显示颜色
     ---@field public ARMY_SELECT_COLOR battle.STAMINA_COLOR[] @部队选择界面显示颜色
        local class = {
            { name='REDUCE_BY_SOLDIER_NUM', type='common.IntIntPair[]'},
            { name='PVP_ONCE_COST_MAX', type='integer'},
            { name='GENERAL_COLOR', type='battle.STAMINA_COLOR[]'},
            { name='ARMY_SELECT_COLOR', type='battle.STAMINA_COLOR[]'},
        }
        beans['slg.TableStaminaConst'] = class
    end
    do
    ---@class slg.TableStaminaCost 
     ---@field public id integer
     ---@field public rule battle.STAMINA_COST[] @扣除方部队等级大于等级
        local class = {
            { name='id', type='integer'},
            { name='rule', type='battle.STAMINA_COST[]'},
        }
        beans['slg.TableStaminaCost'] = class
    end
    do
    ---@class slg.TableTypeElement 
     ---@field public type integer @type
     ---@field public name string @名称
     ---@field public is_tile boolean @是否生成tile用作编辑
     ---@field public has_instance boolean @是否有实例
     ---@field public typeSortName string @收藏的分类名
     ---@field public color string @颜色
     ---@field public sprite_path string @贴图
        local class = {
            { name='type', type='integer'},
            { name='name', type='string'},
            { name='is_tile', type='boolean'},
            { name='has_instance', type='boolean'},
            { name='typeSortName', type='string'},
            { name='color', type='string'},
            { name='sprite_path', type='string'},
        }
        beans['slg.TableTypeElement'] = class
    end
    do
    ---@class slg.TableTypeLevelElement 
     ---@field public type integer @类型
     ---@field public level integer @等级
     ---@field public allyBuildingTypeLevel integer @联盟建筑类型
        local class = {
            { name='type', type='integer'},
            { name='level', type='integer'},
            { name='allyBuildingTypeLevel', type='integer'},
        }
        beans['slg.TableTypeLevelElement'] = class
    end
    do
    ---@class strategy.BaseStrategy  @策牌功能
        local class = {
        }
        beans['strategy.BaseStrategy'] = class
    end
    do
    ---@class strategy.StrategyJiShenJiDian :strategy.BaseStrategy 
     ---@field public addProducePerMember number
     ---@field public memberThresholds integer
     ---@field public thresholdsExAddProduce integer
     ---@field public singTime integer
     ---@field public radius integer
        local class = {
            { name='addProducePerMember', type='number'},
            { name='memberThresholds', type='integer'},
            { name='thresholdsExAddProduce', type='integer'},
            { name='singTime', type='integer'},
            { name='radius', type='integer'},
        }
        beans['strategy.StrategyJiShenJiDian'] = class
    end
    do
    ---@class strategy.StrategyKaiKen :strategy.BaseStrategy 
     ---@field public singTime integer
        local class = {
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyKaiKen'] = class
    end
    do
    ---@class strategy.StrategySha :strategy.BaseStrategy 
     ---@field public worldBuff integer
     ---@field public worldBuffTime integer
     ---@field public battleBuff integer
     ---@field public singTime integer
        local class = {
            { name='worldBuff', type='integer'},
            { name='worldBuffTime', type='integer'},
            { name='battleBuff', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategySha'] = class
    end
    do
    ---@class strategy.StrategyShan :strategy.BaseStrategy 
     ---@field public worldBuff integer
     ---@field public worldBuffTime integer
     ---@field public battleBuff integer
     ---@field public singTime integer
        local class = {
            { name='worldBuff', type='integer'},
            { name='worldBuffTime', type='integer'},
            { name='battleBuff', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyShan'] = class
    end
    do
    ---@class strategy.StrategyWuGuFengDeng :strategy.BaseStrategy 
     ---@field public gridRange integer
     ---@field public singTime integer
        local class = {
            { name='gridRange', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyWuGuFengDeng'] = class
    end
    do
    ---@class strategy.StrategyWuZhongShengYou :strategy.BaseStrategy 
     ---@field public produceTime integer
     ---@field public singTime integer
        local class = {
            { name='produceTime', type='integer'},
            { name='singTime', type='integer'},
        }
        beans['strategy.StrategyWuZhongShengYou'] = class
    end
    do
    ---@class strategy.SingView 
     ---@field public preb_path_screen_effect string
     ---@field public preb_path_ground_effect integer
     ---@field public preb_path_VXGoId_effect integer
     ---@field public preb_path_self_VXGoId_effect integer
        local class = {
            { name='preb_path_screen_effect', type='string'},
            { name='preb_path_ground_effect', type='integer'},
            { name='preb_path_VXGoId_effect', type='integer'},
            { name='preb_path_self_VXGoId_effect', type='integer'},
        }
        beans['strategy.SingView'] = class
    end
    do
    ---@class Talent.BaseTalent  @天赋树小天赋节点
        local class = {
        }
        beans['Talent.BaseTalent'] = class
    end
    do
    ---@class Talent.Empty :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.Empty'] = class
    end
    do
    ---@class Talent.FengChan :Talent.BaseTalent 
     ---@field public addResProducePerHour integer
        local class = {
            { name='addResProducePerHour', type='integer'},
        }
        beans['Talent.FengChan'] = class
    end
    do
    ---@class Talent.JiangZuo :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.JiangZuo'] = class
    end
    do
    ---@class Talent.JiangZuoGuang :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.JiangZuoGuang'] = class
    end
    do
    ---@class Talent.JieCe :Talent.BaseTalent 
     ---@field public saveStrategy integer
        local class = {
            { name='saveStrategy', type='integer'},
        }
        beans['Talent.JieCe'] = class
    end
    do
    ---@class Talent.JiKui :Talent.BaseTalent 
     ---@field public resAdd integer
        local class = {
            { name='resAdd', type='integer'},
        }
        beans['Talent.JiKui'] = class
    end
    do
    ---@class Talent.JiWu :Talent.BaseTalent 
     ---@field public occupyGetRes integer
     ---@field public occupyGetResLimit integer
        local class = {
            { name='occupyGetRes', type='integer'},
            { name='occupyGetResLimit', type='integer'},
        }
        beans['Talent.JiWu'] = class
    end
    do
    ---@class Talent.MiaoDe :Talent.BaseTalent 
     ---@field public exResRatio integer
        local class = {
            { name='exResRatio', type='integer'},
        }
        beans['Talent.MiaoDe'] = class
    end
    do
    ---@class Talent.PiKuang :Talent.BaseTalent 
        local class = {
        }
        beans['Talent.PiKuang'] = class
    end
    do
    ---@class Talent.PoXian :Talent.BaseTalent 
     ---@field public landLevelLimitTo integer
        local class = {
            { name='landLevelLimitTo', type='integer'},
        }
        beans['Talent.PoXian'] = class
    end
    do
    ---@class Talent.SuiRen :Talent.BaseTalent 
     ---@field public foodRroduceRatioAdd integer
        local class = {
            { name='foodRroduceRatioAdd', type='integer'},
        }
        beans['Talent.SuiRen'] = class
    end
    do
    ---@class Talent.TuoJiang :Talent.BaseTalent 
     ---@field public radiusAdd integer
     ---@field public dailyCardAdd integer
        local class = {
            { name='radiusAdd', type='integer'},
            { name='dailyCardAdd', type='integer'},
        }
        beans['Talent.TuoJiang'] = class
    end
    do
    ---@class Talent.TuoTu :Talent.BaseTalent 
     ---@field public landLimitAdd integer
        local class = {
            { name='landLimitAdd', type='integer'},
        }
        beans['Talent.TuoTu'] = class
    end
    do
    ---@class Talent.WoYe :Talent.BaseTalent 
     ---@field public produceExRatio integer
     ---@field public duration integer
        local class = {
            { name='produceExRatio', type='integer'},
            { name='duration', type='integer'},
        }
        beans['Talent.WoYe'] = class
    end
    do
    ---@class Talent.XuShi :Talent.BaseTalent 
     ---@field public strategyRecoverAdd integer
        local class = {
            { name='strategyRecoverAdd', type='integer'},
        }
        beans['Talent.XuShi'] = class
    end
    do
    ---@class Talent.XuShiYing :Talent.BaseTalent 
     ---@field public strategyLimitAdd integer
        local class = {
            { name='strategyLimitAdd', type='integer'},
        }
        beans['Talent.XuShiYing'] = class
    end
    do
    ---@class task._preCondition 
        local class = {
        }
        beans['task._preCondition'] = class
    end
    do
    ---@class task._armyCondition :task._preCondition 
        local class = {
        }
        beans['task._armyCondition'] = class
    end
    do
    ---@class task._buildingCondition :task._preCondition 
     ---@field public buildingData table<integer,integer> @建筑id和所需等级
        local class = {
            { name='buildingData', type='table<integer,integer>'},
        }
        beans['task._buildingCondition'] = class
    end
    do
    ---@class task._emptyCondition :task._preCondition 
        local class = {
        }
        beans['task._emptyCondition'] = class
    end
    do
    ---@class task.collectFunc  @任务进度搜集方法
        local class = {
        }
        beans['task.collectFunc'] = class
    end
    do
    ---@class task._taskGetAllResProduceInHour :task.collectFunc 
        local class = {
        }
        beans['task._taskGetAllResProduceInHour'] = class
    end
    do
    ---@class task._taskGetArmyMaxEquipTactic :task.collectFunc 
        local class = {
        }
        beans['task._taskGetArmyMaxEquipTactic'] = class
    end
    do
    ---@class task._taskGetBWTacticNumByLevel :task.collectFunc 
        local class = {
        }
        beans['task._taskGetBWTacticNumByLevel'] = class
    end
    do
    ---@class task._taskGetChapterComplete :task.collectFunc 
        local class = {
        }
        beans['task._taskGetChapterComplete'] = class
    end
    do
    ---@class task._taskGetDrillChallengeCount :task.collectFunc 
        local class = {
        }
        beans['task._taskGetDrillChallengeCount'] = class
    end
    do
    ---@class task._taskGetDrillLevel :task.collectFunc 
        local class = {
        }
        beans['task._taskGetDrillLevel'] = class
    end
    do
    ---@class task._taskGetFarmerWork :task.collectFunc 
        local class = {
        }
        beans['task._taskGetFarmerWork'] = class
    end
    do
    ---@class task._taskGetGachaDraw :task.collectFunc 
        local class = {
        }
        beans['task._taskGetGachaDraw'] = class
    end
    do
    ---@class task._taskGetHeroNumByLevel :task.collectFunc 
        local class = {
        }
        beans['task._taskGetHeroNumByLevel'] = class
    end
    do
    ---@class task._taskGetHomeBuildingLevel :task.collectFunc 
        local class = {
        }
        beans['task._taskGetHomeBuildingLevel'] = class
    end
    do
    ---@class task._taskGetHomeTechLevel :task.collectFunc 
        local class = {
        }
        beans['task._taskGetHomeTechLevel'] = class
    end
    do
    ---@class task._taskGetMaxArmySoldierNum :task.collectFunc 
        local class = {
        }
        beans['task._taskGetMaxArmySoldierNum'] = class
    end
    do
    ---@class task._taskGetOccupyLand :task.collectFunc 
        local class = {
        }
        beans['task._taskGetOccupyLand'] = class
    end
    do
    ---@class task._taskGetProsperity :task.collectFunc 
        local class = {
        }
        beans['task._taskGetProsperity'] = class
    end
    do
    ---@class task._taskGetTacticNum :task.collectFunc 
        local class = {
        }
        beans['task._taskGetTacticNum'] = class
    end
    do
    ---@class vector2 
     ---@field public x number
     ---@field public y number
        local class = {
            { name='x', type='number'},
            { name='y', type='number'},
        }
        beans['vector2'] = class
    end
    do
    ---@class vector3 
     ---@field public x number
     ---@field public y number
     ---@field public z number
        local class = {
            { name='x', type='number'},
            { name='y', type='number'},
            { name='z', type='number'},
        }
        beans['vector3'] = class
    end
    do
    ---@class vector4 
     ---@field public x number
     ---@field public y number
     ---@field public z number
     ---@field public w number
        local class = {
            { name='x', type='number'},
            { name='y', type='number'},
            { name='z', type='number'},
            { name='w', type='number'},
        }
        beans['vector4'] = class
    end

local tables =
{
    { name='TbTags', file='common_tbtags', mode='map', index='name', value_type='common.TableTags' },
    { name='TbBattleTactic', file='battle_tbbattletactic', mode='map', index='id', value_type='battle.TableBattleTactic' },
    { name='TbTacticConst', file='battle_tbtacticconst', mode='one', value_type='battle.TableTacticConst'},
    { name='TbBattleEffectSound', file='battle_tbbattleeffectsound', mode='map', index='id', value_type='battle.TableBattleEffectSound' },
    { name='TbBattleBuff', file='battle_tbbattlebuff', mode='map', index='id', value_type='battle.TableBattleBuff' },
    { name='TbBattleBuffGroup', file='battle_tbbattlebuffgroup', mode='map', index='id', value_type='battle.TableBattleBuffGroup' },
    { name='TbBattleBuffValid', file='battle_tbbattlebuffvalid', mode='map', index='battle_type', value_type='battle.TableBattleBuffValid' },
    { name='TbBattleFormulas', file='battle_tbbattleformulas', mode='one', value_type='battle.TableBattleFormulas'},
    { name='TbBattleAttribute', file='battle_tbbattleattribute', mode='list', index='', value_type='battle.TableBattleAttribute' },
    { name='TbBattleFormation', file='battle_tbbattleformation', mode='list', index='army_type+combat_status+main_status', value_type='battle.TableBattleFormation' },
    { name='TbTemplateHero', file='battle_tbtemplatehero', mode='map', index='id', value_type='battle.TableTemplateHero' },
    { name='TbHeroDynasty', file='battle_tbherodynasty', mode='map', index='id', value_type='battle.TableHeroDynasty' },
    { name='TbHeroArmyType', file='battle_tbheroarmytype', mode='map', index='id', value_type='battle.TableHeroArmyType' },
    { name='TbHeroConst', file='battle_tbheroconst', mode='one', value_type='battle.TableHeroConst'},
    { name='TbHeroExp', file='common_tbheroexp', mode='map', index='level', value_type='common.TableHeroExp' },
    { name='TbNPCTeam', file='battle_tbnpcteam', mode='map', index='Id', value_type='battle.TableNPCTeam' },
    { name='TbNPCPool', file='battle_tbnpcpool', mode='map', index='Id', value_type='battle.TableNPCPool' },
    { name='TbNPCDifficulty', file='battle_tbnpcdifficulty', mode='map', index='level', value_type='battle.TableNPCDifficulty' },
    { name='TbBattleRecordText', file='battle_tbbattlerecordtext', mode='map', index='detailRecordType', value_type='battle.TableBattleRecordText' },
    { name='TbBattleRecordCrowdControl', file='battle_tbbattlerecordcrowdcontrol', mode='map', index='crowdControlType', value_type='battle.TableBattleRecordCrowdControl' },
    { name='TbArmyTypeQualification', file='battle_tbarmytypequalification', mode='map', index='id', value_type='battle.TableArmyTypeQualification' },
    { name='TbEventPriority', file='battle_tbeventpriority', mode='list', index='battle_event+name', value_type='battle.TableEventPriority' },
    { name='TbFormulaIndex', file='battle_tbformulaindex', mode='list', index='name+key', value_type='battle.TableFormulaIndex' },
    { name='TbFormulaIndexName', file='battle_tbformulaindexname', mode='one', value_type='battle.TableFormulaIndexName'},
    { name='TbText', file='common_tbtext', mode='map', index='id', value_type='common.TableText' },
    { name='TbCurrency', file='common_tbcurrency', mode='map', index='id', value_type='common.TableCurrency' },
    { name='TbRetCode', file='common_tbretcode', mode='map', index='id', value_type='common.TableRetCode' },
    { name='TbSwitch', file='common_tbswitch', mode='map', index='name', value_type='common.TableSwitch' },
    { name='TbSchedule', file='common_tbschedule', mode='map', index='ScheduleId', value_type='common.TableSchedule' },
    { name='TbEffect', file='common_tbeffect', mode='map', index='id', value_type='common.TableEffect' },
    { name='TbHeroEffect', file='common_tbheroeffect', mode='list', index='id+hero_id', value_type='common.TableHeroEffect' },
    { name='TbCommonConfig', file='common_tbcommonconfig', mode='one', value_type='common.TableCommonConfig'},
    { name='TbBattleConfig', file='common_tbbattleconfig', mode='one', value_type='common.TableBattleConfig'},
    { name='TbSlgConfig', file='common_tbslgconfig', mode='one', value_type='common.TableSlgConfig'},
    { name='TbActorType', file='common_tbactortype', mode='map', index='id', value_type='common.TableActorType' },
    { name='TbActorMove', file='common_tbactormove', mode='map', index='id', value_type='common.TableActorMove' },
    { name='TbJudgeBehaviorTo', file='common_tbjudgebehaviorto', mode='map', index='id', value_type='common.TableJudgeBehaviorTo' },
    { name='TbValidBehavior', file='common_tbvalidbehavior', mode='map', index='status', value_type='common.TableValidBehavior' },
    { name='TbBehaviorConfig', file='common_tbbehaviorconfig', mode='map', index='id', value_type='common.TableBahaviorConfig' },
    { name='TbActorContainer', file='common_tbactorcontainer', mode='map', index='id', value_type='common.TableActorContainer' },
    { name='TbActorContainerConfig', file='common_tbactorcontainerconfig', mode='map', index='id', value_type='common.TableActorContainerConfig' },
    { name='TbSingleBehaviorConfig', file='common_tbsinglebehaviorconfig', mode='map', index='behavior_type', value_type='common.TableSingleBehaviorConfig' },
    { name='TbTermEntry', file='common_tbtermentry', mode='map', index='id', value_type='common.TableTermEntry' },
    { name='TbTermEntry1', file='common_tbtermentry1', mode='map', index='name', value_type='common.TableTermEntry1' },
    { name='TbHeroAttrType', file='common_tbheroattrtype', mode='map', index='id', value_type='common.TableHeroAttrType' },
    { name='TbHeroTitleType', file='common_tbherotitletype', mode='map', index='id', value_type='common.TableHeroTitleType' },
    { name='TbHeroRoleType', file='common_tbheroroletype', mode='map', index='id', value_type='common.TableHeroRoleType' },
    { name='TbHeroSuitRank', file='common_tbherosuitrank', mode='map', index='id', value_type='common.TableHeroSuitRank' },
    { name='TbRarityRank', file='common_tbrarityrank', mode='map', index='id', value_type='common.TableRarityRank' },
    { name='TbWorldBuff', file='common_tbworldbuff', mode='map', index='id', value_type='common.TableWorldBuff' },
    { name='TbLandBuff', file='common_tblandbuff', mode='map', index='id', value_type='common.TableLandBuff' },
    { name='TbLotteryPack', file='common_tblotterypack', mode='map', index='id', value_type='common.TableLotteryPack' },
    { name='TbMailTemplate', file='common_tbmailtemplate', mode='map', index='id', value_type='common.TableMailTemplate' },
    { name='TbMailConst', file='common_tbmailconst', mode='one', value_type='common.TableMailConst'},
    { name='TbAnimLength', file='common_tbanimlength', mode='one', value_type='common.TableAnimLength'},
    { name='TbHomeBuildingType', file='common_tbhomebuildingtype', mode='map', index='id', value_type='common.TableHomeBuildingType' },
    { name='TbHomeBuilding', file='common_tbhomebuilding', mode='list', index='id+level', value_type='common.TableHomeBuilding' },
    { name='TbHomeBuildingConst', file='common_tbhomebuildingconst', mode='one', value_type='common.TableHomeBuildingConst'},
    { name='TbHomeBuildFuncBtn', file='common_tbhomebuildfuncbtn', mode='map', index='id', value_type='common.TableHomeBuildFuncBtn' },
    { name='TbHomeTech', file='common_tbhometech', mode='map', index='id', value_type='common.TableHomeTech' },
    { name='TbTask', file='common_tbtask', mode='map', index='id', value_type='common.TableTask' },
    { name='TbTaskChapterInterlude', file='common_tbtaskchapterinterlude', mode='map', index='taskId', value_type='common.TableTaskChapterInterlude' },
    { name='TbTaskConst', file='common_tbtaskconst', mode='one', value_type='common.TableTaskConst'},
    { name='TbItem', file='common_tbitem', mode='map', index='id', value_type='common.TableItem' },
    { name='TbItemConst', file='common_tbitemconst', mode='one', value_type='common.TableItemConst'},
    { name='TbMaterial', file='common_tbmaterial', mode='map', index='id', value_type='common.TableMaterial' },
    { name='TbToken', file='common_tbtoken', mode='map', index='id', value_type='common.TableToken' },
    { name='TbEffectItem', file='common_tbeffectitem', mode='map', index='id', value_type='common.TableEffectItem' },
    { name='TbHeroItem', file='common_tbheroitem', mode='map', index='id', value_type='common.TableHeroItem' },
    { name='TbBattleTacticItem', file='common_tbbattletacticitem', mode='map', index='id', value_type='common.TableBattleTacticItem' },
    { name='TbIdSegment', file='common_tbidsegment', mode='map', index='id', value_type='common.TableIdSegment' },
    { name='TbRuleText', file='common_tbruletext', mode='map', index='id', value_type='common.TableRuleText' },
    { name='TbSLGRuleText', file='common_tbslgruletext', mode='map', index='id', value_type='common.TableSLGRuleText' },
    { name='TbLandConst', file='common_tblandconst', mode='one', value_type='common.TableLandConst'},
    { name='TbLandFavoriteName', file='common_tblandfavoritename', mode='map', index='id', value_type='common.TableLandFavoriteName' },
    { name='TbShopConst', file='common_tbshopconst', mode='one', value_type='common.TableShopConst'},
    { name='TbShop', file='common_tbshop', mode='map', index='id', value_type='common.TableShop' },
    { name='TbShopGoods', file='common_tbshopgoods', mode='map', index='id', value_type='common.TableShopGoods' },
    { name='TbCareerConst', file='common_tbcareerconst', mode='one', value_type='common.TableCareerConst'},
    { name='TbStrategy', file='common_tbstrategy', mode='map', index='id', value_type='common.TableStrategy' },
    { name='TbCareerTelent', file='common_tbcareertelent', mode='map', index='id', value_type='common.TableCareerTelent' },
    { name='TbCareerTree', file='common_tbcareertree', mode='map', index='id', value_type='common.TableCareerTree' },
    { name='TbEquipConst', file='common_tbequipconst', mode='one', value_type='common.TableEquipConst'},
    { name='TbEquip', file='common_tbequip', mode='map', index='id', value_type='common.TableEquip' },
    { name='TbHorse', file='common_tbhorse', mode='map', index='id', value_type='common.TableHorse' },
    { name='TbEquipEffect', file='common_tbequipeffect', mode='map', index='id', value_type='common.TableEquipEffect' },
    { name='TbEquipQualityRatio', file='common_tbequipqualityratio', mode='map', index='id', value_type='common.TableEquipQualityRatio' },
    { name='TbHeroMainIdentity', file='common_tbheromainidentity', mode='map', index='id', value_type='common.TableHeroMainIdentity' },
    { name='TbHeroSubIdentity', file='common_tbherosubidentity', mode='map', index='id', value_type='common.TableHeroSubIdentity' },
    { name='TbHeroIdentityAttribute', file='common_tbheroidentityattribute', mode='map', index='id', value_type='common.TableHeroIdentityAttribute' },
    { name='TbHeroIdentityConst', file='common_tbheroidentityconst', mode='one', value_type='common.TableHeroIdentityConst'},
    { name='TbRank', file='common_tbrank', mode='map', index='id', value_type='common.TableRank' },
    { name='TbRankConst', file='common_tbrankconst', mode='one', value_type='common.TableRankConst'},
    { name='TbSetting', file='common_tbsetting', mode='map', index='id', value_type='common.TableSetting' },
    { name='TbSettingRelation', file='common_tbsettingrelation', mode='map', index='id', value_type='common.TableSettingRelation' },
    { name='TbSettingGroup', file='common_tbsettinggroup', mode='map', index='id', value_type='common.TableSettingGroup' },
    { name='TbSettingConst', file='common_tbsettingconst', mode='one', value_type='common.TableSettingConst'},
    { name='TbMapElement', file='slg_tbmapelement', mode='map', index='id', value_type='slg.TableMapElement' },
    { name='TbTypeElement', file='slg_tbtypeelement', mode='map', index='type', value_type='slg.TableTypeElement' },
    { name='TbMapElementInstance', file='slg_tbmapelementinstance', mode='list', index='level_id+id', value_type='slg.TableMapElementInstance' },
    { name='TbTypeLevelElement', file='slg_tbtypelevelelement', mode='list', index='type+level', value_type='slg.TableTypeLevelElement' },
    { name='TbMapGIS', file='slg_tbmapgis', mode='list', index='level_id+id', value_type='slg.TableMapGIS' },
    { name='TbMapViewLod', file='slg_tbmapviewlod', mode='map', index='id', value_type='slg.TableMapViewLod' },
    { name='TBHudViewLod', file='slg_tbhudviewlod', mode='map', index='id', value_type='slg.TableHudViewLod' },
    { name='TbLandColor', file='slg_tblandcolor', mode='map', index='id', value_type='slg.TableLandColor' },
    { name='TbMainCityDistScore', file='slg_tbmaincitydistscore', mode='map', index='id', value_type='slg.TableMainCityDistScore' },
    { name='TbMainCityResScore', file='slg_tbmaincityresscore', mode='map', index='id', value_type='slg.TableMainCityResScore' },
    { name='TbMainCityRandomConfig', file='slg_tbmaincityrandomconfig', mode='one', value_type='slg.TableMainCityRandomConfig'},
    { name='TbBuilding', file='slg_tbbuilding', mode='list', index='id+sub_id', value_type='slg.TableBuilding' },
    { name='TbBuildingConst', file='slg_tbbuildingconst', mode='one', value_type='slg.TableBuildingConst'},
    { name='TbPresetBuilding', file='slg_tbpresetbuilding', mode='list', index='id+level', value_type='slg.TablePresetBuilding' },
    { name='TbStaminaCost', file='slg_tbstaminacost', mode='map', index='id', value_type='slg.TableStaminaCost' },
    { name='TbStaminaConst', file='slg_tbstaminaconst', mode='one', value_type='slg.TableStaminaConst'},
    { name='TbS1', file='season_tbs1', mode='map', index='id', value_type='season.TableS1' },
    { name='TbS1Const', file='season_tbs1const', mode='one', value_type='season.TableS1Const'},
    { name='TbS1CountList', file='season_tbs1countlist', mode='map', index='id', value_type='season.TableS1Count' },
    { name='TbS1Reward', file='season_tbs1reward', mode='map', index='id', value_type='season.TableS1Reward' },
    { name='TbS1Gameplay', file='season_tbs1gameplay', mode='map', index='id', value_type='season.TableS1Gameplay' },
    { name='TbS1GameplayInfo', file='season_tbs1gameplayinfo', mode='map', index='id', value_type='season.TableS1GameplayInfo' },
    { name='TbS1IdentityInfo', file='season_tbs1identityinfo', mode='map', index='identity', value_type='season.TableS1IdentityInfo' },
    { name='TbS1EmpireWords', file='season_tbs1empirewords', mode='list', index='', value_type='season.TableS1EmpireWords' },
    { name='TbS1CampInfo', file='season_tbs1campinfo', mode='map', index='identity', value_type='season.TableS1CampInfo' },
    { name='TbS1FullscreenToast', file='season_tbs1fullscreentoast', mode='map', index='id', value_type='season.TableS1FullscreenToast' },
    { name='TbConst', file='drill_tbconst', mode='one', value_type='drill.TableDrillConst'},
    { name='TbChapter', file='drill_tbchapter', mode='map', index='id', value_type='drill.TableDrillChapter' },
    { name='TbLevel', file='drill_tblevel', mode='map', index='id', value_type='drill.TableDrillLevel' },
    { name='TbMapLevel', file='map_tbmaplevel', mode='map', index='id', value_type='map.TableMapLevel' },
    { name='TbRedpoint', file='redpoint_tbredpoint', mode='map', index='id', value_type='redpoint.TableRedpoint' },
    { name='TbJump', file='jump_tbjump', mode='map', index='id', value_type='jump.TableJump' },
    { name='TbSiegeConst', file='siege_tbsiegeconst', mode='one', value_type='siege.TableSiegeConst'},
    { name='TbFuncOpen', file='funcopen_tbfuncopen', mode='map', index='id', value_type='funcopen.TableFuncOpen' },
    { name='TbFuncOpenTrigger', file='funcopen_tbfuncopentrigger', mode='map', index='id', value_type='funcopen.TableFuncOpenTrigger' },
    { name='TbGachaSummary', file='common_tbgachasummary', mode='map', index='id', value_type='common.TableGachaSummary' },
    { name='TbRandomItem', file='common_tbrandomitem', mode='list', index='pool_id+auto_id', value_type='common.TableRandomItem' },
    { name='TbRechargeJade', file='common_tbrechargejade', mode='map', index='id', value_type='common.TableRechargeJade' },
    { name='TbClanConst', file='common_tbclanconst', mode='one', value_type='common.TableClanConst'},
    { name='TbClanLevel', file='common_tbclanlevel', mode='map', index='level', value_type='common.TableClanLevel' },
    { name='TbNewbeeDialogue', file='common_tbnewbeedialogue', mode='map', index='id', value_type='common.TableNewbeeDialogue' },
    { name='TbNewbeeGuidePerson', file='common_tbnewbeeguideperson', mode='map', index='id', value_type='common.TableNewbeeGuidePerson' },
    { name='TbNewbeeConfig', file='common_tbnewbeeconfig', mode='one', value_type='common.TableNewbeeConfig'},
    { name='TbChatConst', file='common_tbchatconst', mode='one', value_type='common.TableChatConst'},
}

TableConst = { enums = enums, beans = beans, tables = tables }
DeclareAndSetGlobal("TableConst", TableConst)

