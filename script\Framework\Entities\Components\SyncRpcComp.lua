﻿xrequire("Framework.Async.Require")
---@type FlagsEnv
local Flags = xrequire(EZFPath .. ".Utils.Flags")
---@type ComponentBaseEnv
local ComponentBase = xrequire(EZFPath .. ".Entities.Components.ComponentBase")
---@type GlobalEntityMgrEnv
local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")


--region SyncRpcHolder

---处理一个rpc的协程封装，一个holder处理一个rpc，可复用
---@class SyncRpcHolder: AsyncExecutorHolder
SyncRpcHolder = DefineClass("SyncRpcHolder", Async.AsyncExecutorHolder)

---@param entity Entity | SyncRpcComp
function SyncRpcHolder:ctor(entity)
    self.entity = entity
end

---@param ar? AsyncResult
---@param ... any
---@return AsyncResult, ...
function SyncRpcHolder:handleResume(ar, ...)
    if self.ae:status() == "dead" then
        if not ar then
            self.entity:unwarpRpcResult(self.src, self.rpcType, self.cbid, self.rpcName, Flags.RPC_OK, ...)
        else
            self.entity:unwarpRpcResult(self.src, self.rpcType, self.cbid, self.rpcName, Flags.RPC_ERR_INVOKE, ...)
            error(string.format("run %s.%s error(%s), rpcerr(%s)", self.entity.__cname, self.rpcName, ar, Flags.RPC_ERR_INVOKE))
        end
        -- 回收
        self:removeAsyncExecutor()
        self:recycle()
    end
    return ar, ...
end

function SyncRpcHolder:recycle()
    self.entity.co2aeh[self.cokey] = nil
    self.rpcName = nil
    self.src = nil
    self.rpcType = nil
    self.cbid = nil
    self.cokey = nil
    table.insert(self.entity.aeHolders, self)
    self.entity:debugSyncRpcComp("recycle")
end

--endregion SyncRpcHolder


--region ISyncRpcComp

---同步rpc接口组件
---@class ISyncRpcComp: ComponentBase
ISyncRpcComp = DefineClass("ISyncRpcComp", ComponentBase.ComponentBase)
ISyncRpcComp.RpcErr = {
    ERROR = Flags.RPC_ERR_INVOKE,
    IDLE = Flags.RPC_OK,
}

---向目标Entity发rpc并等待回调，只能在协程中使用
---@param ae AsyncExecutor
---@param timeout number
---@param mb LuaMailbox
---@param method string
---@return RpcErr, ...
function ISyncRpcComp:syncServerRpc(ae, timeout, mb, method, ...)
    if Async.AsyncExecutor.isMainThread() then
        self:logError("call syncServerRpc in main thread, %s(%v)", method, {...})
        return Flags.RPC_ERR_MAINTHREAD
    end
    if self:migratable() then
        self:logError("call syncServerRpc on migratable entity, %s(%v)", method, {...})
        return Flags.RPC_ERR_MIGRATABLE
    end
    if not self:serverRpcCallback(EntCb("syncServerRpcReturn", ae.aeId), timeout, mb, method, ...) then
        -- 不应该出现，失败的情况应该在引擎层就抛异常了
        error(string.format("send syncServerRpc(%s) failed", method))
    end
    return self:unwarpSyncServerRpcReturn(ae:yield(nil, 0))
end

---向目标Service发rpc并等待回调，只能在协程中使用
---@param ae AsyncExecutor
---@param timeout number
---@param serviceName string
---@param method string
---@return RpcErr, ...
function ISyncRpcComp:syncServiceRpc(ae, timeout, serviceName, method, ...)
    local info = GlobalEntityMgr.instance:getGlobalEntityInfo(serviceName)
    if not info then
        self:logError("%s is not exist when calling %s(%v)", serviceName, method, {...})
        return Flags.RPC_ERR_TARGET_UNEXIST
    end
    if not info.ready then
        if not EZE.hasRpcFlagValue(serviceName, method, DefsConst.RPC_FLAG.INIT) then
            self:logError("%s is not ready when calling %s(%v)", serviceName, method, {...})
            return Flags.RPC_ERR_TARGET_UNEXIST
        end
    end
    return ISyncRpcComp.syncServerRpc(self, ae, timeout, info.mailbox, method, ...)
end

---@param aeId AeId
---@param rpcerr RpcErr
function ISyncRpcComp:syncServerRpcReturn(aeId, rpcerr, ...)
    if Async.AsyncExecutor.isMainThread() then
        local ae = AsyncExecutorMgr:get(aeId)
        if not ae then
            error(string.format("call syncServerRpcReturn(%s, %s) failed, miss AsyncExecutor", aeId, rpcerr))
        end
        ae:resume(nil, rpcerr, ...)
    else
        -- 不应该出现，回调只会在主协程调用
        error(string.format("call syncServerRpcReturn(%s, %s) failed", aeId, rpcerr))
    end
end

---@param ar AsyncResult
---@param rpcErr RpcErr
---@param ... any
---@return RpcErr, ...
function ISyncRpcComp:unwarpSyncServerRpcReturn(ar, rpcErr,  ...)
    return rpcErr or ISyncRpcComp.RpcErr[ar] or Flags.RPC_OK, ...
end

--endregion ISyncRpcComp


--region SyncRpcComp

---同步rpc组件，同时负责管理SyncRpcHolder
---@class SyncRpcComp: Entity, ISyncRpcComp
---@field syncRpcCls ComponentBase @定义同步rpc的组件，用来判定某个rpc是否应该在协程中运行
---@field aeHolders AsyncExecutorHolder[]
SyncRpcComp = DefineClass("SyncRpcComp", ISyncRpcComp)

---@param syncRpcCls ComponentBase
---@param aeMax int
function SyncRpcComp:enableSyncRpc(syncRpcCls, aeMax)
    self.syncRpcCls = syncRpcCls
    self.aeMax = aeMax

    self.aeHolders = {}
    self.aeNum = 0
    self:setRpcHook("hookSyncRpc")
end

---@param operation string
function SyncRpcComp:debugSyncRpcComp(operation)
    -- self:logDebug("%s.%s SyncRpcHolder(num: %s/%s, usable: %s)", self.__cname, operation, self.aeNum, self.aeMax, #self.aeHolders)
end

-- 进入到hook方法中，必须手动处理回调
---@param src EntityId
---@param rpcType int
---@param cbid int
---@param rpcName string
---@return boolean @rpc是否执行完毕且没有抛异常
function SyncRpcComp:hookSyncRpc(src, rpcType, cbid, rpcName, ...)
    local rpc = self.syncRpcCls[rpcName]
    if rpc then
        -- 在协程中执行rpc
        ---@type SyncRpcHolder
        local holder = nil
        if #self.aeHolders > 0 then
            holder = table.remove(self.aeHolders)
        elseif self.aeNum < self.aeMax then
            self.aeNum = self.aeNum + 1
            holder = SyncRpcHolder.new(self)
        else
            ErrorLog("out of SyncRpcHolder(%s/%s)", self.aeNum, self.aeMax)
            return
        end
        self:debugSyncRpcComp("alloc")
        holder.src = src
        holder.rpcType = rpcType
        holder.cbid = cbid
        holder.rpcName = rpcName
        holder:createAsyncExecutor(
            EZE.genUUID(),
            function(...)
                return nil, rpc(self, ...)
            end,
            function(ar, ...)
                return holder:handleResume(ar, ...)
            end)
        holder.cokey = tostring(holder.ae.co)
        self.co2aeh[holder.cokey] = holder
        holder:resume(nil, ...)
    else
        -- 直接执行rpc
        self:unwarpRpcResult(Flags.RPC_OK, src, rpcType, cbid, rpcName, Flags.RPC_OK, self[rpcName](self, ...))
    end
end

---@param dst EntityId
---@param rpcType int
---@param cbid int
---@param rpc string
---@param res boolean @执行完毕且没有抛异常
---@param rpcErr? RpcErr
---@param ... any
---@return boolean
function SyncRpcComp:unwarpRpcResult(dst, rpcType, cbid, rpc, rpcErr, ...)
    -- 手动返回rpc结果
    if cbid ~= 0 then
        self:serverRpcReturn(dst, rpcType, cbid, rpc, rpcErr, ...)
    end
end

---向目标Entity发rpc并等待回调，只能在协程中使用
---@param timeout number
---@param mb LuaMailbox
---@param method string
---@return RpcErr, ...
function SyncRpcComp:syncServerRpc(timeout, mb, method, ...)
    return ISyncRpcComp.syncServerRpc(self, self.co2aeh[tostring(coroutine.running())].ae, timeout, mb, method, ...)
end

---向目标Service发rpc并等待回调，只能在协程中使用
---@param timeout number
---@param serviceName string
---@param method string
---@return RpcErr, ...
function SyncRpcComp:syncServiceRpc(timeout, serviceName, method, ...)
    return ISyncRpcComp.syncServiceRpc(self, self.co2aeh[tostring(coroutine.running())].ae, timeout, serviceName, method, ...)
end

--endregion SyncRpcComp


-- for emmylua
---@class SyncRpcCompEnv
local SyncRpcComp = {
    SyncRpcHolder=SyncRpcHolder,
    ISyncRpcComp=ISyncRpcComp,
    SyncRpcComp=SyncRpcComp,
}
return SyncRpcComp
