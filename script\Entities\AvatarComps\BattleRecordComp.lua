﻿local DBUtils = xrequire(EZFPath .. ".Utils.DBUtils")
local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ServerTimeEnv = xrequire("Utils.ServerTime")

---@class (partial) Avatar
---@class BattleRecordComp: ComponentBase
BattleRecordComp = DefineClass("BattleRecordComp", ComponentBaseEnv.ComponentBase)

---@diagnostic disable-next-line: duplicate-set-field
function BattleRecordComp:ctor()
    self.collAbstract = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_abstract")
    self.collStatistics = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_statistics")
    self.collDetails = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_details")
    self.collDrillAbstract = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_drill_abstract")  -- 军演abstract

    self.battleAbstractsRedDotCounter = { outer = {}, inner = {} }  -- outer是大世界上的战报图标上面的红点数量, inner是战报UI的红点数量
end

function BattleRecordComp:getSelfGuidFromAbstract(abstract)
    if abstract.battleType == TableConst.enums.BattleType.PVP then
        for camp, team in pairs(abstract.teamAbstractDict) do
            if team.gid == self.props.gid or team.allyId == self.props.allyId or team.clanId == self.props.clanId then
                return abstract.guid[camp]
            end
        end

        return abstract.guid[TableConst.enums.Camp.A]
    else
        return abstract.guid[TableConst.enums.Camp.A]
    end
end

function BattleRecordComp:getSelfGuidFromAbstractFilter(filter)
    if filter.battleType == TableConst.enums.BattleType.PVP then
        for camp, gid in pairs(filter.playerList) do
            if gid == self.props.gid then
                return filter.guid[camp]
            end
        end

        for camp, allyId in pairs(filter.allyList) do
            if allyId == self.worldAvatar.props.allyId then
                return filter.guid[camp]
            end
        end

        for camp, clanId in pairs(filter.clanList) do
            if clanId == self.worldAvatar.props.clanId then
                return filter.guid[camp]
            end
        end

        return filter.guid[TableConst.enums.Camp.A]
    else
        return filter.guid[TableConst.enums.Camp.A]
    end
end

function BattleRecordComp:OnGetBattleRecord(abstract)
    local guid = self:getSelfGuidFromAbstract(abstract)
    local outerTimestamp = math.max(abstract.timestamp, self.battleAbstractsRedDotCounter.outer[guid] or 0)
    if outerTimestamp > self.props.battleAbstractsRedDot.globalOuterStamp then
        if not self.battleAbstractsRedDotCounter.outer[guid] then
            self.props.battleAbstractsRedDot.outerCount = self.props.battleAbstractsRedDot.outerCount + 1
        end
        self.battleAbstractsRedDotCounter.outer[guid] = outerTimestamp
    end

    local innerTimestamp = math.max(abstract.timestamp, self.battleAbstractsRedDotCounter.inner[guid] or 0)
    local innerReadStamp = math.max(self.props.battleAbstractsRedDot.globalStamp, self.props.battleAbstractsRedDot.guid2Stamp[guid] or 0)
    if innerTimestamp > innerReadStamp then
        if not self.battleAbstractsRedDotCounter.inner[guid] then
            self.props.battleAbstractsRedDot.innerCount = self.props.battleAbstractsRedDot.innerCount + 1
        end
        self.battleAbstractsRedDotCounter.inner[guid] = innerTimestamp
    end
end

function BattleRecordComp:OnGetDrillBattleRecord(abstract)
    for i = #self.props.drillBattleAbstracts, 1, -1 do
        if self:getSelfGuidFromAbstract(self.props.drillBattleAbstracts[i]) == self:getSelfGuidFromAbstract(abstract) then
            -- merge group
            local origAbstract = self.props.drillBattleAbstracts[i]
            self.props.drillBattleAbstracts:remove(i)
            if not origAbstract.groupedAbstractUids then
                origAbstract.groupedAbstractUids = {}
            end
            origAbstract.groupedAbstractUids:insert(1, origAbstract.firstAbstractUid)
            origAbstract.firstAbstractUid = abstract.uid
            origAbstract.firstAbstract = EZE.packProp(abstract, "BattleAbstract")
            self.props.drillBattleAbstracts:insert(origAbstract)

            return
        end
    end

    -- first abstract
    self.props.drillBattleAbstracts:insert({
                firstAbstractUid = abstract.uid,
                guid = abstract.guid,
                firstAbstract = EZE.packProp(abstract, "BattleAbstract")
            })
end

function BattleRecordComp:QueryDrillBattleAbstractList(filter, start, count)
    local res = {}
    local left = #self.props.drillBattleAbstracts - start + 1
    local right = math.max(left - count + 1, 1)
    if left < right then
        self:clientRpc("OnQueryBattleAbstractList", filter, start, count, res, 0)
        return
    end

    for i = left, right, -1 do
        table.insert(res, self.props.drillBattleAbstracts[i])
    end
    self:logInfo("QueryDrillBattleAbstractList. res: %v", res)
    self:clientRpc("OnQueryBattleAbstractList", filter, start, count, res, start + count)
end

function BattleRecordComp:QueryBattleAbstractList(filter, start, count)
    if count <= 0 then
        self:logError("QueryBattleAbstractList Fail. count: %s", count)
        self:clientRpc("OnQueryBattleAbstractList", filter, start, count, {}, 0)
        return
    end

    if start <= 0 then
        start = 1
    end

    if filter.battleType == TableConst.enums.BattleType.PVE_DRILL then
        self:QueryDrillBattleAbstractList(filter, start, count)
        return
    end

    local query = self:genBattleAbstractsQuery(filter)
    if not query.playerList and not query.allyList and not query.clanList then
        self:logError("QueryBattleAbstractList Fail. filter: %v", filter)
        self:clientRpc("OnQueryBattleAbstractList", filter, start, count, {}, 0)
    end

    self:logInfo("QueryBattleAbstractList. query: %v, filter: %v, start: %s, count: %s", query, filter, start, count)

    self.collAbstract:find(query, {}, { sort = { timestamp = -1 } }, function(succ, docs)
        if not succ then
            self:logError("QueryBattleAbstractList Fail. Find DB Error. filter: %v", filter)
            self:clientRpc("OnQueryBattleAbstractList", filter, start, count, {}, 0)
            return
        end

        if #docs == 0 then
            self:logInfo("QueryBattleAbstractList. No Abstracts. filter: %v", filter)
            self:clientRpc("OnQueryBattleAbstractList", filter, start, count, {}, 0)
            return
        end

        local abstractGroups = {}
        local guidRecorder = {}
        for _, doc in ipairs(docs) do
            local guid = self:getSelfGuidFromAbstractFilter(doc)
            if guidRecorder[guid] then
                local index = guidRecorder[guid]
                table.insert(abstractGroups[index].groupedAbstractUids, doc._id)
            else
                table.insert(abstractGroups, { firstAbstract = doc.data, groupedAbstractUids = {} })
                guidRecorder[guid] = #abstractGroups
            end
        end

        if start > #abstractGroups then
            self:logInfo("QueryBattleAbstractList. Abstracts Come To End. filter: %v, length: %s, start: %s", filter, #abstractGroups, start)
            self:clientRpc("OnQueryBattleAbstractList", filter, start, count, {}, 0)
            return
        end

        local res = {}
        local realCount = math.min(#abstractGroups - start + 1, count)
        for i = start, start + realCount - 1 do
            table.insert(res, abstractGroups[i])
        end

        self:logInfo("QueryBattleAbstractList Succ. res: %v", res)
        self:clientRpc("OnQueryBattleAbstractList", filter, start, count, res, start + realCount)
    end)
end

function BattleRecordComp:genBattleAbstractsQuery(filter)
    local query = {}

    if filter.battleType then
        query.battleType = { ["$bitsAnySet"] = filter.battleType }
    end

    if filter.position then
        query.position = filter.position
    end

    if filter.allyId and self.worldAvatar.props.allyId > 0 then
        query.allyList = { ["$in"] = { self.worldAvatar.props.allyId } }
    elseif filter.clanId and self.worldAvatar.props.clanId > 0 then
        query.clanList = { ["$in"] = { self.worldAvatar.props.clanId } }
    end

    if filter.playerGid then
        query.playerList = { ["$in"] = { self.props.gid } }
    end

    if filter.heroList then
        if filter.needHeroWin == true then
            query.winHeroList = { ["$in"] = filter.heroList }
        elseif filter.needHeroWin == false then
            query.loseHeroList = { ["$in"] = filter.heroList }
        else
            query.heroList = { ["$in"] = filter.heroList }
        end
    end

    return query
end

function BattleRecordComp:QueryBattleAbstractsByUid(uid)
    self.collAbstract:find({_id = uid}, {}, {}, function(succ, docs)
        if succ then
            assert(#docs == 1)
            self:clientRpc("OnQueryBattleAbstractsByUid", succ, uid, { firstAbstract = docs[1].data })
        else
            self:clientRpc("OnQueryBattleAbstractsByUid", succ, uid, {})
        end
    end)
end

function BattleRecordComp:QueryMultiBattleAbstractsByUid(battleType, uids)
    if #uids == 0 then
        self:clientRpc("OnQueryMultiBattleAbstractsByUid", battleType, uids, {})
        return
    end

    local coll
    if battleType == TableConst.enums.BattleType.PVE_DRILL then
        coll = self.collDrillAbstract
    else
        coll = self.collAbstract
    end
    coll:find({ _id = { ["$in"] = uids } }, { data = 1 }, { sort = { timestamp = -1 } }, function(succ, docs)
        if not succ then
            self:clientRpc("OnQueryMultiBattleAbstractsByUid", battleType, uids, {})
            return
        end

        local result = {}
        for _, doc in ipairs(docs) do
            table.insert(result, { firstAbstract = doc.data })
        end
        self:logInfo("QueryMultiBattleAbstractsByUid. uids: %v, result: %v", uids, result)
        self:clientRpc("OnQueryMultiBattleAbstractsByUid", battleType, uids, result)
    end)
end

function BattleRecordComp:QueryBattleAbstractsByGuid(guid)
    self.collAbstract:find({ guid = { ["$in"] = { guid } } }, { data = 1 }, { sort = { timestamp = -1 } }, function(succ, docs)
        if not succ then
            self:clientRpc("OnQueryBattleAbstractsByGuid", succ, guid, {})
            return
        end

        local result
        for _, doc in ipairs(docs) do
            if result then
                table.insert(result.groupedAbstractUids, doc._id)
            else
                result = { firstAbstract = doc.data, groupedAbstractUids = {} }
            end
        end
        self:logInfo("QueryBattleAbstractsByGuid. guid: %s, result: %v", guid, result)
        self:clientRpc("OnQueryBattleAbstractsByGuid", succ, guid, result)
    end)
end

function BattleRecordComp:QueryBattleStatistics(uid)
    self.collStatistics:find({_id = uid}, {}, {}, function(succ, docs)
        if succ then
            assert(#docs == 1)
            self:clientRpc("OnQueryBattleStatistics", succ, uid, docs[1].data)
        else
            self:clientRpc("OnQueryBattleStatistics", succ, uid, "")
        end
    end)
end

function BattleRecordComp:QueryBattleDetails(uid)
    self.collDetails:find({_id = uid}, {}, {}, function(succ, docs)
        if succ then
            assert(#docs == 1)
            self:clientRpc("OnQueryBattleDetails", succ, uid, docs[1].data)
        else
            self:clientRpc("OnQueryBattleDetails", succ, uid, "")
        end
    end)
end

function BattleRecordComp:StarBattleAbstract(abstract)
    self.props.starredBattleAbstracts:insert(abstract)
    if #self.props.starredBattleAbstracts > 20 then
        self.props.starredBattleAbstracts:remove(1)
    end
    self:clientRpc("OnStarBattleAbstract")
end

--region 红点

function BattleRecordComp:loginCollectBARedDot()
    local query = {
        playerList = { ["$in"] = { self.props.gid } }
    }
    self.collAbstract:find(query, {}, { sort = { timestamp = -1 } }, function(succ, docs)
        if not succ then
            self:logError("loginCollectBARedDot Fail. Find DB Error. query: %v", query)
            return
        end

        for _, doc in ipairs(docs) do
            local guid = self:getSelfGuidFromAbstractFilter(doc)
            -- outer
            local outerTimestamp = math.max(doc.timestamp, self.battleAbstractsRedDotCounter.outer[guid] or 0)
            if outerTimestamp > self.props.battleAbstractsRedDot.globalOuterStamp then
                self.battleAbstractsRedDotCounter.outer[guid] = outerTimestamp
            end

            -- inner
            local innerTimestamp = math.max(doc.timestamp, self.battleAbstractsRedDotCounter.inner[guid] or 0)
            local innerReadStamp = math.max(self.props.battleAbstractsRedDot.globalStamp, self.props.battleAbstractsRedDot.guid2Stamp[guid] or 0)
            if innerTimestamp > innerReadStamp then
                self.battleAbstractsRedDotCounter.inner[guid] = innerTimestamp
            end
        end

        for _, _ in pairs(self.battleAbstractsRedDotCounter.outer) do
            self.props.battleAbstractsRedDot.outerCount = self.props.battleAbstractsRedDot.outerCount + 1
        end
        for _, _ in pairs(self.battleAbstractsRedDotCounter.inner) do
            self.props.battleAbstractsRedDot.innerCount = self.props.battleAbstractsRedDot.innerCount + 1
        end

        self:logInfo("loginCollectBARedDot Succ. outerCount: %s, innerCount: %s", self.props.battleAbstractsRedDot.outerCount, self.props.battleAbstractsRedDot.innerCount)
    end)
end

--- @param guid2Stamp table key: guid 战报组id, value: stamp 时间戳
function BattleRecordComp:ReadBattleAbstractList(guid2Stamp)
    for guid, ts in pairs(guid2Stamp) do
        self.props.battleAbstractsRedDot.guid2Stamp[guid] = ts
        if self.battleAbstractsRedDotCounter.inner[guid] then
            local innerTimestamp = self.battleAbstractsRedDotCounter.inner[guid]
            local innerReadStamp = math.max(self.props.battleAbstractsRedDot.globalStamp, self.props.battleAbstractsRedDot.guid2Stamp[guid])
            if innerTimestamp <= innerReadStamp then
                self.battleAbstractsRedDotCounter.inner[guid] = nil
                self.props.battleAbstractsRedDot.innerCount = self.props.battleAbstractsRedDot.innerCount - 1
            end
        end
    end
end

function BattleRecordComp:ReadAllBattleAbstract()
    self.props.battleAbstractsRedDot.globalStamp = ServerTimeEnv.GetServerNowInt()
    self.props.battleAbstractsRedDot.guid2Stamp = {}
    self.battleAbstractsRedDotCounter.inner = {}
    self.props.battleAbstractsRedDot.innerCount = 0
end

function BattleRecordComp:EnterBattleAbstractUI()
    self.props.battleAbstractsRedDot.globalOuterStamp = ServerTimeEnv.GetServerNowInt()
    self.battleAbstractsRedDotCounter.outer = {}
    self.props.battleAbstractsRedDot.outerCount = 0
end

--endregion 红点