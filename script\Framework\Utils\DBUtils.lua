﻿---@type ConfigEnv
local Config = xrequire(EZFPath .. ".Utils.Config")

---@param dbname? string
---@return string
function getClusterDB(dbname)
    local db = Config.getContainerConfig(dbname or "database")
    local strCluster = string.format("%d", Config.getNodeConfig("cluster"))
    db = string.gsub(db, "{cluster}", strCluster)
    return db
end

local function _ensureEntitesIndexes()
    local createIndex = Config.getContainerConfig("create_entities_indexes")
    if createIndex then
        EZE.createEntitiesIndexes(dbmgrId, dbName)
    end
end

function chooseDBMgr()
    ---@type GlobalEntityMgrEnv
    local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")
    local info = GlobalEntityMgr.instance:getGlobalEntityInfo("DBMgr")
    local dbmgrMb = info and info.mailbox or nil
    if dbmgrMb == nil then
        ErrorLog("no available DBMgr")
        dbmgrId = nil
    elseif not dbmgrId then
        dbmgrId = dbmgrMb:getEntityId()
        EZE.setWatcher(dbmgrId, "container", "dbmgrId")
        _ensureEntitesIndexes()
        GlobalEntityMgr.instance:continueCreateFromDB()
    end
end

---@type string
dbName = getClusterDB()
---@type EntityId | nil
dbmgrId = dbmgrId or nil

-- for emmylua
---@class DBUtilsEnv
local DBUtilsEnv = {
    chooseDBMgr=chooseDBMgr,
    getClusterDB=getClusterDB,
    dbName=dbName,
    dbmgrId=dbmgrId,
}
return DBUtilsEnv
