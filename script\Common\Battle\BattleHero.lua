﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local BattleTacticEnv = xrequire("Common.Battle.BattleTactic")
local BattleBuffEnv = xrequire("Common.Battle.BattleBuff")
local BattlePositionUtilsEnv = xrequire("Common.Battle.BattlePositionUtils")

---@class AttributeModifier
local AttributeModifier = DefineClass("AttributeModifier")

function AttributeModifier:ctor(key, modifyValue)
    self.key = key
    self.modifyValue = modifyValue
end

function AttributeModifier:GetModifyValue()
    return self.modifyValue
end

---@class BattleHero
local BattleHero = DefineClass("BattleHero")

function BattleHero:ctor(battleGame, camp, index, data, teamData)
    self.battleGame = battleGame
    -- 基本信息
    self.heroId = data.heroId
    self.index = index
    BattlePositionUtilsEnv.SetTargetPos(self, index) --军职标记位 hero.posFlags
    self.camp = camp
    self.uniqueId = tostring(self.camp) .. "_" .. tostring(self.heroId)
    self.stars = data.stars
    self.dynasty = data.dynasty
    self.armyType = teamData.battleArmyData.armyType
    self.advanceArmyType = teamData.battleArmyData.advanceArmyType or TableConst.enums.GeneralArmyType.None
    -- 属性
    self.attributes = {}
    for _, attributeType in pairs(TableConst.enums.BattleAttributeType) do
        if data.attributes[attributeType] then
            self.attributes[attributeType] = data.attributes[attributeType]
        else
            local attributeData = GameCommon.TableDataManager:GetAttributeData(attributeType)
            self.attributes[attributeType] = attributeData.default_value
        end
    end
    -- 属性修改器
    self.attributeModifiers = {}
    self._attributeModifierKeyGenerator = 0
    -- 普攻
    self.plainAttackId = Config.Battle.PlainAttackTacticId
    -- 战法
    self.selfTactics = data.selfTactics
    self.carryingTactics = data.carryingTactics or {}
    self.tacticsData = {}
    self.tactics = {}
    self.activeTactics = {} --主动战法
    self.pursueTactics = {} --追击战法
    -- buff
    self.buffs = {} --自身拥有的buff
    self.extraBuffs = teamData.buffs or {} --局外传入的buff
    self.appliedBuffs = {} --自己施加的buff 用于自身死亡时移除对应的buff
    self.crowdControls = {}
    self._crowdControlKeyGenerator = 0
    -- 优先选择目标
    self.priorSelectTargets = {}
    self._priorSelectTargetsKeyGenerator = 0
    -- other
    self.damagePackage = nil
end

function BattleHero:Reset()
    self.damagePackage = nil
    self.attributeModifiers = {}
    self._attributeModifierKeyGenerator = 0
    self.crowdControls = {}
    self._crowdControlKeyGenerator = 0
    self.priorSelectTargets = {}
    self._priorSelectTargetsKeyGenerator = 0
    self.buffs = {}
    self.appliedBuffs = {}
    self.battleGame.battleGameEvent:UnregisterHeroBattleEvents(self.uniqueId)
    self.tacticsData = {}
    table.extend(self.tacticsData, self.selfTactics, self.carryingTactics)
    self.tactics = {}
    self.activeTactics = {}
    self.pursueTactics = {}
    for _, tactic in ipairs(self.selfTactics) do
        self:AddTactic(tactic, true)
    end
    for _, tactic in ipairs(self.carryingTactics) do
        self:AddTactic(tactic, false)
    end
    self.plainAttackTactic = BattleTacticEnv.BattleTactic.new(self, self.plainAttackId, 1, 1)

    self:AddBuff(999001, self.uniqueId, 1, 1)
    for _, buff in pairs(self.extraBuffs) do
        self:AddBuff(buff.id, self.uniqueId, 1, buff.level, { fixedArgs = buff.fixedArgs })
    end
end

function BattleHero:IsAlive()
    local curHealth = self:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    if not (curHealth > 0) and self:CheckCrowdControl(TableConst.enums.CrowdControlType.AvoidDeath) then
        self:SetAttribute(TableConst.enums.BattleAttributeType.Health, 1)
        self:RemoveCrowdControl(TableConst.enums.CrowdControlType.AvoidDeath, 1)
        return true
    end
    return curHealth > 0
end

function BattleHero:Die()
    self:RemoveAppliedBuffs()
    self.battleGame.battleGameEvent:UnregisterHeroBattleEvents(self.uniqueId)
    if BattlePositionUtilsEnv.IsCaptain(self) then
        self.battleGame:CampCaptainDead(self.camp)
    end
    self.battleGame:CheckBattleEnd()
end

function BattleHero:RemoveAppliedBuffs()
    for i = #self.appliedBuffs, 1, -1 do
        local buff = self.appliedBuffs[i]
        if GameCommon.TableDataManager:GetBuffData(buff.buffId).sourceDeadClear then
            if buff.hero:GetBuff(buff.buffId) then
                buff.hero:RemoveBuff(buff)
            end
            table.remove(self.appliedBuffs, i)
        end
    end
end

function BattleHero:InitAttributeTypeModifier(attributeType)
    self.attributeModifiers[attributeType] = {}
    self.attributeModifiers[attributeType].Modifies = {}
    self.attributeModifiers[attributeType].isDirty = true
end

function BattleHero:GetAttribute(attributeType)
    if not self.attributeModifiers[attributeType] then
        self:InitAttributeTypeModifier(attributeType)
    else
        if not self.attributeModifiers[attributeType].isDirty then
            return self.attributeModifiers[attributeType].cacheData
        end
    end
    local attributeData = GameCommon.TableDataManager:GetAttributeData(attributeType)

    if attributeType == TableConst.enums.BattleAttributeType.DodgeRate then
        local dodgeRateResult = self.attributes[attributeType]
        for _, modifier in pairs(self.attributeModifiers[attributeType].Modifies or {}) do
            dodgeRateResult = 1 - (1 - dodgeRateResult) * (1 - modifier:GetModifyValue())
        end
        dodgeRateResult = math.clamp(dodgeRateResult, attributeData.min_value, attributeData.max_value)
        self.attributeModifiers[attributeType].cacheData = dodgeRateResult
        self.attributeModifiers[attributeType].isDirty = false
        return dodgeRateResult
    end

    local result = self.attributes[attributeType]

    for _, modifier in pairs(self.attributeModifiers[attributeType].Modifies or {}) do
        result = result + modifier:GetModifyValue()
    end
    result = math.clamp(result, attributeData.min_value, attributeData.max_value)
    self.attributeModifiers[attributeType].cacheData = result
    self.attributeModifiers[attributeType].isDirty = false
    return result
end

function BattleHero:SetAttribute(attributeType, value)
    local oldValue = self.attributes[attributeType]
    self.attributes[attributeType] = value
    if not self.attributeModifiers[attributeType] then
        self:InitAttributeTypeModifier(attributeType)
    end
    self.attributeModifiers[attributeType].isDirty = oldValue ~= value
end

function BattleHero:ModifyHealth(graphData, delta)
    delta = math.floor(delta) -- 修改值都取整数
    local healthAttributeType = TableConst.enums.BattleAttributeType.Health
    local health = self:GetAttribute(healthAttributeType)
    local recoverableDelta = 0
    if delta < 0 then --伤害
        if health + delta <= 0 then
            delta = -health
        end
        recoverableDelta = self:ModifyRecoverableHealth(delta, Config.Battle.DamageChangeToRecoverableHealthScale) --伤害转换为伤兵
    else --治疗
        local recoverableHealthAttributeType = TableConst.enums.BattleAttributeType.RecoverableHealth
        local recoverableHealth = self:GetAttribute(recoverableHealthAttributeType)
        if delta > recoverableHealth then
            delta = recoverableHealth
        end
        recoverableDelta = -self:ModifyRecoverableHealth(delta, -1) --治疗消耗伤兵
    end
    self:SetAttribute(healthAttributeType, health + delta)

    if not self.attributeModifiers[healthAttributeType] then
        self:InitAttributeTypeModifier(healthAttributeType)
    end
    self.attributeModifiers[healthAttributeType].isDirty = true
    return delta, recoverableDelta
end

function BattleHero:ModifyRecoverableHealth(value, scale)
    local recoverableHealthAttributeType = TableConst.enums.BattleAttributeType.RecoverableHealth
    local recoverableHealth = self:GetAttribute(recoverableHealthAttributeType)
    if value then
        value = math.abs(value)
    else
        value = recoverableHealth --value为nil表示将伤兵转换为死兵
    end
    --if value == 0 then
    --    return
    --end
    local deltaValue = math.floor(value * scale)
    local finalValue = recoverableHealth + deltaValue
    self:SetAttribute(recoverableHealthAttributeType, finalValue)
    return deltaValue
end

function BattleHero:AddAttributeModifier(attributeType, modifyValue, modifierType, isKeepProportion)
    self._attributeModifierKeyGenerator = self._attributeModifierKeyGenerator + 1
    local key = self._attributeModifierKeyGenerator
    if not self.attributeModifiers[attributeType] then
        self:InitAttributeTypeModifier(attributeType)
    end

    if modifierType == BattleConstEnv.AttributeModifierType.Rate then
        modifyValue = self:GetAttribute(attributeType) * modifyValue
    end

    self.attributeModifiers[attributeType].Modifies[key] = AttributeModifier.new(key, modifyValue)
    self.attributeModifiers[attributeType].isDirty = true

    self.battleGame.recorder:DetailModifyAttribute(self.uniqueId, attributeType, self:GetAttribute(attributeType))

    return key
end

function BattleHero:RemoveAttributeModifier(attributeType, key)
    self.attributeModifiers[attributeType].Modifies[key] = nil
    self.attributeModifiers[attributeType].isDirty = true
    self.battleGame.recorder:DetailModifyAttribute(self.uniqueId, attributeType, self:GetAttribute(attributeType))
end

function BattleHero:BatchRemoveAttributeModifier(keyTuples)
    local modifiedAttributeTypes = {}
    for _, keyTuple in pairs(keyTuples) do
        local attributeType, key = keyTuple[1], keyTuple[2]
        modifiedAttributeTypes[attributeType] = true
        self.attributeModifiers[attributeType].Modifies[key] = nil
        self.attributeModifiers[attributeType].isDirty = true
    end
    for attributeType in pairs(modifiedAttributeTypes) do
        self.battleGame.recorder:DetailModifyAttribute(self.uniqueId, attributeType, self:GetAttribute(attributeType))
    end
end

------------------------------------------------------

function BattleHero:AddCrowdControl(ccType, buffId)
    self._crowdControlKeyGenerator = self._crowdControlKeyGenerator + 1
    local key = self._crowdControlKeyGenerator
    if not self.crowdControls[ccType] then
        self.crowdControls[ccType] = {}
    end
    self.crowdControls[ccType][key] = buffId
    --self.battleGame.recorder:DetailAddCrowdControl(self.uniqueId, ccType)
    if ccType == TableConst.enums.CrowdControlType.Stun or ccType == TableConst.enums.CrowdControlType.Silence then
        self:AllTacticsInterruptPrepare()
    end
    return key
end

function BattleHero:GetCrowdControlBuffs(ccType)
    local buffs = {}
    for _, buffId in pairs(self.crowdControls[ccType] or {}) do
        local buff = self:GetBuff(buffId)
        table.insert(buffs, buff)
    end
    return buffs
end

function BattleHero:GetCrowdControlRemainRound(ccType)
    local maxRemainRound = 0
    for _, buffId in pairs(self.crowdControls[ccType] or {}) do
        local buff = self:GetBuff(buffId)
        if buff then
            maxRemainRound = math.max(maxRemainRound, buff:GetRemainRound())
        end
    end
    return maxRemainRound
end

function BattleHero:CheckCrowdControl(ccType)
    return not table.isnilorempty(self.crowdControls[ccType])
end

function BattleHero:RemoveCrowdControl(ccType, key)
    self.crowdControls[ccType][key] = nil
    --if not self:CheckCrowdControl(ccType) then
    --    self.battleGame.recorder:DetailRemoveCrowdControl(self.uniqueId, ccType)
    --end
end

--- tactic

function BattleHero:AddTactic(tacticData, isSelfTactic)
    local tactic = BattleTacticEnv.BattleTactic.new(self, tacticData.id, tacticData.level, tacticData.stars, isSelfTactic)
    table.insert(self.tactics, tactic)
    if tactic.tacticType == TableConst.enums.TacticType.Active then
        table.insert(self.activeTactics, tactic)
    end
    if tactic.tacticType == TableConst.enums.TacticType.Pursue then
        table.insert(self.pursueTactics, tactic)
    end
    for eventType, event in pairs(tactic.battleEvents) do
        self:RegisterBattleEvent(eventType, event.priority, self.uniqueId, BattleConstEnv.BattleHandlerType.Tactic, tactic.uid, event.isSelf)
    end
end

function BattleHero:GetTactic(tacticId)
    for _, tactic in ipairs(self.tactics) do
        if tactic.tacticId == tacticId then
            return tactic
        end
    end
    return nil
end

function BattleHero:GetTacticByUid(tacticUid)
    for _, tactic in ipairs(self.tactics) do
        if tactic.uid == tacticUid then
            return tactic
        end
    end
    return nil
end

function BattleHero:AllTacticsInterruptPrepare()
    for _, tactic in ipairs(self.tactics) do
        tactic:InterruptPrepare()
    end
end

--- buff

function BattleHero:AddBuff(buffId, sourceUid, count, level, extraArgs)
    local recorder = self.battleGame.recorder
    recorder:SetHero(self.uniqueId)
    local detailPointer = recorder:GetDetailPointerAndHero()
    local initId
    local nestIds = {}
    for _ = 1, count do --层数变化nestId
        local nestId = self.battleGame.recorder:GetNestId()
        table.insert(nestIds, nestId)
    end

    local coveredTotalRound
    if extraArgs and extraArgs.coveredTotalRound then
        coveredTotalRound = extraArgs.coveredTotalRound
    end

    local buff = self:GetBuff(buffId)
    if not buff then
        initId = self.battleGame.recorder:GetNestId()
        buff = BattleBuffEnv.BattleBuff.new(self, buffId, sourceUid, level, extraArgs)
        local sourceHero = self.battleGame:GetHeroByUid(sourceUid)
        table.insert(sourceHero.appliedBuffs, buff)
        local isCanAdd, targetCoverBuff = self:CheckBuffGroupConflictRule(buffId, coveredTotalRound, sourceHero)
        if not isCanAdd then
            return
        end
        if targetCoverBuff then
            self:RemoveBuff(targetCoverBuff)
        end
        table.insert(self.buffs, buff)
        for eventType, event in pairs(buff.battleEvents) do
            self:RegisterBattleEvent(eventType, event.priority, self.uniqueId, BattleConstEnv.BattleHandlerType.Buff, buffId, event.isSelf)
        end

        local instance = self.battleGame.abilitySystemManager
        local args = {
            Level = buff.level,
            Round = buff.hero.battleGame.round,
            nestId = initId
        }
        table.update(args, buff.extraArgs)
        local briefData, reason = instance:TriggerBuffGraph(buff, buff.buffLayers[1], "next", args)
        if briefData then
        end
    end

    local increased = buff:IncreaseLayerCount(count, nestIds, coveredTotalRound)
    local newRecord = {}
    newRecord.initId = initId
    newRecord.nestIds = nestIds
    if increased > 0 then
        recorder:DetailAddBuff(self.uniqueId, buffId, buff:GetLayerCount(), detailPointer)
    end

    
    return buff, newRecord
end

function BattleHero:CheckBuffGroupConflictRule(addedBuffId, coveredTotalRound, sourceHero)
    local isCanAdd = true
    local targetCoverBuff
    local addedBuffData = GameCommon.TableDataManager:GetBuffData(addedBuffId)
    if not table.isnilorempty(addedBuffData.groupTypeList) then
        for _, buff in ipairs(self.buffs) do
            for _, groupType in pairs(buff.buffData.groupTypeList) do
                for _, addedBuffGroupType in pairs(addedBuffData.groupTypeList) do
                    if groupType == addedBuffGroupType then
                        if groupType == TableConst.enums.BuffGroupConflictRule.Invalid then
                            isCanAdd = false
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Cover then
                            isCanAdd = true
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Round then
                            local addedBuffTotalRound = coveredTotalRound and coveredTotalRound or addedBuffData.total_round
                            local isAddBuffRoundLonger = addedBuffTotalRound > buff:GetRemainRound()
                            isCanAdd = isAddBuffRoundLonger
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Attribute then
                            local buffGroupData = GameCommon.TableDataManager:GetBuffGroupData(groupType)
                            local AssociatedAttribute = buffGroupData.attribute_association
                            local selfAttributeValue = sourceHero:GetAttribute(AssociatedAttribute)
                            local buffAttributeValue = buff.hero:GetAttribute(AssociatedAttribute)
                            isCanAdd = selfAttributeValue > buffAttributeValue
                        end
                        if isCanAdd then
                            targetCoverBuff = buff
                            goto BuffGroupCheckEnd
                        end
                    end
                end
            end
        end
    end

    ::BuffGroupCheckEnd::
    return isCanAdd, targetCoverBuff
end

function BattleHero:GetBuff(buffId)
    for _, buff in ipairs(self.buffs) do
        if buff.buffId == buffId then
            return buff
        end
    end
    return nil
end

function BattleHero:RemoveBuff(buff)
    self.battleGame.recorder:DetailRemoveBuff(self.uniqueId, buff.buffId)
    buff:OnDestroy()
    table.remove(self.buffs, table.index(self.buffs, buff))
    for eventType, event in pairs(buff.battleEvents) do
        self:UnregisterBattleEvent(eventType, event.priority, self.uniqueId, BattleConstEnv.BattleHandlerType.Buff, buff.buffId)
    end
end

function BattleHero:CalculateBuffRemainRound()
    for _, buff in ipairs(self.buffs) do
        buff:CalculateRemainRound()
    end
end

function BattleHero:CollectBuffBeforeHeroTurnStart()
    for _, buff in pairs(self.buffs) do
        for _, buffLayer in pairs(buff.buffLayers) do
            buffLayer.isNeedToCalculateRemainRound = true
        end
    end
end

------------------

function BattleHero:ReplacePlainAttack(targetPlainAttackId)
    self.plainAttackId = targetPlainAttackId
    self.plainAttackTactic = BattleTacticEnv.BattleTactic.new(self, self.plainAttackId, 1, 1)
end

function BattleHero:CanPlainAttack(ignoreCrowdControl)
    return ignoreCrowdControl or (not self:CheckCrowdControl(TableConst.enums.CrowdControlType.Disarm and not self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun)))
end

function BattleHero:PlainAttack(targetId, ignoreCrowdControl)
    -- 普攻
    if not self:CanPlainAttack(ignoreCrowdControl) then
        return {}
    end

    local recordArgs = {
        uniqueId = self.uniqueId,
        Round = self.battleGame.round
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforePlainAttack, recordArgs, self.uniqueId)
    local args = {}
    if not self.battleGame.skillVariables.hero[self.uniqueId] then
        self.battleGame.skillVariables.hero[self.uniqueId] = {}
    end
    if not string.isnilorempty(targetId) then
        args.TargetId = targetId
    end
    self.battleGame.skillVariables.hero[self.uniqueId].plainAttackTargetId = nil
    local success = self:UseTactic(self.plainAttackTactic, args)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterPlainAttack, recordArgs, self.uniqueId)

    if success then -- 命中
        -- 执行追击
        for _, pursueTactic in ipairs(self.pursueTactics) do
            if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun) then
                self.battleGame.recorder:DetailStun(self.uniqueId, self:GetCrowdControlRemainRound(TableConst.enums.CrowdControlType.Stun))
                break
            end
            self:UseTactic(pursueTactic, args)
        end
    end

    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterPlainAttackProcess, recordArgs, self.uniqueId)
end

function BattleHero:IsAliveAndNotBattleEnd()
    return self:IsAlive() and not self.battleGame.battleEnd
end

----------- battle stages -----------

function BattleHero:SimulateHeroTurn()
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self:CollectBuffBeforeHeroTurnStart()
    self:HeroTurnStart()
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self:HeroTurnAction()
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self:HeroTurnEnd()
end

function BattleHero:HeroTurnStart()
    local recordArgs = {
        uniqueId = self.uniqueId,
        Round = self.battleGame.round
    }
    self.battleGame.recorder:DetailHeroTurnStart(self.uniqueId)
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.HeroTurnStart, recordArgs, self.uniqueId)
end

function BattleHero:HeroTurnAction()
    local recordArgs = {
        uniqueId = self.uniqueId,
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.HeroTurnAction, recordArgs, self.uniqueId)
    if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun) then
        self.battleGame.recorder:DetailStun(self.uniqueId, self:GetCrowdControlRemainRound(TableConst.enums.CrowdControlType.Stun))
        return
    end
    -- 主动技能
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    for _, activeTactic in ipairs(self.activeTactics) do
        if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun) then
            self.battleGame.recorder:DetailStun(self.uniqueId, self:GetCrowdControlRemainRound(TableConst.enums.CrowdControlType.Stun))
            return
        end
        if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Silence) then
            self.battleGame.recorder:DetailSilence(self.uniqueId, self:GetCrowdControlRemainRound(TableConst.enums.CrowdControlType.Silence))
            break
        end
        self:UseTactic(activeTactic)
    end
    -- 普攻和追击
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Disarm) then
        self.battleGame.recorder:DetailDisarm(self.uniqueId, self:GetCrowdControlRemainRound(TableConst.enums.CrowdControlType.Disarm))
        return
    end
    self:PlainAttack() --取消普攻注释这里
    -- 判定连击
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Disarm) then
        self.battleGame.recorder:DetailDisarm(self.uniqueId, self:GetCrowdControlRemainRound(TableConst.enums.CrowdControlType.Disarm))
        return
    end
    local comboRate = self:GetAttribute(TableConst.enums.BattleAttributeType.ComboRate)
    if comboRate > 0 then
        if math.random() < comboRate then
            self.battleGame.recorder:DetailCombo(self.uniqueId, comboRate)
            self:PlainAttack()
        else
            self.battleGame.recorder:DetailComboFailed(self.uniqueId, comboRate)
        end
    end
end

function BattleHero:HeroTurnEnd()
    local recordArgs = {
        uniqueId = self.uniqueId,
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.HeroTurnEnd, recordArgs, self.uniqueId)
    self:CalculateBuffRemainRound()
    recordArgs.buffs = {}
    for _, buff in ipairs(self.buffs) do
        if buff.buffData.show then
            table.insert(recordArgs.buffs, buff:Dump())
        end
    end
end

-------------------------------------

function BattleHero:RegisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId, isSelf)
    self.battleGame.battleGameEvent:RegisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId, isSelf)
end

function BattleHero:UnregisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId)
    self.battleGame.battleGameEvent:UnregisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId)
end

-------------------------------------

function BattleHero:CalculateCd()
    for _, tactic in ipairs(self.tactics) do
        tactic:CalculateCd()
    end
end

function BattleHero:CanUseTactic(tactic)
    if tactic.tacticType == TableConst.enums.TacticType.Active then --是主动战法
        if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Silence) then --被沉默
            return false
        end
    end
    if not tactic:CanUse() then --战法被禁用
        return false
    end
    return true
end


function BattleHero:GetTacticProbability(tactic)
    local tacticUseFailedProbability = tactic:GetAttribute(TableConst.enums.TacticAttributeType.UseFailedProbability)
    local totalSuccessProbability = 1 - tacticUseFailedProbability

    if tactic.tacticType == TableConst.enums.TacticType.Active then
        totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.ActiveTacticProbability)
    elseif tactic.tacticType == TableConst.enums.TacticType.Pursue then
        totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.PursueTacticProbability)
    end

    if tactic.isSelfTactic then
        if tactic.tacticType == TableConst.enums.TacticType.Active then
            totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.SelfActiveTacticProbability)
        elseif tactic.tacticType == TableConst.enums.TacticType.Pursue then
            totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.SelfPursueTacticProbability)
        end
    end

    totalSuccessProbability = math.clamp(totalSuccessProbability, 0, 1)
    return totalSuccessProbability
end

function BattleHero:AddPriorSelectTarget(data)
    self._priorSelectTargetsKeyGenerator = self._priorSelectTargetsKeyGenerator + 1
    local key = self._priorSelectTargetsKeyGenerator
    self.priorSelectTargets[key] = data
    return key
end

function BattleHero:RemovePriorSelectTarget(key)
    self.priorSelectTargets[key] = nil
end

function BattleHero:DecreasePriorSelectTargetCount(key)
    if not self.priorSelectTargets[key] then
        return false
    end
    if not self.priorSelectTargets[key].useCount then
        return false
    end
    if self.priorSelectTargets[key].count <= 0 then
        return false
    end
    self.priorSelectTargets[key].count = self.priorSelectTargets[key].count - 1
    return true
end

function BattleHero:UseTactic(tactic, args)
    if not self:IsAliveAndNotBattleEnd() then
        tactic:InterruptPrepare()
        return false
    end

    if not self:CanUseTactic(tactic) then
        tactic:InterruptPrepare()
        return false
    end

    local inputArgs = {
        InputTacticId = tactic.tacticId,
        InputTacticUid = tactic.uid,
        EventSourceId = self.uniqueId,
    }

    args = args or {}

    local prob = 1
    if not tactic:IsTacticPreparing() then
        local extraProb = args.extraProb and args.extraProb or 0
        prob = self:GetTacticProbability(tactic) + extraProb
        local randomNum = math.random()
        DebugLog(string.format("[BattleHero] UseTactic %s %s %s %s", self.uniqueId, tactic.tacticId, prob, randomNum))
        if randomNum > prob then
            self.battleGame.recorder:DetailTacticFailed(self.uniqueId, tactic.tacticId, prob)
            self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.TacticProbFailed, inputArgs, self.uniqueId)
            return false
        end
    end

    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeUseTactic, inputArgs, self.uniqueId)

    -- 优先选择目标
    local topPriority = -999999
    local priorSelectTargetId
    local priorSelectTargetKey
    for key, data in pairs(self.priorSelectTargets) do
        if (not data.useCount or data.count > 0) and math.random() < data.probability then
            if data.changeTargetRangeType == BattleConstEnv.ChangeTargetRangeType.ByTacticType then
                if data.tacticTypes[tactic.tacticType] then
                    if data.selectPriority > topPriority then
                        topPriority = data.selectPriority
                        priorSelectTargetId = data.targetId
                        priorSelectTargetKey = key
                    end
                end
            elseif data.changeTargetRangeType == BattleConstEnv.ChangeTargetRangeType.ByTacticId then
                if data.tacticIds[tactic.tacticId] then
                    if data.selectPriority > topPriority then
                        topPriority = data.selectPriority
                        priorSelectTargetId = data.targetId
                        priorSelectTargetKey = key
                    end
                end
            elseif data.changeTargetRangeType == BattleConstEnv.ChangeTargetRangeType.All then
                if data.selectPriority > topPriority then
                    topPriority = data.selectPriority
                    priorSelectTargetId = data.targetId
                    priorSelectTargetKey = key
                end
            end
        end
    end
    args.PriorSelectTargetId = priorSelectTargetId
    args.PriorSelectTargetKey = priorSelectTargetKey

    local success = tactic:Use(args, prob)
    if success then
        if priorSelectTargetKey and self.priorSelectTargets[priorSelectTargetKey] then
            if self.priorSelectTargets[priorSelectTargetKey].useCount and self.priorSelectTargets[priorSelectTargetKey].count <= 0 then
                local buff = self:GetBuff(self.priorSelectTargets[priorSelectTargetKey].buffId)
                if buff then
                    buff:DecreaseLayerCount(1)
                end
            end
        end

        self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterUseTactic, inputArgs, self.uniqueId)
    end
    return success
end

function BattleHero:Dump()
    return {
        heroId = self.heroId,
        index = self.index,
        camp = self.camp,
        uniqueId = self.uniqueId,
        attributes = self.attributes,
        tactics = self.tactics,
    }
end
