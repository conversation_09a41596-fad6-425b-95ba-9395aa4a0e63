{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "3"}, "number2": {"Type": "number", "Value": "0"}}}, "2": {"Type": "GetAttributeNode", "Field": {"heroIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": {"Items": []}}}, "3": {"Type": "GetAttributeNode", "Field": {"heroIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "2"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": {"Items": []}}}, "5": {"Type": "GetBuffNode", "Field": {"heroId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "buffType": {"Items": [{"Type": "number", "Value": "3"}]}, "buffGroupType": {"Items": []}, "buffIds": {"Items": []}}}, "6": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "True"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}, "conditions": {}}}, "8": {"Type": "GetBuffNode", "Field": {"heroId": {"Type": "number", "Value": "0"}, "buffType": {"Items": [{"Type": "number", "Value": "1"}, {"Type": "number", "Value": "2"}]}, "buffGroupType": {"Items": []}, "buffIds": {"Items": []}}}, "9": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "False"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}, "10": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "False"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}, "11": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "True"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}, "12": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "True"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}, "13": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam1"}, "desc": {"Type": "string", "Value": "兵无常势兵刃伤害公式"}, "result": {"Type": "number", "Value": "0"}}}, "14": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam2"}, "desc": {"Type": "string", "Value": "兵无常势谋略伤害公式"}, "result": {"Type": "number", "Value": "0"}}}, "15": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "True"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}, "conditions": {}}}, "16": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "statistic": {"Type": "boolean", "Value": "False"}, "sourceId": {"Type": "number", "Value": "0"}, "tacticId": {"Type": "number", "Value": "0"}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgsList": {"Items": [], "Merge": true}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "overwriteRecoverableRate": {"Type": "boolean", "Value": "False"}, "recoverableRate": {"Type": "number", "Value": "0"}, "damageValues": {"Items": []}, "damageHeroIds": {"Items": []}, "damagePackageIds": {"Items": []}, "missHeroIds": {"Items": []}}}, "17": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "2"}, "statistic": {"Type": "boolean", "Value": "False"}, "sourceId": {"Type": "number", "Value": "0"}, "tacticId": {"Type": "number", "Value": "0"}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgsList": {"Items": [], "Merge": true}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "overwriteRecoverableRate": {"Type": "boolean", "Value": "False"}, "recoverableRate": {"Type": "number", "Value": "0"}, "damageValues": {"Items": []}, "damageHeroIds": {"Items": []}, "damagePackageIds": {"Items": []}, "missHeroIds": {"Items": []}}}, "18": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "True"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}}, "Links": {"0": {"next": ["2.prev"]}, "1": {"elseNode": ["6.prev"], "next": ["15.prev"]}, "2": {"next": ["11.prev"]}, "3": {"next": ["12.prev"]}, "5": {"elseNode": ["9.prev"], "next": ["9.prev"]}, "6": {"next": ["18.prev"]}, "8": {"elseNode": ["10.prev"], "next": ["10.prev"]}, "9": {"next": ["13.prev"]}, "10": {"next": ["14.prev"]}, "11": {"next": ["3.prev"]}, "12": {"next": ["1.prev"]}, "13": {"next": ["16.prev"]}, "14": {"next": ["17.prev"]}, "15": {"next": ["5.prev"]}, "18": {"next": ["8.prev"]}}, "DataFlows": {"2": {"attributeValues": ["11.list"]}, "3": {"attributeValues": ["12.list"]}, "5": {"buffIds": ["9.list"]}, "6": {"targetIds": ["18.list", "17.targetIds"]}, "8": {"buffIds": ["10.list"]}, "9": {"listLength": ["13.a"], "indexElement": ["13.a"]}, "10": {"listLength": ["14.a"]}, "11": {"indexElement": ["1.number1"]}, "12": {"indexElement": ["1.number2"]}, "13": {"result": ["16.damageFactor"]}, "14": {"result": ["17.damageFactor"]}, "15": {"targetIds": ["16.targetIds"]}, "18": {"indexElement": ["8.heroId"]}}}