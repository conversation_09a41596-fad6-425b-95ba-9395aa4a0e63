local ServerTimeEnv = xrequire("Utils.ServerTime")
local CoordUtils = xrequire("Common.Utils.CoordUtils")
local WorldConst = xrequire("Common.Const.WorldConst")
local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local EntityUtils = xrequire("Common.Utils.EntityUtils")
local ActorUtils = xrequire("Utils.ActorUtils")

---@class MoveComp : ComponentBase, WorldActor
---@field moveTimerId TimerId?
---@field props MoveCompProps
MoveComp = DefineClass("MoveComp", ComponentBaseEnv.ComponentBase)
MoveComp.HasMoveComp = true  ---@type boolean

function MoveComp:ctor()
    self.moveNextTargetIdx = nil   ---@type int?
    self.moveTimerId = nil
    self.moveFollowers = nil
end

function MoveComp:OnDestroy()
    self.moveFollowers = nil
end

function MoveComp:onLeaveSpace()
    self:RemoveMoveLine()  -- GM销毁或者传送部队，需要清理原行军线
end

function MoveComp:IsMoving()
    return self.moveTimerId ~= nil
end

function MoveComp:StartMove(path, speed, continueData, moveType, targetEid, callback)
    local targetIdx = 1
    if continueData and continueData.targetIdx then
        targetIdx = continueData.targetIdx
    end
    local startTime = ServerTimeEnv.GetServerNow()
    if continueData and continueData.timeElapse then
        startTime = startTime - continueData.timeElapse
    end 
    assert(not self:IsMoving())
    assert(#path > 0)
    assert(targetIdx)

    local curPos = self:GetDoubledPos2DV()
    local targetPos = path[#path]
    self:logInfo("move from (%s, %s) to (%s, %s)", curPos.x, curPos.y, targetPos.x, targetPos.y)

    self.props.moveStartPos = curPos
    self.props.movePath = path
    self.props.moveTargetPos = self.props.movePath[#self.props.movePath]:copy()
    self.props.moveSpeed = speed -- 每个格子moveSpeed秒
    self.props.moveType = moveType
    self.props.targetEid = targetEid
    self.props.moveStartTime = startTime or ServerTimeEnv.GetServerNow()
    self.props.moveLastTickTime = self.props.moveStartTime

    self.moveNextTargetIdx = targetIdx
    self.moveCb = callback
    self.moveTimerId = self:addTimer(self.props.moveSpeed, self.props.moveSpeed, function() self:onMoveTick() end)

    self:refreshMainStatus()
    self:setFollowerMoving(true)

    self:syncActorView({
        moveStartPos = self.props.moveStartPos,
        movePath = self.props.movePath,
        moveTargetPos = self.props.moveTargetPos,
        moveSpeed = self.props.moveSpeed,
        moveStartTime = self.props.moveStartTime,
    })
    self:UpdateFocusListen()
end

function MoveComp:StopMove(pause)
    if not self:IsMoving() then
        return
    end
    local cb = self.moveCb
    local continueData
    if pause then
        continueData = {
            targetIdx = self.moveNextTargetIdx,
            timeElapse = ServerTimeEnv.GetServerNow() - self.props.moveLastTickTime,
            path = self.props.movePath:copy(),
        }
    end
    self:clearMove()
    if cb then
        cb(false, pause)
    end
    return continueData
end

function MoveComp:clearMove()
    if self.moveTimerId then
        self:delTimer(self.moveTimerId)
        self.moveTimerId = nil
    end
    self.props.moveStartPos = {}
    self.props.moveTargetPos = {}
    self.props.movePath = {}
    self.props.moveSpeed = 0
    self.props.moveStartTime = 0
    self.props.moveLastTickTime = 0
    self.props.moveType = 0
    self.props.targetEid = nil

    self.moveNextTargetIdx = nil
    self.moveCb = nil

    self:refreshMainStatus()
    self:setFollowerMoving(false)
    self:syncActorView({
        moveStartPos = self.props.moveStartPos,
        movePath = self.props.movePath,
        moveTargetPos = self.props.moveTargetPos,
        moveSpeed = self.props.moveSpeed,
        moveStartTime = self.props.moveStartTime,
    })
    self:UpdateFocusListen()
end

function MoveComp:onMoveTick()
    local curPos = self:GetDoubledPos2DV()
    local targetPos = self.props.movePath[self.moveNextTargetIdx]  ---@type table
    assert(not (curPos:Equals(targetPos)))
    local moveDir = CoordUtils.GetDirection(curPos, targetPos)
    self:logDebug("move tick from (%s, %s) to (%s, %s)", curPos.x, curPos.y, curPos.x + moveDir.x, curPos.y + moveDir.y)
    curPos:Add(moveDir)
    CoordUtils.CheckValidDoubledV(curPos)

    --- OPTIMIZA(qun): 需求要求实时检测移动前往的格子，如果是不可通行的关口城门，需要打断移动
    local packedPos = CoordUtils.PackPos(curPos.x, curPos.y)
    local gridTp = self.space.mapData:GetElementType(packedPos)
    if not ActorUtils.IGNORE_OBSTACLE_MOVE[self.props.moveType] and ProcessedTableEnv.MOVE_REALTIME_CHECK_OBSTACLE[gridTp] then
        local info = self.space:GetGridInfo(curPos.x, curPos.y)
        local building = self:getActor(info and info.buildingUid)
        if not info or (not EntityUtils.FRIENDLY_RELATION[EntityUtils.GetRelationShip(self.props, info)] and building.id ~= self.props.targetEid) then
            self:RemoveMoveLine()
            self:StopMove()
            return
        end
    end

    local oldX, oldY = self:SetDoubledPos2D(curPos.x, curPos.y)
    self.props.moveLastTickTime = ServerTimeEnv.GetServerNow()

    self:checkFollowMove(curPos)

    if self.HasArmySupplyComp and not ActorUtils.IGNORE_MOVE_DEC_SUPPLY[self.props.moveType] then
        self:decSupply(Config.Slg.MOVE_SUPPLY_COST_PER_GRID)
    end

    self:OnPosChanged(oldX, oldY)

    if curPos:Equals(targetPos) then
        self.moveNextTargetIdx = self.moveNextTargetIdx + 1
        if self.moveNextTargetIdx > #self.props.movePath then
            local cb = self.moveCb
            self:clearMove()
            if cb then
                cb(true)
            end
        end
    end
end

function MoveComp:AddMoveLine()
    if not self:IsMoving() then
        self:logError("[lb] add move line failed, not moving currently")
        return false
    end

    if self.props.moveLineId >= 0 then
        self:logWarn("[lb] add move line, remove current line %s first", self.props.moveLineId)
        self:RemoveMoveLine()
    end

    local x0, y0 = self:getPos2D()
    local x1, y1 = CoordUtils.Doubled2Offset(self.props.moveTargetPos.x, self.props.moveTargetPos.y)
    self.props.moveLineId = self.space:addAoiData({x = x0, y = 0, z = y0}, nil, 
        {WorldConst.AoiDataType.MarchLine, self.props.mainStatus, self.id, self.props.gid, self.props.allyId, {x = x1, y = y1}},
        {WorldConst.AoiDataShape.Line, {x = x1, y = 0, z = y1}})

    if self.props.moveLineId < 0 then
        self:logError("[lb] add move line failed, add aoi data error")
        return false
    end

    self.moveLineUpdateTimerId = self:addTimer(10, 10, function() self:onMoveLineUpdate() end)

    return true
end

function MoveComp:RemoveMoveLine()
    if self.props.moveLineId >= 0 then
        self.space:removeAoiData(self.props.moveLineId)
        self.props.moveLineId = -1
    end
    if self.moveLineUpdateTimerId then
        self:delTimer(self.moveLineUpdateTimerId)
        self.moveLineUpdateTimerId = nil
    end
end

function MoveComp:onMoveLineUpdate()
    if not self:IsMoving() then
        self:logWarn("[lb] update move line when not moving, remove current line %s", self.props.moveLineId)
        self:RemoveMoveLine()
        return
    end

    local x, y = self:getPos2D()
    self.space:setAoiDataPos2D(self.props.moveLineId, x, y)
end

---获取移动一个格子消耗的时间/秒
---@param moveType int 移动类型
function MoveComp:GetMoveSpeedByType(moveType)
    -- 这里返回的时，耗费多久时间走一个格子
    local ratio = 100
    if not ActorUtils.IGNORE_MOVE_BUFF[moveType] then
        for src, add in pairs(self.props.moveSpeedAdd) do
            ratio = ratio + add
        end
    end
    ratio = ratio + (self._gmMoveBuff or 0)
    local config = GameCommon.TableDataManager:GetActorMoveConfig(moveType)
    if config then
        ratio = ratio + config.add_ratio
    end
    ratio = math.min(1000, math.max(1, ratio))
    return Config.Slg.MOVE_SPEED * 100 / ratio
end

function MoveComp:SetMoveSpeedAdd(src, add)
    self.props.moveSpeedAdd[src] = add
end

function MoveComp:RemoveMoveSpeedAdd(src)
    self.props.moveSpeedAdd[src] = nil
end

function MoveComp:gmSetArmyMoveBuff(ratio)
    self._gmMoveBuff = ratio
end

function MoveComp:checkFollowMove(curPos)
    if not self.moveFollowers then
        return
    end
    for _, follower in ipairs(self.moveFollowers) do
        local oldX, oldY = follower:SetDoubledPos2D(curPos.x, curPos.y)
        follower:OnPosChanged(oldX, oldY)
    end
end

function MoveComp:addMoveFollower(follower)
    if not self.moveFollowers then
        self.moveFollowers = {}
    end
    table.insert(self.moveFollowers, follower)
end

function MoveComp:delMoveFollower(follower)
    if not self.moveFollowers then
        return
    end
    for i, v in ipairs(self.moveFollowers) do
        if v == follower then
            table.remove(self.moveFollowers, i)
            follower:SetInFollowMoving(nil)
            break
        end
    end
    if table.isempty(self.moveFollowers) then
        self.moveFollowers = nil
    end
end

function MoveComp:setFollowerMoving(isMoving)
    if not self.moveFollowers then
        return
    end
    for _, follower in ipairs(self.moveFollowers) do
        if isMoving then
            follower:SetInFollowMoving(self.id)
        else
            follower:SetInFollowMoving(nil)
        end
    end
end

---更新位置变更的时间戳，并同步给客户端用于actor显示优先级计算
function MoveComp:SetPosChangeTime()
    self.props.posTime = ServerTimeEnv.GetServerNow()
end
