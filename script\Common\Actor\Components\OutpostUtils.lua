local CompatEnv = xrequire("Common.Actor.Compat")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")

--[[据点组件
概念：具备建造、耐久、守军、调动属性之一的Actor即为据点
]]


---@class CommonOutpost: ComponentBase, CommonWorldActor
---@field props OutpostCompProps
---@diagnostic disable-next-line: assign-type-mismatch
CommonOutpost = DefineClass("CommonOutpost", CompatEnv.ComponentBaseEnv.ComponentBase)

---@param actor CommonArmy
function CommonOutpost:Valid<PERSON>e<PERSON><PERSON><PERSON>(actor)
    ---@diagnostic disable-next-line: cast-type-mismatch
    ---@cast self CommonCityGate
    if self.HasCommonCityGate then
        if not self:IsInWarWith(actor.props.allyId) then
            return false
        end
    end
    ---@diagnostic disable-next-line: cast-type-mismatch
    ---@cast self CommonOutpost
    if self.props.durability <= 0 then
        return false
    end
    if not BehaviorJudgeEnv.ValidBehaviorTo(actor.props, self.props, actor._actorType, self._actorType, TableConst.enums.InteractiveBehavior.Attack) then
        return false
    end
    return true
end

return {
    CommonOutpost = CommonOutpost,
}