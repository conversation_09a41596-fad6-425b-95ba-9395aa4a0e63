local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local BattleTeamUtilsEnv = xrequire("Common.Battle.BattleTeamUtils")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local ProduceEnv = xrequire("Common.Produce")

--[[军演组件
军演NPC平局需求：平局后继续参加下一轮战斗，但是连续平局次数超过上限则挑战失败
]]

---@class (partial) Avatar
---@class DrillComp: ComponentBase
DrillComp = DefineClass("DrillComp", ComponentBaseEnv.ComponentBase)

---@diagnostic disable-next-line: duplicate-set-field
function DrillComp:ctor()
    self.challengeDrill = {}
    self:InitDrillHangingBox()
    local cycleId, stageId, nextTime = EZGlobal.Schedule:getCurSchedule(TableConst.enums.ScheduleType.CommonDaily)
    self:OnDrillDailySchedule(cycleId)
end

function DrillComp:OnDrillDailySchedule(nowCycle)
    if nowCycle ~= self.props.drillDailyChance.cycleId then
        self.props.drillDailyChance.cycleId = nowCycle
        self.props.drillDailyChance.count = GameCommon.TableDataManager:GetDrillConst("DAILY_CHANCE")
    end
end

--region 挑战关卡

function DrillComp:ChallengeDrillLevel(levelId, battleOrder, armyTypeByOrder)
    local levelConfig = GameCommon.TableDataManager:GetDrillLevel(levelId)
    if not levelConfig then
        self:logInfo("[qun] Level %s not found", levelId)
        return
    end
    if not self:isDrillChapterUnlocked(levelId) then
        self:logInfo("[qun] Level %s is not unlocked", levelId)
        return
    end
    if not self:isNextLevel(levelId) then
        self:logInfo("[qun] Level %s is not a next-level", levelId)
        return
    end
    local thisChapterId = ProcessedTableEnv.DRILL.level2Chapter[levelId]
    if levelId % 100 == 1 then
        -- 前一章的章节奖励已经全部领取
        local lastChapterConfig = GameCommon.TableDataManager:GetDrillChapter(thisChapterId - 1)
        if thisChapterId ~= 1 then
            if self.props.drillChapterBox.chapterId ~= thisChapterId - 1 or self.props.drillChapterBox.boxIdx ~= #lastChapterConfig.rewards then
                self:logInfo("[qun] GetAllChapterBox before challenging level %s", levelId)
                return
            end
        end
    end
    if thisChapterId ~= self.props.currentDrillChapter then
        self:logInfo("[qun] Current chapter %s does not match level %s", self.props.currentDrillChapter, levelId)
        return
    end
    if self.props.drillDailyChance.count <= 0 then
        self:logInfo("[qun] Daily challenge chance is exhausted")
        return
    end
    assert(#battleOrder <= levelConfig.teamCount)
    self:logInfo("[qun] Challenging level %s", levelId)
    local challengeId = EZE.genUUID()
    self.challengeDrill = {
        challengeId = challengeId,  -- 挑战请求唯一标识符
        levelId = levelId,  -- 关卡ID
        battleOrder = battleOrder,  -- 挑战顺序
        armyTypeByOrder = armyTypeByOrder,  -- 配置的兵种类型
        armyList = {},  -- 我方部队列表
        enemyList = {},  -- 敌方部队列表
        battleIdx = 0,  -- 当前战斗场数索引
        battleResult = {},  -- 各场战斗结果
        curBattleCount = 0,  -- 当前进行中的战斗数量
    }
    local dumpCheck = {}
    for idx, armySlotIdx in ipairs(battleOrder) do
        local armyData = self.worldAvatar:GetAndCheckArmy(armySlotIdx, "Drill")
        if not armyData then
            self:logInfo("[qun] Army %s not found", armySlotIdx)
            return
        end
        if not self.worldAvatar:validArmyType(armyTypeByOrder[idx]) then
            self:logInfo("[qun] Invalid army type %s for slot %s", armyTypeByOrder[idx], armySlotIdx)
            return
        end
        if dumpCheck[armySlotIdx] then
            self:logInfo("[qun] Duplicate army slot %s in battle order", armySlotIdx)
            return
        end
        dumpCheck[armySlotIdx] = true
        self:callActor(armyData.uid, "GetBattleData", TableConst.enums.Camp.A, self:makeCallback("OnGetBattleData", challengeId, idx))
    end
    self:clientRpc("OnStartChallengeDrillLevel", levelId)
end

function DrillComp:OnGetBattleData(challengeId, orderIdx, attTeam)
    if self.challengeDrill.challengeId ~= challengeId then
        self:logInfo("[qun] Challenge %s is not valid", challengeId)
        return
    end
    assert(attTeam, "Battle data is nil for index: " .. orderIdx)
    assert(not self.challengeDrill.armyList[orderIdx], "Battle data already exists for index: " .. orderIdx)
    local armyData = self.worldAvatar:GetAndCheckArmy(attTeam.armySlotIdx, "Drill")
    assert(armyData, "Army data not found for index: " .. orderIdx)
    -- 应用客户端传来的兵种类型
    local originalArmyType = armyData.armyType
    armyData.armyType = self.challengeDrill.armyTypeByOrder[orderIdx]
    self.worldAvatar:modifyBattleTeamData(attTeam, attTeam.armySlotIdx, self.worldAvatar:generateBattleArmyData(armyData, true))
    -- 恢复原始兵种类型,避免影响其他系统
    armyData.armyType = originalArmyType
    attTeam["orderIdx"] = orderIdx  -- 保存部队顺序索引
    self.challengeDrill.armyList[orderIdx] = attTeam
    for idx, _ in pairs(self.challengeDrill.battleOrder) do
        if not self.challengeDrill.armyList[idx] then
            return
        end
    end
    -- 开始本次挑战
    local levelConfig = GameCommon.TableDataManager:GetDrillLevel(self.challengeDrill.levelId)
    assert(levelConfig, "Level config not found for level ID: " .. self.challengeDrill.levelId)
    for npcOrderIdx, npcTeamId in ipairs(levelConfig.teamId) do
        local npcTeam = BattleTeamUtilsEnv.GetNpcBattleTeam(npcTeamId)
        npcTeam["orderIdx"] = npcOrderIdx  -- 保存NPC部队顺序索引
        table.insert(self.challengeDrill.enemyList, npcTeam)
    end
    self:startNextDrillBattle()
end

function DrillComp:startNextDrillBattle()
    local curBattleCount = math.min(#self.challengeDrill.armyList, #self.challengeDrill.enemyList)
    assert(curBattleCount > 0, "No battles to start")

    self.challengeDrill.battleIdx = self.challengeDrill.battleIdx + 1
    self.challengeDrill.battleResult[self.challengeDrill.battleIdx] = {}
    self.challengeDrill.curBattleCount = curBattleCount

    self:logInfo("[qun] Drill %s battle(%s) count(%s)",
        self.challengeDrill.challengeId, self.challengeDrill.battleIdx, curBattleCount)

    for inBattleSeq = 1, curBattleCount do
        self:serviceRpc("BattleService", "CalcBattleResult",
            self.challengeDrill.armyList[inBattleSeq], self.challengeDrill.enemyList[inBattleSeq],
            self:genBattleInfo(self.challengeDrill.levelId, self.challengeDrill.battleIdx, inBattleSeq),
            self:makeCallback("OnDrillBattleEnd", self.challengeDrill.challengeId, self.challengeDrill.battleIdx, inBattleSeq)
        )
    end
end

function DrillComp:OnDrillBattleEnd(challengeId, battleIdx, inBattleSeq, battleResult, briefResult, battleAbstracts)
    if not battleResult.success then
        self:logError("[qun] Battle %s failed for challenge %s", battleIdx, challengeId)
        return
    end
    if self.challengeDrill.challengeId ~= challengeId then
        self:logInfo("[qun] Challenge %s is not valid", challengeId)
        return
    end
    assert(self.challengeDrill.curBattleCount > 0, "No battles in progress")
    assert(self.challengeDrill.battleResult[battleIdx], "Battle result not found for index: " .. battleIdx)
    assert(not self.challengeDrill.battleResult[battleIdx][inBattleSeq], "Battle result already exists for sequence: " .. inBattleSeq)
    -- 战斗结果同步到部队
    local totalSoldierLeft = BattleTeamUtilsEnv.UpdateBattleTeamHealth(self.challengeDrill.armyList[inBattleSeq], battleResult.heroesHealth[TableConst.enums.Camp.A])
    self.challengeDrill.armyList[inBattleSeq].totalSoldierLeft = totalSoldierLeft
    totalSoldierLeft = BattleTeamUtilsEnv.UpdateBattleTeamHealth(self.challengeDrill.enemyList[inBattleSeq], battleResult.heroesHealth[TableConst.enums.Camp.B])
    self.challengeDrill.enemyList[inBattleSeq].totalSoldierLeft = totalSoldierLeft
    -- 记录当前回合各个战斗结果
    self.challengeDrill.battleResult[battleIdx][inBattleSeq] = {
        winCamp = briefResult.winCamp,
        selfOrderIdx = self.challengeDrill.armyList[inBattleSeq].orderIdx,
        enemyOrderIdx = self.challengeDrill.enemyList[inBattleSeq].orderIdx,
        selfSoldierLeft = self.challengeDrill.armyList[inBattleSeq].totalSoldierLeft,
        enemySoldierLeft = self.challengeDrill.enemyList[inBattleSeq].totalSoldierLeft,
        selfRecoverable = battleAbstracts[1].teamAbstractDict[TableConst.enums.Camp.A].recoverableHealth,
        enemyRecoverable = battleAbstracts[1].teamAbstractDict[TableConst.enums.Camp.B].recoverableHealth
    }
    local rec = self.challengeDrill.battleResult[battleIdx][inBattleSeq]
    self:logInfo("[qun] Drill %s battle(%s) seq(%s) self(%s)(%s) enemy(%s)(%s)",
        self.challengeDrill.challengeId, battleIdx, inBattleSeq,
        rec.selfOrderIdx, rec.selfSoldierLeft,
        rec.enemyOrderIdx, rec.enemySoldierLeft)
    -- 记录战报
    for _, abstract in ipairs(battleAbstracts) do
        self:OnGetDrillBattleRecord(abstract)
    end
    -- 累积平局次数
    if rec.selfSoldierLeft > 0 and rec.enemySoldierLeft > 0 then
        self.challengeDrill.continuousDraw = (self.challengeDrill.continuousDraw or 0) + 1
    end
    -- 判断挑战结果
    if self.challengeDrill.curBattleCount ~= #self.challengeDrill.battleResult[battleIdx] then
        return
    end
    -- 清理死掉的部队
    for i = #self.challengeDrill.armyList, 1, -1 do
        if self.challengeDrill.armyList[i].totalSoldierLeft and self.challengeDrill.armyList[i].totalSoldierLeft <= 0 then
            table.remove(self.challengeDrill.armyList, i)
        end
    end
    for i = #self.challengeDrill.enemyList, 1, -1 do
        if self.challengeDrill.enemyList[i].totalSoldierLeft and self.challengeDrill.enemyList[i].totalSoldierLeft <= 0 then
            table.remove(self.challengeDrill.enemyList, i)
        end
    end
    if table.isempty(self.challengeDrill.armyList) or (self.challengeDrill.continuousDraw or 0) >= Config.Battle.MaxContinousDraw then
        -- 失败
        local failedDrill = self.challengeDrill
        self:failedDrillLevel(self.challengeDrill.levelId)
        self:safeClientRpc("OnChallengeDrillEnd", failedDrill.levelId, false, failedDrill.battleResult)
    elseif table.isempty(self.challengeDrill.enemyList) then
        -- 胜利
        local levelConfig = GameCommon.TableDataManager:GetDrillLevel(self.challengeDrill.levelId)
        assert(levelConfig)
        self:logInfo("[qun] Challenge level %s completed successfully", self.challengeDrill.levelId)
        if not self:isNextLevel(self.challengeDrill.levelId) then
            self:logError("[qun] Level %s is not a next-level after victory", self.challengeDrill.levelId)
            return
        end
        local successDrill = self.challengeDrill
        self:completeDrillLevel(self.challengeDrill.levelId)
        self:safeClientRpc("OnChallengeDrillEnd", successDrill.levelId, true, successDrill.battleResult)
    else
        -- 再战
        self:startNextDrillBattle()
    end
end

function DrillComp:completeDrillLevel(levelId)
    self.props.finishedDrillLevel = levelId  -- 更新已完成的关卡
    local levelConfig = GameCommon.TableDataManager:GetDrillLevel(levelId)
    self:AddReward(levelConfig.rewards, TableConst.enums.Reason.DRILL_LEVEL_REWARD)
    self:updateDrillProduceSpeed(levelConfig.output)
    self.challengeDrill = {}
    self.props.drillChallengeCount = self.props.drillChallengeCount + 1
    self:fireAvatarEvent(TableConst.enums.SAvatarEvent.CHALLENGE_DRILL, self.props.finishedDrillLevel)
end

function DrillComp:failedDrillLevel(levelId)
    self.props.drillDailyChance.count = math.max(0, self.props.drillDailyChance.count - 1)  -- 减少挑战机会次数
    self.challengeDrill = {}
    self.props.drillChallengeCount = self.props.drillChallengeCount + 1
    self:fireAvatarEvent(TableConst.enums.SAvatarEvent.CHALLENGE_DRILL, self.props.finishedDrillLevel)
end

function DrillComp:_taskGetDrillChallengeCount()
    return {num = self.props.drillChallengeCount}
end

function DrillComp:_taskGetDrillLevel(cond)
    -- 获取已完成的关卡等级，军演关卡ID确保是增序的，所以直接返回当前完成id
    local value = (self.props.finishedDrillLevel and (self.props.finishedDrillLevel >= cond.id)) and 1 or 0
    return {id = cond.id, value = value}
end

---@return BattleInfo
function DrillComp:genBattleInfo(levelId, battleIdx, inBattleSeq, guid)
    local levelConfig = GameCommon.TableDataManager:GetDrillLevel(levelId)
    local battleInfo = {
        gridName = levelConfig.name,
        position = -1,
        battleType = TableConst.enums.BattleType.PVE_DRILL,  -- 军演类型
        drillLevel = levelId,  -- 关卡ID
        battleIdx = battleIdx,  -- 当前战斗场次
        inBattleSeq = inBattleSeq,  -- 当前战斗序列
        guid = {
            [TableConst.enums.Camp.A] = self.challengeDrill.challengeId,
        },  -- 合并标识
    }
    return battleInfo
end

function DrillComp:isDrillChapterUnlocked(levelId)
    -- level到chapter的映射
    local thisChapterId = ProcessedTableEnv.DRILL.level2Chapter[levelId]
    local unlockDay = GameCommon.TableDataManager:GetDrillChapter(thisChapterId).unlock_day
    local cycleId, stageId, nextTime = EZGlobal.Schedule:getCurSchedule(TableConst.enums.ScheduleType.CommonDaily)
    return cycleId >= unlockDay
end

function DrillComp:isNextLevel(levelId)
    -- level前置关卡映射
    return ProcessedTableEnv.DRILL.level2LastLevel[levelId] == self.props.finishedDrillLevel
end

--endregion

--region 关卡宝箱

function DrillComp:GetDrillChapterBox(chapterId, boxIdx)
    local levelConfig = GameCommon.TableDataManager:GetDrillLevel(self.props.finishedDrillLevel)
    assert(levelConfig)
    if ProcessedTableEnv.DRILL.level2Chapter[self.props.finishedDrillLevel] ~= chapterId then
        -- 还在当前章节
        self:logInfo("[qun] Level %s does not belong to chapter %s", self.props.finishedDrillLevel, chapterId)
        return
    end
    local validBoxIdx = 0
    for idx, needLevel in ipairs(GameCommon.TableDataManager:GetDrillConst("CHAPTER_REWARD_UNLOCK")) do
        if needLevel <= (self.props.finishedDrillLevel % 100) then
            validBoxIdx = idx
        end
    end
    if boxIdx > validBoxIdx then
        self:logInfo("[qun] Box index %s exceeds valid range %s for chapter %s", boxIdx, validBoxIdx, chapterId)
        return
    end
    if self.props.drillChapterBox.chapterId > chapterId or (self.props.drillChapterBox.chapterId == chapterId and self.props.drillChapterBox.boxIdx >= boxIdx) then
        self:logInfo("[qun] Box %s in chapter %s is already claimed", boxIdx, chapterId)
        return
    end
    local rewardsList = {}
    local boxStartIdx = 1
    if self.props.drillChapterBox.chapterId == chapterId then
        boxStartIdx = self.props.drillChapterBox.boxIdx + 1
    end
    local chapterConfig = GameCommon.TableDataManager:GetDrillChapter(chapterId)
    for idx = boxStartIdx, validBoxIdx do
        table.insert(rewardsList, chapterConfig.rewards[idx])
    end
    assert(not table.isempty(rewardsList))
    self.props.drillChapterBox = {
        chapterId = chapterId,
        boxIdx = validBoxIdx,
    }
    self:AddReward(rewardsList, TableConst.enums.Reason.DRILL_CHAPTER_REWARD)
    self:clientRpc("OnGetDrillChapterBox", chapterId, boxIdx)
end

function DrillComp:GotoNextChapter(chapterId)
    local curChapterId = ProcessedTableEnv.DRILL.level2Chapter[self.props.finishedDrillLevel]
    if curChapterId + 1 ~= chapterId then
        self:logInfo("[qun] Can only go to the next chapter %s from current chapter %s", chapterId, curChapterId)
        return
    end
    if not GameCommon.TableDataManager:GetDrillChapter(chapterId) then
        self:logInfo("[qun] Chapter %s not found", chapterId)
        return
    end
    if self.props.drillChapterBox.chapterId ~= curChapterId or self.props.drillChapterBox.boxIdx ~= #GameCommon.TableDataManager:GetDrillChapter(curChapterId).rewards then
        self:logInfo("[qun] All boxes in chapter %s must be claimed before proceeding to chapter %s", curChapterId, chapterId)
        return
    end
    self.props.currentDrillChapter = chapterId
    self:clientRpc("OnGotoNextChapter", chapterId)
end

--endregion

--region 挂机奖励

function DrillComp:GetDrillHangingReward()
    local produce, hasNewProduce, lastTime, fixedRewardProcess = ProduceEnv.SettleDrillHangingReward(self.props.drillHangingBox)
    if not hasNewProduce then
        self:logInfo("[qun] No hanging rewards available")
        return
    end
    local addNum, leftNum = {}, {}
    for itemId, floatNum in pairs(produce) do
        local add, left = math.modf(floatNum)
        if add > 0 then
            addNum[itemId] = add
        end
        if left > 0 then
            leftNum[itemId] = left
        end
    end
    self.props.drillHangingBox.lastPickTime = lastTime
    self.props.drillHangingBox.lastSettleTime = lastTime
    self.props.drillHangingBox.fixedRewardProcess = fixedRewardProcess
    self.props.drillHangingBox.partProduceFloat = leftNum
    if not table.isnilorempty(addNum) then
        self:AddToBag(addNum, TableConst.enums.Reason.DRILL_HANGING_REWARD)
        self:clientRpc("OnGetDrillHangingReward", addNum)
    end
end

function DrillComp:InitDrillHangingBox()
    if self.props.drillHangingBox.lastSettleTime > 0 then
        -- 已经初始化过了
        return
    end
    local curTime = ServerTimeEnv.GetServerNow()
    self.props.drillHangingBox = {
        lastPickTime = curTime,
        lastSettleTime = curTime,
    }
end

function DrillComp:updateDrillProduceSpeed(speed)
    -- 结算当前部分奖励，并更新结算时间
    local produce, hasNewProduce, lastTime, fixedRewardProcess = ProduceEnv.SettleDrillHangingReward(self.props.drillHangingBox)
    if hasNewProduce then
        self.props.drillHangingBox.lastSettleTime = lastTime
        self.props.drillHangingBox.fixedRewardProcess = fixedRewardProcess
        self.props.drillHangingBox.partProduceFloat = produce
    end
    -- 更新生产速度
    self.props.drillHangingBox.speed = speed
end


--endregion

function DrillComp:gmCompleteDrill()
    local levelId = GameCommon.TableDataManager:GetDrillChapter(1).level_list[1]
    if self.props.finishedDrillLevel then
        for lv, lastLv in pairs(ProcessedTableEnv.DRILL.level2LastLevel) do
            if self.props.finishedDrillLevel == lastLv then
                levelId = lv
                break
            end
        end
    end
    self:completeDrillLevel(levelId)
end

function DrillComp:gmResetDrill()
    self.props.currentDrillChapter = 1
    self.props.finishedDrillLevel = nil
    self.props.drillChapterBox = {}
    self.props.drillHangingBox = {}
    self.props.drillDailyChance = {}
    self:InitDrillHangingBox()
    local cycleId, stageId, nextTime = EZGlobal.Schedule:getCurSchedule(TableConst.enums.ScheduleType.CommonDaily)
    self:OnDrillDailySchedule(cycleId)
end
