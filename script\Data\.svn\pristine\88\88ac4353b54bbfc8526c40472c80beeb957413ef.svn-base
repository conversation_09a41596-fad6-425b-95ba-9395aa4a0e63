{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode", "Field": {"event1": {"Type": "number", "Value": "109"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}, "event2": {"Type": "number", "Value": "5"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "False"}}}, "2": {"Type": "RandomNode", "Field": {"randomType": {"Type": "number", "Value": "1"}, "useFormula": {"Type": "boolean", "Value": "False"}, "probability": {"Type": "number", "Value": "0.8"}, "probabilityFormula": {"Type": "string", "Value": ""}, "record": {"Type": "boolean", "Value": "False"}, "integer": {"Type": "boolean", "Value": "False"}, "min": {"Type": "number", "Value": "0"}, "max": {"Type": "number", "Value": "0"}, "minFormula": {"Type": "string", "Value": ""}, "maxFormula": {"Type": "string", "Value": ""}, "numberResult": {"Type": "number", "Value": "0"}, "byWeight": {"Type": "boolean", "Value": "False"}, "idList": [], "weights": [], "count": {"Type": "number", "Value": "1"}, "result": []}}, "3": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "EventSourceId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "True"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "4": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetIds": [], "buffId": {"Type": "number", "Value": "10010"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "5": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": "0.6"}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "6": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "TargetId"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "7": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": "0.6+lv*0.06"}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "8": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "0"}, "key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "True"}, "showName": {"Type": "string", "Value": "刚烈次数"}, "percent": {"Type": "boolean", "Value": "False"}}}, "10": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "11": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "2"}, "number2": {"Type": "number", "Value": "1"}}}, "13": {"Type": "SetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "input": {"Type": "nil", "Value": "null"}, "record": {"Type": "boolean", "Value": "True"}, "showName": {"Type": "string", "Value": "刚烈次数"}, "percent": {"Type": "boolean", "Value": "False"}}}, "14": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "15": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": ""}, "formulaItems": [[{"Type": "string", "Value": "a+1"}, {"Type": "number", "Value": 3.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "0"}, "noLeftBoundary": {"Type": "boolean", "Value": "False"}, "noRightBoundary": {"Type": "boolean", "Value": "False"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "16": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "other1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "刚烈生效概率"}, "result": {"Type": "number", "Value": "0"}}}, "17": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "刚烈伤害系数"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"AfterBeDamagedNode": ["16.prev"], "RoundPrepareNode": ["8.prev"]}, "2": {"next": ["10.prev"]}, "3": {"next": ["4.prev"]}, "4": {"next": ["6.prev"]}, "6": {"next": ["17.prev"]}, "10": {"next": ["11.prev"]}, "11": {"next": ["3.prev"]}, "13": {"next": ["5.prev"]}, "14": {"next": ["15.prev"]}, "15": {"next": ["13.prev"]}, "16": {"next": ["2.prev"]}, "17": {"next": ["14.prev"]}}, "DataFlows": {"3": {"targetIds": ["4.targetIds", "5.targetIds"]}, "6": {"attributeValues": ["17.a"]}, "10": {"output": ["11.number1"]}, "14": {"output": ["15.a"]}, "15": {"result": ["13.input"]}, "16": {"result": ["2.probability"]}, "17": {"result": ["5.damageFactor"]}}}