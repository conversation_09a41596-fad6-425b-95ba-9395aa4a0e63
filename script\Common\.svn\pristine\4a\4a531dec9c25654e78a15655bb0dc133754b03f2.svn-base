﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")

ERR_ALREAY_INFORMATION = -1

function CloneFormations(formations)
    local ret = {}
    if formations ~= nil then
        for teamIndex,formation in pairs(formations) do
            local newFormation = CloneFormation(formation)
            ret[teamIndex] = newFormation
        end
    end
    return ret
end

function CloneFormation(formation)
    if formation == nil then
        return nil
    end

    local ret = {}
    for posId,slotInfo in pairs(formation) do
        local newSlotInfo = {}
        newSlotInfo.heroId = slotInfo.id or slotInfo.heroId
        newSlotInfo.uniqueId = slotInfo.uid or slotInfo.uniqueId

        ret[posId] = newSlotInfo
    end
    return ret
end

function TransferFormations2WebFormat(formations)
    local ret = {}
    for teamIndex,formation in pairs(formations) do
        if not IsFormationEmpty(formation) then
            local newForamtion = TransferFormation2WebFormat(formation)
            ret[teamIndex] = newForamtion
        end
    end
    return ret
end

function TransferFormation2WebFormat(formation)
    local ret = {}
    for posId,slotInfo in pairs(formation) do
        local newSlotInfo = {}
        newSlotInfo.id = slotInfo.heroId or slotInfo.id
        newSlotInfo.uid = slotInfo.uniqueId or slotInfo.uid
        ret[posId] = newSlotInfo
    end
    return ret
end

--- @param battleFormationData BattleFormationData 来自avatar.props.battleFormations 见Avatar.json和alias.json
--- @param completedHeroes CompletedHeroes avatar.props.completedHeroes 见Avatar.json
function TransferBattleFormationData2WebFormat(battleFormationData, completedHeroes)
    local ret = {}
    for index, completedHeroUid in pairs(battleFormationData.heroes) do
        local heroData = completedHeroes[completedHeroUid]
        ret[index] = {id = heroData.templateHeroId, uid = completedHeroUid}
    end
    return ret
end

function TransferBattleFormationDataDict2WebFormat(battleFormationDataDict, completedHeroes)
    local ret = {}
    for teamIndex, battleFormationData in pairs(battleFormationDataDict) do
        ret[teamIndex] = TransferBattleFormationData2WebFormat(battleFormationData, completedHeroes)
    end
    return ret
end

function InsertHero2Formations(formations, heroId, teamIndex, posId)
    DeleteHeroFromFormations(formations, heroId)

    local formation = formations[teamIndex]
    if formation == nil then
        formation = {}
        formations[teamIndex] = formation
    end

    InsertHero2Formation(formation, heroId, posId)
end

-- 因为被插入武将未必是该formation的武将，所以，单一formation的插入不做事先的delete操作
function InsertHero2Formation(formation, heroId, posId)
    local newSlotInfo = formation[posId]
    if newSlotInfo == nil then
        newSlotInfo = {}
        formation[posId] = newSlotInfo
    end

    if newSlotInfo.heroId == heroId then
        return ERR_ALREAY_INFORMATION
    end

    newSlotInfo.posId = posId
    newSlotInfo.heroId = heroId
end

function InsertHero2FormationsOrdered(formations, heroId, teamIndex)
    DeleteHeroFromFormations(formations, heroId)

    local formation = formations[teamIndex]
    if formation == nil then
        formation = {}
        formations[teamIndex] = formation
    end

    local posId = InsertHero2FormationOrdered(formation, heroId)
    if posId then
        return teamIndex, posId
    end
end

function InsertHero2FormationOrdered(formation, heroId)
    local pos = GetHeroPosInFormation(formation, heroId)
    if pos ~= nil then
        return ERR_ALREAY_INFORMATION
    end

    for n=1,BattleConstEnv.TEAM_SLOT_NUM do
        if IsTeamSlotEmpty(formation[n]) then
            local newSlotInfo = {}
            newSlotInfo.posId = n
            newSlotInfo.heroId = heroId
            formation[n] = newSlotInfo
            return n
        end
    end
end

function ExchangeHeroPosInFormation(formation, pos1, pos2)
    local heroId1 = GetHeroIdInFormation(formation, pos1)
    local heroId2 = GetHeroIdInFormation(formation, pos2)

    DeleteHeroFromFormationByPos(formation, pos1)
    DeleteHeroFromFormationByPos(formation, pos2)

    if heroId1 then
        InsertHero2Formation(formation, heroId1, pos2)
    end

    if heroId2 then
        InsertHero2Formation(formation, heroId2, pos1)
    end
end

function ExchangeFormationIndexInFormations(formations, index1, index2)
    local formation1 = formations[index1]
    local formation2 = formations[index2]

    formations[index1] = formation2
    formations[index2] = formation1
end

function DeleteHeroFromFormations(formations, heroId)
    local teamIndex, posId = GetHeroPosInFormations(formations, heroId)
    if teamIndex ~= nil and posId ~= nil then
        formations[teamIndex][posId] = nil
    end
end

function DeleteHeroFromFormation(formation, heroId)
    local posId = GetHeroPosInFormation(formation, heroId)
    if posId ~= nil then
        DeleteHeroFromFormationByPos(formation, posId)
    end
end

function DeleteHeroFromFormationByPos(formation, posId)
    if formation ~= nil then
        formation[posId] = nil
    end
end

function GetHeroPosInFormations(formations, heroId)
    for teamIndex, formation in pairs(formations) do
        local posId = GetHeroPosInFormation(formation, heroId)
        if posId ~= nil then
            return teamIndex, posId
        end
    end
end

function GetHeroPosInFormation(formation, heroId)
    if formation ~= nil then
        for posId, slotInfo in pairs(formation) do
            if slotInfo.heroId == heroId then
                return posId
            end
        end
    end
end

function GetHeroCntInFormation(formation)
    if formation == nil then
        return 0
    end

    local cnt = 0
    for n=1, BattleConstEnv.TEAM_SLOT_NUM do
        local v = formation[n]
        if v and v.heroId then
            cnt = cnt + 1
        end
    end
    return cnt
end

function GetHeroIdInFormations(formations, teamIndex, posId)
    local formation = formations[teamIndex]
    return GetHeroIdInFormation(formation, posId)
end

function GetHeroIdInFormation(formation, posId)
    local slotInfo = formation and formation[posId]
    return slotInfo and slotInfo.heroId
end

function IsTeamSlotEmpty(teamSlotInfo)
    return teamSlotInfo == nil or teamSlotInfo.heroId == nil
end

function IsFormationEmpty(formation)
    local cnt = GetHeroCntInFormation(formation)
    return cnt == 0
end

function IsFormationSlotEmpty(formation, posId)
    local slotInfo = formation and formation[posId]
    return IsTeamSlotEmpty(slotInfo)
end

function IsHeroInFormations(formations, heroId)
    local teamIndex, posId = GetHeroPosInFormations(formations, heroId)
    return teamIndex ~= nil and posId ~= nil
end

function IsHeroInFormation(formation, heroId)
    local posId = GetHeroPosInFormation(formation, heroId)
    return posId ~= nil
end

function GetHeroList(formations)
    local retTbl = {}

    local rawHeroDatas = GameCommon.TableDataManager:GetTemplateHeroDatas()
    for _, rawData in pairs(rawHeroDatas) do
        local heroId = rawData.id
        local teamIndex, pos = GetHeroPosInFormations(formations, heroId)

        local newData = {}
        newData.heroId = heroId
        -- newData.uniqueId = self:GetCompletedId(heroId)
        newData.teamIndex = teamIndex
        newData.pos = pos

        table.insert(retTbl, newData)
    end

    return retTbl
end

function SortedHeroList(HeroList, teamIndex)
    table.sort(HeroList, function(a, b)
        return CompareHeroes(a, b, teamIndex)
    end)

    return HeroList
end

function GetSortedHeroList(formations, teamIndex)
    local retTbl = GetHeroList(formations) ---@type table

    table.sort(retTbl, function(a, b)
        return CompareHeroes(a, b, teamIndex)
    end)

    return retTbl
end

function CompareHeroes(a, b, teamIndex)
    -- 当前阵容中的武将排在前面
    if a.teamIndex == teamIndex and b.teamIndex ~= teamIndex then
        return true
    end

    if a.teamIndex ~= teamIndex and b.teamIndex == teamIndex then
        return false
    end

    -- 按照队伍位置排序
    if a.pos and b.pos then
        if a.pos < b.pos then
            return true
        end
        if a.pos > b.pos then
            return false
        end
    end

    -- 有记忆卡的武将排在前面
    if a.uniqueId == nil and b.uniqueId ~= nil then
        return false
    end
    if a.uniqueId ~= nil and b.uniqueId == nil then
        return true
    end

    return false
end

SORTING_TYPE = {
    rarity = 1,
    rating = 2
}

function GetSortedTemplateHeroListBySortingType(sortingType)
    local templateHeroesData = GameCommon.TableDataManager:GetTemplateHeroDatas() ---@type table
    local retList = {}
    for _, heroData in pairs(templateHeroesData) do
        table.insert(retList, heroData)
    end
    table.sort(retList, function(a, b)
        if sortingType == SORTING_TYPE.rarity then
            return a.rarity > b.rarity
        elseif sortingType == SORTING_TYPE.rating then
            return a.rating > b.rating
        end
    end)
    return retList
end

---@param staminaDetail HeroStaminaDetail
---@param staminaMax int
---@param nowTime number
function CalcStaminaRecover(staminaDetail, staminaMax, nowTime)
    local val = staminaDetail.val
    local recoverCount, exTime = math.modf(math.max(0, nowTime - staminaDetail.lastRecover) / Config.Slg.STAMINA_RECOVER_TIME)
    if val + recoverCount >= staminaMax then
        return staminaMax, nowTime
    end
    return val + recoverCount, nowTime - exTime * Config.Slg.STAMINA_RECOVER_TIME
end

---@param armyData ArmyData
---@param staminaMax int
---@param nowTime number
function GetMinStamina(armyData, staminaMax, nowTime)
    local minStamina, maxLastRecover = nil, 0.0
    local ret = nil
    for _, unit in pairs(armyData.heroes) do
        local curStamina, curRecover = CalcStaminaRecover(unit.staminaDetail, staminaMax, nowTime)
        if minStamina == nil or curStamina < minStamina then
            minStamina = curStamina
            maxLastRecover = curRecover
            ret = unit.staminaDetail
        elseif minStamina == curStamina and curRecover > maxLastRecover then
            maxLastRecover = curRecover
            ret = unit.staminaDetail
        end
    end
    if ret then
        return {
            val = ret.val,
            lastRecover = ret.lastRecover
        }
    end
    return nil
end

---@param armyMinLevel int
---@param battleType int
---@param exInfo table
function CalcPVEStaminaCost(armyMinLevel, battleType, exInfo)
    local config = GameCommon.TableDataManager:GetStaminaCostConfig(battleType)
    if not config then
        return 0
    end
    local rule = nil   ---@type battle.STAMINA_COST?
    for _, cfg in ipairs(config.rule) do
        if cfg.self_level < armyMinLevel then
            rule = cfg
        else
            break
        end
    end
    if not rule then
        return 0
    end
    if battleType == TableConst.enums.BattleType.PVE_LAND and exInfo.landLevel then
        local cost = rule.enemy_level_to_cost[exInfo.landLevel]
        return cost or 0
    end
    return 0
end

---@param armyData ArmyData
function GetArmyMinLevel(armyData)
    local armyLevel = nil
    for _, unit in pairs(armyData.heroes) do
        armyLevel = armyLevel and math.min(armyLevel, unit.level) or unit.level
    end
    return armyLevel or 0
end

return {
    CalcStaminaRecover = CalcStaminaRecover,
    GetMinStamina = GetMinStamina,
    CalcPVEStaminaCost = CalcPVEStaminaCost,
    GetArmyMinLevel = GetArmyMinLevel,
}