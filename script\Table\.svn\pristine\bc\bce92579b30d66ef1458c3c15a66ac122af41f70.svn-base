-- Excel: 模拟三国杀/公式/系数公式索引表.xlsx
-- Table Type: 列表

return
{
{name="buff_100000",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"2+2*lv",},},
{name="buff_100004",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+0.5*lv",},},
{name="buff_10006",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.3",},},
{name="buff_10100101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"18+lv*2",},},
{name="buff_10100201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.18-lv*0.02",},},
{name="buff_10100301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.045-lv*0.005",},},
{name="buff_10100401",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.9+lv*0.1",},},
{name="buff_10100402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.15",},},
{name="buff_10100501",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.27+0.03*lv",},},
{name="buff_10100502",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="buff_10100502",key="other2",var_name="",final_min=-10000,final_max=10000,formulas={"0.45",},},
{name="buff_10200301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-18-lv*2-a*0.02",},},
{name="buff_10200601",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.12+lv*0.02",},},
{name="buff_10200801",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.58+lv*0.06",},},
{name="buff_10200901",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.9+lv*0.09",},},
{name="buff_10300101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-72-lv*8",},},
{name="buff_10400101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.35+lv*0.05",},},
{name="buff_10400101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"-100",},},
{name="buff_10400102",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"-0.1",},},
{name="buff_10400102",key="attr4",var_name="",final_min=-10000,final_max=10000,formulas={"10",},},
{name="buff_10500102",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.23+lv*0.03",},},
{name="buff_10500201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.09+lv*0.01",},},
{name="buff_10500202",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.05+lv*0.005",},},
{name="buff_10600101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.12+lv*0.02",},},
{name="buff_10600101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.2",},},
{name="buff_10600201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.045+lv*0.005",},},
{name="buff_10600202",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="buff_10600203",key="heal2",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="buff_10600204",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"18+lv*2",},},
{name="buff_11100102",key="other2",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_11100302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-9-lv-a*0.1",},},
{name="buff_11100303",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"9+lv+a*0.1",},},
{name="buff_11100401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.05",},},
{name="buff_11100402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"10",},},
{name="buff_11100403",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"10",},},
{name="buff_11100502",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.2",},},
{name="buff_11100502",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.72+lv*0.08",},},
{name="buff_11100502",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.72+lv*0.08",},},
{name="buff_11100503",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15",},},
{name="buff_11200101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.2",},},
{name="buff_11200302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.1",},},
{name="buff_11200501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.035+lv*0.005",},},
{name="buff_11300101",key="other3",var_name="",final_min=-10000,final_max=10000,formulas={"-0.1",},},
{name="buff_11300102",key="other4",var_name="",final_min=-10000,final_max=10000,formulas={"-100",},},
{name="buff_11300201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.03",},},
{name="buff_11400102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5",},},
{name="buff_11400102",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.3",},},
{name="buff_11400103",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"-0.2",},},
{name="buff_11400301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.7-a*0.02",},},
{name="skill_10200101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.54+lv*0.06",},},
{name="skill_10200101",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.27+lv*0.03",},},
{name="skill_10200201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.05",},},
{name="skill_10200401",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"1.1+lv*0.1+a*0.02",},},
{name="skill_10200501",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.58+lv*0.06+a*0.01",},},
{name="skill_10200601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.66+lv*0.06",},},
{name="skill_10200701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.26+0.12",},},
{name="skill_10200701",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.4+lv*0.05",},},
{name="skill_10200901",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+0.05*lv",},},
{name="skill_10201001",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.045",},},
{name="skill_10300101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.72+lv*0.08",},},
{name="skill_10300201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.9+lv*0.1",},},
{name="skill_10300301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.18+lv*0.02",},},
{name="skill_10300301",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="skill_11100101",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05+a*0.01",},},
{name="skill_11100101",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15",},},
{name="skill_11100301",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8",},},
{name="skill_11200101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.36+lv*0.036",},},
{name="skill_11300101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.3+lv*0.12",},},
{name="skill_11300101",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"1.2+lv*0.12",},},
{name="skill_11400401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.36+lv*0.04",},},
{name="skill_11400401",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8",},},
{name="skill_999999",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1",},},
}