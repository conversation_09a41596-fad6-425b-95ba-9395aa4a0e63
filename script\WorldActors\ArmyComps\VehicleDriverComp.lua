local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")

---@class (partial) Army
---@class VehicleDriverComp : ComponentBase
VehicleDriverComp = DefineClass("VehicleDriverComp", ComponentBaseEnv.ComponentBase)

---@diagnostic disable: duplicate-set-field
function VehicleDriverComp:ctor()
    
end

function VehicleDriverComp:CmdOperateVehicle(cmdUid, targetPos, targetEid)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.OperateVehicle) then
        return
    end
    local moveType = TableConst.enums.MoveType.Default
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}},
        {action="ArmyOperateVehicle", params={entityId=targetEid}},
    })
end

function VehicleDriverComp:OnArmyDefeated()
    local vehicle = self:GetInCon(TableConst.enums.InConType.InVehicle)  ---@type WorldBuildingType?
    if vehicle then
        vehicle:forceOperatorLeave()
    end
end

function VehicleDriverComp:CmdThrowStone(cmdUid, targetEid)
    local target = self.space:getActorByEid(targetEid)
    local vehicle = self:GetInCon(TableConst.enums.InConType.InVehicle)
    if not self:ValidThrowStone(vehicle, target) then
        return
    end
    local remainTime = GameCommon.TableDataManager:GetSlgBuildingConst("STONE_VEHICLE_ATTACK_TIME")
    self:StartCmd(cmdUid, {
        {action="ArmyThrowStone", params={target=target.props.uid, remainTime=remainTime, startTime=0, timerId=0}},
    })
end

function VehicleDriverComp:doThrowStone(targetUid)
    local target = self:getActor(targetUid)
    local vehicle = self:GetInCon(TableConst.enums.InConType.InVehicle)
    if not self:ValidThrowStone(vehicle, target) then
        return false
    end
    local vehicle = self:GetInCon(TableConst.enums.InConType.InVehicle)   ---@type StoneVehicle
    local damage = GameCommon.TableDataManager:GetSlgBuildingConst("STONE_VEHICLE_ATTACK_DAMAGE")
    local succ, trueConsume, duraLeft = target:TakeRemoteDamage(damage, self)
    if not succ then
        self:logInfo("doThrowStone: target take damage failed")
        return false
    end
    self:GenerateConsumeDuraBattleRecord(target, trueConsume, duraLeft <= 0)
    vehicle.props.stoneNum = math.max(0, vehicle.props.stoneNum - 1)
    if vehicle.props.stoneNum <= 0 then
        vehicle:DestroyBy(self.id, "StoneUsedOut", true)
    end
    return true
end
