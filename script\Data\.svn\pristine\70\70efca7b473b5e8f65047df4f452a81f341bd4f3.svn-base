{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode"}, "1": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "desc": {"Type": "string", "Value": "折冲御侮统帅降低"}, "result": {"Type": "number", "Value": "0"}}}, "2": {"Type": "ModifyAttributeNode", "Field": {"heroIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "attributeType": {"Type": "number", "Value": "4"}, "modifierType": {"Type": "number", "Value": "1"}, "value": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"onLayerIncreased": ["1.prev"]}, "1": {"next": ["2.prev"]}}, "DataFlows": {"1": {"result": ["2.value"]}}}