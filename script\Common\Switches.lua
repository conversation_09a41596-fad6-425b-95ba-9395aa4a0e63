﻿
IsRelease = false

function InitSwitches()
    local tblSwitch = GameCommon.TableDataManager:GetTable("common_tbswitch")
    local useRelease = IsRelease
    local env = getfenv(1)
    for k, v in pairs(tblSwitch) do
        local value = v.value
        local relValue = v.release_value
        if useRelease and relValue ~= nil then
            value = relValue
        end
        env[k] = value
    end
end

function GetSwitchDefault(switchName)
    local tblSwitch = GameCommon.TableDataManager:GetTable("common_tbswitch")
    assert(tblSwitch[switchName], "Switch not found: " .. switchName)
    if IsRelease and tblSwitch[switchName].release_value ~= nil then
        return tblSwitch[switchName].release_value
    else
        return tblSwitch[switchName].value
    end
end

InitSwitches()
