-- Excel: 模拟三国杀/Buff列表@战斗buff.xlsx
-- Table Type: 映射表
-- Index: id

return
{
[10002] = {id=10002,name="计穷",desc="负面状态，无法使用主动战法",show=true,record=true,json_file="buff_10002",type=1,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff2",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加乐",jump_on_remove="移除乐",max_combat=0,buff_source=0,battle_white_list={},},
[10003] = {id=10003,name="缴械",desc="无法普攻",show=true,record=true,json_file="buff_10003",type=1,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff3",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加缴械",jump_on_remove="移除缴械",max_combat=0,buff_source=0,battle_white_list={},},
[10004] = {id=10004,name="震慑",desc="无法行动",show=true,record=true,json_file="buff_10004",type=1,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff1",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=10010,effect_socket="HpPoint",jump_on_add="添加震慑",jump_on_remove="移除震慑",max_combat=0,buff_source=0,battle_white_list={},},
[10006] = {id=10006,name="虚弱",desc="负面状态，造成伤害减少70%",show=true,record=true,json_file="buff_10006",type=1,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加粮",jump_on_remove="移除粮",max_combat=0,buff_source=0,battle_white_list={},},
[10007] = {id=10007,name="断粮",desc="负面状态，受到治疗效果降低70%",show=true,record=true,json_file="buff_10007",type=1,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加粮",jump_on_remove="移除粮",max_combat=0,buff_source=0,battle_white_list={},},
[10008] = {id=10008,name="嘲讽",desc="强制攻击目标",show=true,record=true,json_file="buff_10008",type=1,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加嘲讽",jump_on_remove="移除嘲讽",max_combat=0,buff_source=0,battle_white_list={},},
[10009] = {id=10009,name="抵御",desc="受到的下一次伤害减少70%-100%  可叠加2层",show=true,record=true,json_file="buff_10009",type=3,total_round=1,can_stack=true,stack_rule=1,max_stack_count=2,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加抵御",jump_on_remove="移除抵御",max_combat=0,buff_source=0,battle_white_list={},},
[10010] = {id=10010,name="威慑",desc="下一次主动或追击战法目标优先选择Buff来源且造成伤害降低35%",show=true,record=true,json_file="buff_10010",type=1,total_round=1,can_stack=true,stack_rule=1,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加威慑",jump_on_remove="移除威慑",max_combat=0,buff_source=0,battle_white_list={},},
[10011] = {id=10011,name="混乱",desc="主动战法、追击战法、普通攻击会选择除自己以外的所有人",show=true,record=true,json_file="buff_10011",type=1,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加混乱",jump_on_remove="移除混乱",max_combat=0,buff_source=0,battle_white_list={},},
[10012] = {id=10012,name="必中",desc="你造成的伤害无视抵御、闪避",show=true,record=true,json_file="buff_10012",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10013] = {id=10013,name="神医",desc="治疗效果提升30%，每次治疗消耗1层，最多叠加9层",show=true,record=true,json_file="buff_10013",type=3,total_round=9,can_stack=true,stack_rule=2,max_stack_count=9,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10014] = {id=10014,name="先机",desc="优先行动，若都有先机则根据速度决定行动顺序",show=true,record=true,json_file="buff_10014",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10015] = {id=10015,name="破阵",desc="获得50%的破甲与看破",show=true,record=true,json_file="buff_10015",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10016] = {id=10016,name="坚守",desc="受到的伤害降低25%",show=true,record=true,json_file="buff_10016",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10017] = {id=10017,name="洞察",desc="免疫负面状态",show=true,record=true,json_file="buff_10017",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10018] = {id=10018,name="溅射",desc="普攻会额外对另外2人造成30%伤害",show=true,record=true,json_file="buff_10018",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10019] = {id=10019,name="机敏",desc="使下一个释放的非自带主动技能必定成功",show=true,record=true,json_file="buff_10019",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10020] = {id=10020,name="援助",desc="受到伤害时有概率由队友承担（受目标当前兵力影响）",show=true,record=true,json_file="buff_10020",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10021] = {id=10021,name="援助[预备]",desc="60%概率（受自身当前兵力影响）替队友承担本次伤害",show=true,record=true,json_file="buff_10021",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10022] = {id=10022,name="怯阵",desc="负面状态，统率降低50点",show=true,record=true,json_file="buff_10022",type=1,total_round=1,can_stack=true,stack_rule=1,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="添加威慑",jump_on_remove="移除威慑",max_combat=0,buff_source=0,battle_white_list={},},
[10023] = {id=10023,name="连弩",desc="连弩获得连击",show=true,record=true,json_file="buff_10023",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10024] = {id=10024,name="攻势",desc="造成伤害提升25%",show=true,record=true,json_file="buff_10024",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10101] = {id=10101,name="火攻",desc="火攻-通用状态",show=true,record=true,json_file="buff_10101",type=2,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff4",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={1,},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10201] = {id=10201,name="雷击",desc="张角-雷击-雷击（持续状态可叠加）",show=true,record=true,json_file="buff_10201",type=2,total_round=9,can_stack=true,stack_rule=2,max_stack_count=3,overlay_rule=2,icon_path="icon-buff4",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={3,},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10301] = {id=10301,name="水攻",desc="水攻-通用状态",show=true,record=true,json_file="buff_10301",type=2,total_round=2,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff4",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={2,},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[12333] = {id=12333,name="额外军职-主公",desc="在考虑额外军职时视为主公",show=true,record=true,json_file="buff_12333",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[20001] = {id=20001,name="不屈",desc="伤害分摊",show=true,record=true,json_file="buff_20001",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="免死",jump_on_remove="移除粮",max_combat=0,buff_source=0,battle_white_list={},},
[10100101] = {id=10100101,name="忠勇[预备]",desc="忠勇加属性",show=true,record=true,json_file="buff_10100101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100201] = {id=10100201,name="严阵[预备]",desc="严阵减伤",show=true,record=true,json_file="buff_10100201",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100301] = {id=10100301,name="亲民减伤",desc="亲民减伤",show=true,record=true,json_file="buff_10100301",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=4,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100302] = {id=10100302,name="亲民[预备]",desc="亲民减伤",show=true,record=true,json_file="buff_10100302",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100401] = {id=10100401,name="五禽戏[预备]",desc="五禽戏[预备]",show=true,record=true,json_file="buff_10100401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100402] = {id=10100402,name="五禽戏减伤",desc="五禽戏减伤",show=true,record=true,json_file="buff_10100402",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100501] = {id=10100501,name="请君入瓮[预备]",desc="请君入瓮[预备]",show=true,record=true,json_file="buff_10100501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100502] = {id=10100502,name="请君入瓮",desc="请君入瓮控制",show=true,record=true,json_file="buff_10100502",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100701] = {id=10100701,name="测试闪避",desc="测试闪避",show=true,record=true,json_file="buff_10100701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180101] = {id=10180101,name="应援[预备]",desc="应援回合末恢复友军兵力",show=true,record=true,json_file="buff_10180101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180201] = {id=10180201,name="潜袭[预备]",desc="潜袭对敌军造成伤害",show=true,record=true,json_file="buff_10180201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180301] = {id=10180301,name="毅烈[预备]",desc="毅烈获得连弩或者必中",show=true,record=true,json_file="buff_10180301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180401] = {id=10180401,name="夺刀[预备]",desc="夺刀计穷并造成兵刃伤害",show=true,record=true,json_file="buff_10180401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180501] = {id=10180501,name="定仪[预备]",desc="定仪造成谋略伤害并降低主动概率",show=true,record=true,json_file="buff_10180501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180601] = {id=10180601,name="止戈[预备]",desc="自身行动时使随机单体对敌方随机单体发动普攻",show=true,record=true,json_file="buff_10180601",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180701] = {id=10180701,name="狼袭[预备]",desc="狼袭造成兵刃伤害",show=true,record=true,json_file="buff_10180701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180801] = {id=10180801,name="伺盗[预备]",desc="伺盗敌方受伤增加并施加断粮",show=true,record=true,json_file="buff_10180801",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180802] = {id=10180802,name="伺盗标记buff",desc="用于标记伤害次数",show=false,record=false,json_file="buff_10180802",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180901] = {id=10180901,name="悍勇[预备]",desc="悍勇加buff",show=true,record=true,json_file="buff_10180901",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10180902] = {id=10180902,name="悍勇",desc="受到物理伤害降低",show=true,record=true,json_file="buff_10180902",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181001] = {id=10181001,name="雉盗[预备]",desc="稚盗加buff",show=true,record=true,json_file="buff_10181001",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181002] = {id=10181002,name="雉盗",desc="自己属性被偷取，友军受到伤害减少",show=true,record=false,json_file="buff_10181002",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181101] = {id=10181101,name="当先[预备]",desc="提升武力属性，回合开始概率获得先机",show=true,record=true,json_file="buff_10181101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181201] = {id=10181201,name="链祸[预备]",desc="偶数回合恢复兵力，自身受到伤害时对友军造成传递伤害",show=true,record=true,json_file="buff_10181201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181301] = {id=10181301,name="衅刃[预备]",desc="友军受到普攻时对攻击目标造成兵刃伤害",show=true,record=true,json_file="buff_10181301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181401] = {id=10181401,name="后殁[预备]",desc="对已行动的敌军目标造成谋略伤害，本回合每有一个敌方目标行动增加伤害率",show=true,record=true,json_file="buff_10181401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181601] = {id=10181601,name="揖让[预备]",desc="揖让[预备]",show=true,record=true,json_file="buff_10181601",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10181602] = {id=10181602,name="揖让[预备]",desc="移交智力属性、治疗自身",show=true,record=true,json_file="buff_10181602",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10200301] = {id=10200301,name="散谣[预备]",desc="散谣减属性",show=true,record=true,json_file="buff_10200301",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10200601] = {id=10200601,name="一箭双雕[预备]",desc="一箭双雕加暴击",show=true,record=true,json_file="buff_10200601",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10200801] = {id=10200801,name="自愈",desc="自愈回血",show=true,record=true,json_file="buff_10200801",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10200901] = {id=10200901,name="诱敌深入",desc="诱敌深入",show=true,record=false,json_file="buff_10200901",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10202001] = {id=10202001,name="灵机一动[预备]",desc="灵机一动加智力",show=true,record=true,json_file="buff_10202001",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10280301] = {id=10280301,name="援护[预备]",desc="援护加统率",show=true,record=true,json_file="buff_10280301",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10280501] = {id=10280501,name="秉正[增伤]",desc="秉正己方增伤",show=true,record=true,json_file="buff_10280501",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10280601] = {id=10280601,name="急救[预备]",desc="急救受到伤害概率恢复兵力",show=true,record=true,json_file="buff_10280601",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10280701] = {id=10280701,name="火攻",desc="焚营-火攻",show=true,record=true,json_file="buff_10280701",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10300101] = {id=10300101,name="短兵[预备]",desc="短兵减统率",show=true,record=true,json_file="buff_10300101",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400101] = {id=10400101,name="一鼓作气[预备]",desc="一鼓作气加连击",show=true,record=true,json_file="buff_10400101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400102] = {id=10400102,name="一鼓作气衰减[预备]",desc="一鼓作气每回合减连击",show=true,record=true,json_file="buff_10400102",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=10,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400301] = {id=10400301,name="大器晚成[预备]",desc="大器晚成[预备]",show=true,record=true,json_file="buff_10400301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400302] = {id=10400302,name="大器晚成[名望]",desc="大器晚成[名望]",show=true,record=true,json_file="buff_10400302",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400401] = {id=10400401,name="士别三日[预备]",desc="士别三日[预备]",show=true,record=true,json_file="buff_10400401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400501] = {id=10400501,name="贯阵摧营[预备]",desc="贯阵摧营[预备]",show=true,record=true,json_file="buff_10400501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400601] = {id=10400601,name="舌战群儒[预备]",desc="舌战群儒[预备]",show=true,record=true,json_file="buff_10400601",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10480101] = {id=10480101,name="司敌[预备]",desc="司敌受伤害加计穷",show=true,record=true,json_file="buff_10480101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10480201] = {id=10480201,name="倾袭[预备]",desc="倾袭加普攻伤害",show=true,record=true,json_file="buff_10480201",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10480202] = {id=10480202,name="倾袭[普攻增伤]",desc="倾袭加普攻伤害",show=true,record=true,json_file="buff_10480202",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500101] = {id=10500101,name="虎臣枪卫[预备]",desc="检测buff",show=true,record=true,json_file="buff_10500101",type=4,total_round=4,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500102] = {id=10500102,name="虎臣枪卫-暴击[预备]",desc="加暴击",show=true,record=true,json_file="buff_10500102",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500301] = {id=10500301,name="藤甲锐士[预备]",desc="检测buff",show=true,record=true,json_file="buff_10500301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500401] = {id=10500401,name="青州兵[治疗]",desc="治疗",show=true,record=true,json_file="buff_10500401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500402] = {id=10500402,name="青州兵[预备]",desc="加武力",show=true,record=true,json_file="buff_10500402",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600101] = {id=10600101,name="锋矢阵[预备]",desc="锋矢阵先锋增伤",show=true,record=true,json_file="buff_10600101",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600102] = {id=10600102,name="锋矢阵[预备]",desc="锋矢阵主将减伤",show=true,record=true,json_file="buff_10600102",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600301] = {id=10600301,name="鹤翼阵[预备]",desc="鹤翼阵[预备]",show=true,record=true,json_file="buff_10600301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600302] = {id=10600302,name="鹤翼阵[预备]",desc="鹤翼阵[预备]",show=true,record=true,json_file="buff_10600302",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600303] = {id=10600303,name="鹤翼阵[选目标]",desc="鹤翼阵[预备]",show=true,record=true,json_file="buff_10600303",type=4,total_round=6,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600401] = {id=10600401,name="风扬阵[预备]",desc="风扬阵[预备]",show=true,record=true,json_file="buff_10600401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600402] = {id=10600402,name="风扬阵[预备增伤]",desc="风扬阵[预备增伤]",show=true,record=false,json_file="buff_10600402",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=8,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600403] = {id=10600403,name="风扬阵[减伤]",desc="风扬阵[减伤]",show=true,record=true,json_file="buff_10600403",type=4,total_round=4,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10700101] = {id=10700101,name="辕门射戟[预备]",desc="辕门射戟[预备]",show=true,record=true,json_file="buff_10700101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10700102] = {id=10700102,name="辕门射戟[普攻增伤]",desc="辕门射戟[普攻增伤]",show=true,record=false,json_file="buff_10700102",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100101] = {id=11100101,name="仁德[预备]",desc="仁德[预备]",show=false,record=true,json_file="buff_11100101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100102] = {id=11100102,name="仁德[武]",desc="仁德[武]",show=false,record=true,json_file="buff_11100102",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=8,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100103] = {id=11100103,name="仁德[统]",desc="仁德[统]",show=false,record=true,json_file="buff_11100103",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100104] = {id=11100104,name="激将[预备]",desc="邀请普攻",show=false,record=true,json_file="buff_11100104",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100105] = {id=11100105,name="仁德[奶自己]",desc="仁德[奶自己]",show=false,record=false,json_file="buff_11100105",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=3,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100201] = {id=11100201,name="破军[预备]",desc="破军预备",show=false,record=true,json_file="buff_11100201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100401] = {id=11100401,name="鬼才[预备]",desc="鬼才监听",show=false,record=true,json_file="buff_11100401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100402] = {id=11100402,name="鬼才[属性]",desc="鬼才加属性（非军）",show=false,record=true,json_file="buff_11100402",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100403] = {id=11100403,name="鬼才[属性]",desc="鬼才加属性（军）",show=false,record=true,json_file="buff_11100403",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100501] = {id=11100501,name="黄天[预备]",desc="黄天[预备]",show=false,record=true,json_file="buff_11100501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100502] = {id=11100502,name="雷击[预备]",desc="雷击[预备]",show=false,record=true,json_file="buff_11100502",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100503] = {id=11100503,name="黄天[预备]",desc="黄天[预备]",show=false,record=false,json_file="buff_11100503",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100601] = {id=11100601,name="激怒[预备]",desc="激怒[预备]",show=false,record=true,json_file="buff_11100601",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=30,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100701] = {id=11100701,name="乱武[预备]",desc="乱武[预备]",show=false,record=true,json_file="buff_11100701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100702] = {id=11100702,name="乱武[友军]",desc="友方乱武",show=false,record=true,json_file="buff_11100702",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100703] = {id=11100703,name="乱武[敌军]",desc="敌方乱武",show=false,record=true,json_file="buff_11100703",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101001] = {id=11101001,name="崩坏[预备]",desc="崩坏[预备]",show=false,record=true,json_file="buff_11101001",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101002] = {id=11101002,name="暴乱",desc="暴乱提升连击率和受到伤害",show=false,record=true,json_file="buff_11101002",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101101] = {id=11101101,name="铁骑[预备]",desc="铁骑[预备]",show=false,record=true,json_file="buff_11101101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101201] = {id=11101201,name="再起[预备]",desc="再起[预备]",show=false,record=true,json_file="buff_11101201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101202] = {id=11101202,name="再起[增伤]",desc="再起[增伤]",show=false,record=false,json_file="buff_11101202",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=7,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101301] = {id=11101301,name="放权[预备]",desc="放权[预备]",show=false,record=true,json_file="buff_11101301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101302] = {id=11101302,name="放权[加暴]",desc="放权[加暴]",show=false,record=false,json_file="buff_11101302",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101601] = {id=11101601,name="突袭[预备]",desc="突袭[预备]",show=false,record=true,json_file="buff_11101601",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101602] = {id=11101602,name="突袭[加速]",desc="突袭[加速]",show=false,record=false,json_file="buff_11101602",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=12,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101701] = {id=11101701,name="龙胆[预备]",desc="龙胆[预备]",show=false,record=true,json_file="buff_11101701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101702] = {id=11101702,name="龙胆[统转武]",desc="龙胆[统转武]",show=false,record=false,json_file="buff_11101702",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101703] = {id=11101703,name="龙胆[武转统]",desc="龙胆[武转统]",show=false,record=false,json_file="buff_11101703",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101801] = {id=11101801,name="观星[预备]",desc="观星[预备]",show=false,record=true,json_file="buff_11101801",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101802] = {id=11101802,name="观星[伤害]",desc="观星[伤害]",show=false,record=true,json_file="buff_11101802",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102101] = {id=11102101,name="克己[预备]",desc="克己[预备]",show=false,record=true,json_file="buff_11102101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102102] = {id=11102102,name="克己[奇谋]",desc="克己[奇谋]",show=false,record=true,json_file="buff_11102102",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102103] = {id=11102103,name="克己[会心]",desc="克己[会心]",show=false,record=true,json_file="buff_11102103",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102201] = {id=11102201,name="遗计[预备]",desc="遗计[预备]",show=false,record=true,json_file="buff_11102201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102202] = {id=11102202,name="遗计",desc="遗计",show=false,record=true,json_file="buff_11102202",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=99,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102203] = {id=11102203,name="遗计[发动率]",desc="遗计[发动率]",show=false,record=true,json_file="buff_11102203",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=5,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102301] = {id=11102301,name="节命[预备]",desc="节命[预备]",show=false,record=true,json_file="buff_11102301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102302] = {id=11102302,name="节命",desc="降低受到谋略伤害",show=false,record=false,json_file="buff_11102302",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180101] = {id=11180101,name="应援[预备]",desc="应援回合末恢复友军兵力",show=true,record=true,json_file="buff_11180101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180201] = {id=11180201,name="潜袭[预备]",desc="潜袭对敌军造成伤害",show=true,record=true,json_file="buff_11180201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180301] = {id=11180301,name="毅烈[预备]",desc="毅烈获得连弩或者必中",show=true,record=true,json_file="buff_11180301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180302] = {id=11180302,name="连弩[预备]",desc="连弩获得连击",show=true,record=true,json_file="buff_11180302",type=3,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180401] = {id=11180401,name="夺刀[预备]",desc="夺刀计穷并造成兵刃伤害",show=true,record=true,json_file="buff_11180401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180501] = {id=11180501,name="定仪[预备]",desc="定仪造成谋略伤害并降低主动概率",show=true,record=true,json_file="buff_11180501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180601] = {id=11180601,name="止戈[预备]",desc="自身行动时使随机单体对敌方随机单体发动普攻",show=true,record=true,json_file="buff_11180601",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180701] = {id=11180701,name="狼袭[预备]",desc="狼袭造成兵刃伤害",show=true,record=true,json_file="buff_11180701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180801] = {id=11180801,name="伺盗[预备]",desc="伺盗敌方受伤增加并施加断粮",show=true,record=true,json_file="buff_11180801",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180802] = {id=11180802,name="伺盗标记buff",desc="用于标记伤害次数",show=false,record=false,json_file="buff_11180802",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180901] = {id=11180901,name="悍勇[预备]",desc="悍勇加buff",show=true,record=true,json_file="buff_11180901",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11180902] = {id=11180902,name="悍勇",desc="受到物理伤害降低",show=true,record=true,json_file="buff_11180902",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=6,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181001] = {id=11181001,name="雉盗[预备]",desc="稚盗加buff",show=true,record=true,json_file="buff_11181001",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181002] = {id=11181002,name="雉盗",desc="自己被偷取属性，友军受到伤害减少",show=true,record=false,json_file="buff_11181002",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181101] = {id=11181101,name="当先[预备]",desc="提升武力属性，回合开始概率获得先机",show=true,record=true,json_file="buff_11181101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181201] = {id=11181201,name="链祸[预备]",desc="偶数回合恢复兵力，自身受到伤害时对友军造成传递伤害",show=true,record=true,json_file="buff_11181201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181501] = {id=11181501,name="忠勇[预备]",desc="忠勇加属性",show=true,record=true,json_file="buff_11181501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181601] = {id=11181601,name="揖让[预备]",desc="揖让[预备]",show=true,record=true,json_file="buff_11181601",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11181602] = {id=11181602,name="揖让[预备]",desc="移交智力属性、治疗自身",show=true,record=true,json_file="buff_11181602",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11200101] = {id=11200101,name="洛神[预备]",desc="概率降低",show=false,record=false,json_file="buff_11200101",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=5,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11200501] = {id=11200501,name="离间",desc="追击战法发动率提升",show=false,record=true,json_file="buff_11200501",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11200701] = {id=11200701,name="狂骨[预备]",desc="造成兵刃伤害吸血",show=false,record=true,json_file="buff_11200701",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11200901] = {id=11200901,name="连环[预备]",desc="连环传递伤害",show=false,record=true,json_file="buff_11200901",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11201301] = {id=11201301,name="裸衣[降低属性]",desc="裸衣降低武力统率属性",show=false,record=true,json_file="buff_11201301",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11201501] = {id=11201501,name="英魂",desc="英魂转移属性",show=false,record=true,json_file="buff_11201501",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11201601] = {id=11201601,name="戮力",desc="戮力增加连击率和兵刃伤害",show=false,record=false,json_file="buff_11201601",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11201701] = {id=11201701,name="冲阵[预备]",desc="发动主动战法后，有50%概率（受武力影响）对敌军单体造成1次兵刃伤害",show=false,record=true,json_file="buff_11201701",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11280301] = {id=11280301,name="援护[预备]",desc="援护加统率",show=true,record=true,json_file="buff_11280301",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11280501] = {id=11280501,name="秉正[增伤]",desc="秉正己方增伤",show=true,record=true,json_file="buff_11280501",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11280601] = {id=11280601,name="急救[预备]",desc="急救受到伤害概率恢复兵力",show=true,record=true,json_file="buff_11280601",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11280701] = {id=11280701,name="火攻",desc="焚营-火攻",show=true,record=true,json_file="buff_11280701",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11280901] = {id=11280901,name="散谣[预备]",desc="散谣减属性",show=true,record=true,json_file="buff_11280901",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11300101] = {id=11300101,name="奇袭",desc="奇袭",show=false,record=false,json_file="buff_11300101",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=8,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11300102] = {id=11300102,name="奇袭-统率",desc="奇袭",show=false,record=false,json_file="buff_11300102",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11380101] = {id=11380101,name="短兵[预备]",desc="短兵减统率",show=true,record=true,json_file="buff_11380101",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400101] = {id=11400101,name="咆哮[预备]",desc="自己回合普攻追击",show=false,record=true,json_file="buff_11400101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400102] = {id=11400102,name="咆哮[预备]",desc="兵刃伤害提升",show=false,record=false,json_file="buff_11400102",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400103] = {id=11400103,name="咆哮[预备]",desc="兵刃伤害提升",show=false,record=false,json_file="buff_11400103",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=9,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400201] = {id=11400201,name="武圣[发动主动战法]",desc="破甲提升",show=true,record=true,json_file="buff_11400201",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400202] = {id=11400202,name="武魂[废弃]",desc="武圣-武力 主动发动率提升",show=true,record=true,json_file="buff_11400202",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=4,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400203] = {id=11400203,name="武圣[减甲]",desc="水攻buff",show=true,record=true,json_file="buff_11400203",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400204] = {id=11400204,name="武圣[预备]",desc="武圣预备",show=false,record=true,json_file="buff_11400204",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400301] = {id=11400301,name="不屈[预备]",desc="不屈预备",show=false,record=true,json_file="buff_11400301",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=13,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400701] = {id=11400701,name="无双[预备]",desc="无双[预备]",show=false,record=true,json_file="buff_11400701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11480101] = {id=11480101,name="司敌[预备]",desc="司敌受伤害加计穷",show=true,record=true,json_file="buff_11480101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11480201] = {id=11480201,name="倾袭[预备]",desc="倾袭加普攻伤害",show=true,record=true,json_file="buff_11480201",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11480202] = {id=11480202,name="倾袭[普攻增伤]",desc="倾袭加普攻伤害",show=true,record=true,json_file="buff_11480202",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11700101] = {id=11700101,name="凿险[预备]",desc="凿险[预备]",show=false,record=true,json_file="buff_11700101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11700201] = {id=11700201,name="志继[预备]",desc="志继[预备]",show=false,record=true,json_file="buff_11700201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11700202] = {id=11700202,name="志继[监听]",desc="志继[监听]",show=false,record=false,json_file="buff_11700202",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[999001] = {id=999001,name="兵种适应性",desc="兵种适应性属性加成",show=true,record=false,json_file="buff_999001",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="兵种适应性",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100302] = {id=11100302,name="奸雄",desc="奸雄减属性",show=false,record=true,json_file="buff_11100302",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100303] = {id=11100303,name="奸雄",desc="奸雄加属性",show=false,record=true,json_file="buff_11100303",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11100304] = {id=11100304,name="奸雄[预备]",desc="奸雄预备buff",show=true,record=true,json_file="buff_11100304",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[100000] = {id=100000,name="太学-科技武",desc="城建科技增加武力",show=true,record=true,json_file="buff_100000",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100001] = {id=100001,name="太学-科技谋",desc="城建科技增加智力",show=true,record=true,json_file="buff_100001",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100002] = {id=100002,name="太学-科技御",desc="城建科技增加统帅",show=true,record=true,json_file="buff_100002",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100003] = {id=100003,name="太学-科技速",desc="城建科技增加速度",show=true,record=true,json_file="buff_100003",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100004] = {id=100004,name="太学-科技坚盾",desc="城建科技增加盾兵增减伤",show=true,record=true,json_file="buff_100004",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100005] = {id=100005,name="太学-科技贯弓",desc="城建科技增加弓兵增减伤",show=true,record=true,json_file="buff_100005",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100006] = {id=100006,name="太学-科技铁骑",desc="城建科技增加骑兵增减伤",show=true,record=true,json_file="buff_100006",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100007] = {id=100007,name="太学-科技韧枪",desc="城建科技增加枪兵增减伤",show=true,record=true,json_file="buff_100007",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100008] = {id=100008,name="太学-科技强魏",desc="魏国武将属性提升",show=true,record=true,json_file="buff_100008",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100009] = {id=100009,name="太学-科技强蜀",desc="蜀国武将属性提升",show=true,record=true,json_file="buff_100009",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100010] = {id=100010,name="太学-科技强吴",desc="吴国武将属性提升",show=true,record=true,json_file="buff_100010",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100011] = {id=100011,name="太学-科技强群",desc="群国武将属性提升",show=true,record=true,json_file="buff_100011",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100012] = {id=100012,name="第2回合，我方盾兵受到伤害降低7%",desc="城建科技盾兵十级特殊效果",show=true,record=true,json_file="buff_100012",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100013] = {id=100013,name="第3回合，我方弓兵会心和奇谋几率提升10%",desc="城建科技弓兵十级特殊效果",show=true,record=true,json_file="buff_100013",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100014] = {id=100014,name="首回合，我方骑兵规避率提升5%",desc="城建科技骑兵十级特殊效果",show=true,record=true,json_file="buff_100014",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100015] = {id=100015,name="第4回合，我方枪兵破甲和看破提升8%",desc="城建科技枪兵十级特殊效果",show=true,record=true,json_file="buff_100015",type=4,total_round=4,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=2,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100016] = {id=100016,name="太学-科技御刃",desc="上阵武将受到的兵刃伤害减少",show=true,record=true,json_file="buff_100016",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100017] = {id=100017,name="太学-科技摧锋",desc="上阵武将造成的兵刃伤害增加",show=true,record=true,json_file="buff_100017",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100018] = {id=100018,name="太学-科技固心",desc="上阵武将受到的谋略伤害减少",show=true,record=true,json_file="buff_100018",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100019] = {id=100019,name="太学-科技灼策",desc="上阵武将造成的谋略伤害增加",show=true,record=true,json_file="buff_100019",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={},},
[100020] = {id=100020,name="太学-科技镇贼",desc="上阵武将对土地守军的造成的伤害增加",show=true,record=true,json_file="buff_100020",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={256,},},
[100021] = {id=100021,name="太学-科技卫贼",desc="上阵武将受到来自土地守军的伤害减少",show=true,record=true,json_file="buff_100021",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=4,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=1,battle_white_list={256,},},
[200001] = {id=200001,name="策牌-杀",desc="策牌-杀",show=true,record=true,json_file="buff_100101",type=4,total_round=4,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=1,buff_source=2,battle_white_list={},},
[200002] = {id=200002,name="策牌-闪",desc="策牌-闪",show=true,record=true,json_file="buff_100101",type=4,total_round=4,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=1,buff_source=2,battle_white_list={},},
[11200201] = {id=11200201,name="天香[闪避]",desc="闪避提升",show=true,record=true,json_file="buff_11200201",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11200301] = {id=11200301,name="集智",desc="奇才BUFF",show=true,record=true,json_file="buff_11200301",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500201] = {id=10500201,name="闪避增加[预备]",desc="闪避提升",show=true,record=true,json_file="buff_10500201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500202] = {id=10500202,name="前三回合闪避增加[预备]",desc="闪避提升（前3回合）",show=true,record=true,json_file="buff_10500202",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600201] = {id=10600201,name="胭脂阵发动率增加[预备]",desc="发动率提升",show=true,record=true,json_file="buff_10600201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600202] = {id=10600202,name="胭脂阵回血[预备]",desc="胭脂阵治疗",show=true,record=true,json_file="buff_10600202",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400501] = {id=11400501,name="淑慎[预备]",desc="淑慎[预备]",show=true,record=true,json_file="buff_11400501",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11300201] = {id=11300201,name="弓腰姬",desc="弓腰姬增伤",show=true,record=true,json_file="buff_11300201",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=5,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600203] = {id=10600203,name="胭脂阵先锋回血[预备]",desc="胭脂阵先锋回血",show=true,record=true,json_file="buff_10600203",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10600204] = {id=10600204,name="胭脂阵属性增加[预备]",desc="胭脂阵属性增加",show=true,record=true,json_file="buff_10600204",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10401101] = {id=10401101,name="钝兵挫锐减伤[预备]",desc="钝兵挫锐减伤",show=true,record=true,json_file="buff_10401101",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10401201] = {id=10401201,name="以逸待劳[预备]",desc="以逸待劳[预备]",show=true,record=true,json_file="buff_10401201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10401202] = {id=10401202,name="以逸待劳[减伤]",desc="以逸待劳[减伤]",show=true,record=true,json_file="buff_10401202",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10401301] = {id=10401301,name="乘胜追击[预备]",desc="乘胜追击",show=true,record=true,json_file="buff_10401301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10401302] = {id=10401302,name="乘胜追击连击率提升",desc="连击率提升",show=true,record=true,json_file="buff_10401302",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=false,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[100101] = {id=100101,name="蛮力[装备特技]",desc="提高5点武力",show=true,record=true,json_file="buff_100101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[100201] = {id=100201,name="聪明[装备特技]",desc="提高5点智力",show=true,record=true,json_file="buff_100201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[100301] = {id=100301,name="稳重[装备特技]",desc="提高5点统率",show=true,record=true,json_file="buff_100301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[100401] = {id=100401,name="快速[装备特技]",desc="提高5点速度",show=true,record=true,json_file="buff_100401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[200101] = {id=200101,name="神力[装备特技]",desc="提高8点武力",show=true,record=true,json_file="buff_200101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[200201] = {id=200201,name="聪慧[装备特技]",desc="提高8点智力",show=true,record=true,json_file="buff_200201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[200301] = {id=200301,name="厚重[装备特技]",desc="提高8点统率",show=true,record=true,json_file="buff_200301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[200401] = {id=200401,name="神速[装备特技]",desc="提高8点速度",show=true,record=true,json_file="buff_200401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[300101] = {id=300101,name="百战[装备特技]",desc="提高12点武力",show=true,record=true,json_file="buff_300101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[300201] = {id=300201,name="神算[装备特技]",desc="提高12点智力",show=true,record=true,json_file="buff_300201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[300301] = {id=300301,name="勇毅[装备特技]",desc="提高12点统率",show=true,record=true,json_file="buff_300301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=5,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=3,battle_white_list={},},
[11101401] = {id=11101401,name="制衡[减属性]",desc="制衡[减属性]",show=true,record=true,json_file="buff_11101401",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101402] = {id=11101402,name="制衡[加属性]",desc="制衡[加属性]",show=true,record=true,json_file="buff_11101402",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101403] = {id=11101403,name="制衡[预备]",desc="制衡[预备]",show=true,record=true,json_file="buff_11101403",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101501] = {id=11101501,name="英姿[加奇谋]",desc="英姿[预备]",show=true,record=true,json_file="buff_11101501",type=3,total_round=1,can_stack=true,stack_rule=2,max_stack_count=5,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101502] = {id=11101502,name="反间[减属性]",desc="反间[减属性]",show=true,record=true,json_file="buff_11101502",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101503] = {id=11101503,name="反间[加属性]",desc="反间[加属性]",show=true,record=true,json_file="buff_11101503",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101504] = {id=11101504,name="英姿[预备]",desc="英姿[预备]",show=true,record=true,json_file="buff_11101504",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101901] = {id=11101901,name="悲歌[计数]",desc="悲歌[计数]",show=true,record=true,json_file="buff_11101901",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=99,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101902] = {id=11101902,name="悲歌",desc="悲歌",show=true,record=true,json_file="buff_11101902",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=99,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11101903] = {id=11101903,name="悲歌[预备]",desc="悲歌[预备]",show=true,record=true,json_file="buff_11101903",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102001] = {id=11102001,name="巧变[预备]",desc="巧变回血转移状态",show=true,record=true,json_file="buff_11102001",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11102002] = {id=11102002,name="巧变标记",desc="标记",show=false,record=false,json_file="buff_11102002",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11201101] = {id=11201101,name="苦肉[概率降低]",desc="苦肉[概率降低]",show=true,record=true,json_file="buff_11201101",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400401] = {id=11400401,name="刚烈",desc="刚烈",show=true,record=true,json_file="buff_11400401",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[11400402] = {id=11400402,name="刚烈[预备]",desc="刚烈[预备]",show=true,record=true,json_file="buff_11400402",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="icon-buff5",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10201701] = {id=10201701,name="运筹帷幄",desc="增加奇谋",show=true,record=true,json_file="buff_10201701",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400701] = {id=10400701,name="意气风发[预备]",desc="意气风发[预备]",show=true,record=true,json_file="buff_10400701",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400801] = {id=10400801,name="妙手回春[预备]",desc="妙手回春[预备]",show=true,record=true,json_file="buff_10400801",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400802] = {id=10400802,name="妙手回春[奋发]",desc="妙手回春[奋发]",show=true,record=true,json_file="buff_10400802",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10400901] = {id=10400901,name="金蝉脱壳[预备]",desc="金蝉脱壳[预备]",show=true,record=true,json_file="buff_10400901",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10300901] = {id=10300901,name="穷追不舍[追击率提升]",desc="穷追不舍[追击率提升]",show=true,record=true,json_file="buff_10300901",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=4,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=true,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[210101] = {id=210101,name="明主[预备]",desc="明主",show=true,record=true,json_file="buff_210101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21010101] = {id=21010101,name="任贤[预备]",desc="任贤",show=true,record=true,json_file="buff_21010101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21010102] = {id=21010102,name="任贤[增伤]",desc="任贤",show=true,record=true,json_file="buff_21010102",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21010201] = {id=21010201,name="勤政[预备]",desc="勤政",show=true,record=true,json_file="buff_21010201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[210201] = {id=210201,name="仁君[预备]",desc="仁君",show=true,record=true,json_file="buff_210201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21020101] = {id=21020101,name="布施[预备]",desc="布施",show=true,record=true,json_file="buff_21020101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21020102] = {id=21020102,name="布施[统率增加]",desc="布施",show=true,record=true,json_file="buff_21020102",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21020201] = {id=21020201,name="德音[预备]",desc="德音",show=true,record=true,json_file="buff_21020201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[210301] = {id=210301,name="守业[预备]",desc="守业",show=true,record=true,json_file="buff_210301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21030101] = {id=21030101,name="防患[预备]",desc="防患",show=true,record=true,json_file="buff_21030101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21030201] = {id=21030201,name="韬晦[预备]",desc="韬晦",show=true,record=true,json_file="buff_21030201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[210401] = {id=210401,name="霸主[预备]",desc="霸主",show=true,record=true,json_file="buff_210401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21040101] = {id=21040101,name="威压[预备]",desc="威压",show=true,record=true,json_file="buff_21040101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21040102] = {id=21040102,name="威压[统率降低]",desc="威压",show=true,record=true,json_file="buff_21040102",type=4,total_round=2,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21040201] = {id=21040201,name="集权[预备]",desc="集权",show=true,record=true,json_file="buff_21040201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[21040202] = {id=21040202,name="集权[友降低伤害]",desc="集权",show=true,record=true,json_file="buff_21040202",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[220101] = {id=220101,name="先锋[预备]",desc="先锋",show=true,record=true,json_file="buff_220101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22010101] = {id=22010101,name="陷阵[预备]",desc="陷阵",show=true,record=true,json_file="buff_22010101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22010201] = {id=22010201,name="破敌[预备]",desc="破敌",show=true,record=true,json_file="buff_22010201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[220201] = {id=220201,name="军师[预备]",desc="军师",show=true,record=true,json_file="buff_220201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22020101] = {id=22020101,name="料敌[预备]",desc="料敌",show=true,record=true,json_file="buff_22020101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22020201] = {id=22020201,name="洞悉[预备]",desc="洞悉",show=true,record=true,json_file="buff_22020201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[220301] = {id=220301,name="监军[预备]",desc="监军",show=true,record=true,json_file="buff_220301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22030101] = {id=22030101,name="肃纪[预备]",desc="肃纪",show=true,record=true,json_file="buff_22030101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22030201] = {id=22030201,name="清侧[预备]",desc="清侧",show=true,record=true,json_file="buff_22030201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[220401] = {id=220401,name="巾帼[预备]",desc="巾帼",show=true,record=true,json_file="buff_220401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22040101] = {id=22040101,name="英姿[预备]",desc="英姿",show=true,record=true,json_file="buff_22040101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[22040201] = {id=22040201,name="明慧[预备]",desc="明慧",show=true,record=true,json_file="buff_22040201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[230101] = {id=230101,name="黄巾[预备]",desc="黄巾",show=true,record=true,json_file="buff_230101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23010101] = {id=23010101,name="妖言[预备]",desc="妖言",show=true,record=true,json_file="buff_23010101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23010102] = {id=23010102,name="妖言[降低伤害]",desc="妖言[降低伤害]",show=true,record=false,json_file="buff_23010102",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23010201] = {id=23010201,name="惑民[预备]",desc="惑民",show=true,record=true,json_file="buff_23010201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23010202] = {id=23010202,name="惑民[降发动率]",desc="惑民[降发动率]",show=true,record=false,json_file="buff_23010202",type=4,total_round=3,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[230201] = {id=230201,name="流寇[预备]",desc="流寇",show=true,record=true,json_file="buff_230201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23020101] = {id=23020101,name="劫掠[预备]",desc="劫掠",show=true,record=true,json_file="buff_23020101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23020102] = {id=23020102,name="劫掠[降属性]",desc="劫掠[降属性]",show=true,record=false,json_file="buff_23020102",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23020201] = {id=23020201,name="游击[预备]",desc="游击",show=true,record=true,json_file="buff_23020201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[230301] = {id=230301,name="义军[预备]",desc="义军",show=true,record=true,json_file="buff_230301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23030101] = {id=23030101,name="举义[预备]",desc="举义",show=true,record=true,json_file="buff_23030101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23030102] = {id=23030102,name="举义[加智力]",desc="举义[加智力]",show=true,record=false,json_file="buff_23030102",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23030201] = {id=23030201,name="济贫[预备]",desc="济贫",show=true,record=true,json_file="buff_23030201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[230401] = {id=230401,name="割据[预备]",desc="割据",show=true,record=true,json_file="buff_230401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23040101] = {id=23040101,name="恃险[预备]",desc="恃险",show=true,record=true,json_file="buff_23040101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[23040201] = {id=23040201,name="裂土[预备]",desc="裂土",show=true,record=true,json_file="buff_23040201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[240101] = {id=240101,name="野心[预备]",desc="野心",show=true,record=true,json_file="buff_240101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24010101] = {id=24010101,name="伪忠[预备]",desc="伪忠",show=true,record=true,json_file="buff_24010101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24010102] = {id=24010102,name="伪忠[增伤]",desc="伪忠[增伤]",show=true,record=true,json_file="buff_24010102",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24010201] = {id=24010201,name="窃国[预备]",desc="窃国",show=true,record=true,json_file="buff_24010201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24010202] = {id=24010202,name="窃国[加速]",desc="窃国[加速]",show=true,record=false,json_file="buff_24010202",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[240201] = {id=240201,name="暗枭[预备]",desc="暗枭",show=true,record=true,json_file="buff_240201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24020101] = {id=24020101,name="离间[预备]",desc="离间",show=true,record=true,json_file="buff_24020101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24020201] = {id=24020201,name="嫁祸[预备]",desc="嫁祸",show=true,record=true,json_file="buff_24020201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24020202] = {id=24020202,name="嫁祸[易伤]",desc="嫁祸[易伤]",show=true,record=true,json_file="buff_24020202",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[240301] = {id=240301,name="​乱臣[预备]",desc="​乱臣",show=true,record=true,json_file="buff_240301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24030101] = {id=24030101,name="弄权[预备]",desc="弄权",show=true,record=true,json_file="buff_24030101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24030102] = {id=24030102,name="弄权[增发动率]",desc="弄权[增发动率]",show=true,record=true,json_file="buff_24030102",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24030201] = {id=24030201,name="乱政[预备]",desc="乱政",show=true,record=true,json_file="buff_24030201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[240401] = {id=240401,name="祸水[预备]",desc="祸水",show=true,record=true,json_file="buff_240401",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24040101] = {id=24040101,name="奢靡[预备]",desc="奢靡",show=true,record=true,json_file="buff_24040101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24040102] = {id=24040102,name="奢靡[减伤]",desc="奢靡[减伤]",show=true,record=false,json_file="buff_24040102",type=4,total_round=1,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24040201] = {id=24040201,name="乱宫[预备]",desc="乱宫",show=true,record=true,json_file="buff_24040201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[24040202] = {id=24040202,name="乱宫[奇谋]",desc="乱宫[奇谋]",show=true,record=true,json_file="buff_24040202",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[310101] = {id=310101,name="护驾[预备]",desc="护驾[预备]",show=true,record=true,json_file="buff_310101",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[310201] = {id=310201,name="激将[预备]",desc="激将[预备]",show=true,record=true,json_file="buff_310201",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[310301] = {id=310301,name="救援[预备]",desc="救援[预备]",show=true,record=true,json_file="buff_310301",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[310302] = {id=310302,name="救援[治疗加成]",desc="救援[治疗加成]",show=true,record=false,json_file="buff_310302",type=4,total_round=9,can_stack=false,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=6,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10100601] = {id=10100601,name="传檄而定[预备]",desc="传檄而定[预备]",show=true,record=true,json_file="buff_10100601",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=3,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10202501] = {id=10202501,name="蓄势[预备]",desc="蓄势[预备]",show=true,record=true,json_file="buff_10202501",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10202502] = {id=10202502,name="蓄势[层数]",desc="蓄势[层数]",show=true,record=true,json_file="buff_10202502",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=4,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10202701] = {id=10202701,name="一往无前[减益]",desc="一往无前[减益]",show=true,record=true,json_file="buff_10202701",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10301101] = {id=10301101,name="威震天下[减益]",desc="威震天下[减益]",show=true,record=true,json_file="buff_10301101",type=4,total_round=1,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10301301] = {id=10301301,name="折冲御侮[统帅降低]",desc="折冲御侮[统帅降低]",show=true,record=true,json_file="buff_10301301",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10301302] = {id=10301302,name="折冲御侮[统帅提升]",desc="折冲御侮[统帅提升]",show=true,record=true,json_file="buff_10301302",type=4,total_round=2,can_stack=true,stack_rule=2,max_stack_count=2,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500501] = {id=10500501,name="虎豹骑[速度提升]",desc="虎豹骑[速度提升]",show=false,record=true,json_file="buff_10500501",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500502] = {id=10500502,name="虎豹骑[预备]",desc="虎豹骑[预备]",show=true,record=true,json_file="buff_10500502",type=4,total_round=3,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500601] = {id=10500601,name="陷阵营[统帅提升]",desc="陷阵营[统帅提升]",show=false,record=true,json_file="buff_10500601",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10500602] = {id=10500602,name="陷阵营[预备]",desc="陷阵营[预备]",show=true,record=true,json_file="buff_10500602",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=1,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
[10301001] = {id=10301001,name="当阳震怒[伤害提升]",desc="当阳震怒[伤害提升]",show=true,record=true,json_file="buff_10301001",type=4,total_round=9,can_stack=true,stack_rule=2,max_stack_count=99,overlay_rule=2,icon_path="",add_timing=0,remove_timing=1,dispellable=false,groupTypeList={},sourceDeadClear=true,effect_id=0,effect_socket="",jump_on_add="",jump_on_remove="",max_combat=0,buff_source=0,battle_white_list={},},
}