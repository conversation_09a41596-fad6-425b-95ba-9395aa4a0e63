﻿local NodeBaseServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.NodeBaseServer")

---@class AddBuffNodeServer : NodeBaseServer
local AddBuffNodeServer = DefineClass("AddBuffNodeServer", NodeBaseServerEnv.NodeBaseServer)
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

function AddBuffNodeServer:ctor()
    self:MarkExecute()
end

function AddBuffNodeServer:Execute(graphData)
    local targetIds = graphData:GetProperty(self, "targetIds")
    local buffId = graphData:GetProperty(self, "buffId")
    local sourceId = graphData:GetProperty(self, "sourceId")
    if string.isnilorempty(sourceId) then
        sourceId = graphData.caster.uniqueId
    end
    local count = graphData:GetProperty(self, "count")
    
    local coverTotalRound = graphData:GetProperty(self, "coverTotalRound") --覆盖表中回合数
    local totalRound
    if coverTotalRound then
        totalRound = graphData:GetProperty(self, "totalRound")
    end
    
    local sourceTacticId, sourceTacticHero = BattleUtilsEnv.GetGraphGetSourceTacticIdAndHero(graphData)

    for _, targetId in ipairs(targetIds) do
        local target = graphData.caster.battleGame.heroUniqueIdMap[targetId]
        local buffExtraArgs = { coveredTotalRound = totalRound, sourceTacticId = sourceTacticId, sourceTacticHero = sourceTacticHero }
        target:AddBuff(buffId, sourceId, count, graphData:GetFromBlackboard("Level"), buffExtraArgs)
    end
end
