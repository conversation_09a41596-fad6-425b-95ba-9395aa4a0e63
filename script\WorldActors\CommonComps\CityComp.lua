local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local WorldCityDataEnv = xrequire("Modules.World.WorldCityData")
local CoordUtils = xrequire("Common.Utils.CoordUtils")

local MapElementType = TableConst.enums["slg.MapElementType"]

---@class CityComp: ComponentBase
---@field props CityCompProps|WorldBuildingCompProps|OutpostCompProps|WorldActorProps
CityComp = DefineClass("CityComp", ComponentBaseEnv.ComponentBase)
CityComp.HasCityComp = true

function CityComp:ctor()
    self.siegeGroups = {}
    self.siegeBarracks = {}
    local data = WorldCityDataEnv.WORLD_CITY[self.props.instanceId]
    if not data then
        self:logWarn("No city data found for instanceId: %s", self.props.instanceId)
        self:DestroyBy(self.id, "invalid city instanceId", true)
        return
    end
    assert(data)
    for i, _ in pairs(data.groups) do
        local barracks = {}
        for _, info in ipairs(data.groups[i][TableConst.enums.ActorType.CITY_BARRACKS]) do
            table.insert(barracks, info.elementId)
            if not self.siegeBarracks[info.elementId] then
                self.siegeBarracks[info.elementId] = {
                    actor = nil,
                    groups = {},
                }
            end
            table.insert(self.siegeBarracks[info.elementId].groups, i)
        end
        self.siegeGroups[i] = {
            [TableConst.enums.ActorType.SIEGE_CAMP] = nil,
            [TableConst.enums.ActorType.CITY_GATE] = nil,
            [TableConst.enums.ActorType.INNER_CITY_GATE] = nil,
            [TableConst.enums.ActorType.CITY_BARRACKS] = barracks,
        }
        self.props.siegeGroupsByIdx[i] = {}
    end
    self.siegeRecords = {}
end

function CityComp:onEnterSpace()
    self:initResetSurroundingBelong()
    self:checkCreateSurroundingBuildings()
    self:serviceRpc("AmbitionsService", "UpdateCityOccupier", self.props.instanceId, self.props.allyId)
end

function CityComp:fullSyncAllyView()
    self:SyncAllyView({
        instanceId = self.props.instanceId,
        pos = self:GetDoubledPos2DV(),
    }, true)
end

--region 周边建筑管理创建

function CityComp:validAnnex(actor, config)
    -- 这个函数返回false是正常的，只是遍历所有组找正确的配置，不需要Error日志
    if not config then
        return false
    end
    -- 校验建筑位置发生变化
    if CoordUtils.PackPos(actor:GetDoubledPos2D()) ~= config.pos then
        return false
    end
    -- 校验非静态建筑
    if actor.props.slgTp ~= config.elementId then
        local verifyType = false
        local elementType = GameCommon.TableDataManager:GetMapElementData(config.elementId).type
        for _, validType in ipairs(actor.worldBuildingConfig.valid_grid) do
            if validType == elementType then
                verifyType = true
                break
            end
        end
        if not verifyType then
            return false
        end
    end
    return true
end

function CityComp:onContentEnter(actor, inConType)
    if inConType ~= TableConst.enums.InConType.Annex then
        return
    end
    local data = WorldCityDataEnv.WORLD_CITY[self.props.instanceId]
    if actor._actorType == TableConst.enums.ActorType.CITY_BARRACKS then
        local config = data.allBarracks[actor.props.slgTp]
        if not self:validAnnex(actor, config) or self.siegeBarracks[actor.props.slgTp].actor then
            actor:DelayDestroyBy(self.id, "invalid_CITY_BARRACKS", true)
            return false
        end
        self.siegeBarracks[actor.props.slgTp].actor = actor
        actor.props.belongCity = self.id
    else
        local config = nil
        local idx = nil
        for groupIdx, group in pairs(data.groups) do
            if self:validAnnex(actor, group[actor._actorType]) then
                config = group[actor._actorType]
                idx = groupIdx
                break
            end
        end
        if not idx or not config or self.siegeGroups[idx][actor._actorType] then
            actor:DelayDestroyBy(self.id, "invalid_annex", true)
            return false
        end
        self.siegeGroups[idx][actor._actorType] = actor
        self.props.siegeGroupsByIdx[idx][actor._actorType] = actor.id

        actor.props.belongCity = self.id
        actor.props.siegeGroupIdx = idx
    end
end

function CityComp:onContentLeave(actor, inConType)
    if inConType ~= TableConst.enums.InConType.Annex then
        return
    end
    if actor._actorType ~= TableConst.enums.ActorType.SIEGE_CAMP then
        self:logWarn("Invalid actor type for leaving annex: %s", actor._actorType)
        return
    end
    local idx = nil
    for groupIdx, group in pairs(self.siegeGroups) do
        if group[actor._actorType] == actor then
            idx = groupIdx
            break
        end
    end
    if not idx then
        self:logWarn("Actor not found in siege groups: %s", actor)
        return
    end
    self.siegeGroups[idx][actor._actorType] = nil
    self.props.siegeGroupsByIdx[idx][actor._actorType] = nil
end

function CityComp:checkCreateSurroundingBuildings()
    local data = WorldCityDataEnv.WORLD_CITY[self.props.instanceId]
    assert(data)
    for groupIdx, group in pairs(data.groups) do
        if not self.siegeGroups[groupIdx][TableConst.enums.ActorType.CITY_GATE] then
            self:createAnnexBuilding(group[TableConst.enums.ActorType.CITY_GATE])
        end
        if not self.siegeGroups[groupIdx][TableConst.enums.ActorType.INNER_CITY_GATE] and group[TableConst.enums.ActorType.INNER_CITY_GATE] then
            self:createAnnexBuilding(group[TableConst.enums.ActorType.INNER_CITY_GATE])
        end
    end
    for _, barracks in pairs(data.allBarracks) do
        if self.siegeBarracks[barracks.elementId] and not self.siegeBarracks[barracks.elementId].actor then
            self:createAnnexBuilding(barracks)
        end
    end
end

function CityComp:createAnnexBuilding(buildingData)
    local ActorCollectorEnv = xrequire("Utils.ActorCollector")
    local actorType = nil
    local config = GameCommon.TableDataManager:GetMapElementData(buildingData.elementId)
    if config.type == MapElementType.CityGate or config.type == MapElementType.FrontierPassWall or config.type == MapElementType.FerryyPavilion then
        actorType = TableConst.enums.ActorType.CITY_GATE
    elseif config.type == MapElementType.InnerCityGate then
        actorType = TableConst.enums.ActorType.INNER_CITY_GATE
    elseif config.type == MapElementType.CityBarracks then
        actorType = TableConst.enums.ActorType.CITY_BARRACKS
    end
    assert(actorType, "Unknown annex building type: " .. buildingData.elementId)
    local clsName = ActorCollectorEnv.ACTOR_TYPE_TO_CLS[actorType].__cname
    local x, y = CoordUtils.UnpackPos(buildingData.pos)
    local position = {x = x, y = y}
    self.space:createActor(clsName, {
        subId = config.level,
        staticMapElementId = buildingData.elementId,
        allyId = self.props.allyId,
    }, position, nil, function(building)
        self:logInfo("[qun] createAnnexBuilding %s", building)
        local succ = self:dealActorEnter(building, TableConst.enums.InConType.Annex)
        assert(succ, "Failed to enter annex building into city")
    end)
end

function CityComp:ValidBuildNewSiegeCamp(newProps)
    for _, group in pairs(self.siegeGroups) do
        if group[TableConst.enums.ActorType.SIEGE_CAMP] and group[TableConst.enums.ActorType.SIEGE_CAMP].props.allyId == newProps.allyId then
            self:logInfo("ValidBuildNewSiegeCamp failed, already has siege camp for allyId %s", newProps.allyId)
            return false
        end
    end
    return true
end

--endregion

--region 攻城结算

---@param siegeCamp SiegeCamp
function CityComp:OnOccupiedBy(allyId, siegeCamp, gate)
    self.siegeRecords[siegeCamp.props.siegeGroupIdx]["lastHit"] = gate.lastHitFrom
    self.occupiedGroupIdx = siegeCamp.props.siegeGroupIdx
    self:OnSyncAlly(allyId)
end

function CityComp:OnAllyChange(oriAllyId)
    self:serviceRpc("AmbitionsService", "UpdateCityOccupier", self.props.instanceId, self.props.allyId)
    for _, group in pairs(self.siegeGroups) do
        local gate = group[TableConst.enums.ActorType.CITY_GATE]
        if gate then
            gate:OnSyncAlly(self.props.allyId)
        end
        local innerGate = group[TableConst.enums.ActorType.INNER_CITY_GATE]
        if innerGate then
            innerGate:OnSyncAlly(self.props.allyId)
        end
        local otherSiegeCamp = group[TableConst.enums.ActorType.SIEGE_CAMP]  ---@type SiegeCamp?
        if otherSiegeCamp and otherSiegeCamp.props.siegeGroupIdx ~= self.occupiedGroupIdx then
            otherSiegeCamp:OnCityOccupiedByOther()
        end
    end
    for _, info in pairs(self.siegeBarracks) do
        if info.actor then
            info.actor:OnSyncAlly(self.props.allyId)
        end
    end
    -- 中断其他城门的战斗
    -- 结束其他攻城大营的攻城中流程
    self.occupiedGroupIdx = nil
end

function CityComp:doSiegeEnd(siegeGroupIdx, allyId)
    self:logInfo("doSiegeEnd RemoveAllyInWar allyId %s siegeGroupIdx %s", allyId, siegeGroupIdx)
    self.props.allyInWar[allyId] = nil
    local group = self.siegeGroups[siegeGroupIdx]
    if not group then
        self:logError("doSiegeEnd no siegeGroupIdx: %s", siegeGroupIdx)
        return
    end
    local gate = group[TableConst.enums.ActorType.CITY_GATE]
    if gate then
        gate:SetInWar(nil)
        gate:fireEntityEvent("OnSiegeEnd", allyId)
    end
    local innerGate = group[TableConst.enums.ActorType.INNER_CITY_GATE]
    if innerGate then
        innerGate:SetInWar(nil)
        innerGate:fireEntityEvent("OnSiegeEnd", allyId)
    end
    for _, barrackId in ipairs(group[TableConst.enums.ActorType.CITY_BARRACKS]) do
        local barrackInfo = self.siegeBarracks[barrackId]
        if barrackInfo and barrackInfo.actor then
            barrackInfo.actor:fireEntityEvent("OnSiegeEnd", allyId)
        end
    end
end

function CityComp:SetAllyPreWar(allyId, siegeGroupIdx)
    self:logInfo("SetAllyPreWar allyId %s siegeGroupIdx %s", allyId, siegeGroupIdx)
    self.props.allyInWar[allyId] = {siegeGroupIdx = siegeGroupIdx}
end

function CityComp:SetAllyInWar(allyId, siegeGroupIdx)
    self:logInfo("SetAllyInWar allyId %s siegeGroupIdx %s", allyId, siegeGroupIdx)
    self.props.allyInWar[allyId] = {siegeGroupIdx = siegeGroupIdx, startAssault = true}
end

---@param relation Relation
function CityComp:gmOccupy(relation)
    self:OnSyncAlly(relation.allyId)
end

--endregion

--region 攻城数据收集

function CityComp:RecordSiegeBreakDurability(allyId, avatarGid, durability)
    local allyInWarDetail = self.props.allyInWar[allyId]  ---@type AllyInWarDetail?
    if not allyInWarDetail or not allyInWarDetail.startAssault then
        return
    end
    local siegeGroupIdx = allyInWarDetail.siegeGroupIdx
    if not self.siegeRecords[siegeGroupIdx] then
        self.siegeRecords[siegeGroupIdx] = {
            breakDurability = {},
            killSysDefender = {},
        }
    end
    self.siegeRecords[siegeGroupIdx].breakDurability[avatarGid] = (self.siegeRecords[siegeGroupIdx].breakDurability[avatarGid] or 0) + durability
end

function CityComp:RecordSiegeKillSysDefender(allyId, avatarGid, num)
    local allyInWarDetail = self.props.allyInWar[allyId]  ---@type AllyInWarDetail?
    if not allyInWarDetail or not allyInWarDetail.startAssault then
        return
    end
    local siegeGroupIdx = allyInWarDetail.siegeGroupIdx
    if not self.siegeRecords[siegeGroupIdx] then
        self.siegeRecords[siegeGroupIdx] = {
            breakDurability = {},
            killSysDefender = {},
        }
    end
    self.siegeRecords[siegeGroupIdx].killSysDefender[avatarGid] = (self.siegeRecords[siegeGroupIdx].killSysDefender[avatarGid] or 0) + num
end

function CityComp:getSiegeRecords(siegeGroupIdx)
    if not self.siegeRecords[siegeGroupIdx] then
        return 0, {}, {}, {}
    end
    local record = self.siegeRecords[siegeGroupIdx]
    self.siegeRecords[siegeGroupIdx] = nil
    local lastHit = record.lastHit or 0
    local killRank, duraRank, participate = {}, {}, {}
    for gid, val in pairs(record.killSysDefender) do
        table.insert(killRank, {gid = gid, value = val, detail = {}})
        participate[gid] = true
    end
    for gid, val in pairs(record.breakDurability) do
        table.insert(duraRank, {gid = gid, value = val, detail = {}})
        participate[gid] = true
    end
    table.sort(killRank, function(a, b) return a.value > b.value end)
    table.sort(duraRank, function(a, b) return a.value > b.value end)
    local tmp, killRank = killRank, {}
    for idx, item in ipairs(tmp) do
        if idx > GameCommon.TableDataManager:GetSiegeConst("RankTopN") then
            break
        end
        table.insert(killRank, {gid = item.gid, value = item.value, detail = record.killSysDefender[item.gid]})
    end
    local tmp, duraRank = duraRank, {}
    for idx, item in ipairs(tmp) do
        if idx > GameCommon.TableDataManager:GetSiegeConst("RankTopN") then
            break
        end
        table.insert(duraRank, {gid = item.gid, value = item.value, detail = record.breakDurability[item.gid]})
    end
    return lastHit, duraRank, killRank, participate
end

--endregion

function CityComp:ClearOccupyOnFailed(siegeGroupIdx, allyId)
    local gate = self.siegeGroups[siegeGroupIdx][TableConst.enums.ActorType.CITY_GATE]  ---@type CityGate?
    if gate then
        if gate.props.allyId == allyId then
            gate:OnSyncAlly(self.props.allyId)
        end
        gate:OnCityOccupyFailed()
    end
    local innerGate = self.siegeGroups[siegeGroupIdx][TableConst.enums.ActorType.INNER_CITY_GATE]  ---@type InnerCityGate?
    if innerGate then
        innerGate:OnCityOccupyFailed()
    end
    for _, info in pairs(self.siegeBarracks) do
        if info.actor and info.actor.props.allyId == allyId then
            info.actor:OnSyncAlly(self.props.allyId)
        end
        info.actor:OnCityOccupyFailed()
    end
    -- for _, barrackId in ipairs(self.siegeGroups[siegeGroupIdx][TableConst.enums.ActorType.CITY_BARRACKS]) do
    --     local barrackInfo = self.siegeBarracks[barrackId]
    --     if barrackInfo and barrackInfo.actor then
    --         if barrackInfo.actor.props.allyId == allyId then
    --             barrackInfo.actor:OnSyncAlly(self.props.allyId)
    --         end
    --         barrackInfo.actor:OnCityOccupyFailed()
    --     end
    -- end
end

function CityComp:ClearInsideAttackerOnFailed(allyId, leftCount)
    local allyInWarDetail = self.props.allyInWar[allyId]  ---@type AllyInWarDetail?
    if allyInWarDetail and allyInWarDetail.startAssault then
        -- 同盟还在攻城中，不清理
        return
    end
    -- OPTIMIZE(qun): 遍历城内所有地块上停留的部队，如果联盟相同就驱赶回城
    for _, pos in pairs(WorldCityDataEnv.WORLD_CITY[self.props.instanceId].allGrids) do
        for _, actor in pairs(self.space:GetActorArround(pos.x, pos.y, 0, TableConst.enums.ActorType.ARMY)) do
            if actor.props.allyId == allyId then
                actor:BackToHome()
            end
        end
    end

    if leftCount - 1 > 0 then
        self:addTimer(GameCommon.TableDataManager:GetSiegeConst("CLEAR_INSIDER_REPEAT_INTERNAL"), 0, function(timer_id)
            self:ClearInsideAttackerOnFailed(allyId, leftCount - 1)
        end)
    end
end

function CityComp:SetOccupyOnSuccess(siegeGroupIdx, allyId)
    for _, info in pairs(self.siegeGroups) do
        local gate = info[TableConst.enums.ActorType.CITY_GATE]
        if gate then
            gate:OnCityOccupySucceed()
        end
        local innerGate = info[TableConst.enums.ActorType.INNER_CITY_GATE]
        if innerGate then
            innerGate:OnCityOccupySucceed()
        end
    end
    for _, barrackInfo in pairs(self.siegeBarracks) do
        if barrackInfo and barrackInfo.actor then
            barrackInfo.actor:OnCityOccupySucceed()
        end
    end
end

--初始化后，统一城池附属建筑的归属
function CityComp:initResetSurroundingBelong()
    for _, group in pairs(self.siegeGroups) do
        local gate = group[TableConst.enums.ActorType.CITY_GATE]
        if gate then
            gate:OnSyncAlly(self.props.allyId)
        end
        local innerGate = group[TableConst.enums.ActorType.INNER_CITY_GATE]
        if innerGate then
            innerGate:OnSyncAlly(self.props.allyId)
        end
    end
    for _, info in pairs(self.siegeBarracks) do
        if info.actor then
            info.actor:OnSyncAlly(self.props.allyId)
        end
    end
end

function CityComp:QueryCityDetail(propNameList, mailboxStr, cbName)
    if self.props.allyId == 0 then
        return
    end
    local mailbox = LuaMailbox.createFromString(mailboxStr)
    local args = {}
    for _, propName in ipairs(propNameList) do
        if propName == "firstOccupy" then
            table.insert(args, self.props.firstOccupy or {allyId = self.props.allyId})  -- 兼容GM占领，没有首占信息
        elseif propName == "deployNum" then
            table.insert(args, #self.props.deployers)
        else
            table.insert(args, self.props[propName])
        end
    end
    self:clientViaServerRpc(mailbox, cbName, self.id, table.unpack(args))
end
