{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "118"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}}}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "19"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0.18+0.02*lv"}, "value": {"Type": "number", "Value": "0"}}}, "3": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "False"}, "female": {"Type": "boolean", "Value": "False"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "4": {"Type": "HealNode", "Field": {"targetIds": [], "healFactor": {"Type": "string", "Value": "0.6"}, "healFactorNew": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "True"}, "isFixedHeal": {"Type": "boolean", "Value": "True"}, "fixedHealValue": {"Type": "number", "Value": "0"}, "healHeroIds": [], "healPackageIds": [], "missHeroIds": []}}, "5": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "BlackboardValue": "HealValue"}, "formula": {"Type": "string", "Value": ""}, "formulaItems": [[{"Type": "string", "Value": "0.4*a"}, {"Type": "number", "Value": 9999.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "0"}, "noLeftBoundary": {"Type": "boolean", "Value": "False"}, "noRightBoundary": {"Type": "boolean", "Value": "False"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}}, "Links": {"0": {"next": ["1.prev"], "AfterBeHealedNode": ["5.prev"]}, "3": {"next": ["4.prev"]}, "5": {"next": ["3.prev"]}}, "DataFlows": {"3": {"targetIds": ["4.targetIds"]}, "5": {"result": ["4.fixedHealV<PERSON>ue"]}}}