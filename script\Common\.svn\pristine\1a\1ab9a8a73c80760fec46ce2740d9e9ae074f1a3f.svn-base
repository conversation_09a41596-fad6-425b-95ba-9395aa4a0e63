﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

---@class BattleTactic
local BattleTactic = DefineClass("BattleTactic")

function BattleTactic:ctor(hero, tacticId, level, stars, isSelfTactic, sourceTacticId, sourceTacticHero)
    self.hero = hero
    self.tacticId = tacticId
    self.uid = self.hero.battleGame:GenerateUid()
    self.sourceTacticId = sourceTacticId -- 来源战法id
    self.sourceTacticHero = sourceTacticHero -- 来源战法所属的英雄
    self.tacticData = GameCommon.TableDataManager:GetTacticData(self.tacticId)
    self.isSelfTactic = isSelfTactic --是自带战法
    assert(self.tacticData, string.format("can not find tactic(%s) data from table.", self.tacticId))
    self.tacticSuit = self.tacticData.tactic_suit
    self.tacticType = self.tacticData.tactic_type
    self.purseType = self.tacticData.purse_type
    self.level = level
    self.stars = stars
    self.maxUseStep = self.tacticData.prepare_step + 1
    self.curUseStep = self.maxUseStep
    self.currentCd = 0

    self.maxCountPerRound = 999999 -- TODO: 不知道还有没有用
    self.useCount = 0

    self.attributes = {}
    self.attributes[TableConst.enums.TacticAttributeType.TotalCD] = self.tacticData.cd
    self.attributes[TableConst.enums.TacticAttributeType.GlobalFactor] = 1

    self._attributeModifierKeyGenerator = 0
    self._attributeModifiers = {}

    self.battleEvents = {}
    local jsonFile = self.tacticData.json_file
    if jsonFile ~= "" then
        local graph = self.hero.battleGame.abilitySystemManager:CreateSkillGraph(jsonFile)
        local i = 1
        while true do
            local eventType = graph.rootNode["event" .. i]
            if not eventType then
                break
            end
            if eventType > 0 then
                local eventTypeName = BattleConstEnv.BattleEventRecordTypeNames[eventType]
                self.battleEvents[eventType] = {}
                self.battleEvents[eventType].name = eventTypeName
                local eventPriorityData = GameCommon.TableDataManager:GetRowByMultiIndices("battle_tbeventpriority", {battle_event = eventType, name = jsonFile})
                self.battleEvents[eventType].priority = eventPriorityData and eventPriorityData.priority or 0
                self.battleEvents[eventType].isSelf = graph.rootNode["onlySelf" .. i]
            end
            i = i + 1
        end
    end

    -- 属性修改记录
    self.attributeModifyRecord = {}
    local tacticData = GameCommon.TableDataManager:GetTacticData(self.tacticId)
    local successProbability = BattleUtilsEnv.ExecuteFormulaForTacticLevel(tacticData.probability, level, stars)
    if successProbability and successProbability ~= 1 then
        self:ModifyAttribute(TableConst.enums.TacticAttributeType.UseFailedProbability,
            1 - successProbability, Config.Battle.MaxRound)
    end
end

function BattleTactic:InitAttributeTypeModifier(attributeType)
    self.attributeModifyRecord[attributeType] = {}
    self.attributeModifyRecord[attributeType].Modifies = {}
    self._attributeModifiers[attributeType] = {}
    self._attributeModifiers[attributeType].Modifies = {}
    self._attributeModifiers[attributeType].isDirty = true
end

function BattleTactic:AddAttributeModifyRecord(attributeType, recordModifyValue, continuousRound, args)
    if continuousRound == -1 then
        return
    end
    if not self.attributeModifyRecord[attributeType] then
        self:InitAttributeTypeModifier(attributeType)
    end
    table.insert(self.attributeModifyRecord[attributeType].Modifies, { recordModifyValue = recordModifyValue, continuousRound = continuousRound, args = args })
end

function BattleTactic:CalculateAttributeModifyContinuousRound()
    for attributeType, attributeModifier in pairs(self.attributeModifyRecord) do
        for i, Modifier in ipairs(attributeModifier.Modifies) do
            if not Modifier.isNeedToCalculateRemainRound then
                return
            end
            Modifier.continuousRound = Modifier.continuousRound - 1
            if Modifier.continuousRound == 0 then
                table.remove(attributeModifier.Modifies, i)
                self:RemoveAttributeModifier(attributeType, Modifier.recordModifyValue, Modifier.args)
            end
        end
    end
end

function BattleTactic:RemoveAttributeModifier(attributeType, recordModifyValue, args)
    if attributeType == TableConst.enums.TacticAttributeType.TotalCD or
        attributeType == TableConst.enums.TacticAttributeType.GlobalFactor then
    end
end

function BattleTactic:Reset()
    self.currentCd = 0
end

function BattleTactic:CalculateCd()
    -- 使用手牌的当回合，cd不会减少
    local curRound = self.hero.battleGame.round
    local lastUseRound = self.lastUseRound or -1
    if lastUseRound >= curRound then
        return
    end

    self.useCount = 0
    self:ModifyCd(-1)
end

function BattleTactic:ModifyCd(val, isSetAs)
    if isSetAs then
        self.currentCd = val
    else
        self.currentCd = self.currentCd + val
    end
    if self.currentCd < 0 then
        self.currentCd = 0
    end
end

function BattleTactic:GetCd()
    return self.currentCd
end

function BattleTactic:GetTotalCD()
    return self:GetAttribute(TableConst.enums.TacticAttributeType.TotalCD)
end

function BattleTactic:DoModifyAttribute(attributeType, val)
    self._attributeModifierKeyGenerator = self._attributeModifierKeyGenerator + 1

    if not self._attributeModifiers[attributeType] then
        self:InitAttributeTypeModifier(attributeType)
    end

    local key = self._attributeModifierKeyGenerator
    local tbl = {}
    tbl.key = key
    tbl.modifierType = attributeType
    tbl.modifyValue = val

    self._attributeModifiers[attributeType].Modifies[key] = tbl
    self._attributeModifiers[attributeType].isDirty = true

    return key
end

function BattleTactic:ModifyAttribute(attributeType, value, continuousRound, args)
    ---剩余cd、总cd、伤害系数的修改实际作用在ability上，通过对应的接口进行修改；记录是为了在效果结束时正确移除对应的效果
    ---是否可用、使用失败概率不需要修改实际的值，获取值时通过记录的修改操作计算出结果即可
    local modifyKey
    if attributeType == TableConst.enums.TacticAttributeType.LeftCD then
        value = math.floor(value)
        self:ModifyCd(value, args.isSetAs)
        return
    elseif attributeType == TableConst.enums.TacticAttributeType.TotalCD then
        value = math.floor(value)
        modifyKey = self:DoModifyAttribute(attributeType, value)
        if args.affectLeftCD then
            self:ModifyCd(value, false)
        end
    elseif attributeType == TableConst.enums.TacticAttributeType.GlobalFactor then
        modifyKey = self:DoModifyAttribute(attributeType, value)
    elseif attributeType == TableConst.enums.TacticAttributeType.IsEnabled then
    elseif attributeType == TableConst.enums.TacticAttributeType.UseFailedProbability then
    end

    self:AddAttributeModifyRecord(attributeType, value, continuousRound, { key = modifyKey })
end

function BattleTactic:GetAttribute(attributeType)
    if attributeType == TableConst.enums.TacticAttributeType.IsEnabled then
        return self:GetEnabled(attributeType)
    elseif attributeType == TableConst.enums.TacticAttributeType.UseFailedProbability then
        return self:GetUseFailedProbability(attributeType)
    else
        if not self._attributeModifiers[attributeType] then
            self:InitAttributeTypeModifier(attributeType)
        else
            if not self._attributeModifiers[attributeType].isDirty then
                return self._attributeModifiers[attributeType].cacheData
            end
        end
        local rawValue = self.attributes[attributeType] or 0
        local modifies = self._attributeModifiers[attributeType].Modifies
        local delta = 0
        if modifies then
            for _, k in pairs(modifies) do
                delta = value + k.modifyValue
            end
        end
        local result = rawValue + delta
        self._attributeModifiers[attributeType].cacheData = result
        self._attributeModifiers[attributeType].isDirty = false
        return result
    end
end

function BattleTactic:CanUse()
    if not self:GetAttribute(TableConst.enums.TacticAttributeType.IsEnabled) then
        return false
    end
    if self.maxCountPerRound > 0 and self.useCount >= self.maxCountPerRound then
        return false
    end
    return self.currentCd <= 0
end

function BattleTactic:GetEnabled(isEnabledAttribute)
    if not self.attributeModifyRecord[isEnabledAttribute] then
        return true
    end
    local enabledResult = true
    for _, enabled in ipairs(self.attributeModifyRecord[isEnabledAttribute].Modifies) do
        if not enabled then
            enabledResult = enabled
            break
        end
    end
    return enabledResult
end

function BattleTactic:GetUseFailedProbability(useFailedProbabilityAttribute)
    if not self.attributeModifyRecord[useFailedProbabilityAttribute] then
        return 0
    end
    local totalFailedProbability = 0
    for _, Modify in ipairs(self.attributeModifyRecord[useFailedProbabilityAttribute].Modifies) do
        totalFailedProbability = totalFailedProbability + Modify.recordModifyValue
    end
    return totalFailedProbability
end

function BattleTactic:IsTacticPreparing()
    return self.curUseStep ~= self.maxUseStep
end

function BattleTactic:InterruptPrepare()
    self.curUseStep = self.maxUseStep
end

function BattleTactic:SkipPrepare()
    self.curUseStep = 1
end

function BattleTactic:Use(args, prob)
    args = args or {}
    local recorder = self.hero.battleGame.recorder
    self.curUseStep = self.curUseStep - 1
    if self.curUseStep > 0 then
        recorder:DetailTacticPrepare(self.hero.uniqueId, self.tacticId, self.curUseStep, prob)
        return false
    end
    self.curUseStep = self.maxUseStep
    local additionalCd = 0
    local detailPointer, currentHeroId = recorder:GetDetailPointerAndHero()
    recorder:DetailTactic(self.hero.uniqueId, self.tacticId, prob or 1)
    args.tactic = self
    args.Round = self.hero.battleGame.round
    args.Level = self.level
    args.Stars = self.stars
    local skillRootPortName
    if args.eventType == nil then --主动释放
        skillRootPortName = "next"
    else
        skillRootPortName = self.battleEvents[args.eventType].name .. "Node"
    end
    local instance = self.hero.battleGame.abilitySystemManager
    local globalFactor = self:GetAttribute(TableConst.enums.TacticAttributeType.GlobalFactor)
    globalFactor = math.max(globalFactor, 0)
    local briefData, reason = instance:UseAbility(self.hero, self.tacticId, skillRootPortName, globalFactor, args)
    local success = false
    if briefData then
        if additionalCd == nil then
            additionalCd = 0
        end
        self.currentCd = self:GetTotalCD() + additionalCd
        self.useCount = self.useCount + 1
        self.hero.battleGame.recorder:BriefRecord(briefData)
        success = true
    end
    if success then
        self.lastUseRound = self.hero.battleGame.round
        local isDerivativeTactic = self.sourceTacticId and self.hero:GetTactic(self.sourceTacticId) == nil --是否是衍生战法
        local recordTacticUseTime = isDerivativeTactic and 0 or 1
        local targetRecordTacticId, targetRecordHero = self:GetSourceTacticIdAndHero()
        recorder:TacticStatistic(targetRecordHero.camp, targetRecordHero.uniqueId, targetRecordTacticId, recordTacticUseTime, 0, 0)
    else
        recorder:SetDetailPointerAndHero(detailPointer, currentHeroId)
    end
    return success
end

function BattleTactic:GetSourceTacticIdAndHero()
    --如果没有源id说明是根战法/英雄
    --后续衍生的战法/Buff的源id都指向根战法/英雄
    return self.sourceTacticId and self.sourceTacticId or self.tacticId, self.sourceTacticHero and self.sourceTacticHero or self.hero
end

function BattleTactic:Dump()
    return {
        heroId = self.hero.uniqueId,
        uid = self.uid,
        cd = self.currentCd,
        enable = self:GetEnabled(TableConst.enums.TacticAttributeType.IsEnabled),
    }
end
