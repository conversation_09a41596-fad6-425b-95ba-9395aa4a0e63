# Bot使用手册

Bot即测试机器人。通过模拟玩家的游戏行为，Bot可以实现很复杂的测试流程，比如跑新手任务等。同时，Bot也很容易堆量，是压测必不可少的工具。

# 启动Bot

引擎自带一些简单的Bot测试用例。以Windows为例，可以直接双击`server.bat`起服；随后双击`bot.bat`启动Bot。

运行结束后，Bot（Avatar）会下线并断开连接，但是Bot进程不会退出。

## Bot配置

Bot的默认配置位于`Bot\Configs\Bot.json`：
```json
{
    "#CONTAINER_NUMS": "Bot进程的Container数量，暂时只支持1",
    "CONTAINER_NUMS": 1,
    "#BOT_NUMS_PER_CONTAINER": "每个Container的Bot数量",
    "BOT_NUMS_PER_CONTAINER": 10,
    "#FPS": "Bot Tick的频率",
    "FPS": 5,
    "#BOT_PREFIX": "Bot urs前缀",
    "BOT_PREFIX": "bot_",
    "SERVER_IP": "127.0.0.1",
    "SERVER_PORT": 30000,
    "#NET_PROTOCOL" : "连接服务器的默认协议，也可以测试用例中指定（优先级更高）",
    "NET_PROTOCOL" : "tcp"
}
```

考虑到需求的多种多样，Bot启动时也可以指定其他配置文件，其他配置文件统一放在`Bot\Configs`

## 启动参数

以加密测试（`bot_crypto.bat`）为例：
```bat
set EZBOT_CASE=NetCrypto
set EZBOT_CASE_ARGS=%1,%2
set EZBOT_CASE_ARGS_SEP=,
set EZBOT_CONFIG=Bot/Configs/NetCrypto.json
ezbot.exe --script=%SCRIPT_ROOT_CLIENT%  --run=Bot.Run
```

- `EZBOT_CASE`：测试用例文件名
- `EZBOT_CASE_ARGS`：测试用例的启动参数，即用例中`Run`方法的参数
- `EZBOT_CASE_ARGS_SEP`：参数间隔符，默认为`,`
- `EZBOT_CONFIG`：测试配置文件，默认为`Bot\Configs\Bot.json`

Linux下只是脚本语法有区别，功能完全一样

# 测试用例（case）

Bot的运行单位是测试用例（case），在一个测试用例（case）内部可以调用另一个case，完全由脚本控制。

## 定义测试用例（case）

测试用例都定义在`Bot\Cases`，其文件名即测试用例名。
以`Idle`为例：
```lua
---@param bot BotClient
---@param seconds float
function Run(bot, seconds)
    seconds = seconds or 10
    bot:sleep(seconds)
    InfoLog("bot[%s] idle done.", bot.urs)
    return true
end
```

- 用例中必须定义一个`Run`方法，第一个参数必须是`BotClient`
- 一般情况下，本文所表述的单个`bot`就是指一个`BotClient`，`BotClient.mainEntity`即为`Account`或`Avatar`
- `Run`方法的返回值是`boolean`，当返回false时，该bot会下线并销毁。
- `Run`方法中可以调用其他`case`，注意参数需要一并传入：
    ```lua
    bot:runCase("Login", "tcp", "ChaCha20")
    ```

### hook

Bot使用协程处理rpc等异步操作。简单来说，Bot把`yield -> hook并等待异步回调 -> resume`过程封装为`setHooks`接口。其函数如下：
```lua
---@param hooks table<string, fun> @需要hook的方法名和回调，支持同时hook多个方法
---@param yield boolean @是否在hook之后自动调用yield挂起
---@param ar? AsyncResult | nil @yield返回给主协程的参数
---@param timeout? integer | nil @在yield的情况下生效，默认10秒，0表示没有超时限制
---@param careRawRes? boolean | nil @是否需要原方法的返回值，如填true，hook回调第一个参数就是原方法的返回值
---@return AsyncResult | nil @主协程resume回来的参数
function setHooks(hooks, yield, ar, timeout, careRawRes)
    -- ...
end
```

- hook回调的参数和原方法的参数一致；假如原方法在执行时出现异常，hook回调将被略过，最终可能会超时（取决于timeout参数）
- hook回调的返回值为`true`时，将在下一个tick时`resume`
- 在调用setHooks之前，需要绑定hook对象；bot会自动绑定Account和Avatar

以`Login`为例：

```lua
local ar = bot:setHooks(
    {
        -- hook onGetControl方法
        onGetControl = function(self, ...)
            -- Account和Avatar都有onGetControl方法，只有当Avatar调用onGetControl时，才可认为登陆成功
            if self.__cname == "Avatar" or self.__cname == "TestAvatar" then return true end
        end
    },
    true
)
if ar then -- 如果出现异常（如超时等），ar可能是ERROR或TIMEOUT
    ErrorLog("%s login failed! err: %s", bot.urs, ar)
    return false
end
```

### wait

`setHooks`的功能非常齐全，但很多时候显得有些复杂，所以也提供了简化版本`wait`

```lua
---@param funName string
---@param timeout? number @seconds
---@return AsyncResult, ...
function AsyncExecutorHolder:wait(funName, timeout)
    return self:setHooks({[funName]=function(self, ...)
        return true, ...
    end}, true, nil, timeout)
end

-- 以testRpcFromClient和testRpcToClient为例
bot.mainEntity:serverRpc("testRpcFromClient", a, b, c)
--[[
-- 全功能接口
local ar = bot:setHooks({testRpcToClient = function(self, _a, _b, _c)
    return a == _a and b == _b and c ==_c
end}, true)
if ar then
    InfoLog("%s rpc test failed! err: %s", bot.urs, ar)
    return false
end
--]]
-- 简化接口
-- 其中err是错误信息，正常情况为nil；_a, _b, _c是回调testRpcToClient的参数
local err, _a, _b, _c = bot:wait("testRpcToClient", 10)
if err or a ~= _a or b ~= _b or c ~=_c then
    ErrorLog("%s rpc test failed! err: %s, a: %v, b: %s, c: %s", bot.urs, err, _a, _b, _c)
    return false
end
```

### sleep

`sleep`是`yield -> 等待 -> resume`的封装，参数单位是秒。它的使用非常简单：
```lua
bot:sleep(5)
```


# TODO

- [ ] 玩家操作录制
- [ ] Bot支持多个Container
- [ ] Bot跑完一个case之后，可以通过指令运行其他case
