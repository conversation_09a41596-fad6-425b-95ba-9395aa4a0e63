local TimeIt = xrequire("Framework.Utils.TimeIt")

xrequire("Test.LuaPropPerf")

local function DefineCase(name)
    local cls = {}
    cls.name = name
    return cls
end

-- list 顺序访问
local ListSequentialAccess = DefineCase("ListSequentialAccess")
ListSequentialAccess.Length = 1000
function ListSequentialAccess.Setup()
    local ent = EntityManager.createEntity("LuaPropPerf", 0, {})
    Entity = ent

    local list = ent.props.list
    for i = 1, ListSequentialAccess.Length do
        list[i] = tostring(i)
    end
    List = list
end
function ListSequentialAccess.Run()
    local list = List
    for i = 1, ListSequentialAccess.Length do
        local v = list[i]
    end
end
function ListSequentialAccess.Teardown()
    Entity:destroy()
end

-- list 随机访问
local ListRandomAccess = DefineCase("ListRandomAccess")
ListRandomAccess.Length = 1000
function ListRandomAccess.Setup()
    local ent = EntityManager.createEntity("LuaPropPerf", 0, {})
    Entity = ent

    local list = ent.props.list
    for i = 1, ListRandomAccess.Length do
        list[i] = tostring(i)
    end
    List = list

    math.randomseed(0)
    local idxs = {}
    for i = 1, ListRandomAccess.Length do
        idxs[i] = math.random(1, ListRandomAccess.Length)
    end
    Idxs = idxs
end
function ListRandomAccess.Run()
    local list = List
    local idxs = Idxs
    for _, idx in ipairs(idxs) do
        local v = list[idx]
    end
end
function ListRandomAccess.Teardown()
    Entity:destroy()
end

-- list 遍历
local ListIterate = DefineCase("ListIterate")
ListIterate.Length = 1000
function ListIterate.Setup()
    local ent = EntityManager.createEntity("LuaPropPerf", 0, {})
    Entity = ent

    local list = ent.props.list
    for i = 1, ListIterate.Length do
        list[i] = tostring(i)
    end
    List = list
end
function ListIterate.Run()
    local list = List
    for _, v in ipairs(list) do
        local o = v
    end
end
function ListIterate.Teardown()
    Entity:destroy()
end

-- dict 随机访问
local DictRandomAccess = DefineCase("DictRandomAccess")
DictRandomAccess.Length = 1000
function DictRandomAccess.Setup()
    local ent = EntityManager.createEntity("LuaPropPerf", 0, {})
    Entity = ent

    local dict = ent.props.dict
    for i = 1, DictRandomAccess.Length do
        dict[tostring(i)] = tostring(i)
    end
    Dict = dict

    math.randomseed(0)
    local idxs = {}
    for i = 1, DictRandomAccess.Length do
        idxs[i] = tostring(math.random(1, DictRandomAccess.Length))
    end
    Idxs = idxs
end
function DictRandomAccess.Run()
    local dict = Dict
    local idxs = Idxs
    for _, idx in ipairs(idxs) do
        local v = dict[idx]
    end
end
function DictRandomAccess.Teardown()
    Entity:destroy()
end

-- dict 遍历
local DictIterate = DefineCase("DictIterate")
DictIterate.Length = 1000
function DictIterate.Setup()
    local ent = EntityManager.createEntity("LuaPropPerf", 0, {})
    Entity = ent

    local dict = ent.props.dict
    for i = 1, DictIterate.Length do
        dict[tostring(i)] = tostring(i)
    end
    Dict = dict
end
function DictIterate.Run()
    local dict = Dict
    for _, v in pairs(dict) do
        local o = v
    end
end
function DictIterate.Teardown()
    Entity:destroy()
end

-- stuct 访问
local StructAccess = DefineCase("StructAccess")
StructAccess.Samples = 1000
function StructAccess.Setup()
    local ent = EntityManager.createEntity("LuaPropPerf", 0, {})
    Entity = ent

    local struct = ent.props.struct
    struct.a = "a"
    struct.b = "b"
    struct.c = "c"
    struct.d = "d"
    struct.e = "e"
    Struct = struct

    math.randomseed(0)
    local idxs = {}
    for i = 1, StructAccess.Samples do
        idxs[i] = string.char(string.byte("a") - 1 + math.random(1, 5))
    end
    Idxs = idxs
end
function StructAccess.Run()
    local struct = Struct
    local idxs = Idxs
    for _, idx in ipairs(idxs) do
        local v = struct[idx]
    end
end
function StructAccess.Teardown()
    Entity:destroy()
end

local Cases = {
    ListSequentialAccess,
    ListRandomAccess,
    ListIterate,
    DictRandomAccess,
    DictIterate,
    StructAccess,
}

local function RunCases()
    for _, case in ipairs(Cases) do
        local env = {}
        local t = TimeIt.Timer.new(case.Run, case.Setup, case.Teardown, EZE.getTimeNow)
        local total, count = t:AutoRange()
        print(string.format("%s total: %.3fms, count: %d, avg: %.3fms", case.name, total * 1000, count, total * 1000 / count))
    end
end

return {RunCases = RunCases}
