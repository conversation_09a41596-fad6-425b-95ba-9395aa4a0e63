-- Core
GetFromBlackBoardNode = xrequire("Common.Battle.AbilitySystem.Nodes.Core.GetFromBlackBoardNode").GetFromBlackBoardNode
SetToBlackBoardNode = xrequire("Common.Battle.AbilitySystem.Nodes.Core.SetToBlackBoardNode").SetToBlackBoardNode
RootNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Core.RootNodeServer").RootNodeServer
SkillRootNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Core.SkillRootNodeServer").SkillRootNodeServer
BuffRootNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Core.BuffRootNodeServer").BuffRootNodeServer

SetVariableNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Core.SetVariableNodeServer").SetVariableNodeServer
GetVariableNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Core.GetVariableNodeServer").GetVariableNodeServer
-- Logics
StartNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.StartNodeServer").StartNodeServer
BranchNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.BranchNodeServer").BranchNodeServer
SelectNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.SelectNodeServer").SelectNodeServer
DamageNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.DamageNodeServer").DamageNodeServer
CheckNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.CheckNodeServer").CheckNodeServer
AndNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.AndNodeServer").AndNodeServer
OrNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.OrNodeServer").OrNodeServer
CompareCurArmyTypeNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.CompareCurArmyTypeNodeServer").CompareCurArmyTypeNodeServer
ListProcessingNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.ListProcessingNodeServer").ListProcessingNodeServer
DictProcessingNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.DictProcessingNodeServer").DictProcessingNodeServer
ModifyDamageNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.ModifyDamageNodeServer").ModifyDamageNodeServer
ModifyPositionNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.ModifyPositionNodeServer").ModifyPositionNodeServer
OrNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.OrNodeServer").OrNodeServer
CrowdControlNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.CrowdControlNodeServer").CrowdControlNodeServer
ModifyAttributeNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.ModifyAttributeNodeServer").ModifyAttributeNodeServer
AddBuffNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.AddBuffNodeServer").AddBuffNodeServer
HealNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.HealNodeServer").HealNodeServer
GetAttributeNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.GetAttributeNodeServer").GetAttributeNodeServer
CalculateNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.CalculateNodeServer").CalculateNodeServer
RandomNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.RandomNodeServer").RandomNodeServer
CompareNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.CompareNodeServer").CompareNodeServer
--LoopNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.LoopNodeServer").LoopNodeServer
CountBuffNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.CountBuffNodeServer").CountBuffNodeServer
ReplacePlainAttackNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.ReplacePlainAttackNodeServer").ReplacePlainAttackNodeServer
UseTacticNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.UseTacticNodeServer").UseTacticNodeServer
GetHeroTacticNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.GetHeroTacticNodeServer").GetHeroTacticNodeServer
GetTacticIdsNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.GetTacticIdsNodeServer").GetTacticIdsNodeServer
RemoveBuffNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.RemoveBuffNodeServer").RemoveBuffNodeServer
PlainAttackNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.PlainAttackNodeServer").PlainAttackNodeServer
PrintNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.PrintNodeServer").PrintNodeServer
SetToBlackBoardNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.SetToBlackBoardNodeServer").SetToBlackBoardNodeServer
DecreaseBuffLayerNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.DecreaseBuffLayerNodeServer").DecreaseBuffLayerNodeServer
FormulaNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.FormulaNodeServer").FormulaNodeServer
OverwriteBattleFormulaNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.OverwriteBattleFormulaNodeServer").OverwriteBattleFormulaNodeServer
ChangeSelectTargetNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Logics.ChangeSelectTargetNodeServer").ChangeSelectTargetNodeServer

-- Views
AnimationNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Views.AnimationNodeServer").AnimationNodeServer
DummyViewNodeServer = xrequire("Common.Battle.AbilitySystem.Nodes.Views.DummyViewNodeServer").DummyViewNodeServer
