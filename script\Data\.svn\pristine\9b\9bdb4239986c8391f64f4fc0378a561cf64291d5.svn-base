[{"id": 999999, "name": "普通攻击", "tactic_type": 5, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_999999", "description": "", "brief_description": "", "sprite_path": "sgs_002", "icon_path": "pic_KillCircle", "tactic_grade": 1, "tactic_feature": 1, "born_with": false, "learnable": false, "TAGS": [], "formulas": []}, {"id": 101001, "name": "忠勇", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10100101", "description": "使另外2名友军的武力、智力、统率提升|18+lv*2+stars*2|点", "brief_description": "提升我军群体属性", "sprite_path": "zhanfa_1", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 101002, "name": "援护", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10100201", "description": "前3回合，使我军群体2人受到的伤害降低|18+lv*2(%)|", "brief_description": "降低我军群体收到伤害", "sprite_path": "zhanfa_2", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 4, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 101003, "name": "亲民", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10100301", "description": "每次对单位造成治疗时，提升其|4.5+lv*0.5(%)|的减伤，持续2回合，可叠加4次", "brief_description": "治疗时降低伤害", "sprite_path": "zhanfa_3", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 4, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 101004, "name": "五禽戏", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10100401", "description": "使用主动和追击技能时，对随机友方单位进行治疗(治疗率|90+lv*10(%)|，受智力影响)并使其获得15%减伤，持续1回合", "brief_description": "使用主动、追击技能时进行治疗", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 4, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 101005, "name": "请君入瓮", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10100501", "description": "回合开始时有|30+lv*3(%)|概率使我军随机队友(优先我方非主将单位)获得<keyword>嘲讽</keyword>，并使其获得急救，受到伤害后有50%概率治疗自身，治疗率50%-100%;持续1回合", "brief_description": "使队友获得嘲讽", "sprite_path": "zhanfa_5", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 4, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102001, "name": "水淹七军", "tactic_type": 1, "probability": "0.4", "prepare_step": 0, "cd": 0, "json_file": "skill_10200101", "description": "对敌方全体造成兵刃伤害(伤害率|54+lv*6(%)|)，本局每使用过一次该技能，下次使用时永久额外生效一次，额外生效的伤害为正常伤害的50%；最多额外生效2次.", "brief_description": "对敌军全体造成兵刃伤害", "sprite_path": "zhanfa_7", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102002, "name": "四面楚歌", "tactic_type": 1, "probability": "0.4", "prepare_step": 0, "cd": 0, "json_file": "skill_10200201", "description": "随机对敌方主将或先锋造成4次兵刃伤害(伤害率|50+lv*5(%)|)，最后一次兵刃伤害有50%概率暴击", "brief_description": "造成4次兵刃伤害", "sprite_path": "zhanfa_8", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102003, "name": "散谣", "tactic_type": 1, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10200301", "description": "降低敌方2-3人的武力、智力、统率|18+lv*2|点(受智力影响)，持续2回合，最多叠加2次", "brief_description": "降低敌军群体属性", "sprite_path": "zhanfa_9", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102004, "name": "麻沸散", "tactic_type": 1, "probability": "0.5", "prepare_step": 0, "cd": 0, "json_file": "skill_10200401", "description": "清除我军群体2人负面状态，并恢复其兵力(治疗率|110+lv*10(%)|，受智力影响)", "brief_description": "驱散我军群体负面状态并治疗", "sprite_path": "zhanfa_10", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 3, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102005, "name": "包扎", "tactic_type": 1, "probability": "0.45", "prepare_step": 0, "cd": 0, "json_file": "skill_10200501", "description": "恢复我军群体2人兵力(治疗率|58+lv*6(%)|，受智力影响)", "brief_description": "群体治疗", "sprite_path": "zhanfa_11", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 3, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102006, "name": "扬威", "tactic_type": 1, "probability": "0.4", "prepare_step": 0, "cd": 0, "json_file": "skill_10200601", "description": "使自己获得|12+lv*2(%)|暴击概率，持续2回合；然后对敌方随机2人造成兵刃伤害(伤害率|66+lv*6(%)|)", "brief_description": "提升暴击率并造成群体兵刃伤害", "sprite_path": "zhanfa_12", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102007, "name": "百骑劫营", "tactic_type": 1, "probability": "0.45", "prepare_step": 0, "cd": 0, "json_file": "skill_10200701", "description": "对敌方单体造成兵刃伤害(伤害率|126+lv*12(%)|)，若本次伤害暴击，则额外对敌方主将造成兵刃伤害(伤害率|40+lv*5(%)|)", "brief_description": "造成单体兵刃伤害", "sprite_path": "zhanfa_13", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102008, "name": "金针渡", "tactic_type": 1, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10200801", "description": "使我军随机2-3人获得<keyword>自愈</keyword>状态，持续2回合，每回合行动开始时回复兵力(治疗率|58+lv*6(%)|)", "brief_description": "群体治疗", "sprite_path": "zhanfa_14", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 3, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102009, "name": "洛水惊鸿​", "tactic_type": 1, "probability": "0.4", "prepare_step": 0, "cd": 0, "json_file": "skill_10200901", "description": "使敌军武力最低的单体对自身造成兵刃伤害(伤害率|90+lv*9(%)|)，然后对敌方群体造成谋略伤害(伤害率|50+lv*5(%)|)并附加<keyword>水攻</keyword>", "brief_description": "使自己受到兵刃伤害然后造成群体谋略伤害", "sprite_path": "zhanfa_15", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 5, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102010, "name": "兵粮寸断", "tactic_type": 1, "probability": "0.5", "prepare_step": 0, "cd": 0, "json_file": "skill_10201001", "description": "对敌方群体2-3人造成谋略伤害(伤害率|45+lv*4.5(%)|)，并附加<keyword>断粮</keyword>，持续1回合", "brief_description": "造成群体谋略伤害和断粮", "sprite_path": "zhanfa_16", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 5, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102011, "name": "十面埋伏", "tactic_type": 1, "probability": "0.22+lv*0.02", "prepare_step": 1, "cd": 0, "json_file": "skill_10201101", "description": "准备一回合，对敌军武力、智力、速度最高的人造成<keyword>虚弱</keyword>、<keyword>断粮</keyword>、<keyword>畏惧</keyword>状态，持续2回合", "brief_description": "造成群体谋略伤害和断粮", "sprite_path": "zhanfa_17", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103001, "name": "短兵", "tactic_type": 3, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10300101", "description": "普通攻击之后，降低攻击目标|72+lv*8|点统率，然后对其造成兵刃伤害(伤害率|72+lv*8(%)|)", "brief_description": "降低单体统率后造成兵刃伤害", "sprite_path": "zhanfa_20", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103002, "name": "温酒杀敌", "tactic_type": 3, "probability": "0.4", "prepare_step": 0, "cd": 0, "json_file": "skill_10300201", "description": "普通攻击之后，对目标再造成一次兵刃伤害(伤害率|90+lv*10(%)|)，并<keyword>技穷</keyword>(无法发动主动战法)一回合", "brief_description": "造成单体兵刃伤害和技穷", "sprite_path": "zhanfa_1", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103003, "name": "破阵", "tactic_type": 3, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10300301", "description": "普通攻击之后，对敌方全体造成兵刃伤害(伤害率|45+lv*5(%)|)，对于武力低于自己的单位额外再造成兵刃伤害(伤害率|18+lv*2(%)|)", "brief_description": "对敌军全体造成兵刃伤害", "sprite_path": "zhanfa_2", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 104001, "name": "血战", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10400101", "description": "战斗开始时获得|35+lv*5(%)|连击率与防御-100；每回合衰减10%", "brief_description": "提高自身连击率并降低自身统率", "sprite_path": "zhanfa_3", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 105001, "name": "虎臣枪卫", "tactic_type": 6, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10500101", "description": "将枪兵进阶为威力巨大的虎臣枪卫，前4回合我军先锋造成兵刃伤害时，有|23+lv*3(%)|概率使其暴击，该效果每回合只能触发一次", "brief_description": "将弓兵进阶为威力巨大的元戎弩兵", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 106001, "name": "虎翼阵", "tactic_type": 7, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10600101", "description": "我方主公同时视为先锋；前3回合2名先锋造成的伤害提升|12+lv*2(%)|，受到伤害提升20%", "brief_description": "我方军师视为先锋且获得增益，但会削弱主将", "sprite_path": "zhanfa_5", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 105002, "name": "铜雀台怜", "tactic_type": 6, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_105002", "description": "将弓兵进阶为身形灵巧的铜雀台怜，携带者弓兵适应性提升1级（最高不超过S），且我方全体躲避率提升|9+lv*1(%)|，前三回合额外提升|5+lv*0.5(%)|", "brief_description": "将弓兵进阶为身形灵巧的铜雀台怜", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "army_type_qualification_id": 101, "formulas": []}, {"id": 106002, "name": "胭脂阵", "tactic_type": 7, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_106002", "description": "我军均为女武将时生效：我军全体享受携带者的国家加成，且先锋回合结束时恢复兵力(治疗率|45+lv*5|(%))，主公自带主动战法/追击战法发动率提升|9+lv*1|(%))，军师武力、智力、速度、统率提升|18+lv*2|)", "brief_description": "女武将获得属性加成和治疗", "sprite_path": "zhanfa_5", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 111001, "name": "仁德", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11100101", "description": "每回合行动时回复3名武将血量(治疗率|45+lv*5|(%))受智力影响，且有15%概率清除先锋的所有负面状态，并使其武力、智力、速度提升3%，持续2回合,最多叠加2次。\n<keyword>主公技</keyword>：普攻时有10%概率邀请友方一起普攻一次(每回合仅限触发一次)", "brief_description": "", "sprite_path": "zhanfa_6", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 3, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 111002, "name": "破军", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11100201", "description": "对敌人造成的非普攻伤害提升|39+lv*4(%)|，该效果最多发动6次\n自身为先锋时额外提升5%伤害", "brief_description": "", "sprite_path": "zhanfa_7", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 111003, "name": "奸雄", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11100301", "description": "当我军受到伤害时，有80%概率偷取伤害来源|9+lv*1|点武力、智力、统率属性（受智力影响），最多叠加2层，持续2回合；每回合最多触发4次\n<keyword>主公技</keyword>：受伤时有30%概率由友方承受", "brief_description": "", "sprite_path": "zhanfa_8", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 111004, "name": "鬼才", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11100401", "description": "鬼才：战斗开始时，使我方智力最高单体获得鬼才：使用自带主动技能、追击技能失败时，使其额外尝试一次，该次尝试概率增加5%（受智力影响）；若其成功释放自带技能 则永久增加先锋10点武力和智力，最多可叠加8层 <keyword>军师技</keyword>：最多可叠加12层", "brief_description": "", "sprite_path": "zhanfa_9", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 111005, "name": "雷击", "tactic_type": 4, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11100501", "description": "雷击：获得20%闪避率，当你成功闪避伤害后有40%概率对随机敌方单体造成80%谋略伤害，40%概率治疗血量最低的友方单位；每回合最多触发8次\n<keyword>主公技</keyword>：你的队友行动时赋予你15%的闪避率 可叠加 持续1回合", "brief_description": "", "sprite_path": "zhanfa_9", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 112001, "name": "洛神", "tactic_type": 1, "probability": "0.7", "prepare_step": 0, "cd": 0, "json_file": "skill_11200101", "description": "对随机敌方单体造成谋略伤害(伤害率|72+lv*3.6(%)|)，若成功使用该技能，则再次尝试使用该技能，直到使用失败为止；单回合内每成功使用一次洛神概率降低20%", "brief_description": "", "sprite_path": "zhanfa_11", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 112005, "name": "离间", "tactic_type": 1, "probability": "0.24+lv*0.04", "prepare_step": 0, "cd": 0, "json_file": "skill_11200501", "description": "使己方武力最高单体与随机敌方单位分别普攻2次，己方率先出手，期间无视缴械与震慑状态", "brief_description": "", "sprite_path": "zhanfa_11", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 113001, "name": "奇袭", "tactic_type": 3, "probability": "0.8", "prepare_step": 0, "cd": 0, "json_file": "skill_11300101", "description": "普通攻击之后，降低目标100点统率然后对随机敌军单体发起一次兵刃攻击(伤害率|120+lv*12(%)|)，若命中敌方军师则造成<keyword>技穷</keyword>，持续1回合；每发动一次奇袭，降低10%发动概率\n<keyword>先锋技</keyword>：伤害率提升10%", "brief_description": "", "sprite_path": "zhanfa_13", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 114001, "name": "咆哮", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11400101", "description": "回合开始时获得30%兵刃伤害提升，每普攻一次造成兵刃伤害降低20%，回合结束时重置；张飞每回合有60%概率额外普攻一次；30%概率额外普攻2次；10%概率额外普攻3次\n<keyword>先锋技</keyword>：每回合额外获得20%兵刃伤害提升", "brief_description": "", "sprite_path": "zhanfa_15", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 114002, "name": "武圣", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11400202", "description": "武圣：自身普通攻击后，有40%概率发动携带的主动战法（每个战法最多发动1次），并使全体敌军受到的伤害提升4%，可叠加8次，持续到战斗结束。 当自身为军师时，自带战法普攻触发目标由自己改为自己和武力最高的友军。 ", "brief_description": "", "sprite_path": "zhanfa_16", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 114003, "name": "不屈", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11400301", "description": "战斗开始时获得13层[不屈]，周泰持有[不屈]时，可为己方英雄承担30%其受到的伤害（本次伤害降低30% 受统率影响）；周泰死亡时消耗1层不屈并免疫此次致命伤害；队友造成会心或奇谋时，消耗1层不屈令本次伤害提升30%", "brief_description": "", "sprite_path": "zhanfa_17", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 4, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 114004, "name": "刚烈", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11400401", "description": "刚烈：受到伤害后，80%概率对目标造成|36+lv*4(%)|兵刃伤害（受武力影响），并使目标获得威慑（下一次主动或追击战法目标优先选择夏侯惇且造成伤害降低35%）；每回合最多2次。", "brief_description": "", "sprite_path": "zhanfa_18", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 123456, "name": "测试", "tactic_type": 3, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_123456", "description": "", "brief_description": "测试专用请勿携带", "sprite_path": "", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": [["智力", "1234"], ["2", "2=321"], ["3", "3="], ["4", "4="], ["5", "5="]]}, {"id": 102012, "name": "近攻", "tactic_type": 1, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10201201", "description": "对随机敌军单体造成兵刃伤害(伤害率|50+lv*5(%)|)", "brief_description": "造成单体兵刃伤害", "sprite_path": "zhanfa_1", "icon_path": "pic-dodge", "tactic_grade": 2, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102013, "name": "远射", "tactic_type": 1, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10201301", "description": "对随机敌军单体造成谋略伤害(伤害率|50+lv*5(%)|)", "brief_description": "造成单体谋略伤害", "sprite_path": "zhanfa_2", "icon_path": "pic-dodge", "tactic_grade": 2, "tactic_feature": 5, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 102014, "name": "急救", "tactic_type": 1, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10201401", "description": "恢复军我军随即单体兵力(治疗率|50+lv*5(%)|)", "brief_description": "回复单体兵力", "sprite_path": "zhanfa_3", "icon_path": "pic-dodge", "tactic_grade": 2, "tactic_feature": 3, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103004, "name": "追杀", "tactic_type": 3, "probability": "0.35", "prepare_step": 0, "cd": 0, "json_file": "skill_10300401", "description": "普通攻击之后，对目标再造成一次兵刃伤害(伤害率|40+lv*4(%)|)", "brief_description": "造成单体兵刃伤害", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 2, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 112003, "name": "奇才", "tactic_type": 1, "probability": "0.5", "prepare_step": 0, "cd": 0, "json_file": "skill_11200301", "description": "对敌方随机两人造成谋略伤害（(伤害率|50+lv*5(%)|)），并使自身获得【集智】，持续2回合：友军使用主动或追击技能后，会再次尝试施放奇才。单回合内每使用一次奇才，奇才概率降低10%", "brief_description": "", "sprite_path": "zhanfa_1", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 5, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 114005, "name": "淑慎", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_11400501", "description": "自身受到的治疗效果提升|18+lv*2(%)|).当自身受到治疗时，为随机友军回复生命，相当于本次治疗的40%。", "brief_description": "", "sprite_path": "zhanfa_2", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 3, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 113002, "name": "枭姬", "tactic_type": 3, "probability": "0.5", "prepare_step": 0, "cd": 0, "json_file": "skill_11300201", "description": "随机发动【舞刀】【弄枪】【弓射】中的追击战法，若该战法本局已发动过，则再额外发动一次（本次战法不会触发枭姬）。每次触发枭姬后，获得1层弓腰姬增益，每层使自身造成的兵刃伤害提升3%，最多叠加10次", "brief_description": "", "sprite_path": "zhanfa_3", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 6, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 112002, "name": "天香", "tactic_type": 1, "probability": "0.6", "prepare_step": 0, "cd": 0, "json_file": "skill_11200201", "description": "自身躲避率提高|18+lv*2(%)|，持续2回合，自身躲避成功后，有60%概率对随机敌方目标造成一次谋略伤害，数值等同于本次闪避掉伤害的80%", "brief_description": "", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 4, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}, {"id": 103005, "name": "舞刀", "tactic_type": 3, "probability": "0.3", "prepare_step": 0, "cd": 0, "json_file": "skill_10300501", "description": "普通攻击之后，对目标再造成一次兵刃伤害(伤害率|40+lv*4(%)|)，并使自身获得必中效果，持续1回合", "brief_description": "造成单体兵刃伤害和必中", "sprite_path": "zhanfa_1", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103006, "name": "弄枪", "tactic_type": 3, "probability": "0.3", "prepare_step": 0, "cd": 0, "json_file": "skill_10300601", "description": "普通攻击之后，对目标再造成一次兵刃伤害(伤害率|40+lv*4(%)|)，并使目标陷入技穷效果，持续1回合", "brief_description": "造成单体兵刃伤害和技穷", "sprite_path": "zhanfa_2", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103007, "name": "弓射", "tactic_type": 3, "probability": "0.3", "prepare_step": 0, "cd": 0, "json_file": "skill_10300701", "description": "普通攻击之后，对目标再造成一次兵刃伤害(伤害率|40+lv*4(%)|)，并使目标陷入缴械效果，持续1回合", "brief_description": "造成单体兵刃伤害和缴械", "sprite_path": "zhanfa_3", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 6, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 103008, "name": "文伐", "tactic_type": 3, "probability": "0.3", "prepare_step": 0, "cd": 0, "json_file": "skill_10300801", "description": "普通攻击之后，对目标再造成一次谋略伤害(伤害率|40+lv*4(%)|)，并使目标陷入断粮效果，持续1回合", "brief_description": "造成单体兵刃伤害和断粮", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 3, "tactic_feature": 5, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 104002, "name": "自救", "tactic_type": 2, "probability": "1", "prepare_step": 0, "cd": 0, "json_file": "skill_10400201", "description": "每回合行动时，恢复自身兵力(治疗率|75+lv*7.5(%)|)，有|25+lv*2.5(%)|概率额外生效一次", "brief_description": "每回合治疗自身", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 3, "born_with": false, "learnable": true, "TAGS": [], "formulas": []}, {"id": 112004, "name": "国色", "tactic_type": 1, "probability": "0.36", "prepare_step": 0, "cd": 0, "json_file": "skill_11200401", "description": "使随机1-2个敌方目标陷入震慑状态，持续1回合,若目标已处于震慑状态，则额外造成谋略伤害（伤害率100%）。发动后治疗自身，治疗率100%", "brief_description": "", "sprite_path": "zhanfa_4", "icon_path": "pic-dodge", "tactic_grade": 4, "tactic_feature": 1, "born_with": true, "learnable": false, "TAGS": [], "formulas": []}]