﻿---@class JsonDataManager
---@field jsonDataPath string
---@field jsonDataCache table
JsonDataManager = DefineClass("JsonDataManager")

function JsonDataManager:ctor()
    if IsServer then
        self.jsonDataPath = EZE.getScriptPath() .. "/Data/"
    else
        self.jsonDataPath = "Assets/DataExport/"
    end
    self.jsonDataCache = {}
end

function JsonDataManager:LoadJsonData(path)
    local data = self.jsonDataCache[path]
    if data ~= nil then
        return data
    end

    data = self:LoadJsonDataWithoutCache(path)
    self.jsonDataCache[path] = data

    return data
end

function JsonDataManager:LoadJsonDataWithoutCache(path)
    local content = nil
    if IsServer then
        content = self:loadJsonFileRaw(self.jsonDataPath .. path .. ".json")
    else
        content = self:loadJsonFileUnity(self.jsonDataPath .. path .. ".json")
    end

    local data = GameCommon.json.decode(content)
    return data
end

function JsonDataManager:loadJsonFileUnity(path)
    local request = Temp.AssetManager.LoadAssetAutoRelease(path, typeof(UnityEngine.TextAsset))
    assert(request, "can not load json " .. path)
    return request.asset.text
end

function JsonDataManager:loadJsonFileRaw(path)
    local file = io.open(path, "r")
    if file == nil then
        error(string.format("Can not find json file %s", path))
    end
    local content = file:read("*a")
    file:close()
    return content
end
