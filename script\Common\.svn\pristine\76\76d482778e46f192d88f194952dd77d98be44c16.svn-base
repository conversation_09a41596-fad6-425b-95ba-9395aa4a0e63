﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local BattleTacticEnv = xrequire("Common.Battle.BattleTactic")
local BattleBuffEnv = xrequire("Common.Battle.BattleBuff")
local AttributeHolderEnv = xrequire("Common.Battle.AttributeHolder")
local TableEnumUtilsEnv = xrequire("Common.Utils.TableEnumUtils")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

---@class BattleHero
local BattleHero = DefineClass("BattleHero")

function BattleHero:ctor(battleGame, camp, index, data, teamData)
    self.battleGame = battleGame
    self.data = data
    self.teamData = teamData
    -- 基本信息
    self.heroId = data.heroId
    self.index = index
    self.camp = camp
    self.uniqueId = BattleUtilsEnv.GetHeroUniqueId(self.camp, self.heroId)
    self.level = data.level
    self.stars = data.stars
    self.dynasty = data.dynasty
    self.armyType = teamData.battleArmyData.armyType
    self.advanceArmyType = teamData.battleArmyData.advanceArmyType or TableConst.enums.GeneralArmyType.None
    self.supply = teamData.supply or 100
    self.isAlive = true
    -- 属性
    self.attributeHolder = AttributeHolderEnv.HeroAttributeHolder.new(self)
    for _, attributeType in pairs(TableConst.enums.BattleAttributeType) do
        local attributeData = GameCommon.TableDataManager:GetAttributeData(attributeType)
        if data.attributes[attributeType] then
            self.attributeHolder:InitAttribute(attributeType, data.attributes[attributeType], {attributeData.min_value, attributeData.max_value})
        else
            self.attributeHolder:InitAttribute(attributeType, attributeData.default_value, {attributeData.min_value, attributeData.max_value})
        end
    end
    self.speedOrderRandomValue = 0 -- 速度顺序随机值
    -- 普攻
    self.plainAttackId = Config.Battle.PlainAttackTacticId
    -- 战法
    self.selfTactics = data.selfTactics
    self.carryingTactics = data.carryingTactics or {}
    self.tacticsData = {}
    self.tactics = {}
    self.activeTactics = {} --主动战法
    self.pursueTactics = {} --追击战法
    -- buff
    self.buffs = {} --自身拥有的buff
    self.extraBuffs = {} --局外传入的buff
    self:InitExtraBuffs(teamData.buffs)
    self.delayAddBuffs = {} -- 延迟添加的buff，等待特定时机
    self.appliedBuffs = {} --自己施加的buff 用于自身死亡时移除对应的buff
    self.crowdControls = {}
    self.positiveEffects = {}
    -- 优先选择目标
    self.priorSelectTargets = {}
    self._priorSelectTargetsKeyGenerator = 0
    -- other
    self.damagePackage = nil
end

function BattleHero:Reset()
    self.isAlive = true
    self.damagePackage = nil
    self.attributeHolder:Reset()
    self.crowdControls = {}
    self.positiveEffects = {}
    self.priorSelectTargets = {}
    self._priorSelectTargetsKeyGenerator = 0
    self.buffs = {}
    self.appliedBuffs = {}
    self.battleGame.battleGameEvent:UnregisterHeroBattleEvents(self.uniqueId)
    self.tacticsData = {}
    table.extend(self.tacticsData, self.selfTactics, self.carryingTactics)
    self.tactics = {}
    self.activeTactics = {}
    self.pursueTactics = {}
    for _, tactic in ipairs(self.selfTactics) do
        self:AddTactic(tactic, true)
    end
    for _, tactic in ipairs(self.carryingTactics) do
        self:AddTactic(tactic, false)
    end
    self.plainAttackTactic = BattleTacticEnv.BattleTactic.new(self, self.plainAttackId, 1, 1)

    self:AddBuff(999001, self.uniqueId, 1, 1)
    for _, buff in pairs(self.extraBuffs) do
        local buffData = GameCommon.TableDataManager:GetBuffData(buff.id)
        local timing = buffData.add_timing
        if timing == TableConst.enums.BuffAddTiming.None -- None先直接添加
            or timing == TableConst.enums.BuffAddTiming.Initialize then
            self:AddBuff(buff.id, self.uniqueId, 1, buff.level, { fixedArgs = buff.fixedArgs })
        else
            if not self.delayAddBuffs[timing] then
                self.delayAddBuffs[timing] = {}
            end
            table.insert(self.delayAddBuffs[timing], buff)
        end
    end
end

function BattleHero:InitExtraBuffs(buffsBySrc)
    local extraBuffs = {}
    -- 将部队的buffs属性转为战斗内的格式，需要考虑战斗类型和武将专属影响buff生效
    local battleType = self.battleGame.battleInfo.battleType
    for srcType, buffsDisct in pairs(buffsBySrc or {}) do
        local validConfig = GameCommon.TableDataManager:GetValidBattleBuffConfig(battleType)
        if validConfig and not table.contains(validConfig.ban_buff_source, srcType) then
            for _, buff in pairs(buffsDisct) do
                local buffConfig = GameCommon.TableDataManager:GetBuffData(buff.buffId)
                if buffConfig and (table.isnilorempty(buffConfig.battle_white_list) or table.contains(buffConfig.battle_white_list, battleType)) and (not buff.heroOnly or buff.heroOnly == self.heroId) then
                    local extraValue1 = buff.isPercentage and buff.value / 100 or buff.value
                    table.insert(extraBuffs, {
                        id = buff.buffId,
                        level = buff.level,
                        fixedArgs = {
                            extraValue1 = extraValue1,
                        },
                    })
                end
            end
        end
    end
    self.extraBuffs = extraBuffs
end

function BattleHero:AddDelayBuffs(timing)
    for _, buff in ipairs(self.delayAddBuffs[timing] or {}) do
        self:AddBuff(buff.id, self.uniqueId, 1, buff.level, { fixedArgs = buff.fixedArgs })
    end
    self.delayAddBuffs[timing] = nil
end

function BattleHero:IsAlive()
    return self.isAlive
end

function BattleHero:Die()
    self.isAlive = false
    -- 直接死亡，不移除buff
    --while not table.isnilorempty(self.buffs) do
    --    self:RemoveBuff(self.buffs[1])
    --end
    self:RemoveAppliedBuffs()
    self.battleGame.battleGameEvent:UnregisterHeroBattleEvents(self.uniqueId)
    self.battleGame:OnHeroDie(self.camp, self.uniqueId)
end

function BattleHero:RemoveAppliedBuffs()
    for i = #self.appliedBuffs, 1, -1 do
        local buff = self.appliedBuffs[i]
        if GameCommon.TableDataManager:GetBuffData(buff.buffId).sourceDeadClear then
            if buff.hero:IsAlive() and buff.hero:GetBuff(buff.buffId) then
                buff.hero:RemoveBuff(buff)
            end
            table.remove(self.appliedBuffs, i)
        end
    end
end

function BattleHero:GetAttribute(attributeType)
    if attributeType == TableConst.enums.BattleAttributeType.Speed then
        local value = self.attributeHolder:GetAttribute(attributeType)
        value = math.floor(value * 10 + 0.5) / 10 -- 速度四舍五入保留1位小数
        return value
    else
        return self.attributeHolder:GetAttribute(attributeType)
    end
end

function BattleHero:SetAttribute(attributeType, value)
    self.attributeHolder:SetAttribute(attributeType, value)
    if attributeType == TableConst.enums.BattleAttributeType.Speed then
        self.battleGame:SetHeroSpeedDirty()
    end
end

function BattleHero:ModifyHealth(graphData, delta, recoverableRate)
    local healthAttributeType = TableConst.enums.BattleAttributeType.Health
    local health = self:GetAttribute(healthAttributeType)
    local recoverableDelta = 0
    if delta < 0 then --伤害
        delta = math.ceil(delta) -- 修改值都取整数
        if health + delta <= 0 then
            delta = -health
        end
        recoverableRate = recoverableRate or Config.Battle.DamageChangeToRecoverableHealthScale
        recoverableDelta = self:ModifyRecoverableHealth(delta, recoverableRate) --伤害转换为伤兵
        self.battleGame.recorder:AddTotalKill(BattleUtilsEnv.GetOpponentCamp(self.camp), -delta)
    else --治疗
        delta = math.floor(delta) -- 修改值都取整数
        local recoverableHealthAttributeType = TableConst.enums.BattleAttributeType.RecoverableHealth
        local recoverableHealth = self:GetAttribute(recoverableHealthAttributeType)
        if delta > recoverableHealth then
            delta = recoverableHealth
        end
        recoverableDelta = -self:ModifyRecoverableHealth(delta, -1) --治疗消耗伤兵
    end
    self:SetAttribute(healthAttributeType, health + delta)
    local curHealth = self:GetAttribute(healthAttributeType)
    if curHealth <= 0 and self:CheckPositiveEffect(TableConst.enums.PositiveEffectType.AvoidDeath) then
        self:SetAttribute(TableConst.enums.BattleAttributeType.Health, 1)
        self:RemovePositiveEffect(TableConst.enums.PositiveEffectType.AvoidDeath, 1)
    end
    return delta, recoverableDelta
end

function BattleHero:ModifyRecoverableHealth(value, scale)
    local recoverableHealthAttributeType = TableConst.enums.BattleAttributeType.RecoverableHealth
    local recoverableHealth = self:GetAttribute(recoverableHealthAttributeType)
    if value then
        value = math.abs(value)
    else
        value = recoverableHealth --value为nil表示将伤兵转换为死兵
    end
    --if value == 0 then
    --    return
    --end
    local deltaValue = math.floor(value * scale)
    local finalValue = recoverableHealth + deltaValue
    self:SetAttribute(recoverableHealthAttributeType, finalValue)
    return deltaValue
end

function BattleHero:AddAttributeModifier(attributeType, modifyValue, modifierType, extraInfo)
    local key = self.attributeHolder:AddAttributeModifier(attributeType, modifyValue, modifierType, extraInfo)
    self.battleGame.recorder:DetailModifyAttribute(self.uniqueId, attributeType, self:GetAttribute(attributeType))
    if attributeType == TableConst.enums.BattleAttributeType.Speed then
        self.battleGame:SetHeroSpeedDirty()
    end
    return key
end

function BattleHero:RemoveAttributeModifier(attributeType, key)
    self.attributeHolder:RemoveAttributeModifier(attributeType, key)
    self.battleGame.recorder:DetailModifyAttribute(self.uniqueId, attributeType, self:GetAttribute(attributeType))
    if attributeType == TableConst.enums.BattleAttributeType.Speed then
        self.battleGame:SetHeroSpeedDirty()
    end
end

function BattleHero:BatchRemoveAttributeModifier(keyTuples)
    local modifiedAttributeTypes = self.attributeHolder:BatchRemoveAttributeModifier(keyTuples)
    for attributeType, _ in pairs(modifiedAttributeTypes) do
        self.battleGame.recorder:DetailModifyAttribute(self.uniqueId, attributeType, self:GetAttribute(attributeType))
        if attributeType == TableConst.enums.BattleAttributeType.Speed then
            self.battleGame:SetHeroSpeedDirty()
        end
    end
end

function BattleHero:GetAttributeModifier(attributeType)
    return self.attributeHolder:GetAttributeModifier(attributeType)
end
------------------------------------------------------

function BattleHero:AddCrowdControl(ccType, buffId)
    local key = self.battleGame:GenerateUid()
    if not self.crowdControls[ccType] then
        self.crowdControls[ccType] = {}
    end
    self.crowdControls[ccType][key] = buffId
    if not self:CheckPositiveEffect(TableConst.enums.PositiveEffectType.Insight) then
        if ccType == TableConst.enums.CrowdControlType.BanActive
            or ccType == TableConst.enums.CrowdControlType.Silence
            or ccType == TableConst.enums.CrowdControlType.Stun then
                self:AllTacticsResetPrepare(buffId)
        end
    else
        for ccTp, ccTable in pairs(self.crowdControls) do
            if not table.isnilorempty(ccTable) then
                self.battleGame.recorder:DetailInsight(self.uniqueId, ccTp, true)
            end
        end
    end
    return key
end

function BattleHero:GetCrowdControlBuffs(ccType)
    local buffs = {}
    for _, buffId in pairs(self.crowdControls[ccType] or {}) do
        local buff = self:GetBuff(buffId)
        table.insert(buffs, buff)
    end
    return buffs
end

function BattleHero:GetCrowdControlRemainRound(ccType)
    local maxRemainRound = 0
    for _, buffId in pairs(self.crowdControls[ccType] or {}) do
        local buff = self:GetBuff(buffId)
        if buff then
            maxRemainRound = math.max(maxRemainRound, buff:GetRemainRound())
        end
    end
    return maxRemainRound
end

function BattleHero:CheckCrowdControl(ccType)
    if self:CheckPositiveEffect(TableConst.enums.PositiveEffectType.Insight) then
        return false
    end
    local result = not table.isnilorempty(self.crowdControls[ccType])
    if result then
        if TableEnumUtilsEnv.CheckEnumItemTag("CrowdControlType", ccType, "DETAIL") then
            self.battleGame.recorder:DetailCheckCrowdControl(self.uniqueId, ccType, self:GetCrowdControlRemainRound(ccType))
        end
    end
    return result
end

function BattleHero:RemoveCrowdControl(ccType, key)
    self.crowdControls[ccType][key] = nil
end


function BattleHero:AddPositiveEffect(peType, buffId)
    local key = self.battleGame:GenerateUid()
    if not self.positiveEffects[peType] then
        self.positiveEffects[peType] = {}
    end
    self.positiveEffects[peType][key] = buffId
    if peType == TableConst.enums.PositiveEffectType.Insight then
        for ccTp, ccTable in pairs(self.crowdControls) do
            if not table.isnilorempty(ccTable) then
                self.battleGame.recorder:DetailInsight(self.uniqueId, ccTp, true)
            end
        end
    end
    return key
end

function BattleHero:CheckPositiveEffect(peType)
    return not table.isnilorempty(self.positiveEffects[peType])
end

function BattleHero:RemovePositiveEffect(peType, key)
    self.positiveEffects[peType][key] = nil
    if peType == TableConst.enums.PositiveEffectType.Insight then
        for ccTp, ccTable in pairs(self.crowdControls) do
            if not table.isnilorempty(ccTable) then
                self.battleGame.recorder:DetailInsight(self.uniqueId, ccTp, false)
            end
        end
    end
end

--- tactic

function BattleHero:AddTactic(tacticData, isSelfTactic)
    local tactic = BattleTacticEnv.BattleTactic.new(self, tacticData.id, tacticData.level, tacticData.stars, isSelfTactic)
    table.insert(self.tactics, tactic)
    if tactic.tacticType == TableConst.enums.TacticType.Active then
        table.insert(self.activeTactics, tactic)
    end
    if tactic.tacticType == TableConst.enums.TacticType.Pursue then
        table.insert(self.pursueTactics, tactic)
    end
    for eventType, event in pairs(tactic.battleEvents) do
        self:RegisterBattleEvent(eventType, event.priority, self.uniqueId, BattleConstEnv.BattleHandlerType.Tactic, tactic.uid, event.isSelf)
    end
end

function BattleHero:GetTactic(tacticId)
    if tacticId == self.plainAttackId then
        return self.plainAttackTactic
    end
    for _, tactic in ipairs(self.tactics) do
        if tactic.tacticId == tacticId then
            return tactic
        end
    end
    return nil
end

function BattleHero:GetTacticByUid(tacticUid)
    if tacticUid == self.plainAttackTactic.uid then
        return self.plainAttackTactic
    end
    for _, tactic in ipairs(self.tactics) do
        if tactic.uid == tacticUid then
            return tactic
        end
    end
    return nil
end

function BattleHero:AllTacticsResetPrepare(reasonBuffId)
    for _, tactic in ipairs(self.tactics) do
        if tactic.isPreparing then
            tactic:ResetPrepare()
            self.battleGame.recorder:DetailInterruptPrepare(self.uniqueId, tactic.tacticId, reasonBuffId)
        end
    end
end

--- buff

function BattleHero:AddBuff(buffId, sourceUid, count, level, extraArgs)
    local addBuffArgs = {
        BuffId = buffId,
        SourceId = sourceUid,
        TargetId = self.uniqueId,
        Count = count,
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeAddBuff, addBuffArgs, sourceUid)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBeAddBuff, addBuffArgs, self.uniqueId)

    local recorder = self.battleGame.recorder
    recorder:SetHero(self.uniqueId)
    local detailPointer = recorder:GetDetailPointerAndHero()
    local initId
    local nestIds = {}
    for _ = 1, count do --层数变化nestId
        local nestId = self.battleGame.recorder:GetNestId()
        table.insert(nestIds, nestId)
    end

    local coveredTotalRound
    if extraArgs and extraArgs.coveredTotalRound then
        coveredTotalRound = extraArgs.coveredTotalRound
    end

    local buff = self:GetBuff(buffId)
    if not buff then
        initId = self.battleGame.recorder:GetNestId()
        buff = BattleBuffEnv.BattleBuff.new(self, buffId, sourceUid, level, extraArgs)
        local sourceHero = self.battleGame:GetHeroByUid(sourceUid)
        local sourceTacticHero = buff.sourceTacticHero
        if sourceTacticHero then
            table.insert(sourceTacticHero.appliedBuffs, buff)
        else
            table.insert(sourceHero.appliedBuffs, buff)
        end
        local isCanAdd, targetCoverBuff = self:CheckBuffGroupConflictRule(buffId, coveredTotalRound, sourceHero)
        if not isCanAdd then
            return
        end
        if targetCoverBuff then
            self:RemoveBuff(targetCoverBuff)
        end
        table.insert(self.buffs, buff)
        for eventType, event in pairs(buff.battleEvents) do
            self:RegisterBattleEvent(eventType, event.priority, self.uniqueId, BattleConstEnv.BattleHandlerType.Buff, buffId, event.isSelf)
        end

        local instance = self.battleGame.abilitySystemManager
        local args = {
            Level = buff.level,
            Stars = buff.stars,
            Round = buff.hero.battleGame.round,
            SourceId = sourceUid,
            SourceTacticHeroId = sourceTacticHero and sourceTacticHero.uniqueId,
        }
        table.update(args, buff.extraArgs)
        instance:TriggerBuffGraph(buff, buff.buffLayers[1], "next", 1, args)
    end

    local increased = buff:IncreaseLayerCount(count, nestIds, coveredTotalRound)
    local newRecord = {}
    newRecord.initId = initId
    newRecord.nestIds = nestIds
    if increased > 0 then
        recorder:DetailAddBuff(self.uniqueId, buffId, buff:GetLayerCount(), detailPointer)
    end

    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterAddBuff, addBuffArgs, sourceUid)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBeAddBuff, addBuffArgs, self.uniqueId)
    
    return buff, newRecord
end

function BattleHero:CheckBuffGroupConflictRule(addedBuffId, coveredTotalRound, sourceHero)
    local isCanAdd = true
    local targetCoverBuff
    local addedBuffData = GameCommon.TableDataManager:GetBuffData(addedBuffId)
    if not table.isnilorempty(addedBuffData.groupTypeList) then
        for _, buff in ipairs(self.buffs) do
            for _, groupType in pairs(buff.buffData.groupTypeList) do
                for _, addedBuffGroupType in pairs(addedBuffData.groupTypeList) do
                    if groupType == addedBuffGroupType then
                        if groupType == TableConst.enums.BuffGroupConflictRule.Invalid then
                            isCanAdd = false
                            targetCoverBuff = buff
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Cover then
                            isCanAdd = true
                            targetCoverBuff = buff
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Round then
                            local addedBuffTotalRound = coveredTotalRound and coveredTotalRound or addedBuffData.total_round
                            local isAddBuffRoundLonger = addedBuffTotalRound > buff:GetRemainRound()
                            isCanAdd = isAddBuffRoundLonger
                            targetCoverBuff = buff
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Attribute then
                            local buffGroupData = GameCommon.TableDataManager:GetBuffGroupData(groupType)
                            local AssociatedAttribute = buffGroupData.attribute_association
                            local selfAttributeValue = sourceHero:GetAttribute(AssociatedAttribute)
                            local buffAttributeValue = buff.hero:GetAttribute(AssociatedAttribute)
                            isCanAdd = selfAttributeValue > buffAttributeValue
                            targetCoverBuff = buff
                        elseif groupType == TableConst.enums.BuffGroupConflictRule.Coexist then
                            isCanAdd = true
                            targetCoverBuff = nil
                        end
                        if isCanAdd then
                            goto BuffGroupCheckEnd
                        end
                    end
                end
            end
        end
    end

    ::BuffGroupCheckEnd::
    return isCanAdd, targetCoverBuff
end

function BattleHero:GetBuff(buffId)
    for _, buff in ipairs(self.buffs) do
        if buff.buffId == buffId then
            return buff
        end
    end
    return nil
end

function BattleHero:RemoveBuff(buff)
    local index = table.index(self.buffs, buff)
    if not index then
        return
    end
    local removeBuffArgs = {
        BuffId = buff.buffId,
        SourceId = buff.sourceId,
        TargetId = self.uniqueId,
        Count = buff:GetLayerCount(),
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBeRemoveBuff, removeBuffArgs, self.uniqueId)
    self.battleGame.recorder:DetailRemoveBuff(self.uniqueId, buff.buffId)
    buff:OnDestroy()
    if index and self.buffs[index] == buff then
        table.remove(self.buffs, index)
    else
        return
    end
    for eventType, event in pairs(buff.battleEvents) do
        self:UnregisterBattleEvent(eventType, event.priority, self.uniqueId, BattleConstEnv.BattleHandlerType.Buff, buff.buffId)
    end
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBeRemoveBuff, removeBuffArgs, self.uniqueId)
end

function BattleHero:CalculateBuffRemainRound()
    for i = #self.buffs, 1, -1 do
        local buff = self.buffs[i]
        buff:CalculateRemainRound()
    end
end

function BattleHero:CollectBuffBeforeHeroTurnStart()
    for _, buff in pairs(self.buffs) do
        for _, buffLayer in pairs(buff.buffLayers) do
            buffLayer.isNeedToCalculateRemainRound = true
        end
    end
end

------------------

function BattleHero:ReplacePlainAttack(targetPlainAttackId)
    self.plainAttackId = targetPlainAttackId
    self.plainAttackTactic = BattleTacticEnv.BattleTactic.new(self, self.plainAttackId, 1, 1)
end

function BattleHero:PlainAttack(targetId, ignorePursue)
    local confusion = false
    if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Confusion) then -- 混乱
        confusion = true
    end
    -- Select普攻目标
    if not CheckNumberId(targetId) then
        targetId = BattleUtilsEnv.SelectPlainAttackTarget(self, confusion)
    end
    if not CheckNumberId(targetId) then
        ErrorLog("[BattleHero] PlainAttack targetId is nil")
        return
    end
    -- 战报 xx对xx普攻
    self.battleGame.recorder:DetailPlainAttackTarget(self.uniqueId, targetId)

    local plainAttackPackage = {
        attackerId = self.uniqueId,
        defenderId = targetId,
        tacticId = self.plainAttackId,
        tacticUid = self.plainAttackTactic.uid,
    }
    local packageId = self.battleGame:PushPlainAttackPackage(plainAttackPackage)
    local recordArgs = {
        Index = packageId,
        Round = self.battleGame.round,
        AttackerId = self.uniqueId,
        DefenderId = targetId,
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforePlainAttack, recordArgs, plainAttackPackage.attackerId)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBePlainAttacked, recordArgs, plainAttackPackage.defenderId)

    plainAttackPackage = self.battleGame.plainAttackPackageMap[packageId]
    if plainAttackPackage.disable then
        return
    end
    recordArgs.AttackerId = plainAttackPackage.attackerId
    recordArgs.DefenderId = plainAttackPackage.defenderId

    local args = {}
    if not self.battleGame.skillVariables.hero[self.uniqueId] then
        self.battleGame.skillVariables.hero[self.uniqueId] = {}
    end
    if CheckNumberId(plainAttackPackage.defenderId) then
        args.TargetId = plainAttackPackage.defenderId
    end
    local success = self.plainAttackTactic:Use(args)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterPlainAttack, recordArgs, plainAttackPackage.attackerId)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBePlainAttacked, recordArgs, plainAttackPackage.defenderId)

    if success and not ignorePursue then -- 命中
        -- 执行追击
        for _, pursueTactic in ipairs(self.pursueTactics) do
            if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun)
                or self:CheckCrowdControl(TableConst.enums.CrowdControlType.BanPursue) then
                break
            end
            self:UseTactic(pursueTactic, args)
        end
    end

    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterPlainAttackProcess, recordArgs, self.uniqueId)
end

function BattleHero:IsAliveAndNotBattleEnd()
    return self:IsAlive() and not self.battleGame.battleEnd
end

----------- battle stages -----------

function BattleHero:SimulateHeroTurn()
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self:CollectBuffBeforeHeroTurnStart()
    self:HeroTurnStart()
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self:HeroTurnAction()
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self:HeroTurnEnd()
end

function BattleHero:HeroTurnStart()
    local recordArgs = {
        HeroId = self.uniqueId,
        Round = self.battleGame.round
    }
    self.battleGame.recorder:DetailHeroTurnStart(self.uniqueId)
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.HeroTurnStart, recordArgs, self.uniqueId)
end

function BattleHero:HeroTurnAction()
    local recordArgs = {
        HeroId = self.uniqueId,
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.HeroTurnAction, recordArgs, self.uniqueId)
    if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun) then
        return
    end
    -- 主动技能
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    for _, activeTactic in ipairs(self.activeTactics) do
        if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun) then
            activeTactic:ResetPrepare()
            return
        end
        if self:CheckCrowdControl(TableConst.enums.CrowdControlType.BanActive)
            or self:CheckCrowdControl(TableConst.enums.CrowdControlType.Silence) then
            activeTactic:ResetPrepare()
            break
        end
        self:UseTactic(activeTactic)
    end
    -- 普攻和追击
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun)
        or self:CheckCrowdControl(TableConst.enums.CrowdControlType.BanPlainAttack)
        or self:CheckCrowdControl(TableConst.enums.CrowdControlType.Disarm) then
        return
    end
    self:PlainAttack()
    -- 判定连击
    if not self:IsAliveAndNotBattleEnd() then
        return
    end
    local comboRate = self:GetAttribute(TableConst.enums.BattleAttributeType.ComboRate)
    if comboRate > 0 then
        if math.random() < comboRate then
            self.battleGame.recorder:DetailCombo(self.uniqueId, comboRate)
            if self:CheckCrowdControl(TableConst.enums.CrowdControlType.Stun)
                or self:CheckCrowdControl(TableConst.enums.CrowdControlType.BanPlainAttack)
                or self:CheckCrowdControl(TableConst.enums.CrowdControlType.Disarm) then
                return
            end
            self:PlainAttack()
        else
            self.battleGame.recorder:DetailComboFailed(self.uniqueId, comboRate)
        end
    end
end

function BattleHero:HeroTurnEnd()
    local recordArgs = {
        HeroId = self.uniqueId,
    }
    self.battleGame.recorder:DetailHeroTurnEnd(self.uniqueId)
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.HeroTurnEnd, recordArgs, self.uniqueId)
    self:CalculateBuffRemainRound()
end

-------------------------------------

function BattleHero:RegisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId, isSelf)
    self.battleGame.battleGameEvent:RegisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId, isSelf)
end

function BattleHero:UnregisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId)
    self.battleGame.battleGameEvent:UnregisterBattleEvent(eventType, eventPriority, heroUid, handlerType, handlerId)
end

-------------------------------------

function BattleHero:CalculateCd()
    for _, tactic in ipairs(self.tactics) do
        tactic:CalculateCd()
    end
end

function BattleHero:GetTacticProbability(tactic)
    local totalSuccessProbability = tactic:GetProbability()

    if tactic.tacticType == TableConst.enums.TacticType.Active then
        totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.ActiveTacticProbability)
    elseif tactic.tacticType == TableConst.enums.TacticType.Pursue then
        totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.PursueTacticProbability)
    end

    if tactic.isSelfTactic then
        if tactic.tacticType == TableConst.enums.TacticType.Active then
            totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.SelfActiveTacticProbability)
        elseif tactic.tacticType == TableConst.enums.TacticType.Pursue then
            totalSuccessProbability = totalSuccessProbability + self:GetAttribute(TableConst.enums.BattleAttributeType.SelfPursueTacticProbability)
        end
    end

    totalSuccessProbability = math.clamp(totalSuccessProbability, 0, 1)
    return totalSuccessProbability
end

function BattleHero:AddPriorSelectTarget(data)
    self._priorSelectTargetsKeyGenerator = self._priorSelectTargetsKeyGenerator + 1
    local key = self._priorSelectTargetsKeyGenerator
    self.priorSelectTargets[key] = data
    return key
end

function BattleHero:RemovePriorSelectTarget(key)
    self.priorSelectTargets[key] = nil
end

function BattleHero:DecreasePriorSelectTargetCount(key)
    if not self.priorSelectTargets[key] then
        return false
    end
    if not self.priorSelectTargets[key].useCount then
        return true
    end
    if self.priorSelectTargets[key].count <= 0 then
        return false
    end
    self.priorSelectTargets[key].count = self.priorSelectTargets[key].count - 1
    return true
end

function BattleHero:UseTactic(tactic, args)
    if not self:IsAliveAndNotBattleEnd() then
        tactic:ResetPrepare()
        return false
    end

    args = args or {}

    local tacticPackage = {
        casterId = self.uniqueId,
        tacticId = tactic.tacticId,
        tacticUid = tactic.uid,
    }
    local packageId = self.battleGame:PushTacticPackage(tacticPackage)
    local inputArgs = {
        Index = packageId,
        InputTacticId = tactic.tacticId,
        InputTacticUid = tactic.uid,
        EventSourceId = self.uniqueId,
    }
    self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeTryUseTactic, inputArgs, self.uniqueId)
    tacticPackage = self.battleGame.tacticPackageMap[packageId]

    local prob = self:GetTacticProbability(tactic)
    local needPrepare = tactic:GetMaxPrepareStep() > 0
    local detailPointer, currentHeroId

    if needPrepare then
        if not tactic.isPreparing then
            local randomNum = math.random()
            if randomNum > prob then
                self.battleGame.recorder:DetailTacticFailed(self.uniqueId, tactic.tacticId, prob)
                self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.TacticProbFailed, inputArgs, self.uniqueId)
                return false
            end
            tactic:ResetPrepare()
            self.battleGame.recorder:DetailTacticPrepare(self.uniqueId, tactic.tacticId, tactic:GetPrepareStep(), prob)
            self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeUseTactic, inputArgs, self.uniqueId)
            tactic.isPreparing = true
            tacticPackage = self.battleGame.tacticPackageMap[packageId]
            if tacticPackage.disable then
                tactic:ResetPrepare()
                return false
            end
            if tactic:IsTacticFinishPrepare() then
                detailPointer, currentHeroId = self.battleGame.recorder:GetDetailPointerAndHero()
                self.battleGame.recorder:DetailTactic(self.uniqueId, tactic.tacticId, 1)
            else
                return true
            end
        else
            tactic:DecreasePrepareStep()
            if tactic:IsTacticFinishPrepare() then
                detailPointer, currentHeroId = self.battleGame.recorder:GetDetailPointerAndHero()
                self.battleGame.recorder:DetailTactic(self.uniqueId, tactic.tacticId, 1)
            else
                self.battleGame.recorder:DetailTacticPrepare(self.uniqueId, tactic.tacticId, tactic:GetPrepareStep(), 1)
                return true
            end
        end
    else
        local randomNum = math.random()
        if randomNum > prob then
            self.battleGame.recorder:DetailTacticFailed(self.uniqueId, tactic.tacticId, prob)
            self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.TacticProbFailed, inputArgs, self.uniqueId)
            return false
        end
        detailPointer, currentHeroId = self.battleGame.recorder:GetDetailPointerAndHero()
        self.battleGame.recorder:DetailTactic(self.uniqueId, tactic.tacticId, prob or 1)
        self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeUseTactic, inputArgs, self.uniqueId)
        tacticPackage = self.battleGame.tacticPackageMap[packageId]
        if tacticPackage.disable then
            tactic:ResetPrepare()
            return false
        end
    end

    -- 优先选择目标
    local priorSelectTargetId, priorSelectTargetKey = BattleUtilsEnv.GetPriorSelectTargetIdAndKey(self, tactic.tacticType)
    args.PriorSelectTargetId = priorSelectTargetId
    args.PriorSelectTargetKey = priorSelectTargetKey

    local success = tactic:Use(args)
    tactic:ResetPrepare()
    if success then
        if priorSelectTargetKey and self.priorSelectTargets[priorSelectTargetKey] then
            if self.priorSelectTargets[priorSelectTargetKey].useCount and self.priorSelectTargets[priorSelectTargetKey].count <= 0 then
                local buff = self:GetBuff(self.priorSelectTargets[priorSelectTargetKey].buffId)
                if buff then
                    buff:DecreaseLayerCount(1)
                end
            end
        end

        self.battleGame.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterUseTactic, inputArgs, self.uniqueId)
    else
        self.battleGame.recorder:SetDetailPointerAndHero(detailPointer, currentHeroId)
    end
    return success
end
