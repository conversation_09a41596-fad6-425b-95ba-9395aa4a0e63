{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "8"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}, "event2": {"Type": "number", "Value": "106"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "True"}}}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "28"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": ""}, "value": {"Type": "number", "Value": "0"}}}, "2": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "洛神概率降低"}, "result": {"Type": "number", "Value": "0"}}}, "4": {"Type": "RemoveBuffNode", "Field": {"targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "random": {"Type": "boolean", "Value": "False"}, "count": {"Type": "number", "Value": "1"}, "isDebuff": {"Type": "boolean", "Value": "False"}, "byId": {"Type": "boolean", "Value": "True"}, "buffId": {"Type": "number", "Value": "11200101"}, "byType": {"Type": "boolean", "Value": "False"}, "buffType": [], "byBuffGroupType": {"Type": "boolean", "Value": "False"}, "buffGroupType": []}}, "6": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "BlackboardValue": "InputTacticId"}, "op": {"Type": "number", "Value": "5"}, "number2": {"Type": "number", "Value": "112001"}}}, "7": {"Type": "UseTacticNode", "Field": {"casterIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "tacticId": {"Type": "number", "Value": "112001"}, "needProb": {"Type": "boolean", "Value": "True"}, "extraProb": {"Type": "number", "Value": "0"}, "notNeedCarried": {"Type": "boolean", "Value": "False"}, "argNames": [], "argsCount": {"Type": "number", "Value": "1"}, "argName0": {"Type": "string", "Value": "TargetId"}, "argValue0": {"Type": "string", "Value": ""}, "argName1": {"Type": "string", "Value": ""}, "argValue1": {"Type": "string", "Value": ""}, "argName2": {"Type": "string", "Value": ""}, "argValue2": {"Type": "string", "Value": ""}, "argName3": {"Type": "string", "Value": ""}, "argValue3": {"Type": "string", "Value": ""}, "argName4": {"Type": "string", "Value": ""}, "argValue4": {"Type": "string", "Value": ""}, "argName5": {"Type": "string", "Value": ""}, "argValue5": {"Type": "string", "Value": ""}, "argName6": {"Type": "string", "Value": ""}, "argValue6": {"Type": "string", "Value": ""}, "argName7": {"Type": "string", "Value": ""}, "argValue7": {"Type": "string", "Value": ""}, "argName8": {"Type": "string", "Value": ""}, "argValue8": {"Type": "string", "Value": ""}, "argName9": {"Type": "string", "Value": ""}, "argValue9": {"Type": "string", "Value": ""}}}}, "Links": {"0": {"onLayerIncreased": ["2.prev"], "HeroTurnEndNode": ["4.prev"], "AfterUseTacticNode": ["6.prev"]}, "2": {"next": ["1.prev"]}, "6": {"next": ["7.prev"]}}, "DataFlows": {"2": {"result": ["1.value"]}}}