---@type ServiceEnv
local Service = xrequire(EZFPath .. ".Services.Service")
---@type ContainerSingletonCompEnv
local ContainerSingletonComp = xrequire(EZFPath .. ".Entities.Components.ContainerSingletonComp")
---@type GlobalDataAckCompEnv
local GlobalDataAckComp = xrequire(EZFPath .. ".Entities.Components.GlobalDataAckComp")
---@type AsyncCompEnv
local AsyncComp = xrequire(EZFPath .. ".Services.Components.AsyncComp")
---@type CmdUtilsEnv
local CmdUtils = xrequire(EZFPath .. ".Utils.CmdUtils")
---@type GlobalEntityMgrEnv
local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")


--region CmdProxy

---@class CmdProxy @和alias中的CmdProxy保持一致
---@field aeId string @这里只用了string

--endregion CmdProxy


--region CmdHandler

-- 所有的可执行命令都定义在CmdHandler中
---@class CmdHandler
CmdHandler = DefineClass("CmdHandler")

---@param ae AsyncExecutor
---@param cmd CmdBase @任意cmd
function CmdHandler:forward(ae, cmd)
    local fwd = CmdUtils.CmdForward[cmd.name]
    return CmdService.instance:awaitServiceRpc(ae, fwd.service, fwd.method, {aeId=ae.aeId}, cmd)
end

---@param ae AsyncExecutor
---@param cmd CmdBase
function CmdHandler:shutdown(ae, cmd)
    EZE.startShutdown()
    return ae:yield(nil, 600)
end

---@param ae AsyncExecutor
---@param cmd Cmd
function CmdHandler:service(ae, cmd)
    local res = {suc=true}
    for name, info in pairs(GlobalEntityMgr.instance.globalEntities) do
        if cmd.args[1] then
            -- 只收录active
            for eid, einfo in pairs(info.entities) do
                if GlobalEntityMgr.instance:isActive(name, eid) then
                    res[name] = {node=EZE.entityIdToNodeId(eid), container=EZE.entityIdToContainerId(eid), eid=eid, ready=einfo.ready, active=GlobalEntityMgr.instance:isActive(name, eid)}
                    break
                end
            end
        else
            res[name] = {}
            for eid, einfo in pairs(info.entities) do
                table.insert(res[name], {node=EZE.entityIdToNodeId(eid), container=EZE.entityIdToContainerId(eid), eid=eid, ready=einfo.ready, active=GlobalEntityMgr.instance:isActive(name, eid)})
            end
        end
    end
    return nil, res
end

---@param ae AsyncExecutor
---@param cmd Cmd
---@param allowList? int[]
function CmdHandler:broadcast(ae, cmd, allowList)
    local args = {}
    for i = 2, #cmd.args do
        table.insert(args, cmd.args[i])
    end
    CmdService.instance:broadcast(ae.aeId, allowList, cmd.args[1], args, function(finishContainers)
        Functor("broadcastFeedback", CmdService.instance)(ae.aeId, finishContainers)
    end, cmd.ignoreLogRes)
    return ae:yield(nil, 600)
end

---@param ae AsyncExecutor
---@param cmd Cmd
function CmdHandler:multicast(ae, cmd)
    local allowList = nil
    local allow = table.remove(cmd.args, 1)
    if type(allow) == "number" then
        allowList = {allow}
    elseif type(allow) == "table" then
        allowList = allow
    elseif type(allow) == "string" then
        allowList = string.ezsplit(allow, ",", function(sub) return tonumber(sub) end)
    else
        return "ERROR", "no target containers"
    end
    if #allowList == 0 then
        return "ERROR", "invalid target containers"
    end
    return self:broadcast(ae, cmd, allowList)
end

--endregion


--region CmdService

-- 执行命令并返回结果的service
---@class CmdService: Service, ContainerSingletonComp<CmdService>, GlobalDataAckComp, AsyncComp
---@field locks table<string, int> @执行锁，对一些互斥的情况进行限制
CmdService = DefineEntity("CmdService", {Service.Service}, {
    ContainerSingletonComp.ContainerSingletonComp,
    GlobalDataAckComp.GlobalDataAckComp,
    AsyncComp.AsyncComp,
})
CmdService.SERIALIZE_TYPE = "msgpack"

function CmdService:ctor()
    self.tcpServer = LuaTcpServer.new()
    if self.tcpServer:start(
            LuaNodeConfig.getConfig("cmdservice.host"), 
            LuaNodeConfig.getConfig("cmdservice.port"), 100, self) then
        self:regist(true, {})
        self.locks = {}
    else
        error("tcp server start failed")
    end
end

function CmdService:handleNewConnnection(connId)
    self:logInfo("TcpServer new connection %s", connId)
end

function CmdService:handleCloseConnection(connId, reason)
    self:logInfo("TcpServer close connection %s, reason %s", connId, reason)
end

function CmdService:handleConnectionData(connId, data)
    -- 目前tick机制下无法完整的处理分包
    if string.len(data) < 4 then
        local err = "data error"
        self:logError(err)
        return self.tcpServer:send(connId, CmdService.serialize(CmdUtils.CmdUtils.res("ERROR", err)))
    end
    local size = CmdUtils.CmdUtils.bytes2int(string.sub(data, 1, 4), "big", false)
    if string.len(data) ~= size then
        local err = "data error"
        self:logError(err)
        return self.tcpServer:send(connId, CmdService.serialize(CmdUtils.CmdUtils.res("ERROR", err)))
    end
    data = string.sub(data, 5, size)
    ---@type CmdBase
    local cmd = CmdService.unserialize(data)
    self:processCmd(cmd, function (ar, res)
        self.tcpServer:send(connId, CmdService.serialize(CmdUtils.CmdUtils.res(ar, res)))
    end)
end

---@param cmd Cmd
---@param mailboxStr string
---@param cb string
---@param userdata any
function CmdService:cmd(cmd, mailboxStr, cb, userdata)
    local mb = LuaMailbox.createFromString(mailboxStr)
    self:processCmd(cmd, function (ar, res)
        self:serverRpc(mb, cb, userdata, CmdUtils.CmdUtils.res(ar, res))
    end)
end

---@param cmd Cmd
---@param cb fun(ar: AsyncResult, res: any):void
function CmdService:processCmd(cmd, cb)
    if not cmd or type(cmd) ~= "table" or not cmd.name then
        local err = "cmd struct err"
        self:logError(err)
        return cb("ERROR", err)
    end
    local handler = CmdUtils.CmdForward[cmd.name] and CmdHandler.forward or CmdHandler[cmd.name]
    if not handler then
        local err = string.format("unknown cmd(%s)", cmd.name)
        self:logError(err)
        return cb("ERROR", err)
    end
    local lock, aeId = self:lock(CmdUtils.CmdLock[cmd.name])
    if not lock then
        local err = string.format("cmd(%s) is locked by %s", cmd.name, aeId)
        self:logError(err)
        return cb("ERROR", err)
    end
    self:genAsyncExecutor(function(id)
        assert(id == aeId)
        local ae = self.running[id]
        self:logInfo("start run cmd %s %v", ae.aeId, cmd)
        local ar, res = handler(CmdHandler, ae, cmd)
        self:logInfo("finish run cmd %s, err: %s, data: %v", ae.aeId, ar, cmd.ignoreLogRes and type(res) or res)
        self:unlock(id, CmdUtils.CmdLock[cmd.name])
        cb(ar, res)
    end, aeId)
end

---@param aeId AeId
---@param hd GlobalDataAckHolder
function CmdService:broadcastFeedback(aeId, hd)
    local res = self:genBroadcastRes(aeId, hd)
    local ar = (next(res.miss) or next(res.failed)) and "ERROR" or nil
    self:asyncFeedback({aeId=aeId}, CmdUtils.CmdUtils.res(ar, res))
end

---@param cmdLocks string[]
---@return boolean, AeId
function CmdService:lock(cmdLocks)
    if not cmdLocks or not next(cmdLocks) then
        return true, EZE.genUUID()
    end
    if cmdLocks.global then
        local lock, aeId = next(self.locks)
        if lock then
            return false, aeId
        end
    end
    for lock, _ in pairs(cmdLocks) do
        if self.locks[lock] then
            return false, self.locks[lock]
        end
    end
    -- 此处已检查成功
    local aeId = EZE.genUUID()
    for lock, _ in pairs(cmdLocks) do
        self.locks[lock] = aeId
    end
    return true, aeId
end

---@param aeId AeId
---@param cmdLocks string[]
---@return boolean, AeId
function CmdService:unlock(aeId, cmdLocks)
    if not cmdLocks or not next(cmdLocks) then
        return
    end
    for lock, _ in pairs(cmdLocks) do
        if self.locks[lock] == aeId then
            self.locks[lock] = nil
        else
            self:logError("unlock but aeId mismatch, cur: %s, expect: %s", self.locks[lock], aeId)
        end
    end
end

function CmdService.packSize(bytes)
    return CmdUtils.CmdUtils.int2bytes(string.len(bytes) + 4, "big", false, 4) .. bytes
end

if CmdService.SERIALIZE_TYPE == "json" then
    local cjson = cjson or require(EZFPath .. ".Utils.json")
    function CmdService.serialize(data)
        return CmdService.packSize(cjson.encode(data))
    end

    function CmdService.unserialize(data)
        return cjson.decode(data)
    end
elseif CmdService.SERIALIZE_TYPE == "msgpack" then
    function CmdService.serialize(data)
        return CmdService.packSize(EZE.msgpackPack(data))
    end

    function CmdService.unserialize(data)
        return EZE.msgpackUnpack(data)
    end
end

--endregion

-- for emmylua
---@class CmdServiceEnv
local CmdServiceEnv = {
    CmdService=CmdService,
}
return CmdServiceEnv
