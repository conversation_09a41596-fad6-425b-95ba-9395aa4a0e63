----------------------- WARNING -----------------------
---     目前coroutine有风险；游戏正式模块请勿使用      ---
----------------------- WARNING -----------------------

---@type AsyncExecutorEnv
local AsyncExecutor = xrequire(EZFPath .. ".Async.AsyncExecutor")


---@class HookInfo
---@field raw AnyFunc
---@field hook AnyFunc
---@field careRawRes bool
HookInfo = DefineClass("HookInfo")

function HookInfo:ctor(raw, hook, careRawRes)
    self.raw = raw
    self.hook = hook
    self.careRawRes = careRawRes
end


---@class AsyncExecutorHolder
---@field ae AsyncExecutor
---@field hookEntity Entity | nil
---@field curHooks table<table, table<string, HookInfo>>
AsyncExecutorHolder = DefineClass("AsyncExecutorHolder")

function AsyncExecutorHolder:ctor()
    self.ae = nil
    self.hookEntity = nil
    self.curHooks = {}
end

---@param aeId AeId
---@param fun AsyncFunc
---@param handleResume? HandleResume
---@param hookResume? HookResume
function AsyncExecutorHolder:createAsyncExecutor(aeId, fun, handleResume, hookResume)
    assert(not self.ae)
    if hookResume then
        self.ae = AsyncExecutorMgr:create(aeId, fun, handleResume, function(...) return hookResume(self:hookResume(...)) end)
    else
        self.ae = AsyncExecutorMgr:create(aeId, fun, handleResume, function(...) return self:hookResume(...) end)
    end
end

---@param force? boolean
function AsyncExecutorHolder:removeAsyncExecutor(force)
    assert(self.ae)
    AsyncExecutorMgr:remove(self.ae.aeId, force)
    self.ae:clear()
    self.ae = nil
end

---@param ar? AsyncResult
---@return AsyncResult, ...
function AsyncExecutorHolder:hookResume(ar, ...)
    if ar == "TIMEOUT" then
        self:resetHooks()
    end
    return ar, ...
end

---@param ar? AsyncResult | nil
---@param timeout? number | nil @在yield的情况下生效，默认10秒，0表示没有超时限制
---@param ... any
---@return AsyncResult | nil, ...
function AsyncExecutorHolder:yield(ar, timeout, ...)
    return self.ae:yield(ar, timeout, ...)
end

---@param seconds number @延迟时间（秒）
function AsyncExecutorHolder:sleep(seconds)
    local ar = self.ae:yield(nil, seconds)
    assert(ar == "TIMEOUT", string.format("sleep but resume case %s", ar))
end

---@param ar? AsyncResult
---@param ... any
---@return AsyncResult, ...
function AsyncExecutorHolder:resume(ar, ...)
    return self.ae:resume(ar, ...)
end

---@deprecated use AsyncExecutorHolder.hook instead
-- 切换对象时，需要重新绑定hook
---@param ent Entity | nil
function AsyncExecutorHolder:setHookEntity(ent)
    self.hookEntity = ent
end

---@deprecated use AsyncExecutorHolder.hook instead
---@param hooks table<string, AnyFunc> @需要hook的方法名和回调，支持同时hook多个方法
---@param yield boolean @是否在hook之后自动调用yield挂起
---@param ar? AsyncResult | nil @yield返回给主协程的参数
---@param timeout? integer | nil @在yield的情况下生效，默认10秒，0表示没有超时限制
---@param careRawRes? boolean | nil @是否需要原方法的返回值，如填true，hook回调第一个参数就是原方法的返回值
---@return AsyncResult, ... @主协程resume回来的参数
function AsyncExecutorHolder:setHooks(hooks, yield, ar, timeout, careRawRes)
    for fname, hook in pairs(hooks) do
        self:_hook(self.hookEntity, fname, hook, careRawRes)
    end
    if yield then
        return self:yield(ar, timeout)
    end
end

---@param inst table | nil @需要hook的实例
---@param fname string @hook方法名
---@param func function @hook的方法执行时，需要执行的函数
---@param yield boolean @是否在hook之后自动调用yield挂起
---@param ar? AsyncResult | nil @yield返回给主协程的参数
---@param timeout? integer | nil @在yield的情况下生效，默认10秒，0表示没有超时限制
---@param careRawRes? boolean | nil @是否需要原方法的返回值，如填true，hook回调第一个参数就是原方法的返回值
---@return AsyncResult, ... @主协程resume回来的参数
function AsyncExecutorHolder:hook(inst, fname, func, yield, ar, timeout, careRawRes)
    self:_hook(inst, fname, func, careRawRes)
    if yield then
        return self:yield(ar, timeout)
    end
end

function AsyncExecutorHolder:resetHooks()
    for inst, hooks in pairs(self.curHooks) do
        for fname, _ in pairs(hooks) do
            self:_unhook(inst, fname)
        end
    end
end

---@param inst table | nil
---@param fname string
---@param func AnyFunc
---@param careRawRes? boolean | nil
function AsyncExecutorHolder:_hook(inst, fname, func, careRawRes)
    if inst == nil then
        ErrorLog("%s hook fun %s without hook instance", self.__cname, fname)
        return
    elseif inst[fname] == nil then
        ErrorLog("%s hook fun %s not exist", self.__cname, fname)
        return
    end
    if self.curHooks[inst] == nil then
        self.curHooks[inst] = {}
    end
    assert(not self.curHooks[inst][fname], "hook dups! - func:" .. fname)
    self.curHooks[inst][fname] = HookInfo.new(inst[fname], func, careRawRes)
    inst[fname] = function(...) self:_runHook(inst, fname, ...) end
end

---@param inst table
---@param fname string
function AsyncExecutorHolder:_unhook(inst, fname)
    assert(self.curHooks[inst][fname] ~= nil, "unhook err! - func:" .. fname)
    self.curHooks[inst][fname] = nil
    if inst and inst[fname] then
        inst[fname] = nil
    end
end

---@param inst table
---@param fname string
function AsyncExecutorHolder:_runHook(inst, fname, ...)
    local hookInfo = self.curHooks[inst][fname]
    local rawRes = hookInfo.raw(...)
    if hookInfo.careRawRes then
        local args = { ... }
        -- 放在self之后，其他参数之前
        table.insert(args, 2, rawRes)
        return self:_handleHookResult(hookInfo.hook(unpack(args, 1, table.maxn(args))))
    else
        return self:_handleHookResult(hookInfo.hook(...))
    end
end

---@param res boolean | nil @true表示成功；false表示失败；nil表示忽略，等待下一次hook
---@param ... any
function AsyncExecutorHolder:_handleHookResult(res, ...)
    if res == nil then return end
    self:resetHooks()
    self:resume(not res and "ERROR" or nil, ...)
end

--region hook的封装

---@param funName string
---@param timeout? number @seconds
---@return AsyncResult.err, ...
function AsyncExecutorHolder:wait(funName, timeout)
    return self:setHooks({[funName]=function(self, ...)
        return true, ...
    end}, true, nil, timeout)
end

--endregion

-- for emmylua
---@class AsyncExecutorHolderEnv
local AsyncExecutorHolderEnv = {AsyncExecutorHolder=AsyncExecutorHolder}
return AsyncExecutorHolderEnv
