local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")

---@class AvatarBattleComponent : ComponentBase
AvatarGMComp = DefineClass("AvatarGMComp", ComponentBaseEnv.ComponentBase)

function AvatarGMComp:GmCommand(func, args)
    local executor = self
    if executor[func] == nil then
        if self.worldAvatar and self.worldAvatar[func] then
            executor = self.worldAvatar
        else
            self:logWarn("[lb] gm command not found: %s", func)
            return
        end
    end

    self:logInfo("[lb] gm command: %s %v", func, args)
    executor[func](executor, unpack(args))
end