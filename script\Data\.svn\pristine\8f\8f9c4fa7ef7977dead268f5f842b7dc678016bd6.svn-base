{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "20"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "3": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "洛神伤害率"}, "result": {"Type": "number", "Value": "0"}}}, "4": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "2"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": ""}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "5": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "buffId": {"Type": "number", "Value": "11200101"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}}, "Links": {"0": {"next": ["1.prev"]}, "1": {"next": ["3.prev"]}, "3": {"next": ["4.prev"]}, "4": {"next": ["5.prev"]}}, "DataFlows": {"1": {"targetIds": ["4.targetIds"]}, "3": {"result": ["4.damageFactor"]}}}