---@type GlobalEntitySelectorEnv
local GlobalEntitySelector = xrequire(EZFPath .. ".Utils.GlobalEntitySelector")
---@type GlobalDataMgrEnv
local GlobalDataMgr = xrequire(EZFPath .. ".Utils.GlobalDataMgr")
---@type NodeMgrEnv
local NodeMgr = xrequire(EZFPath .. ".Utils.NodeMgr")
---@type ConfigEnv
local Config = xrequire(EZFPath .. ".Utils.Config")
---@type DBUtilsEnv
local DBUtils = xrequire(EZFPath .. ".Utils.DBUtils")


--region GlobalEntityInfo

---@class GlobalEntityInfo
---@field mailbox LuaMailbox
---@field ready bool
---@field instanceData table
GlobalEntityInfo = DefineClass("GlobalEntityInfo")

function GlobalEntityInfo:ctor(mailbox, ready, instanceData)
    self.mailbox = mailbox
    self.ready = ready
    self.instanceData = instanceData
end

--endregion GlobalEntityInfo


--region GlobalEntityEntry

---@class GlobalEntityEntry
---@field globalEntityType string
---@field conf table
---@field strategy string
---@field selector SelectorBase
---@field entities table<EntityId, GlobalEntityInfo>
---@field enabledCnt integer
GlobalEntityEntry = DefineClass("GlobalEntityEntry")

---@param globalEntityType string
---@param strategy string
---@param info GlobalEntityInfo
function GlobalEntityEntry:ctor(globalEntityType, strategy, info)
    self.globalEntityType = globalEntityType
    self.conf = instance.conf[globalEntityType]
    self.strategy = self.conf.active_standby and "active_standby" or self.conf.strategy or "random_once"
    self.entities = {}
    self.selector = GlobalEntitySelector.GlobalEntitySelector.genSelector(self.strategy, self)
    self.enabledCnt = 0
end

---@param info GlobalEntityInfo
---@return boolean, GlobalEntityInfo @是否刚好满足数量需求
function GlobalEntityEntry:add(info)
    if self.entities[info.mailbox:getEntityId()] then
        ErrorLog("GlobalEntityEntry:add failed, entity[%s:%s] dup", self.globalEntityType, info.mailbox:getEntityId())
    else
        self.entities[info.mailbox:getEntityId()] = info
        self.selector:onAdd(info)
        instance:onAddCb(self.globalEntityType, info)
        if info.ready then
            self.enabledCnt = self.enabledCnt + 1
            return (self.conf.instances_required or 1) == self.enabledCnt, info
        end
    end
    return false, info
end

---@param mailbox LuaMailbox
---@return boolean, GlobalEntityInfo @是否刚好满足数量需求
function GlobalEntityEntry:ready(mailbox)
    local info = self.entities[mailbox:getEntityId()]
    if info then
        info.ready = true
        instance:onReadyCb(self.globalEntityType, info)
        self.enabledCnt = self.enabledCnt + 1
        return (self.conf.instances_required or 1) == self.enabledCnt, info
    else
        ErrorLog("GlobalEntityEntry:ready failed, entity[%s:%s] not exist", self.globalEntityType, mailbox:getEntityId())
    end
    return false, info
end

---@param mailbox LuaMailbox
---@return boolean, GlobalEntityInfo @是否刚好不满足数量需求
function GlobalEntityEntry:del(mailbox)
    ---@type GlobalEntityInfo
    local info = self.entities[mailbox:getEntityId()]
    if info then
        self.selector:onDel(info)
        self.entities[mailbox:getEntityId()] = nil
        instance:onRemovedCb(self.globalEntityType, info)
        self.enabledCnt = self.enabledCnt - 1
        return (self.conf.instances_required or 1) == (self.enabledCnt + 1), info
    else
        ErrorLog("GlobalEntityEntry:del failed, entity[%s:%s] not exist", self.globalEntityType, mailbox:getEntityId())
    end
    return false, info
end

--endregion GlobalEntityEntry


--region GlobalEntityMgr

---@alias GlobalEntityCB fun(info: GlobalEntityInfo):boolean @返回值true表示回调完成后自动删除回调

---@class GlobalEntityMgr
---@field globalEntities table<string, GlobalEntityEntry>
---@field conf table<string, table> @配置信息
---@field dependence table<string, table<string, boolean>>
---@field reversedDep table<string, string[]>
---@field delay table<TimerId, string> @有些需求如主备需要先随机延迟，用来确定哪个是主实例
---@field pending table<string, list> @正在等待创建的对象
---@field dbPending table<string, list> @正在等待db的待创建的对象
---@field onAddCbCache table<string, table<string | int, GlobalEntityCB>> @add的回调缓存
---@field onReadyCbCache table<string, table<string | int, GlobalEntityCB>> @ready的回调缓存
---@field onInstanceDataCbCache table<string, table<string | int, GlobalEntityCB>> @ready的回调缓存
---@field onRemovedCbCache table<string, table<string | int, GlobalEntityCB>> @removed的回调缓存
---@field onMeetRequirementsCbCache table<string, table<string | int, GlobalEntityCB>> @达到依赖需求时的回调缓存
---@field onNotMeetRequirementsCbCache table<string, table<string | int, GlobalEntityCB>> @低于依赖需求时的回调缓存
GlobalEntityMgr = DefineClass("GlobalEntityMgr")
GlobalEntityMgr.ACTIVE_PRIORITY_RANGE = 10000

function GlobalEntityMgr:ctor()
    self.globalEntities = {}
    self.conf = LuaNodeConfig.getConfig("global_entities") or {}
    self.dependence = {}
    self.reversedDep = {}
    self.delay = {}
    self.pending = {}
    self.dbPending = {}
    self.onAddCbCache = {}
    self.onReadyCbCache = {}
    self.onInstanceDataCbCache = {}
    self.onRemovedCbCache = {}
    self.onMeetRequirementsCbCache = {}
    self.onNotMeetRequirementsCbCache = {}
    -- 初始化依赖和被依赖的关系表
    for etype, econf in pairs(self.conf) do
        if econf.dependence and #econf.dependence > 0 then
            self.dependence[etype] = {}
            for _, dtype in ipairs(econf.dependence) do
                self.dependence[etype][dtype] = true
                if not self.reversedDep[dtype] then self.reversedDep[dtype] = {} end
                self.reversedDep[dtype][#self.reversedDep[dtype] + 1] = etype
            end
        end
    end
end

---@param name string @global entity type
function GlobalEntityMgr:getGlobalEntityCount(name)
    ---@type GlobalEntityEntry
    local entry = self.globalEntities[name]
    return entry and entry.selector:count() or 0
end

---@param name string @global entity type
---@param selectorKey? string | integer | nil @Selector的辅助数据，可以做随机种子等
---@return GlobalEntityInfo | nil
function GlobalEntityMgr:getGlobalEntityInfo(name, selectorKey)
    ---@type GlobalEntityEntry
    local entry = self.globalEntities[name]
    if entry then
        local res = entry.selector:get(selectorKey)
        return res
    end
    return nil
end

---@param name string @global entity type
---@param eid EntityId
---@return GlobalEntityInfo | nil
function GlobalEntityMgr:getGlobalEntityInfoById(name, eid)
    ---@type GlobalEntityEntry
    local entry = self.globalEntities[name]
    return entry and entry.entities[eid]
end

---@param name string @global entity type
---@return GlobalEntityEntry | nil
function GlobalEntityMgr:getGlobalEntityEntry(name)
    return self.globalEntities[name]
end

--region engine callback

function GlobalEntityMgr:onRegistered(name, mailbox, ready, instanceData)
    ---@type GlobalEntityEntry
    local entry = self.globalEntities[name]
    if not entry then
        entry = GlobalEntityEntry(name)
        self.globalEntities[name] = entry
    end
    local meet, info = entry:add(GlobalEntityInfo(mailbox, ready, instanceData))
    if meet then
        self:onMeetRequirements(name, info)
    end
end

function GlobalEntityMgr:onReady(name, mailbox)
    ---@type GlobalEntityEntry
    local entry = self.globalEntities[name]
    if entry then
        local meet, info = entry:ready(mailbox)
        if meet then
            self:onMeetRequirements(name, info)
        end
    else
        ErrorLog("GlobalEntityMgr.onReady failed, no global entity(%s) registed, onReady mailbox(%s)",
            name, mailbox:getEntityId())
    end
end

function GlobalEntityMgr:onRemoved(name, mailbox)
    ---@type GlobalEntityEntry
    local entry = self.globalEntities[name]
    if entry then
        local notMeet, info = entry:del(mailbox)
        if notMeet then
            self:onNotMeetRequirements(name, info)
        end
    else
        ErrorLog("GlobalEntityMgr.onRemove failed, no global entity(%s) registed, remove mailbox(%s)",
            name, mailbox:getEntityId())
    end
end

--endregion engine callback

--region create entity

-- 创建GlobalEntity
---@param name string @GlobalEntity名称
---@param eid EntityId @指定entityid，一般填0，会交给引擎生成
---@param prop? table @entity的属性
---@param dbidGenArgs? list | nil @生成dbid方法generateDBID的参数；nil表示本地创建且不会存盘
---@param dbid? string @dbid
---@return LuaEntityBase | EntityId | boolean
function GlobalEntityMgr:create(name, eid, prop, dbidGenArgs, dbid)
    ---@type GlobalEntity
    local entityType = EntityManager.getEntityType(name)
    if not entityType then
        ErrorLog("%s is not entity type!", name)
        return false
    end
    if not self.conf[name] then
        ErrorLog("%s is not global entity!", name)
        return false
    end
    if not dbid and dbidGenArgs then
        dbid = entityType:generateDBID(unpack(dbidGenArgs))
        if not dbid then
            ErrorLog("%s unknown dbid: %s", name, dbid)
            return false
        end
    end
    if self.conf[name].active_standby then
        -- 主备模式的参数检查
        assert(not self.conf[name].strategy or self.conf[name].strategy == "active_standby")
        assert(entityType.isActiveStandby,
            string.format("%s MUST inherit ActiveStandbyService", name))
    end
    -- 检查通过
    prop = prop or {}
    if self.dependence[name] then
        InfoLog("%s %s is pending", name, dbid)
        self.pending[name] = {eid, prop, dbid}
        return true
    end
    return self:doCreate(name, eid, prop, dbid)
end

---@return LuaEntityBase | EntityId | boolean
function GlobalEntityMgr:doCreate(name, eid, prop, dbid)
    if dbid then
        if self:isStandby(name) then
            -- 容灾重新拉起的进程，可能尚未产生新的active，先以standby的方式创建，以简化逻辑
            prop.__dbid_cache = dbid
            return EntityManager.createEntity(name, eid, prop)
        end
        if not DBUtils.dbmgrId then
            self.dbPending[name] = {eid, prop, dbid}
            return true
        end
        EntityManager.createEntityFromDB(name, dbid, 
            function (errcode, realEntityId)
                if errcode > 0 and errcode ~= 1 then
                    ErrorLog("create global entity %s from db failed, errcode: %s dbid: %s", name, errcode, dbid)
                    return
                end
                self:onCreate(realEntityId, name, eid, prop, dbid)
            end)
        return true
    end
    return EntityManager.createEntity(name, eid, prop)
end

---@return LuaEntityBase | EntityId | boolean
function GlobalEntityMgr:continueCreate(name)
    local args = self.pending[name]
    if args then
        local eid, prop, dbid = unpack(args)
        return self:doCreate(name, eid, prop, dbid)
    end
end

function GlobalEntityMgr:continueCreateFromDB()
    for name, args in pairs(self.dbPending) do
        if args then
            local eid, prop, dbid = unpack(args)
            return self:doCreate(name, eid, prop, dbid)
        end
    end
end

---@return LuaEntityBase | EntityId | boolean
function GlobalEntityMgr:onCreate(realEntityId, name, eid, prop, dbid)
    if realEntityId <= 0 then
        WarnLog("create entity from db failed, try create local %s %s", name, dbid)
        local ent = EntityManager.createEntity(name, eid, prop)
        if type(ent) == "number" then
            WarnLog("create entity with dbid, but ent is not lua entity %s %s", name, dbid)
            return ent
        end
        ent:saveWithDBID(dbid)
        return ent
    end
    return EntityManager.getEntity(realEntityId) or realEntityId
end

--endregion create entity

--region GlobalEntity事件回调

---@param cache table<string, table<string | int, GlobalEntityCB>>
---@param name string
function GlobalEntityMgr:onCallback(cache, name, ...)
    local cc = cache[name]
    if cc and next(cc) then
        cache[name] = {}
        local args = {n = select("#", ...), ...}
        for key, f in pairs(cc) do
            local suc, res = xpcall(function() return f(unpack(args, 1, args.n)) end, function(err) ErrorLog(EZE.traceback(err, 2)) end)
            if suc and not res then
                cache[name][key] = f
            end
        end
    end
end

-- 确保ready的实例数量达到需求
function GlobalEntityMgr:onMeetRequirements(name, info)
    self:onMeetRequirementsCb(name, info)
    -- 如果有其他对象依赖该对象
    if self.reversedDep[name] then
        for _, etype in ipairs(self.reversedDep[name]) do
            self.dependence[etype][name] = nil
            if not next(self.dependence[etype]) then
                self.dependence[etype] = nil
                self:continueCreate(etype)
            end
        end
    end
end

-- 实例丢失后处理
function GlobalEntityMgr:onNotMeetRequirements(name, info)
    WarnLog("GlobalEntityMgr.onNotMeetRequirements %s %s", containerType, name)
    -- 如果有其他对象依赖该对象
    if self.reversedDep[name] then
        for _, etype in ipairs(self.reversedDep[name]) do
            if not self.dependence[etype] then self.dependence[etype] = {} end
            self.dependence[etype][name] = true
        end
    end
    self:onNotMeetRequirementsCb(name, info)
end

--region add事件

-- 注册某个GlobalEntity注册之后的回调，注意在回调中不能再做注册、注销操作
---@param name string
---@param key string | int
---@param f GlobalEntityCB @返回值true表示回调完成后自动删除回调
function GlobalEntityMgr:registerAddCb(name, key, f)
    local cache = self.onAddCbCache[name]
    if not cache then
        cache = {}
        self.onAddCbCache[name] = cache
    end
    if cache[key] then
        WarnLog("registerAddCb but %s.%s is exist, repalce it", name, key)
    end
    cache[key] = f
end

---@param name string
---@param key string | int
function GlobalEntityMgr:unregisterAddCb(name, key)
    local cache = self.onAddCbCache[name]
    if cache then
        if cache[key] then
            cache[key] = nil
        else
            WarnLog("unregisterAddCb but %s.%s is not exist", name, key)
        end
    else
        WarnLog("unregisterAddCb but %s is not exist", name)
    end
end

---@param name string
---@param info GlobalEntityInfo
function GlobalEntityMgr:onAddCb(name, info)
    self:onCallback(self.onAddCbCache, name, info)
    EntityManager.dispatchInterface("onGlobalEntityRegistered", name, info)
    if info.ready then
        self:onReadyCb(name, info)
    end
end

--endregion add事件

--region ready事件

-- 注册某个GlobalEntity初始化完成之后的回调，注意在回调中不能再做注册、注销操作
---@param name string
---@param key string | int
---@param f GlobalEntityCB @返回值true表示回调完成后自动删除回调
function GlobalEntityMgr:registerReadyCb(name, key, f)
    local cache = self.onReadyCbCache[name]
    if not cache then
        cache = {}
        self.onReadyCbCache[name] = cache
    end
    if cache[key] then
        WarnLog("registerReadyCb but %s.%s is exist, repalce it", name, key)
    end
    cache[key] = f
end

---@param name string
---@param key string | int
function GlobalEntityMgr:unregisterReadyCb(name, key)
    local cache = self.onReadyCbCache[name]
    if cache then
        if cache[key] then
            cache[key] = nil
        else
            WarnLog("unregisterReadyCb but %s.%s is not exist", name, key)
        end
    else
        WarnLog("unregisterReadyCb but %s is not exist", name)
    end
end

---@param name string
---@param info GlobalEntityInfo
function GlobalEntityMgr:onReadyCb(name, info)
    self:onCallback(self.onReadyCbCache, name, info)
    EntityManager.dispatchInterface("onGlobalEntityReady", name, info)
end

--endregion ready事件

--region remove事件

-- 注册某个GlobalEntity移除之后的回调，注意在回调中不能再做注册、注销操作
---@param name string
---@param key string | int
---@param f GlobalEntityCB @返回值true表示回调完成后自动删除回调
function GlobalEntityMgr:registerRemovedCb(name, key, f)
    local cache = self.onRemovedCbCache[name]
    if not cache then
        cache = {}
        self.onRemovedCbCache[name] = cache
    end
    if cache[key] then
        WarnLog("registerRemovedCb but %s.%s is exist, repalce it", name, key)
    end
    cache[key] = f
end

---@param name string
---@param key string | int
function GlobalEntityMgr:unregisterRemovedCb(name, key)
    local cache = self.onRemovedCbCache[name]
    if cache then
        if cache[key] then
            cache[key] = nil
        else
            WarnLog("unregisterRemovedCb but %s.%s is not exist", name, key)
        end
    else
        WarnLog("unregisterRemovedCb but %s is not exist", name)
    end
end

---@param name string
---@param mailbox LuaMailbox
---@param info GlobalEntityInfo
function GlobalEntityMgr:onRemovedCb(name, info)
    self:onCallback(self.onRemovedCbCache, name, info)
    EntityManager.dispatchInterface("onGlobalEntityRemoved", name, info)
end

--endregion remove事件

--region 达到数量要求事件

-- 注册某个GlobalEntity达到数量要求之后的回调，注意在回调中不能再做注册、注销操作
-- 这些回调会比创建依赖对象更早执行
---@param name string
---@param key string | int
---@param f GlobalEntityCB @返回值true表示回调完成后自动删除回调
function GlobalEntityMgr:registerMeetRequirementsCb(name, key, f)
    local cache = self.onMeetRequirementsCbCache[name]
    if not cache then
        cache = {}
        self.onMeetRequirementsCbCache[name] = cache
    end
    if cache[key] then
        WarnLog("registerMeetRequirementsCb but %s.%s is exist, repalce it", name, key)
    end
    cache[key] = f
end

---@param name string
---@param key string | int
function GlobalEntityMgr:unregisterMeetRequirementsCb(name, key)
    local cache = self.onMeetRequirementsCbCache[name]
    if cache then
        if cache[key] then
            cache[key] = nil
        else
            WarnLog("unregisterMeetRequirementsCb but %s.%s is not exist", name, key)
        end
    else
        WarnLog("unregisterMeetRequirementsCb but %s is not exist", name)
    end
end

---@param name string
---@param info GlobalEntityInfo
function GlobalEntityMgr:onMeetRequirementsCb(name, info)
    self:onCallback(self.onMeetRequirementsCbCache, name, info)
    EntityManager.dispatchInterface("onGlobalEntityMeetRequirements", name, info)
end

--endregion 达到数量要求事件

--region 低于数量要求事件

-- 注册某个GlobalEntity跌破数量要求之后的回调，注意在回调中不能再做注册、注销操作
---@param name string
---@param key string | int
---@param f GlobalEntityCB @返回值true表示回调完成后自动删除回调
function GlobalEntityMgr:registerNotMeetRequirementsCb(name, key, f)
    local cache = self.onNotMeetRequirementsCbCache[name]
    if not cache then
        cache = {}
        self.onNotMeetRequirementsCbCache[name] = cache
    end
    if cache[key] then
        WarnLog("registerNotMeetRequirementsCb but %s.%s is exist, repalce it", name, key)
    end
    cache[key] = f
end

---@param name string
---@param key string | int
function GlobalEntityMgr:unregisterNotMeetRequirementsCb(name, key)
    local cache = self.onNotMeetRequirementsCbCache[name]
    if cache then
        if cache[key] then
            cache[key] = nil
        else
            WarnLog("unregisterNotMeetRequirementsCb but %s.%s is not exist", name, key)
        end
    else
        WarnLog("unregisterNotMeetRequirementsCb but %s is not exist", name)
    end
end

---@param name string
---@param info GlobalEntityInfo
function GlobalEntityMgr:onNotMeetRequirementsCb(name, info)
    self:onCallback(self.onNotMeetRequirementsCbCache, name, info)
    EntityManager.dispatchInterface("onGlobalEntityNotMeetRequirements", name, info)
end

--endregion 低于数量要求事件

--endregion GlobalEntity事件回调

--region rpc容灾

-- 注册容灾恢复时的上报数据的回调
---@param recoverRpcGetter? fun(name: string): nil | fun(eid: EntityId): void
function GlobalEntityMgr:registerRecoverRpc(recoverRpcGetter)
    recoverRpcGetter = recoverRpcGetter or GlobalEntityMgr.getRecoverRpc
    for name, entry in pairs(self.globalEntities) do
        local _, ent = next(entry.entities)
        if ent.instanceData.hasRecoverRpc then
            local recoverRpc = recoverRpcGetter(name)
            assert(recoverRpc, string.format("miss class %s.recoverRpc", name))
            GlobalDataMgr.instance:registerCallback(name .. ".recover", "recoverRpc", recoverRpc)
        end
    end
end

-- 获取GlobalEntity/Service的恢复数据rpc；建议项目可以自己实现并覆盖
---@param name string @GlobalEntity/Service名称
---@return nil | fun(eid: EntityId): void
function GlobalEntityMgr.getRecoverRpc(name)
    local env = xrequire("Services." .. name)
    assert(env and env[name], string.format("miss class %s", name))
    return env[name].recoverRpc
end

--endregion rpc容灾

--region 主备容灾

---@class ActiveStandbyUnique
---@field nodeUid NodeUid
---@field cid ContainerId

---@param ctnId? ContainerId
---@return ActiveStandbyUnique
function GlobalEntityMgr:getActiveStandbyUnique(ctnId)
    return {nodeUid=EZE.nodeUid(), cid=ctnId or containerId}
end

---@param name string
---@return ContainerId | nil
function GlobalEntityMgr:getActiveCid(name)
    ---@type ActiveStandbyUnique
    local data = GlobalDataMgr.instance:get(name)
    if data and NodeMgr.instance:isValidNode(EZE.containerIdToNodeId(data.cid), data.nodeUid) then
        return data.cid
    end
    return nil
end

---@param name string
---@param eid? EntityId
---@return boolean
function GlobalEntityMgr:isActive(name, eid)
    local cid = self:getActiveCid(name)
    return cid and cid == ((eid and eid ~= 0) and EZE.entityIdToContainerId(eid) or containerId)
end

---@param name string
---@param eid? EntityId
---@return boolean
function GlobalEntityMgr:isStandby(name, eid)
    return self.conf[name].active_standby and not self:isActive(name, eid)
end

---@param name string
function GlobalEntityMgr:missActive(name)
    return self.conf[name].active_standby and not self:getActiveCid(name)
end

---@param name string
---@param waitOthers bool
function GlobalEntityMgr:canCalcActive(name, waitOthers)
    if not self:missActive(name) then
        return false
    end
    if not waitOthers then
        return true
    end
    local entry = self:getGlobalEntityEntry(name)
    return entry and entry.selector:count() >= (self.conf[name].instances_required or 1)
end

-- 计算active实例
---@param name string
---@return EntityId | nil
function GlobalEntityMgr:calcActiveByPriority(name)
    assert(self.conf[name].active_standby)
    ---@type GlobalEntityInfo | nil
    local activeInfo = nil
    local entry = self:getGlobalEntityEntry(name)
    if entry then
        local sum = 0
        local curPriority = self.ACTIVE_PRIORITY_RANGE + 1
        for _, info in pairs(entry.entities) do
            sum = sum + info.instanceData.activePriority
        end
        for eid, info in pairs(entry.entities) do
            local priority = SteadyRandom(sum * info.instanceData.activePriority, self.ACTIVE_PRIORITY_RANGE)
            if not activeInfo
                or (info.ready and not activeInfo.ready)
                or (info.ready == activeInfo.ready and priority < curPriority)
                or (info.ready == activeInfo.ready and priority == curPriority and eid < activeInfo.mailbox:getEntityId()) then
                activeInfo = info
                curPriority = priority
            end
        end
    end
    return activeInfo and activeInfo.mailbox:getEntityId()
end

--endregion 主备容灾

---@type GlobalEntityMgr
instance = instance or GlobalEntityMgr.new()

--endregion GlobalEntityMgr

-- for emmylua
---@class GlobalEntityMgrEnv
local GlobalEntityMgrEnv = {
    GlobalEntityInfo=GlobalEntityInfo,
    GlobalEntityEntry=GlobalEntityEntry,
    GlobalEntityMgr=GlobalEntityMgr,
    ---@type GlobalEntityMgr
    instance=instance,
}
return GlobalEntityMgrEnv
