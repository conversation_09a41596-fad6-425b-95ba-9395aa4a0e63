﻿function DbgHookErrorAndBoardcastClients()
    local EntityEnv = xrequire("Framework.Entities.Entity")
    local SpaceEnv = xrequire("Framework.Entities.Space")

    local broadcastClients = function(msg)
        EZGlobal.Entity:serviceRpc("RoleService", "BroadcastServerError", msg)
    end

    local entityLogError = function(self, format, ...)
        EZE.log.entity_error(nil, self.__info_str or "", format, ...)
        local msg = EZE.log.get_last_log()
        broadcastClients(msg)
    end
    EntityEnv.Entity.logError = entityLogError
    SpaceEnv.Space.logError = entityLogError

    local errorLog = function(format, ...)
        EZE.log.error(nil, format, ...)
        local msg = EZE.log.get_last_log()
        broadcastClients(msg)
    end
    DeclareGlobal("ErrorLog", errorLog)

    local exceptionHandler = function(err)
        msg = EZE.tracebackWithLocals(err, 3)
        ErrorLog(msg)
        return msg
    end
    EZE.hookExceptions(exceptionHandler)
end
