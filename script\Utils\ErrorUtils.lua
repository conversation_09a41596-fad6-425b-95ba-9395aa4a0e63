﻿function DbgHookErrorAndBoardcastClients()
    local EntityEnv = xrequire("Framework.Entities.Entity")
    local SpaceEnv = xrequire("Framework.Entities.Space")

    local broadcastClients = function(msg)
        EZGlobal.Entity:serviceRpc("RoleService", "BroadcastServerError", msg)
    end

    local entityLogError = function(self, format, ...)
        EZE.log.entity_error(nil, self.__info_str or "", format, ...)
        local msg = EZE.log.get_last_log()
        broadcastClients(msg)
    end
    EntityEnv.Entity.logError = entityLogError
    SpaceEnv.Space.logError = entityLogError

    local errorLog = function(format, ...)
        EZE.log.error(nil, format, ...)
        local msg = EZE.log.get_last_log()
        broadcastClients(msg)
    end
    DeclareGlobal("ErrorLog", errorLog)

    local exceptionHandler = function(err)
        msg = TracebackWithLocals(err, 3)
        ErrorLog(msg)
        return msg
    end
    EZE.hookExceptions(exceptionHandler)
end

function TracebackWithLocals(msg, level)
    -- 默认参数处理
    level = level or 1
    level = level + 1
    local TRACEBACK_LEVELS1 = 12  -- 初始显示的堆栈层级数
    local TRACEBACK_LEVELS2 = 10  -- 省略部分后的补充层级数
    
    local result = {}
    
    -- 添加消息
    if msg then
        table.insert(result, msg .. "\n")
    end
    table.insert(result, "stack traceback:")
    
    local lim = TRACEBACK_LEVELS1
    
    -- 遍历堆栈
    while true do
        -- 构建堆栈条目
        local entry = "\n\t"

        -- 获取堆栈信息
        local info = debug.getinfo(level, "Snl")
        if not info then break end
        
        -- 处理堆栈层级限制
        if lim and level > lim then
            if not debug.getinfo(level + TRACEBACK_LEVELS2) then
                level = level - 1
            else
                table.insert(result, "\n\t...")
                local maxLevel = level
                while debug.getinfo() do
                    maxLevel = maxLevel + 1
                end
                -- 跳过中间部分
                level = maxLevel - 1 - TRACEBACK_LEVELS2
            end
            lim = nil  -- 不再限制
            goto continue
        end
        
        -- 处理不同的函数类型
        if info.what == "C" then
            -- C 函数
            if info.name then
                entry = entry .. string.format("%s: in function '%s'", info.short_src or "?", info.name)
            else
                entry = entry .. "[C]: in ?"
            end
        elseif info.what == "main" then
            -- 主 chunk
            entry = entry .. string.format("%s: in main chunk", info.short_src or "?")
        else
            -- Lua 函数
            if info.name then
                entry = entry .. string.format("%s:%d: in function '%s'", info.short_src or "?", info.currentline or 0, info.name)
            else
                entry = entry .. string.format("%s:%d: in function <%s:%d>", info.short_src or "?", info.currentline or 0, info.short_src, info.linedefined or 0)
            end

            -- 添加局部变量信息
            entry = entry .. " locals: {"
            local i = 1
            while true do
                local name, value = debug.getlocal(level, i)
                if not name then
                    break
                end
                entry = entry .. string.format("%s=%s, ", name, tostring(value))
                i = i + 1
            end
            entry = entry .. "}"
        end
        
        table.insert(result, entry)
        
        ::continue::
        level = level + 1
    end
    
    return table.concat(result)
end