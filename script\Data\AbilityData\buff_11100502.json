{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"consts": {"times": {"VariableRangeType": 1, "ResetOnExecute": false, "Type": "number", "Value": 8}}, "event1": {"Type": "number", "Value": "115"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}, "event2": {"Type": "number", "Value": "5"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "False"}}}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "30"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": ""}, "value": {"Type": "number", "Value": "0"}}}, "2": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "张角雷击加闪避"}, "result": {"Type": "number", "Value": "0"}}}, "4": {"Type": "RandomNode", "Field": {"randomType": {"Type": "number", "Value": "1"}, "useFormula": {"Type": "boolean", "Value": "False"}, "probability": {"Type": "number", "Value": "0.8"}, "probabilityFormula": {"Type": "string", "Value": ""}, "record": {"Type": "boolean", "Value": "False"}, "integer": {"Type": "boolean", "Value": "False"}, "min": {"Type": "number", "Value": "0"}, "max": {"Type": "number", "Value": "0"}, "minFormula": {"Type": "string", "Value": ""}, "maxFormula": {"Type": "string", "Value": ""}, "numberResult": {"Type": "number", "Value": "0"}, "byWeight": {"Type": "boolean", "Value": "True"}, "idList": [{"Type": "string", "Value": ""}, {"Type": "string", "Value": ""}, {"Type": "string", "Value": ""}], "weights": [{"Type": "number", "Value": "0"}, {"Type": "number", "Value": "0"}, {"Type": "number", "Value": "0"}], "count": {"Type": "number", "Value": "1"}, "result": []}}, "5": {"Type": "SetVariableNode", "Field": {"input": {"Type": "number", "Value": "8"}, "key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "record": {"Type": "boolean", "Value": "True"}, "showName": {"Type": "string", "Value": "张角雷击次数"}, "percent": {"Type": "boolean", "Value": "False"}}}, "8": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "9": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "3"}, "number2": {"Type": "number", "Value": "0"}}}, "10": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "2"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "12": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "14": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "张角雷击伤害"}, "result": {"Type": "number", "Value": "0"}}}, "15": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "True"}, "attributeType": {"Type": "number", "Value": "20"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "17": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam2"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "张角雷击治疗"}, "result": {"Type": "number", "Value": "0"}}}, "18": {"Type": "RandomNode", "Field": {"randomType": {"Type": "number", "Value": "1"}, "useFormula": {"Type": "boolean", "Value": "False"}, "probability": {"Type": "number", "Value": "0.5"}, "probabilityFormula": {"Type": "string", "Value": ""}, "record": {"Type": "boolean", "Value": "False"}, "integer": {"Type": "boolean", "Value": "False"}, "min": {"Type": "number", "Value": "0"}, "max": {"Type": "number", "Value": "0"}, "minFormula": {"Type": "string", "Value": ""}, "maxFormula": {"Type": "string", "Value": ""}, "numberResult": {"Type": "number", "Value": "0"}, "byWeight": {"Type": "boolean", "Value": "True"}, "idList": [{"Type": "string", "Value": ""}, {"Type": "string", "Value": ""}, {"Type": "string", "Value": ""}], "weights": [{"Type": "number", "Value": "0"}, {"Type": "number", "Value": "0"}, {"Type": "number", "Value": "0"}], "count": {"Type": "number", "Value": "1"}, "result": []}}, "19": {"Type": "HealNode", "Field": {"targetIds": [], "healFactor": {"Type": "string", "Value": ""}, "healFactorNew": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedHeal": {"Type": "boolean", "Value": "True"}, "fixedHealValue": {"Type": "number", "Value": "0"}, "healHeroIds": [], "healPackageIds": [], "missHeroIds": []}}, "20": {"Type": "SetVariableNode", "Field": {"key": {"Type": "string", "Value": ""}, "variableRangeType": {"Type": "number", "Value": "1"}, "input": {"Type": "nil", "Value": "null"}, "record": {"Type": "boolean", "Value": "True"}, "showName": {"Type": "string", "Value": "张角雷击次数"}, "percent": {"Type": "boolean", "Value": "False"}}}, "21": {"Type": "GetVariableNode", "Field": {"key": {"Type": "string", "Value": "times"}, "variableRangeType": {"Type": "number", "Value": "1"}, "output": {"Type": "nil", "Value": "null"}}}, "22": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": ""}, "formulaItems": [[{"Type": "string", "Value": "max(a-1,0)"}, {"Type": "number", "Value": 8.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "0"}, "noLeftBoundary": {"Type": "boolean", "Value": "False"}, "noRightBoundary": {"Type": "boolean", "Value": "False"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}}, "Links": {"0": {"onLayerIncreased": ["2.prev"], "AfterDodgeSuccessNode": ["8.prev"], "RoundPrepareNode": ["5.prev"]}, "2": {"next": ["1.prev"]}, "4": {"next": ["18.prev"]}, "8": {"next": ["9.prev"]}, "9": {"next": ["4.prev"]}, "10": {"next": ["21.prev"]}, "12": {"next": ["14.prev"]}, "14": {"next": ["10.prev"]}, "15": {"next": ["17.prev"]}, "17": {"next": ["19.prev"]}, "18": {"elseNode": ["15.prev"], "next": ["12.prev"]}, "19": {"next": ["21.prev"]}, "21": {"next": ["22.prev"]}, "22": {"next": ["20.prev"]}}, "DataFlows": {"2": {"result": ["1.value"]}, "8": {"output": ["9.number1"]}, "12": {"targetIds": ["10.targetIds"]}, "14": {"result": ["10.damageFactor"]}, "15": {"targetIds": ["19.targetIds"]}, "17": {"result": ["19.<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "21": {"output": ["22.a"]}, "22": {"result": ["20.input"]}}}