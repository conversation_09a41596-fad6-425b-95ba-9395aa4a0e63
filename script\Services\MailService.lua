local ServiceEnv = xrequire("Framework.Services.Service")
local AnyEntityCallerCompEnv = xrequire("Entities.CommonComps.AnyEntityCallerComp")
local HttpServerCompEnv = xrequire("Framework.Entities.Components.HttpServerComp")
local HttpClientPoolEnv = xrequire("Framework.Utils.HttpClientPool")
local Config = xrequire(EZFPath .. ".Utils.Config")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local MailConstEnv = xrequire("Common.Const.MailConst")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")

---@class MailService: Service, AnyEntityCallerComp, HttpServerComp
MailService = DefineEntity("MailService", {ServiceEnv.Service}, {
    AnyEntityCallerCompEnv.AnyEntityCallerComp,  ---@type AnyEntityCallerComp
    HttpServerCompEnv.HttpServerComp,  ---@type HttpServerComp
})
MailService.SERVER_ID = Config.getNodeConfig("cluster")

function MailService:ctor()
    self.processedOrder = {}
    self:initMailServer()
    local host, port, _, ssl = MailConstEnv.GetMailHostPort()
    self.httpClient = HttpClientPoolEnv.HttpClientPool.new(host, port, ssl, 10)
    self.httpClient:Start()
    self:regist(true)
end

--region 发邮件

function MailService:SendTemplateMail(templateId, args, userList, toAll, from, attachment)
    local config = GameCommon.TableDataManager:GetMailTemplate(templateId)
    assert(config, "Invalid mail templateId:" .. tostring(templateId))
    local mailType = config.mail_type
    self:sendMail(templateId, args, nil, nil, userList, toAll, mailType, from, attachment)
end

function MailService:SendRawMail(title, content, userList, toAll, mailType, from, attachment)
    self:sendMail(nil, nil, title, content, userList, toAll, mailType, from, attachment)
end

MailService.VALID_SEND_MAILTYPE = {
    [TableConst.enums.MailType.Ally] = true,
    [TableConst.enums.MailType.AllyGroup] = true,
    [TableConst.enums.MailType.System] = true,
    [TableConst.enums.MailType.Announce] = true,
}

function MailService:sendMail(templateId, args, title, content, userList, toAll, mailType, from, attachment)
    assert((templateId and args) or (title and content), "Invalid mail params")
    assert(not table.isempty(userList), "Invalid user list")
    assert(MailService.VALID_SEND_MAILTYPE[mailType], "Invalid mail type")
    local nowTime = ServerTimeEnv.GetServerNowInt()
    local strUserList = {}
    for _, userId in ipairs(userList) do
        table.insert(strUserList, tostring(userId))
    end
    local body = {
        ServerID = MailService.SERVER_ID,
        OrderID = EZE.genUUID(),  -- 订单ID，失败后采用同一个订单ID重试发送，中台会去重
        To = strUserList,
        From = GameCommon.json.encode({avatarGid = from}),
        MailType = toAll and MailConstEnv.SDK_MAIL_TYPE.ALL_SERVER or MailConstEnv.SDK_MAIL_TYPE.PERSONAL,
        ParamList = {
            type = mailType,
        },
        CreatedTime = nowTime,
        ExpirationTime = 365 * 24 * 60 * 60,  -- 一年后过期
        ServerTime = nowTime,
    }
    -- 邮件内容
    if templateId then
        body.ParamList["templateId"] = templateId
        body.ParamList["args"] = args
    else
        body["Title"] = title
        body["Content"] = content
    end
    local attaches = {}
    local failedAttachment = {}
    for itemId, num in pairs(attachment) do
        if ProcessedTableEnv.GetDataConfig(itemId) and num > 0 then
            table.insert(attaches, {id = itemId, count = num})
        else
            table.insert(failedAttachment, {[itemId] = num})
        end
    end
    if not table.isempty(failedAttachment) then
        self:logError("Invalid attachments: %v %v", userList, failedAttachment)
    end
    if not table.isempty(attaches) then
        body.Attaches = GameCommon.json.encode(attaches)
    end
    -- 序列化
    body.ParamList = GameCommon.json.encode(body.ParamList)
    body = GameCommon.json.encode(body)
    -- 发送请求
    self.httpClient:Request("POST", "/mail/send_sys_mail", body, MailConstEnv.HTTP_HEADER, 10, function(code, msg, headers, body)
        self:logInfo("MailService:sendMail %s %s %v", code, msg, body)
        if code ~= MailConstEnv.HttpStatus.OK then
            return
        end
    end)
end

--endregion

--region 接受邮件和奖励通知

function MailService:initMailServer()
    self:registHttpHandler("/new", "OnNewMail")
    self:registHttpHandler("/reward", "OnRewardMail")
    self:registHttpHandler("/itemlist", "OnGetItemList")
    self:startHttpServer("0.0.0.0", LuaNodeConfig.getConfig("fyc.http_port.mail"))
end

function MailService:OnNewMail(sessionId, request)
    self:logInfo("OnNewMail %v", request)
    local body = GameCommon.json.decode(request.body)
    local avatarGid = body.user_id
    if avatarGid == 0 then
        -- 全服邮件
        self:broadcastAvatarClient("OnNewMail", body.send_time)
    else
        self:callAvatar(avatarGid, "OnNewMail", body.send_time)
    end
    self:httpResponse(sessionId, MailConstEnv.HttpStatus.OK, {["Content-Type"] = "application/json"}, GameCommon.json.encode({code = MailConstEnv.RESPONSE_CODE.SUCC}))
end

function MailService:OnRewardMail(sessionId, request)
    self:logInfo("OnRewardMail %v", request)
    local body = GameCommon.json.decode(request.body)
    assert(body and body.order_id, "Invalid reward mail request")
    if self.processedOrder[body.user_id] and self.processedOrder[body.user_id][body.order_id] then
        self:httpResponse(sessionId, MailConstEnv.HttpStatus.OK, {["Content-Type"] = "application/json"}, GameCommon.json.encode({code = MailConstEnv.RESPONSE_CODE.DUP_ORDER}))
        return
    end
    local reward = {}
    for _, info in ipairs(body.item_list) do
        reward[info.id] = info.count
    end
    local avatarGid = body.user_id
    assert(avatarGid and not table.isempty(reward), "Invalid reward mail data")
    if not self.processedOrder[body.user_id] then
        self.processedOrder[body.user_id] = {}
    end
    self.processedOrder[body.user_id][body.order_id] = true
    self:callAvatar(avatarGid, "OnGetMailReward", reward,  body.mid)
    self:httpResponse(sessionId, MailConstEnv.HttpStatus.OK, {["Content-Type"] = "application/json"}, GameCommon.json.encode({code = MailConstEnv.RESPONSE_CODE.SUCC}))
end

function MailService:OnGetItemList(sessionId, request)
    if not self.itemListCache then
        local itemList = {}
        local function addToList(info, itemTypeId, itemTypeName)
            table.insert(itemList, {
                itemId = info.id,
                itemName = info.name,
                itemTypeId = itemTypeId,
                itemTypeName = itemTypeName,
            })
        end
        for _, info in pairs(GameCommon.TableDataManager:GetCurrencyDatas()) do
            addToList(info, 1, "coin")
        end
        for _, info in pairs(GameCommon.TableDataManager:GetAllItemData()) do
            addToList(info, 2, "item")
        end
        for _, info in pairs(GameCommon.TableDataManager:GetAllMaterialData()) do
            addToList(info, 3, "material")
        end
        for _, info in pairs(GameCommon.TableDataManager:GetAllTokenData()) do
            addToList(info, 4, "token")
        end
        for _, info in pairs(GameCommon.TableDataManager:GetAllEffectItemData()) do
            addToList(info, 5, "effect_item")
        end
        self.itemListCache = GameCommon.json.encode({
            code = MailConstEnv.RESPONSE_CODE.SUCC,
            message = "success",
            data = itemList,
        })
    end
    self:httpResponse(sessionId, MailConstEnv.HttpStatus.OK, {["Content-Type"] = "application/json"}, self.itemListCache)
end

--endregion
