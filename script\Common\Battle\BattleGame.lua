-- 单场战斗的主控器

local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local BattleRecorderEnv = xrequire("Common.Battle.BattleRecorder")
local BattleHeroEnv = xrequire("Common.Battle.BattleHero")
local BattleFormulasEnv = xrequire("Common.Battle.BattleFormulas")
local BattleGameEventEnv = xrequire("Common.Battle.BattleGameEvent")
local AbilitySystemManagerServerEnv = xrequire("Common.Battle.AbilitySystem.Core.AbilitySystemManagerServer")
local BattleCheckerEnv = xrequire("Common.Battle.BattleChecker")
local DetailRecordType = TableConst.enums.DetailRecordType
local IdGeneratorEnv = xrequire("Common.Utils.IdGenerator")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

local Camps = {TableConst.enums.Camp.A, TableConst.enums.Camp.B}

---@class BattleGame
local BattleGame = DefineClass("BattleGame")

function BattleGame:ctor(battleTeamA, battleTeamB, battleInfo, abilitySystemManagerClient)
    self.success = false
    BattleCheckerEnv.CheckInputTeam(Camps[1], battleTeamA)
    BattleCheckerEnv.CheckInputTeam(Camps[2], battleTeamB)

    self.battleInfo = battleInfo or {} -- 额外信息，但是需要记录到战报中的，在这里中转一下
    if self.battleInfo.battleType == TableConst.enums.BattleType.PVE_DRILL or self.battleInfo.battleType == TableConst.enums.BattleType.PVE_LAND then
        battleTeamB.supply = 100
    end
    self.round = 0 -- 回合为0时表示战斗开始阶段
    self.battleEnd = false
    self.winCamp = TableConst.enums.Camp.None

    self.recorder = BattleRecorderEnv.BattleRecorder.new(self)

    self.battleTeamA = battleTeamA
    self.battleTeamB = battleTeamB

    -- 按阵营记录武将
    self.heroes = {
        [TableConst.enums.Camp.A] = {},
        [TableConst.enums.Camp.B] = {},
    }

    -- 记录活着的武将Id，用于判断战斗结束
    self.aliveHeroIds = {
        [TableConst.enums.Camp.A] = {},
        [TableConst.enums.Camp.B] = {},
    }

    -- 按Id记录武将
    self.heroUniqueIdMap = {}

    -- 策划需求：随机为所有武将分配一个极小的速度加成
    -- 在回合开始排序的时候，相同速度的武将在不同场战斗中的排序可能会不同，但在同一场战斗中，排序结果是相同的
    local speedOrderRandomValues = {0.0001, 0.0002, 0.0003, 0.0004, 0.0005, 0.0006}
    table.shuffle(speedOrderRandomValues)
    local speedOrderRandomValueIndex = 1
    for index, heroData in pairs(battleTeamA.battleArmyData.heroes) do
        if heroData.attributes[TableConst.enums.BattleAttributeType.Health] > 0 then
            local hero = BattleHeroEnv.BattleHero.new(self, TableConst.enums.Camp.A, index, heroData, battleTeamA)
            self.heroes[TableConst.enums.Camp.A][index] = hero
            self.heroUniqueIdMap[hero.uniqueId] = hero
            self.aliveHeroIds[TableConst.enums.Camp.A][hero.uniqueId] = true
            hero.speedOrderRandomValue = speedOrderRandomValues[speedOrderRandomValueIndex]
            speedOrderRandomValueIndex = speedOrderRandomValueIndex + 1
        end
    end
    for index, heroData in pairs(battleTeamB.battleArmyData.heroes) do
        if heroData.attributes[TableConst.enums.BattleAttributeType.Health] > 0 then
            local hero = BattleHeroEnv.BattleHero.new(self, TableConst.enums.Camp.B, index, heroData, battleTeamB)
            self.heroes[TableConst.enums.Camp.B][index] = hero
            self.heroUniqueIdMap[hero.uniqueId] = hero
            self.aliveHeroIds[TableConst.enums.Camp.B][hero.uniqueId] = true
            hero.speedOrderRandomValue = speedOrderRandomValues[speedOrderRandomValueIndex]
            speedOrderRandomValueIndex = speedOrderRandomValueIndex + 1
        end
    end

    -- 武将行动和事件响应顺序，按速度排序，所有武将速度都没有改变时，顺序不变，直接用缓存结果
    self.orderedHeroes = {}
    self.orderedHeroesCache = nil

    self._uidGenerator = IdGeneratorEnv.IdGenerator.new()

    -- 这些Packages用于伤害、治疗、发动战法、普攻过程中，动态修改单次数据的用途
    -- 以伤害为例：伤害过程是，先根据双方数据生成伤害数据包（damagePackage），保存到Map中，然后触发伤害前事件。
    --           在触发事件的时候，可能有技能会修改本次伤害的信息，比如修改公式里的某些参数、完全免疫伤害等，此时就会修改Map中的damagePackage
    --           见SubmitDamage函数和ModifyDamageNodeServer节点
    -- 其他同理
    self.damagePackageMap = {}
    self.healPackageMap = {}
    self.tacticPackageMap = {}
    self.plainAttackPackageMap = {}

    self.curActionHeroUid = nil -- 当前行动中的武将Id，Select节点会用到

    -- 技能系统中存储的变量
    self.skillVariables = {}
    self.skillVariables.global = {}
    self.skillVariables.tactic = {}
    self.skillVariables.buff = {}
    self.skillVariables.hero = {}
    self.battleGameEvent = BattleGameEventEnv.BattleGameEvent.new(self)
    self.abilitySystemManager = abilitySystemManagerClient or AbilitySystemManagerServerEnv.AbilitySystemManagerServer.instance()

    self.buffsToRemoveAfterRoundEnd = {}

    self.teamStartTotalHealth = {}
    self.teamStartTotalHealth[TableConst.enums.Camp.A] = self:calcTotalHealth(TableConst.enums.Camp.A)
    self.teamStartTotalHealth[TableConst.enums.Camp.B] = self:calcTotalHealth(TableConst.enums.Camp.B)
end

function BattleGame:GenerateUid()
    return self._uidGenerator:NewId()
end

function BattleGame:GetTacticByUid(tacticUid)
    for _, hero in pairs(self.heroUniqueIdMap) do
        for _, tactic in ipairs(hero.tactics) do
            if tactic.uid == tacticUid then
                return tactic
            end
        end
    end
    return nil
end

function BattleGame:GetHeroByUid(heroUid)
    for _, hero in pairs(self.heroUniqueIdMap) do
        if hero.uniqueId == heroUid then
            return hero
        end
    end
    return nil
end

function BattleGame:ApplyArmyTypeQualificationModify(tableIndex, ownerHero)
    if not tableIndex then
        return
    end
    local data = GameCommon.TableDataManager:GetArmyTypeQualificationModifyData(tableIndex)
    local targets = {}
    if data.target == TableConst.enums.ArmyTypeQualificationTarget.Self then
        table.insert(targets, ownerHero)
    end
    local attributeType = TableConst.enums.BattleAttributeType.ArmyTypeQualification
    local attributeData = GameCommon.TableDataManager:GetAttributeData(attributeType)
    for _, hero in ipairs(targets) do
        local modifyValue = data.modify_map[hero.armyType]
        if modifyValue then
            if data.fix then
                hero.attributeHolder.attributes[attributeType] = math.clamp(hero.attributeHolder.attributes[attributeType] + modifyValue, attributeData.min_value, attributeData.max_value)
            else
                hero:AddAttributeModifier(attributeType, modifyValue, BattleConstEnv.AttributeModifierType.Fix)
            end
        end
    end
end

function BattleGame:OnHeroDie(camp, heroUniqueId)
    self.aliveHeroIds[camp][heroUniqueId] = nil
    if not self.battleEnd and table.isnilorempty(self.aliveHeroIds[camp]) then
        self.battleEnd = true
        self.winCamp = BattleUtilsEnv.GetOpponentCamp(camp)
    end
end

function BattleGame:CheckBattleEnd()
    return self.battleEnd
end

--function BattleGame:GetCampStatus(checkCamp)
--    local campHeroes = self.heroes[checkCamp]
--    for _, hero in pairs(campHeroes) do
--        if hero:IsAlive() then
--            return true
--        end
--    end
--    return false
--end

function BattleGame:Reset()
    self.round = 0
    self.curActionHeroUid = nil
    for _, hero in pairs(self.heroUniqueIdMap) do
        hero:SetAttribute(TableConst.enums.BattleAttributeType.RecoverableHealth, 0) --进入战斗 伤兵清零
        if hero:IsAlive() then
            hero:Reset()
        end
    end

    -- 阵型战法的兵种适应性加成
    for _, hero in pairs(self.heroUniqueIdMap) do
        for _, tactic in ipairs(hero.tactics) do
            if tactic.tacticType == TableConst.enums.TacticType.ArmyType or tactic.tacticType == TableConst.enums.TacticType.Formation then
                local tacticTableData = GameCommon.TableDataManager:GetTacticData(tactic.tacticId)
                self:ApplyArmyTypeQualificationModify(tacticTableData.army_type_qualification_id, hero)
            end
        end
    end

    self.recorder:InitRecorder()
end

function BattleGame:SetHeroSpeedDirty()
    self.orderedHeroesCache = nil
end

function BattleGame:CalculateHeroSpeed()
    if not table.isnilorempty(self.orderedHeroesCache) then
        return self.orderedHeroesCache
    end

    local orderedHeroes = {}
    for _, hero in pairs(self.heroUniqueIdMap) do
        if hero:IsAlive() then
            table.insert(orderedHeroes, hero)
        end
    end
    table.sort(orderedHeroes, function(hero1, hero2)
        return hero1:GetAttribute(TableConst.enums.BattleAttributeType.Speed) + hero1.speedOrderRandomValue > hero2:GetAttribute(TableConst.enums.BattleAttributeType.Speed) + hero2.speedOrderRandomValue
    end)
    self.orderedHeroesCache = orderedHeroes
    return orderedHeroes
end

function BattleGame:SimulateBattleAndGetRecord()
    self.recorder:Reset()
    self:Reset()
    self:SimulateBattle()
    --xpcall(self._SimulateBattleAndGetRecord, ErrorLog, self)
    return self.recorder:GetRecord()
end

--function BattleGame:_SimulateBattleAndGetRecord()
--    self:Reset()
--    self:SimulateBattle()
--end

function BattleGame:SimulateBattle()
    self:BattleStart()
    while not self:CheckBattleEnd() and self.round < Config.Battle.MaxRound do
        self:SimulateRound()
    end
    self:BattleResult()
end

function BattleGame:SimulateRound()
    self.round = self.round + 1
    self:RoundPrepare()
    if self:CheckBattleEnd() then
        return
    end
    for order, hero in ipairs(self.orderedHeroes) do
        if hero:IsAlive() then
            self.curActionHeroUid = hero.uniqueId
            hero:SimulateHeroTurn(order)
            if self:CheckBattleEnd() then
                return
            end
        end
    end
    self:RoundEnd()
end

----------- battle stages -----------

function BattleGame:BattleStart()
    self.orderedHeroes = self:CalculateHeroSpeed()
    self.recorder:DetailBattleStart(DetailRecordType.BattleStart, self.orderedHeroes)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.Initialize, {})
    -- 布阵阶段顺序：
    self:BattleStartStageMorale()
    self:BattleStartStageArmyType()
    self:BattleStartStageTechnology()
    self:BattleStartStageEquipment()
    self:BattleStartStageHeroIdentity()
    self:BattleStartStageTactics()
end

function BattleGame:BattleStartStageMorale()--士气
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationMorale)
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationMorale, {}, hero.uniqueId)
            end
        end
    end
    local supplies = {}
    for _, hero in pairs(self.heroUniqueIdMap) do
        if hero:IsAlive() then
            supplies[hero.uniqueId] = hero.supply
        end
    end
    self.recorder:DetailBattleStartMorale(supplies)
end

function BattleGame:BattleStartStageArmyType()--兵种适应性
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationArmyType)
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationArmyType, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartArmyType)
end

function BattleGame:BattleStartStageTechnology()--科技加成
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationTechnology)
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationTechnology, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartTechnology)
end

function BattleGame:BattleStartStageEquipment()--装备
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationEquipment)
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationEquipment, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartEquipment)
end

function BattleGame:BattleStartStageHeroIdentity()--身份
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationHeroIdentity)
            end
        end
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationHeroIdentity, {})--身份
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartHeroIdentity)
end

function BattleGame:BattleStartStageTactics()
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationTacticsFormation)
            end
        end
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationTacticsFormation, {})--战法-阵型

    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationTacticsArmyType)
            end
        end
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationTacticsArmyType, {})--战法-兵种

    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                hero:AddDelayBuffs(TableConst.enums.BuffAddTiming.FormationTacticsOther)
            end
        end
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationTacticsOther, {})--战法-其他
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartTactics)
end

function BattleGame:RoundPrepare()
    local recordArgs = {
        Round = self.round,
    }
    self.orderedHeroes = {}
    local orderedHeroes = self:CalculateHeroSpeed()
    local fastNormalSep = 1
    for _, hero in ipairs(orderedHeroes) do
        if hero:CheckPositiveEffect(TableConst.enums.PositiveEffectType.Fast) then
            table.insert(self.orderedHeroes, fastNormalSep, hero)
            fastNormalSep = fastNormalSep + 1
        else
            table.insert(self.orderedHeroes, hero)
        end
    end

    self.recorder:DetailRoundStart(self.round, self.orderedHeroes)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.RoundPrepare, recordArgs, true)
end

function BattleGame:RoundEnd()
    local recordArgs = {
        Round = self.round
    }
    self.recorder:DetailRoundEnd(self.round)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.RoundEnd, recordArgs, true)

    self:RemoveBuffsAfterRoundEnd()
    -- 清理伤害和治疗数据包，目前不会跨回合调用
    self.damagePackageMap = {}
    self.healPackageMap = {}

    self:ModifyHeroesRecoverableHealth(nil, -Config.Battle.RoundEndChangeToRecoverableHealthScale) --大回合结束伤兵转换为死兵
    self.recorder:RecordAllBriefHealthPerRound()
end

function BattleGame:RemoveBuffsAfterRoundEnd()
    -- 有的buff配置了移除时延时到在大回合结束时才移除
    for _, buff in ipairs(self.buffsToRemoveAfterRoundEnd) do
        if buff.hero:IsAlive() then
            buff.hero:RemoveBuff(buff)
        end
    end
    self.buffsToRemoveAfterRoundEnd = {}
end


function BattleGame:ModifyHeroesRecoverableHealth(value, scale)
    for _, hero in ipairs(self.orderedHeroes) do
        local convertCount = hero:ModifyRecoverableHealth(value, scale)
        self.recorder:DetailRecoverableToDeath(hero.uniqueId, convertCount)
    end
end

function BattleGame:BattleResult()
    self.recorder:RecordAllBriefHealthPerRound()
    self:ModifyHeroesRecoverableHealth(nil, -Config.Battle.BattleEndChangeToRecoverableHealthScale) --战斗结束伤兵转换为死兵
    self.recorder:DetailResult(self.winCamp)
    self.success = true
    self.recorder:OnBattleFinished()
end

function BattleGame:PushTacticPackage(tacticPackage)
    local index = self:GenerateUid()
    tacticPackage.tacticPackageId = index
    self.tacticPackageMap[index] = tacticPackage
    return index
end

function BattleGame:PushPlainAttackPackage(plainAttackPackage)
    local index = self:GenerateUid()
    plainAttackPackage.plainAttackPackageId = index
    self.plainAttackPackageMap[index] = plainAttackPackage
    return index
end

function BattleGame:PushDamagePackage(damagePackage)
    local index = self:GenerateUid()
    damagePackage.damagePackageId = index
    self.damagePackageMap[index] = damagePackage
    return index
end

function BattleGame:SubmitDamage(graphData, damagePackage)
    local index = self:PushDamagePackage(damagePackage)
    local randomNumbers = {math.random(), math.random()} -- 用几个生成几个
    local caster = self.heroUniqueIdMap[damagePackage.casterId]
    local isInevitableDamage = damagePackage.additionalModification.isInevitableDamage or caster:CheckPositiveEffect(TableConst.enums.PositiveEffectType.Inevitable)

    local commonArgs = {
        Index = index,
        InputTacticId = damagePackage.tacticId,
        InputBuffId = damagePackage.buffId,
        TargetId = damagePackage.targetId,
        InputSourceTacticId = damagePackage.sourceTacticId,
        InputSourceTacticHeroId = damagePackage.sourceTacticHeroId,
        EventSourceId = damagePackage.casterId,
        DamageRange = damagePackage.damageRange,
        DamageType = damagePackage.damageType,
        IsInevitableDamage = isInevitableDamage,
    }

    -- NOTICE 先计算伤害结果，可能跟实际伤害时的结果有差异，因为伤害前事件可能会修改属性导致再计算时伤害结果变化
    --        跟策划商量后，这里由策划来保证，策划需要合理配置伤害前事件的优先级来规避二者结果差异带来问题
    local damageResult = self:GetDamageResult(damagePackage, randomNumbers)
    local needFireEvent = damagePackage.damageType ~= TableConst.enums.BattleDamageType.Share
    local handled, target

    if needFireEvent then
        -- 伤害前事件
        local beforeDamageArgs = table.clone(commonArgs)
        beforeDamageArgs.DamageValue = damageResult.damageValue
        beforeDamageArgs.IsCrit = damageResult.crit
        handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeDamage, beforeDamageArgs, damagePackage.casterId)
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBeDamaged, beforeDamageArgs, damagePackage.targetId)
    end
    target = self.heroUniqueIdMap[damagePackage.targetId]

    -- 闪避
    local targetDodgeRate = target:GetAttribute(TableConst.enums.BattleAttributeType.DodgeRate)
    local dodgeResult
    if not isInevitableDamage and targetDodgeRate > 0 then --只有闪避率大于0的角色会触发闪避前后事件
        local beforeDodgeArgs = table.clone(commonArgs)
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeDodge, beforeDodgeArgs, damagePackage.targetId)
        target = self.heroUniqueIdMap[damagePackage.targetId]
        targetDodgeRate = target:GetAttribute(TableConst.enums.BattleAttributeType.DodgeRate) --闪避前事件中可能修改闪避率
        dodgeResult = targetDodgeRate > math.random()
        if dodgeResult then
            local afterDodgeSuccessArgs = table.clone(commonArgs)
            afterDodgeSuccessArgs.DamageValue = damageResult.damageValue
            afterDodgeSuccessArgs.IsCrit = damageResult.crit
            self.recorder:DetailDodge(target.uniqueId, math.floor(damageResult.damageValue), damageResult.debugData)
            self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterDodgeSuccess, afterDodgeSuccessArgs, damagePackage.targetId)
            return { casterId = damagePackage.casterId, targetId = damagePackage.targetId, accept = false }
        end
    end

    -- 致命伤害
    damagePackage = self.damagePackageMap[index]
    damageResult = self:GetDamageResult(damagePackage, randomNumbers)
    if target:GetAttribute(TableConst.enums.BattleAttributeType.Health) <= damageResult.damageValue then
        local beforeFatalDamageArgs = table.clone(commonArgs)
        beforeFatalDamageArgs.DamageValue = damageResult.damageValue
        beforeFatalDamageArgs.IsCrit = damageResult.crit
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeFatalDamage, beforeFatalDamageArgs, damagePackage.targetId)
    end

    damagePackage = self.damagePackageMap[index]
    -- 伤害无效（免疫等）
    if not isInevitableDamage and damagePackage.disable then
        return { damagePackageId = index, casterId = damagePackage.casterId, targetId = damagePackage.targetId, accept = false }
    end

    caster = self.heroUniqueIdMap[damagePackage.casterId]
    damageResult = self:GetDamageResult(damagePackage, randomNumbers)

    --闪避失败后
    local finalDamage = damageResult.damageValue or 0
    if dodgeResult == false then
        local afterDodgeFailArgs = table.clone(commonArgs)
        afterDodgeFailArgs.DamageValue = damageResult.damageValue
        afterDodgeFailArgs.IsCrit = damageResult.crit
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterDodgeFail, afterDodgeFailArgs, damagePackage.targetId)
    end

    -- 实际扣血
    target = self.heroUniqueIdMap[damagePackage.targetId]
    local rdv, recoverable = target:ModifyHealth(graphData, -finalDamage, damagePackage.recoverableRate)
    local realDamageValue = -rdv
    if realDamageValue < 0 then
        ErrorLog("realDamageValue < 0. %s %s", damagePackage.tacticId, realDamageValue)
    end
    --assert(realDamageValue >= 0)
    damageResult.damageValue = realDamageValue
    self.recorder:DetailDamage(caster.uniqueId, target.uniqueId, damageResult.damageType, damageResult.damageValue, recoverable, damageResult.crit, damagePackage.tacticId, damageResult.debugData)
    -- 死亡
    if target:GetAttribute(TableConst.enums.BattleAttributeType.Health) <= 0 then
        target:Die()
    end

    local result = table.clone(damagePackage)
    if needFireEvent then
        -- 伤害后事件
        local afterDamageArgs = table.clone(commonArgs)
        afterDamageArgs.DamageValue = damageResult.damageValue
        afterDamageArgs.IsCrit = damageResult.crit
        handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterDamage, afterDamageArgs, damagePackage.casterId)
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBeDamaged, afterDamageArgs, damagePackage.targetId)
    end

    -- 吸血（倒戈、攻心）
    if caster then
        local vampHealth
        local attributeType
        local isAttackVamp, isIntelligenceVamp = false, false
        if damagePackage.damageType == TableConst.enums.BattleDamageType.Attack then
            attributeType = TableConst.enums.BattleAttributeType.AttackVamp
            isAttackVamp = true
        elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Intelligence then
            attributeType = TableConst.enums.BattleAttributeType.IntelligenceVamp
            isIntelligenceVamp = true
        end
        if attributeType then
            local vamp = caster:GetAttribute(attributeType)
            if vamp > 0 then
                vampHealth = realDamageValue * vamp
                local healPackage = {
                    heal = nil,
                    healFactor = 1,
                    globalFactor = 1,
                    casterId = caster.uniqueId,
                    targetId = caster.uniqueId,
                    tacticId = damagePackage.tacticId,
                    additionalModification = {
                        isFixedHeal = true,
                        fixedHealValue = vampHealth
                    },
                    isAttackVamp = isAttackVamp,
                    isIntelligenceVamp = isIntelligenceVamp,
                    accept = false
                }
                local vampResult = self:SubmitHeal(graphData, healPackage)
                local attributeModifiers = caster:GetAttributeModifier(attributeType)
                for _, attributeModifier in pairs(attributeModifiers) do
                    if attributeModifier.modifyValue > 0 then
                        local sourceTacticId = attributeModifier.extraInfo.sourceTacticId
                        local sourceTacticHeroId = attributeModifier.extraInfo.sourceTacticHeroId
                        local sourceTacticHero = self.heroUniqueIdMap[sourceTacticHeroId]
                        self.recorder:TacticStatistic(sourceTacticHero.camp, sourceTacticHeroId, sourceTacticId, 0, 0, vampResult.heal * attributeModifier.modifyValue / vamp)
                        self.recorder:RecordBrief(sourceTacticHero.camp, sourceTacticHeroId, sourceTacticId, BattleConstEnv.BriefRecordKey.Heal, vampResult.heal * attributeModifier.modifyValue / vamp)
                    end
                end
            end
        end
    end

    -- 记录并返还结果
    target = self.heroUniqueIdMap[damagePackage.targetId]
    result.health = target:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    result.accept = true
    result.recoverable = recoverable
    return result
end

function BattleGame:GetDamageResult(damagePackage, randomNumbers)
    local damageResult
    local caster = self.heroUniqueIdMap[damagePackage.casterId]
    local target = self.heroUniqueIdMap[damagePackage.targetId]
    local alwaysCrit = damagePackage.alwaysCrit
    local neverCrit = damagePackage.neverCrit
    local tacticId = damagePackage.tacticId
    local tacticData = GameCommon.TableDataManager:GetTacticData(tacticId)
    local finalDamageCoefficient
    if damagePackage.additionalModification.isInevitableDamage then --必中忽略格挡
        finalDamageCoefficient = 1
    else
        finalDamageCoefficient = 1 - target:GetAttribute(TableConst.enums.BattleAttributeType.FinalDamageCoefficient)
    end
    if damagePackage.additionalModification.isFixedDamage then
        damageResult = {
            damageType = damagePackage.damageType,
            casterId = caster.uniqueId,
            damageValue = damagePackage.additionalModification.fixedDamageValue * damagePackage.globalFactor,
            crit = false
        }
    elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Attack then
        damageResult = BattleFormulasEnv.AttackDamage(randomNumbers, caster, target, damagePackage.damageFactor, alwaysCrit, neverCrit, damagePackage.additionalModification, damagePackage.overwriteArgsList, tacticData.tactic_type)
        damageResult.damageValue = damageResult.damageValue * damagePackage.globalFactor * finalDamageCoefficient
    elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Intelligence then
        damageResult = BattleFormulasEnv.IntelligenceDamage(randomNumbers, caster, target, damagePackage.damageFactor, alwaysCrit, neverCrit, damagePackage.additionalModification, damagePackage.overwriteArgsList, tacticData.tactic_type)
        damageResult.damageValue = damageResult.damageValue * damagePackage.globalFactor * finalDamageCoefficient
    elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Share then
        damageResult = {}
        damageResult.damageValue = damagePackage.additionalModification.fixedDamageValue * damagePackage.globalFactor
    end
    damagePackage.damage = damageResult
    return damageResult
end

function BattleGame:PushHealPackage(healPackage)
    local index = self:GenerateUid()
    healPackage.healPackageId = index
    self.healPackageMap[index] = healPackage
    return index
end

function BattleGame:SubmitHeal(graphData, healPackage)
    local index = self:PushHealPackage(healPackage)
    local target = self.heroUniqueIdMap[healPackage.targetId]
    local randomNumbers = {math.random()} -- 用几个生成几个
    local commonArgs = {
        Index = index,
        InputTacticId = healPackage.tacticId,
        InputBuffId = healPackage.buffId,
        TargetId = healPackage.targetId,
        EventSourceId = healPackage.casterId,
        InputSourceTacticId = healPackage.sourceTacticId,
        InputSourceTacticHeroId = healPackage.sourceTacticHeroId,
    }

    -- 治疗前事件
    local beforeArgs = table.clone(commonArgs)
    local handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeHeal, beforeArgs, healPackage.casterId)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBeHealed, beforeArgs, healPackage.targetId)

    healPackage = self.healPackageMap[index]

    -- 治疗无效
    if healPackage.disable then
        return { healPackageId = index, casterId = healPackage.casterId, targetId = healPackage.targetId, accept = false }
    end

    -- 计算治疗值
    local caster = self.heroUniqueIdMap[healPackage.casterId]
    local healResult
    if healPackage.additionalModification.isFixedHeal then
        healResult = {
            casterId = caster.uniqueId,
            heal = healPackage.additionalModification.fixedHealValue,
        }
    else
        healResult = BattleFormulasEnv.Heal(randomNumbers, caster, target, healPackage.healFactor, healPackage.overwriteArgsList)
    end
    healPackage.heal = healResult.heal
    healPackage.heal = healPackage.heal * healPackage.globalFactor
    local originalHeal = healPackage.heal

    -- 实际治疗
    local rdv, recoverable = target:ModifyHealth(graphData, originalHeal)
    local overHeal = originalHeal - rdv
    healPackage.heal = rdv
    if healPackage.isAttackVamp then
        self.recorder:DetailAttackVamp(healPackage.casterId, healPackage.heal, healResult.debugData)
    elseif healPackage.isIntelligenceVamp then
        self.recorder:DetailIntelligenceVamp(healPackage.casterId, healPackage.heal, healResult.debugData)
    else
        self.recorder:DetailHeal(healPackage.casterId, healPackage.targetId, healPackage.heal, healResult.debugData)
    end
    if target:GetAttribute(TableConst.enums.BattleAttributeType.Health) <= 0 then
        target:Die()
    end

    -- 治疗后事件
    local result = table.clone(healPackage)
    local afterArgs = table.clone(commonArgs)
    afterArgs.HealValue = healPackage.heal
    afterArgs.OriginalHealValue = originalHeal
    afterArgs.OverHealValue = overHeal
    handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterHeal, afterArgs, healPackage.casterId)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBeHealed, afterArgs, healPackage.targetId)

    -- 记录并返还结果
    result.health = target:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    result.accept = true
    result.recoverable = recoverable
    result.originalHealValue = originalHeal
    result.overHealValue = overHeal
    return result
end

function BattleGame:GetTeamHealth(camp)
    local heroes = self.heroes[camp]
    if not heroes then
        return 0
    end

    local health = 0
    for _, hero in pairs(heroes) do
        health = health + hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    end

    return health
end

function BattleGame:GetTeamHeroesHealth(camp)
    local heroesHealth = {}

    local heroes = self.heroes[camp]
    if not heroes then
        return heroesHealth
    end

    for idx, hero in pairs(heroes) do
        heroesHealth[idx] = hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    end

    return heroesHealth
end

function BattleGame:GetTeamRecoverableHealth(camp)
    local heroesRecoverableHealth = {}

    local heroes = self.heroes[camp]
    if not heroes then
        return heroesRecoverableHealth
    end

    for idx, hero in pairs(heroes) do
        heroesRecoverableHealth[idx] = hero:GetAttribute(TableConst.enums.BattleAttributeType.RecoverableHealth)
    end

    return heroesRecoverableHealth
end

function BattleGame:calcTotalHealth(camp)
    local totalHealth = 0
    local heroes = self.heroes[camp]
    if not heroes then
        return totalHealth
    end

    for _, hero in pairs(heroes) do
        totalHealth = totalHealth + hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    end

    return totalHealth
end

function BattleGame:GetBattleResult()
    if self.success then
        local heroesHealth = {
            [TableConst.enums.Camp.A] = self:GetTeamHeroesHealth(TableConst.enums.Camp.A),
            [TableConst.enums.Camp.B] = self:GetTeamHeroesHealth(TableConst.enums.Camp.B),
        }
        local teamConsumeHealth = {
            [TableConst.enums.Camp.A] = self.teamStartTotalHealth[TableConst.enums.Camp.A] - self:calcTotalHealth(TableConst.enums.Camp.A),
            [TableConst.enums.Camp.B] = self.teamStartTotalHealth[TableConst.enums.Camp.B] - self:calcTotalHealth(TableConst.enums.Camp.B),
        }
        local heroesRecoverableHealth = {
            [TableConst.enums.Camp.A] = self:GetTeamRecoverableHealth(TableConst.enums.Camp.A),
            [TableConst.enums.Camp.B] = self:GetTeamRecoverableHealth(TableConst.enums.Camp.B),
        }
        return { success = self.success, winCamp = self.winCamp, heroesHealth = heroesHealth, teamConsumeHealth = teamConsumeHealth, heroesRecoverableHealth = heroesRecoverableHealth }
    else
        return { success = false, winCamp = TableConst.enums.Camp.None, heroesHealth = {}, teamConsumeHealth = {} }
    end
end