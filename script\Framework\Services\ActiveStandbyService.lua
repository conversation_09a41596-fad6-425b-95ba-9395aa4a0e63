---@type GlobalEntityMgrEnv
local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")
---@type GlobalDataMgrEnv
local GlobalDataMgr = xrequire(EZFPath .. ".Utils.GlobalDataMgr")
---@type ContainerSingletonCompEnv
local ContainerSingletonComp = xrequire(EZFPath .. ".Entities.Components.ContainerSingletonComp")
---@type ServiceEnv
local Service = xrequire(EZFPath .. ".Services.Service")
---@type GlobalDataAckCompEnv
local GlobalDataAckComp = xrequire(EZFPath .. ".Entities.Components.GlobalDataAckComp")


function genActiveStandbyService(name, base)

-- GlobalEntity的主备模式组件，继承ContainerSingletonComp，处理主备相关业务
---@generic T
---@class ActiveStandbyService<T>: Service, ContainerSingletonComp<T>, GlobalDataAckComp
local ActiveStandbyService = DefineEntity(name, {base}, {
    ContainerSingletonComp.ContainerSingletonComp,
    GlobalDataAckComp.GlobalDataAckComp,
})
ActiveStandbyService.isActiveStandby = true

---@param ready boolean @是否已完成初始化（可以开始正式服务了）
function ActiveStandbyService:regist(ready)
    if ready and self:blockReady() then
        ready = false
    end
    if self.recoverRpc then
        self.instanceData.hasRecoverRpc = true
    end
    self.instanceData.activePriority = math.random(GlobalEntityMgr.GlobalEntityMgr.ACTIVE_PRIORITY_RANGE)
    Service.Service.regist(self, ready)
end

function ActiveStandbyService:ready()
    if self:blockReady() then
        return
    end
    self:allowReady()
end

function ActiveStandbyService:blockReady()
    assert(not self.__prepare_ready, "%s set ready dup", self.__info_str)
    self.__prepare_ready = true
    if GlobalEntityMgr.instance:isStandby(self.__cname) then
        -- 作为备实例，需要等同步数据之后再设置ready
        -- 这里自动处理下，以减少逻辑上的分支
        return not self.__standby_synced
    elseif EZGlobal.ClusterReady and self.recoverRpc and GlobalEntityMgr.instance:isActive(self.__cname) then
        self:startRecover()
        return true
    end
    return false
end

function ActiveStandbyService:allowReady()
    assert(self.__prepare_ready, "%s not ready yet", self.__info_str)
    Service.Service.ready(self)
end

---@param name string
---@param info GlobalEntityInfo
function ActiveStandbyService:onGlobalEntityRegistered(name, info)
    if name == self.__cname then
        if GlobalEntityMgr.instance:canCalcActive(name, true) then
            self:convertToActive()
        elseif GlobalEntityMgr.instance:isActive(self.__cname) then
            if info.mailbox:getEntityId() == self.id and EZGlobal.ClusterReady and self.recoverRpc and self.__prepare_ready then
                self:startRecover()
            elseif info.mailbox:getEntityId() ~= self.id and self:hasReady() then
                local eid = info.mailbox:getEntityId()
                if GlobalEntityMgr.instance:isStandby(self.__cname, eid) then
                    self:logWarn("new standby %s(%s)", self.__cname, eid)
                    self:registerRecoverSync(eid)
                end
            end
        elseif info.mailbox:getEntityId() == self.id and (GlobalEntityMgr.instance:isStandby(name) and not GlobalEntityMgr.instance:missActive(name)) then
            self:onRetainStandby()
        end
    end
end

---@param name string
---@param info GlobalEntityInfo
function ActiveStandbyService:onGlobalEntityRemoved(name, info)
    if name == self.__cname then
        local eid = info.mailbox:getEntityId()
        if GlobalEntityMgr.instance:isStandby(self.__cname, eid) and GlobalEntityMgr.instance:isActive(self.__cname) and self:hasReady() then
            self:logWarn("del standby %s(%s)", self.__cname, eid)
            self:unregisterRecoverSync(eid)
        end
    end
end

---@param name string
---@param info GlobalEntityInfo
function ActiveStandbyService:onGlobalEntityReady(name, info)
    if name == self.__cname and GlobalEntityMgr.instance:isActive(self.__cname) and info.mailbox:getEntityId() == self.id then
        self:onActiveReady()
    end
end

---@param nodeId NodeId
function ActiveStandbyService:onDelNode(nodeId)
    assert(EZE.containerIdToNodeId(containerId) ~= nodeId)
    if GlobalEntityMgr.instance:canCalcActive(self.__cname, false) then
        self:convertToActive()
    end
end

function ActiveStandbyService:onActiveReady()
    self:logWarn("onActiveReady")
    local entry = GlobalEntityMgr.instance:getGlobalEntityEntry(self.__cname)
    -- 现有的standby开启同步
    for _, v in pairs(entry.entities) do
        if v.mailbox:getEntityId() ~= self.id then
            self:logWarn("exist standby %s(%s)", self.__cname, v.mailbox:getEntityId())
            self:registerRecoverSync(v.mailbox:getEntityId())
        end
    end
end

function ActiveStandbyService:onActive()
    self:logWarn("onActive but not ready yet")
end

function ActiveStandbyService:onRetainStandby()
    self:logWarn("onRetainStandby")
end

function ActiveStandbyService:becomeActive()
    if EZGlobal.ClusterReady and self.recoverRpc and self.__prepare_ready then
        return self:startRecover()
    end
    if self.__prepare_ready and not self:hasReadyLocal() then
        return self:allowReady()
    end
    if self:hasReady() then
        return self:onActiveReady()
    else
        return self:onActive()
    end
end

-- 初始化/容灾发生时，尝试转换总入口
function ActiveStandbyService:convertToActive()
    if not GlobalEntityMgr.instance:missActive(self.__cname) or self:getGlobalDataCb(self.__cname, "convertToActive") then
        return
    end
    local active = GlobalEntityMgr.instance:calcActiveByPriority(self.__cname)
    self:logWarn("convert(%s), expected active(%s)", self.id == active, active)
    if self.id ~= active then
        self:onRetainStandby()
        return
    end
    self:registerGlobalDataCb(self.__cname, "convertToActive", function (val, prevVal)
        if GlobalEntityMgr.instance:isActive(self.__cname) then
            -- 竞争成功
            if self:hasReadyLocal() then
                -- 已经ready，说明本地已有数据，直接转化为主实例
                if self.__dbid_cache then
                    self:ensureTerm(self.__dbid_cache, EntCb("ensureTermCb"))
                end
                self:becomeActive()
            elseif self.__dbid_cache then
                -- 从db创建
                GlobalEntityMgr.instance:create(self.__cname, 0, {}, nil, self.__dbid_cache)
                self:destroy()
            else
                self:becomeActive()
            end
        else
            -- 竞争时数据发生变更，可能导致失败
            self:logWarn("unexpected active(%s)", GlobalEntityMgr.instance:getActiveCid(self.__cname))
            self:onRetainStandby()
        end
        return true
    end)
    GlobalDataMgr.instance:push(self.__cname, GlobalEntityMgr.instance:getActiveStandbyUnique(), GlobalDataType.CAS)
end

---@param userdata nil
---@param succ boolean
function ActiveStandbyService:ensureTermCb(userdata, succ)
    assert(succ, string.format("%s ensureTerm failed", self.__info_str))
    self:saveWithDBID(self.__dbid_cache)
end

function ActiveStandbyService:onSyncProperties()
    self.__standby_synced = true
    if self.__prepare_ready then
        self:allowReady()
    end
end

function ActiveStandbyService:finishRecover()
    if not self:hasReadyLocal() then
        self:allowReady()
    elseif self:hasReady() and GlobalEntityMgr.instance:isActive(self.__cname) then
        self:onActiveReady()
    end
end

return ActiveStandbyService
end

ActiveStandbyService = genActiveStandbyService("ActiveStandbyService", Service.Service)

-- for emmylua
---@class ActiveStandbyServiceEnv
local ActiveStandbyServiceEnv = {ActiveStandbyService=ActiveStandbyService, genActiveStandbyService=genActiveStandbyService}
return ActiveStandbyServiceEnv
