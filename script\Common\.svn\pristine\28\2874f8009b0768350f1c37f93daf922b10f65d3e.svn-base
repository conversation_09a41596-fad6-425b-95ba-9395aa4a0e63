﻿local ManagerBaseEnv = xrequire("Common.ManagerBase")

local AdministrativeHierarchy =  TableConst.enums["slg.AdministrativeHierarchy"]   ---@type slg.AdministrativeHierarchy

---@class (partial) TableDataManager : ManagerBase
TableDataManager = DefineClass("TableDataManager", ManagerBaseEnv.ManagerBase)

function TableDataManager:ctor()
    self.tblCache = {}
    self.multiIndicesTblCache = {}
    self.postProcessors = {}
end

function TableDataManager:GetRow(tableName, row)
    local tbl = self.tblCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.tblCache[tableName] = tbl
    end
    return tbl.__xdata[row]
end

function TableDataManager:CloneRow(tableName, row)
    local tbl = self.tblCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.tblCache[tableName] = tbl
    end
    return table.deepclone(tbl.__xdata[row])
end

function TableDataManager:GetRows(tableName, predicate)
    local tbl = self.tblCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.tblCache[tableName] = tbl
    end
    local records = {}
    for _, record in ipairs(tbl.__xdata) do
        if predicate(record) then
            records[#records + 1] = record
        end
    end
    return records
end

function TableDataManager:loadMultiIndicesTable(tableName)
    for _, tableDef in ipairs(TableConst.tables) do
        if tableDef.file == tableName then
            local indices = string.split(tableDef.index, "+")
            local indicesNum = #indices
            assert(indicesNum > 1, string.format("Table %s has no multi indices", tableName))

            local tbl = xrequire("Table.Data." .. tableDef.file)
            local tblCache = {}
            for _, data in ipairs(tbl.__xdata) do
                local curCache = tblCache
                for i = 1, indicesNum - 1 do 
                    local index = indices[i]
                    if not curCache[data[index]] then
                        curCache[data[index]] = {}
                    end
                    curCache = curCache[data[index]]
                end
                curCache[data[indices[#indices]]] = data
            end
            self.multiIndicesTblCache[tableName] = tblCache
            return tblCache
        end
    end
    error(string.format("Table %s is not exists.", tableName))
end

function TableDataManager:GetRowByMultiIndices(tableName, ...)
    local tblCache = self.multiIndicesTblCache[tableName]
    if not tblCache then
        tblCache = self:loadMultiIndicesTable(tableName)
    end

    local curCache = tblCache
    for i = 1, select("#", ...) do
        curCache = curCache[select(i, ...)]
        if not curCache then
            return nil
        end
    end
    return curCache
end

function TableDataManager:GetTable(tableName)
    local tbl = self.tblCache[tableName]
    if not tbl then
        tbl = xrequire("Table.Data." .. tableName)
        self.tblCache[tableName] = tbl
    end
    return tbl.__xdata
end

function TableDataManager.FindTableByValueType(valueType)
    for _, tuple in ipairs(TableConst.tables) do
        if tuple.value_type == valueType then
            return tuple.file
        end
    end
end

function TableDataManager:LoadConfigs()
    local config = {
        Common = {},
        Battle = {},
        Slg = {},
        Ret = {},
    }

    for k, v in pairs(self:GetTable("common_tbcommonconfig")) do
        config.Common[k] = v
    end
    for k, v in pairs(self:GetTable("common_tbbattleconfig")) do
        config.Battle[k] = v
    end
    for k, v in pairs(self:GetTable("common_tbslgconfig")) do
        config.Slg[k] = v
    end
    for k, v in pairs(self:GetTable("common_tbretcode")) do
        config.Ret[v["name"]] = k
    end

    ---@class Config
    ---@field Common common.TableCommonConfig
    ---@field Battle common.TableBattleConfig
    ---@field Slg common.TableSlgConfig
    ---@field Ret table
    Config = setmetatable({}, {
        __index = config,
        __newindex = function (t,k,v)
            error("attempt to update a read-only table", 2)
        end
    })

    DeclareAndSetGlobal("Config", Config)
end

function TableDataManager:SetLevelId(levelId)
    self.worldLevelId = levelId
    self.season_name = self:GetMapLevel(levelId).season
    self.season = self:GetMapLevel(levelId).season_id
    assert(self.season_name)
    self.season_tbname = "season_tb" .. self.season_name  -- 赛季表名
    self.season_consttbname = "season_tb" .. self.season_name  .. "const"
    self.season_countlistname = "season_tb" .. self.season_name  .. "countlist"
    self.season_clearrewardname = "season_tb" .. self.season_name  .. "reward"
    self.season_gameplayname = "season_tb" .. self.season_name  .. "gameplay"
    self.season_gameplayinfoname = "season_tb" .. self.season_name  .. "gameplayinfo"
    -- TODO:后面这些是非通用的部分
    self.season_identityinfo = "season_tb" .. self.season_name  .. "identityinfo"
    self.season_campinfo = "season_tb" .. self.season_name  .. "campinfo"
    self.season_fullscreentoast = "season_tb" .. self.season_name  .. "fullscreentoast"
end

function TableDataManager:LoadPostProcessors()
    xrequire("Common.TableDataPostProcessor.HomeTechPostProcessor")
end

function TableDataManager:RegPostProcessor(name, postProcessor)
    if not XReloading then
        assert(self.postProcessors[name] == nil, "PostProcessor " .. name .. " already exists")
    end
    InfoLog("TableDataManager:RegPostProcessor %s", name)
    self.postProcessors[name] = postProcessor
    self[name .. "PP"] = postProcessor
end

function TableDataManager:GetPPRow(name, ...)
    return self.postProcessors[name]:GetRow(...)
end

function TableDataManager:GetPPTable(name)
    return self.postProcessors[name]:GetTable()
end

function TableDataManager:OnReloadData(...)
    -- 先简单粗暴处理
    table.clear(self.multiIndicesTblCache)
    for name, postProcessor in pairs(self.postProcessors) do
        postProcessor:Reload()
    end
end

----------------------------------------------------------------------------------------------------------------------------
--- 表格读取
----------------------------------------------------------------------------------------------------------------------------

function TableDataManager:GetEffectData(configId)
    return self:GetRow("common_tbeffect", configId)
end

function TableDataManager:GetTextData(configId)
    return self:GetRow("common_tbtext", configId)
end

function TableDataManager:GetGeneralAbilityData(configId)
    return self:GetRow("heros_tbgeneralability", configId)
end

function TableDataManager:GetAllTacticData()
    local allData = self:GetTable("battle_tbbattletactic")
    local result = {}
    for id, data in pairs(allData) do
        if self.season >= data.unlock_season then
            result[id] = data
        end
    end
    return result
end

function TableDataManager:GetTacticData(configId)
    return self:GetRow("battle_tbbattletactic", configId)
end

function TableDataManager:GetTacticConstData(configId)
    if not configId then
        return self:GetTable("battle_tbtacticconst")
    end
    return self:GetRow("battle_tbtacticconst", configId)
end

function TableDataManager:GetTaskConstData(configId)
    if not configId then
        return self:GetTable("common_tbtaskconst")
    end
    return self:GetRow("common_tbtaskconst", configId)
end

---@return common.TableTask?
function TableDataManager:GetTaskData(configId)
    return self:GetRow("common_tbtask", configId)
end

function TableDataManager:GetTaskChapterInterludeData(configId)
    return self:GetRow("common_tbtaskchapterinterlude", configId)
end

---@return battle.TableBattleBuff?
function TableDataManager:GetBuffData(configId)
    return self:GetRow("battle_tbbattlebuff", configId)
end

function TableDataManager:GetBuffGroupData(configId)
    return self:GetRow("battle_tbbattlebuffgroup", configId)
end

---@return battle.TableBattleBuffValid?
function TableDataManager:GetValidBattleBuffConfig(battleType)
    return self:GetRow("battle_tbbattlebuffvalid", battleType)
end

function TableDataManager:GetAttributeData(configId)
    return self:GetRow("battle_tbbattleattribute", configId)
end

function TableDataManager:GetBattleStageData(configId)
    return self:GetRow("battle_tbbattlestage", configId)
end

function TableDataManager:GetBattleStageNpcTeamData(configId)
    return self:GetRow("battle_tbbattlestagenpcteam", configId)
end

function TableDataManager:GetBattleStageNpcData(configId)
    return self:GetRow("battle_tbbattlestagenpc", configId)
end

function TableDataManager:GetNpcDifficultyData(configId)
    return self:GetRow("battle_tbnpcdifficulty", configId)
end

function TableDataManager:GetAnimTransitionData(configId)
    return self:GetRow("battle_tbbattleanimationtransition", configId)
end

function TableDataManager:GetAnimLengthConst(configId)
    return self:GetRow("common_tbanimlength", configId)
end 
-- function TableDataManager:GetCurrencyData(configId)
--     return self:GetRow("common_tbcurrency", configId)
-- end

function TableDataManager:GetHomeConstData(configId)
    if not configId then
        return self:GetTable("common_tbhomebuildingconst")
    end
    return self:GetRow("common_tbhomebuildingconst", configId)
end

function TableDataManager:GetHomeBuildingTypeData(config)
    return self:GetRow("common_tbhomebuildingtype", config)
end

function TableDataManager:GetHomeBuildFuncBtnData(config)
    return self:GetRow("common_tbhomebuildfuncbtn", config)
end

function TableDataManager:GetAllHomeBuildingTypeData()
    return self:GetTable("common_tbhomebuildingtype")
end

function TableDataManager:GetHomeBuildingData(id, level)
    return self:GetRowByMultiIndices("common_tbhomebuilding", id, level)
end

function TableDataManager:GetAllHomeBuildingData()
    return self:GetTable("common_tbhomebuilding")
end

function TableDataManager:GetHomeTechData(id)
    if not id then
        return self:GetTable("common_tbhometech")
    end
    return self:GetRow("common_tbhometech", id)
end


function TableDataManager:GetLevelData(configId)
    return self:GetRow("levels_tblevel", configId)
end

function TableDataManager:GetLevelGoalData(configId)
    return self:GetRow("levels_tbgoal", configId)
end

function TableDataManager:GetCellElementData(configId)
    return self:GetRow("levels_tbcellelement", configId)
end

function TableDataManager:GetLevelElementData(configId)
    return self:GetRow("levels_tbelement", configId)
end

function TableDataManager:GetLevelElemtBonusData(configId)
    return self:GetRow("levels_tbelementbonus", configId)
end


function TableDataManager:GetHeroDynastyData(configId)
    return self:GetRow("battle_tbherodynasty", configId)
end
function TableDataManager:GetAllHeroDynastyData()
    return self:GetTable("battle_tbherodynasty")
end

function TableDataManager:GetRarityRankData(configId)
    return self:GetRow("common_tbrarityrank", configId)
end

function TableDataManager:GetHeroSuitRankData(configId)
    if not configId then
        return self:GetTable("common_tbherosuitrank")
    end
    return self:GetRow("common_tbherosuitrank", configId)
end

function TableDataManager:GetAllTemplateHeroData()
    local allData = self:GetTable("battle_tbtemplatehero")
    local result = {}
    for id, data in pairs(allData) do
        if self.season >= data.unlock_season then
            result[id] = data
        end
    end
    return result
end

function TableDataManager:GetTemplateHeroData(configId)
    return self:GetRow("battle_tbtemplatehero", configId)
end

function TableDataManager:GetHeroConstData(key)
    return self:GetRow("battle_tbheroconst", key)
end

---@return common.TableHeroExp?
function TableDataManager:GetHeroExpData(level)
    return self:GetRow("common_tbheroexp", level)
end

---@return battle.TableNPCPool
function TableDataManager:GetBattleNpcPoolData(configId)
    return self:GetRow("battle_tbnpcpool", configId)
end

---@return battle.TableNPCTeam?
function TableDataManager:GetBattleNpcTeamData(configId)
    return self:GetRow("battle_tbnpcteam", configId)
end

function TableDataManager:GetHeroArmyTypeData(configId)
    if not configId then
        return self:GetTable("battle_tbheroarmytype")
    end
    return self:GetRow("battle_tbheroarmytype", configId)
end

function TableDataManager:GetStorySubTitleData(configId)
    return self:GetRow("StorySubTitleData", configId)
end

function TableDataManager:GetStoryItemRewardData(configId)
    return self:GetRow("StoryItemRewardData", configId)
end

function TableDataManager:GetHeroElementalData(elementalType)
    return self:GetRow("battle_tbheroelemental", elementalType)
end

function TableDataManager:GetBattleRecordText(configId)
    return self:GetRow("battle_tbbattlerecordtext", configId)
end

function TableDataManager:GetBattleRecordCrowdControl(configId)
    return self:GetRow("battle_tbbattlerecordcrowdcontrol", configId)
end

function TableDataManager:GetCurrencyDatas()
    return self:GetTable("common_tbcurrency")
end

---@return common.TableCurrency?
function TableDataManager:GetCurrencyData(tp)
    return self:GetRow("common_tbcurrency", tp)
end

--
function TableDataManager:GetHeroAttrData(heroId)
    return self:GetRow("heros_tbhero", heroId)
end

function TableDataManager:GetHeroBattleAttrData(heroId)
    return self:GetRow("heros_tbbattleattr", heroId)
end

function TableDataManager:GetBattleFormulasParameterTable()
    return self:GetTable("battle_tbbattleformulas")
end

function TableDataManager:GetHeroAttrTypeData(configId)
    return self:GetRow("common_tbheroattrtype", configId)
end

function TableDataManager:GetHeroRoleTypeData(configId)
    return self:GetRow("common_tbheroroletype", configId)
end

---@return slg.TableMapElement?
function TableDataManager:GetMapElementData(id)
    local tblname = "slg_tbmapelement"
    local data = self:GetRow("slg_tbmapelement", id)
    if not data then
        WarnLog("cannot find %s row %s", tblname, id)
    end
    return data
end

function TableDataManager:GetTypeElementData(configId)
    return self:GetRow("slg_tbtypeelement", configId)
end

---@return slg.TableMapElementInstance?
function TableDataManager:GetMapElementInstanceData(instanceId)
    return self:GetRowByMultiIndices("slg_tbmapelementinstance", self.worldLevelId, instanceId)
end

function TableDataManager:GetMapElementDataByInstanceId(id)
    local instanceData = self:GetMapElementInstanceData(id)
    return self:GetMapElementData(instanceData.element_id)
end

function TableDataManager:GetTypeLevelElementData(type, level)
    return self:GetRowByMultiIndices("slg_tbtypelevelelement", type, level)
end

function TableDataManager:GetLandFavoriteNameByType(typeId)
    return self:GetRow("common_tblandfavoritename", typeId)
end 

function TableDataManager:GetMapElementTable()
    return self:GetTable("slg_tbmapelement")
end

---@param AdministrativeHierarchyTypeOnly int? 指定行政区划类型
function TableDataManager:GetMapGISTableIter(AdministrativeHierarchyTypeOnly)
    local key = nil
    ---@return integer?, slg.TableMapGIS?
    return function()
        local value = nil
        for _ = 1, #self:GetTable("slg_tbmapgis") do
            ---@type int?, slg.TableMapGIS?
            key, value = next(self:GetTable("slg_tbmapgis"), key)
            if not key or not value then
                break
            end
            if value.level_id == self.worldLevelId and (not AdministrativeHierarchyTypeOnly or value.administrative_class == AdministrativeHierarchyTypeOnly) then
                break
            end
        end
        return key, value
    end
end

function TableDataManager:GetMapGISTableData(configId)
    return self:GetRowByMultiIndices("slg_tbmapgis", self.worldLevelId, configId)
end

---@return slg.TableMapGIS?
function TableDataManager:GetCountyConfig(countyId)
    local info =  self:GetRowByMultiIndices("slg_tbmapgis", self.worldLevelId, countyId)   ---@type slg.TableMapGIS
    if not info or info.administrative_class ~= AdministrativeHierarchy.County then
        return nil
    end
    return info
end

---@param countyOrCommanderyId County|Commandery
---@param idType? int @默认根据ID自动判断
---@return slg.TableMapGIS?
function TableDataManager:GetCommanderyConfig(countyOrCommanderyId, idType)
    local info = self:GetRowByMultiIndices("slg_tbmapgis", self.worldLevelId, countyOrCommanderyId)   ---@type slg.TableMapGIS
    if not info or (idType and idType ~= info.administrative_class) then
        return nil
    end
    if info.administrative_class == AdministrativeHierarchy.Commandery then
        return info
    elseif info.administrative_class == AdministrativeHierarchy.County then
        return self:GetCommanderyConfig(info.parent_id, AdministrativeHierarchy.Commandery)
    end
    return nil
end


---@param countyOrCommanderyOrPrefectureId County|Commandery|Prefecture
---@param idType? int @默认根据ID自动判断
---@return slg.TableMapGIS?
function TableDataManager:GetPrefectureConfig(countyOrCommanderyOrPrefectureId, idType)
    local info = self:GetRowByMultiIndices("slg_tbmapgis", self.worldLevelId, countyOrCommanderyOrPrefectureId)   ---@type slg.TableMapGIS
    if not info or (idType and idType ~= info.administrative_class) then
        return nil
    end
    if info.administrative_class == AdministrativeHierarchy.Prefecture then
        return info
    elseif info.administrative_class == AdministrativeHierarchy.Commandery then
        return self:GetPrefectureConfig(info.parent_id, AdministrativeHierarchy.Prefecture)
    elseif info.administrative_class == AdministrativeHierarchy.County then
        return self:GetPrefectureConfig(info.parent_id, AdministrativeHierarchy.Commandery)
    end
    return nil
end


function TableDataManager:GetTermEntryData(configId)
    if not configId then
        return self:GetTable("common_tbtermentry")
    end
    return self:GetRow("common_tbtermentry", configId)
end

function TableDataManager:GetTermEntry1Data(configId)
    return self:GetRow("common_tbtermentry1", configId)
end

function TableDataManager:GetMainCityDistScoreTable()
    return self:GetTable("slg_tbmaincitydistscore")
end

function TableDataManager:GetMainCityResScoreTable()
    return self:GetTable("slg_tbmaincityresscore")
end

function TableDataManager:GetMainCityRandomConfigs()
    return self:GetTable("slg_tbmaincityrandomconfig")
end

function TableDataManager:GetArmyTypeQualificationModifyData(index)
    return self:GetRow("battle_tbarmytypequalification", index)
end

function TableDataManager:GetAllScheduleData()
    return self:GetTable("common_tbschedule")
end

function TableDataManager:GetScheduleData(configId)
    return self:GetRow("common_tbschedule", configId)
end

function TableDataManager:GetCareerConst(configId)
    return self:GetRow("common_tbcareerconst", configId)
end

function TableDataManager:GetCareerTreeData(configId)
    if not configId then
        return self:GetTable("common_tbcareertree")
    end
    return self:GetRow("common_tbcareertree", configId)
end

function TableDataManager:GetStrategyData(configId)
    return self:GetRow("common_tbstrategy", configId)
end

function TableDataManager:GetStrategyDatas()
    return self:GetTable("common_tbstrategy")
end

function TableDataManager:GetCareerTalentData(configId)
    return self:GetRow("common_tbcareertelent", configId)
end

function TableDataManager:GetWorldBuffData(configId)
    return self:GetRow("common_tbworldbuff", configId)
end

function TableDataManager:GetLandBuffData(configId)
    return self:GetRow("common_tblandbuff", configId)
end

---@return common.TableItem?
function TableDataManager:GetItemData(configId)
    return self:GetRow("common_tbitem", configId)
end

function TableDataManager:GetAllItemData()
    return self:GetTable("common_tbitem")
end

function TableDataManager:GetItemConst(configId)
    return self:GetRow("common_tbitemconst", configId)
end

---@return common.TableToken?
function TableDataManager:GetTokenData(configId)
    return self:GetRow("common_tbtoken", configId)
end

function TableDataManager:GetAllTokenData()
    return self:GetTable("common_tbtoken")
end

---@return common.TableMaterial?
function TableDataManager:GetMaterialData(configId)
    return self:GetRow("common_tbmaterial", configId)
end

function TableDataManager:GetAllMaterialData()
    return self:GetTable("common_tbmaterial")
end

---@return common.TableEffectItem?
function TableDataManager:GetEffectItemData(configId)
    return self:GetRow("common_tbeffectitem", configId)
end

function TableDataManager:GetAllEffectItemData()
    return self:GetTable("common_tbeffectitem")
end

function TableDataManager:GetHeroItemData(configId)
    return self:GetRow("common_tbheroitem", configId)
end

function TableDataManager:GetBattleTacticItemData(configId)
    return self:GetRow("common_tbbattletacticitem", configId)
end

function TableDataManager:GetEquipConst(configId)
    return self:GetRow("common_tbequipconst", configId)
end

function TableDataManager:GetEquipData(configId)
    return self:GetRow("common_tbequip", configId)
end

function TableDataManager:GetAllEquipData()
    return self:GetTable("common_tbequip")
end

function TableDataManager:GetAllHorseData()
    return self:GetTable("common_tbhorse")
end

function TableDataManager:GetHorseData(configId)
    return self:GetRow("common_tbhorse", configId)
end

function TableDataManager:GetEquipQualityRatio(configId)
    return self:GetRow("common_tbequipqualityratio", configId)
end

function TableDataManager:GetEquipEffectData(configId)
    return self:GetRow("common_tbequipeffect", configId)
end

function TableDataManager:GetAllEquipEffectData()
    return self:GetTable("common_tbequipeffect")
end

function TableDataManager:GetActorContainerData(configId)
    return self:GetRow("common_tbactorcontainer", configId)
end

function TableDataManager:GetActorContainerConfig(configId)
    return self:GetRow("common_tbactorcontainerconfig", configId)
end

function TableDataManager:GetLandConst(configId)
    return self:GetRow("common_tblandconst", configId)
end

function TableDataManager:GetLandColorDatas()
    return self:GetTable("slg_tblandcolor")
end

function TableDataManager:GetLandColorData(configId)
    return self:GetRow("slg_tblandcolor", configId)
end

function TableDataManager:GetAmbitionsData(configId)
    if not configId then
        return self:GetTable(self.season_tbname)
    end
    return self:GetRow(self.season_tbname, configId)
end

function TableDataManager:GetAmbitionsConst(configId)
    return self:GetRow(self.season_consttbname, configId)
end

function TableDataManager:GetAmbitionsCountList(configId)
    if not configId then 
        return self:GetTable(self.season_countlistname)
    end
    return self:GetRow(self.season_countlistname, configId)
end

function TableDataManager:GetAmbitionsClearReward(configId)
    if not configId then 
        return self:GetTable(self.season_clearrewardname)
    end
    return self:GetRow(self.season_clearrewardname, configId)
end

function TableDataManager:GetSeasonGamePlay(configId)
    if not configId then 
        return self:GetTable(self.season_gameplayname)
    end
    return self:GetRow(self.season_gameplayname, configId)
end

function TableDataManager:GetSeasonGamePlayInfo(configId)
    if not configId then 
        return self:GetTable(self.season_gameplayinfoname)
    end
    return self:GetRow(self.season_gameplayinfoname, configId)
end

function TableDataManager:GetSwitchData(configId)
    return self:GetRow("common_tbswitch",configId)
end

function TableDataManager:GetHeroMainIdentityData(configId)
    return self:GetRow("common_tbheromainidentity", configId)
end

function TableDataManager:GetHeroSubIdentityData(configId)
    return self:GetRow("common_tbherosubidentity", configId)
end

function TableDataManager:GetHeroIdentityAttribute(configId)
    return self:GetRow("common_tbheroidentityattribute", configId)
end

function TableDataManager:GetHeroIdentityConst(configId)
    return self:GetRow("common_tbheroidentityconst", configId)
end


function TableDataManager:GetSeasonIdentityData(configId)
    if not configId then
        return self:GetTable(self.season_identityinfo)
    end
    return self:GetRow(self.season_identityinfo, configId)
end

function TableDataManager:GetSeasonCampData(configId)
    if not configId then
        return self:GetTable(self.season_campinfo)
    end
    return self:GetRow(self.season_campinfo, configId)
end

function TableDataManager:GetSeasonFullscreenToast(configId)
    if not configId then
        return self:GetTable(self.season_fullscreentoast)
    end
    return self:GetRow(self.season_fullscreentoast, configId)
end

function TableDataManager:GetRuleTextData(configId)
    return self:GetRow("common_tbruletext", configId)
end

function TableDataManager:GetSLGRuleTextData(configId)
    return self:GetRow("common_tbslgruletext", configId)
end

function TableDataManager:GetDrillChapter(configId)
    return self:GetRow("drill_tbchapter", configId)
end

function TableDataManager:GetDrillLevel(configId)
    return self:GetRow("drill_tblevel", configId)
end

function TableDataManager:GetDrillConst(configId)
    return self:GetRow("drill_tbconst", configId)
end

function TableDataManager:GetMapLevelDatas()
    return self:GetTable("map_tbmaplevel")
end

---@return map.TableMapLevel
function TableDataManager:GetMapLevel(levelId)
    return self:GetRow("map_tbmaplevel", levelId)
end

---@return common.TableMailTemplate?
function TableDataManager:GetMailTemplate(configId)
    return self:GetRow("common_tbmailtemplate", configId)
end

function TableDataManager:GetSettingItemIds(configId)
    return self:GetRow("common_tbsettingrelation", configId)
end

function TableDataManager:GetSettingTabs()
    return self:GetTable("common_tbsetting")
end

function TableDataManager:GetSettingConst(configId)
    return self:GetRow("common_tbsettingconst", configId)
end 

function TableDataManager:GetSettingItem(configId)
    if configId == nil then 
        return self:GetTable("common_tbsettinggroup")
    end 
    return self:GetRow("common_tbsettinggroup", configId)
end

function TableDataManager:GetRankConfig(configId)
    return self:GetRow("common_tbrank", configId)
end

---@return common.TableRank?
function TableDataManager:GetRankConst(configId)
    return self:GetRow("common_tbrankconst", configId)
end

function TableDataManager:GetSlgBuilding(actorType, subId)
    if not actorType then
        return self:GetTable("slg_tbbuilding")
    end
    if not subId then
        return self:GetRowByMultiIndices("slg_tbbuilding", actorType, 0)
    end
    return self:GetRowByMultiIndices("slg_tbbuilding", actorType, subId)
end

function TableDataManager:GetSlgBuildingConst(configId)
    return self:GetRow("slg_tbbuildingconst", configId)
end

---@return slg.TablePresetBuilding?
function TableDataManager:GetSlgPresetBuildingConst(actorType, level)
    if not actorType or not level then
        return nil
    end
    return self:GetRowByMultiIndices("slg_tbpresetbuilding", actorType, level)
end

function TableDataManager:GetRedpointConfig(configId)
    return self:GetRow("redpoint_tbredpoint",configId)
end

function TableDataManager:GetAllRedpointConfig()
    return self:GetTable("redpoint_tbredpoint")
end

function TableDataManager:GetJumpConfig(configId)
    return self:GetRow("jump_tbjump",configId)
end

function TableDataManager:GetMailConst(configId)
    return self:GetRow("common_tbmailconst", configId)
end

function TableDataManager:GetSiegeConst(configId)
    return self:GetRow("siege_tbsiegeconst", configId)
end

function TableDataManager:GetShop(configId)
    return self:GetRow("common_tbshop", configId)
end

function TableDataManager:GetShopConst(configId)
    return self:GetRow("common_tbshopconst", configId)
end

function TableDataManager:GetShopGoods(configId)
    return self:GetRow("common_tbshopgoods", configId)
end

function TableDataManager:GetAllFuncOpenCfg()
    return self:GetTable("funcopen_tbfuncopen")
end

function TableDataManager:GetFuncOpenCfg(configId)
    return self:GetRow("funcopen_tbfuncopen", configId)
end

function TableDataManager:GetAllFuncOpenTriggerCfg()
    return self:GetTable("funcopen_tbfuncopentrigger")
end

function TableDataManager:GetGachaPoolCfg(poolId)
    if poolId == nil then  
        return self:GetTable("common_tbgachasummary")
    end
    return self:GetRow("common_tbgachasummary", poolId)
end

function TableDataManager:GetRandomItemPoolCfg(poolId)
    return self:GetRowByMultiIndices("common_tbrandomitem", poolId)
end

function TableDataManager:GetRechargeJadeData(id)
    if id == nil then
        return self:GetTable("common_tbrechargejade")
    end
    return self:GetRow("common_tbrechargejade", id)
end

function TableDataManager:GetRetCodeData(configId)
    return self:GetRow("common_tbretcode", configId)
end

function TableDataManager:GetRetCodeText(configId)
    local data = self:GetRow("common_tbretcode", configId)
    if not data then
        return ""
    end
    return data.text
end

function TableDataManager:GetClanConst(configId)
    return self:GetRow("common_tbclanconst", configId)
end

function TableDataManager:GetClanLevelConfig(levelId)
    return self:GetRow("common_tbclanlevel", levelId)
end

---@return common.TableActorMove?
function TableDataManager:GetActorMoveConfig(moveType)
    return self:GetRow("common_tbactormove", moveType)
end

function TableDataManager:GetPayGoodsConfig(tb, goodsId)
    return self:GetRow(tb, goodsId)
end

function TableDataManager:GetNewbeeDialogueConfig(guideId)
    return self:GetRow("common_tbnewbeedialogue", guideId)
end

function TableDataManager:GetNewbeeCfgConfig(configId)
    return self:GetRow("common_tbnewbeeconfig", configId)
end

---@return common.TableSingleBehaviorConfig?
function TableDataManager:GetSingleBehaviorConfig(configId)
    return self:GetRow("common_tbsinglebehaviorconfig", configId)
end

---@return slg.TableStaminaCost?
function TableDataManager:GetStaminaCostConfig(battleType)
    return self:GetRow("slg_tbstaminacost", battleType)
end

function TableDataManager:GetStaminaConst(configId)
    return self:GetRow("slg_tbstaminaconst", configId)
end

function TableDataManager:GetChatConstData(key)
    return self:GetRow("common_tbchatconst", key)
end

function TableDataManager:GetMapViewLodData(key)
    return self:GetRow("slg_tbmapviewlod", key)
end

----------------------------------------------------------------------------------------------------------------------------
--- 表格读取(结束)
----------------------------------------------------------------------------------------------------------------------------


---运行时数据表后处理。依然优先使用luban的导表后处理，如果luban后处理无法满足需求，可以使用此机制。
---TableDataManager会统一管理所有后处理方法，并在reload时清空后处理状态，在合适的时机再次执行process方法。
---新增后处理时，需要继承该基类，并且实现process/getRow/getTable三个函数，并调用TableDataManager:RegPostProcessor注册。
---获取数据时，可以使用TableDataManager:GetPPRow/GetPPTable，或者TableDataManager.XXXPP:GetRow/GetTable。
---@class TableDataPostProcessor : LuaClass
---@field ready boolean
TableDataPostProcessor = DefineClass("TableDataPostProcessor")
function TableDataPostProcessor:ctor()
    self.ready = false
    if not self:lazy() then
        self:process()
        self.ready = true
    end
end
function TableDataPostProcessor:Reload()
    self.ready = false
    if not self:lazy() then
        self:process()
        self.ready = true
    end
end

function TableDataPostProcessor:GetRow(...)
    if not self.ready then
        self:process()
        self.ready = true
    end
    return self:getRow(...)
end
function TableDataPostProcessor:GetTable()
    if not self.ready then
        self:process()
        self.ready = true
    end
    return self:getTable()
end

---懒加载，如果为懒加载只有在调用数据访问接口时才调用process方法。
---@protected
function TableDataPostProcessor:lazy()
    return true
end

---进行数据后处理，需要在该方法中完成数据初始化、数据清理、数据加载。
---在reload后，会调用该方法重新生成数据。
---@protected
function TableDataPostProcessor:process()
    error("TableDataPostProcessor:process not implemented")
end
---@protected
function TableDataPostProcessor:getRow(...)
    error("TableDataPostProcessor:getRow not implemented")
end
---@protected
function TableDataPostProcessor:getTable()
    error("TableDataPostProcessor:getTable not implemented")
end

-- for emmylua
return {TableDataManager = TableDataManager, TableDataPostProcessor = TableDataPostProcessor}