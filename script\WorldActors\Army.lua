﻿local WorldActorEnv = xrequire("WorldActors.WorldActor")
local MoveCompEnv = xrequire("WorldActors.CommonComps.MoveComp")
local ActorCmdCompEnv = xrequire("WorldActors.CommonComps.ActorCmdComp")
local AvatarOwningCompEnv = xrequire("WorldActors.CommonComps.AvatarOwningComp")
local ArmyCombatCompEnv = xrequire("WorldActors.ArmyComps.ArmyCombatComp")
local ArmyBuffCompEnv = xrequire("WorldActors.ArmyComps.ArmyBuffComp")
local ActorContentCompEnv = xrequire("WorldActors.CommonComps.ActorContentComp")
local TableEnumUtilsEnv = xrequire("Common.Utils.TableEnumUtils")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")
local NaviCompEnv = xrequire("WorldActors.CommonComps.NaviComp")
local ArmyStrategyCompEnv = xrequire("WorldActors.ArmyComps.ArmyStrategyComp")
local ArmySupplyCompEnv = xrequire("WorldActors.ArmyComps.ArmySupplyComp")
local DefenderCompEnv = xrequire("WorldActors.ArmyComps.DefenderComp")
local VehicleDriverCompEnv = xrequire("WorldActors.ArmyComps.VehicleDriverComp")
local ArmyHomeCompEnv = xrequire("WorldActors.ArmyComps.ArmyHomeComp")
local ArmySiegeCompEnv = xrequire("WorldActors.ArmyComps.ArmySiegeComp")
local OutpostAttackerCompEnv = xrequire("WorldActors.ArmyComps.OutpostAttackerComp")
local InGridCompEnv = xrequire("WorldActors.CommonComps.InGridComp")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local ArmyUtilsEnv = xrequire("Common.Actor.ArmyUtils")

local ArmyActionEnv = xrequire("Modules.World.ArmyAction")
local MoveUtils = xrequire("Utils.MoveUtils")

---@class (partial) Army : WorldActor, CommonArmy, MoveComp, AvatarOwningComp, ArmyCombatComp, ArmyBuffComp, NaviComp, ArmySupplyComp, DefenderComp, VehicleDriverComp, ArmyHomeComp, ArmySiegeComp, OutpostAttackerComp, InGridArmyComp, ActorContentComp
---@field props ArmyProps
Army = DefineEntity("Army", {WorldActorEnv.WorldActor}, {
    ArmyUtilsEnv.CommonArmy,  ---@type CommonArmy
    MoveCompEnv.MoveComp,  ---@type MoveComp
    ActorCmdCompEnv.ActorCmdComp,  ---@type ActorCmdComp
    AvatarOwningCompEnv.AvatarOwningComp,  ---@type AvatarOwningComp
    ArmyCombatCompEnv.ArmyCombatComp,  ---@type ArmyCombatComp
    ArmyBuffCompEnv.ArmyBuffComp,  ---@type ArmyBuffComp
    ActorContentCompEnv.ActorContentComp,  ---@type ActorContentComp
    NaviCompEnv.NaviComp,  ---@type NaviComp
    ArmyStrategyCompEnv.ArmyStrategyComp,  ---@type ArmyStrategyComp
    ArmySupplyCompEnv.ArmySupplyComp,  ---@type ArmySupplyComp
    DefenderCompEnv.DefenderComp,  ---@type DefenderComp
    VehicleDriverCompEnv.VehicleDriverComp,  ---@type VehicleDriverComp
    ArmyHomeCompEnv.ArmyHomeComp,  ---@type ArmyHomeComp
    ArmySiegeCompEnv.ArmySiegeComp,  ---@type ArmySiegeComp
    OutpostAttackerCompEnv.OutpostAttackerComp,  ---@type OutpostAttackerComp
    InGridCompEnv.InGridArmyComp,  ---@type InGridArmyComp
})
Army._actorType = TableConst.enums.ActorType.ARMY
Army.actionMap = {
    ArmyMarch = ArmyActionEnv.ArmyMarch(),
    ArmyReturn = ArmyActionEnv.ArmyReturn(),
    ArmyOccupy = ArmyActionEnv.ArmyOccupy(),
    ArmyDefendGrid = ArmyActionEnv.ArmyDefendGrid(),
    ArmyDefendBuilding = ArmyActionEnv.ArmyDefendBuilding(),
    ArmyOperateVehicle = ArmyActionEnv.ArmyOperateVehicle(),
    ArmySingStrategy = ArmyActionEnv.ArmySingStrategy(),
    RpcCallbackAction = ActorCmdCompEnv.RpcCallbackAction(),
    ArmyStrategySha = ArmyActionEnv.ArmyStrategySha(),
    ArmyStrategyShan = ArmyActionEnv.ArmyStrategyShan(),
    ArmyWuZhongShengYou = ArmyActionEnv.ArmyWuZhongShengYou(),
    ArmyStrategyWuGuFengDeng = ArmyActionEnv.ArmyStrategyWuGuFengDeng(),
    ArmyStrategyKaiKen = ArmyActionEnv.ArmyStrategyKaiKen(),
    ArmyStrategyStartJiShenJiDian = ArmyActionEnv.ArmyStrategyStartJiShenJiDian(),
    ArmyStrategyJoinJiShenJiDian = ArmyActionEnv.ArmyStrategyJoinJiShenJiDian(),
    ArmyThrowStone = ArmyActionEnv.ArmyThrowStone(),
    ArmyAttackBuilding = ArmyActionEnv.ArmyAttackBuilding(),
    CallbackAction = ActorCmdCompEnv.CallbackAction(),
    ArmyBreakDurability = ArmyActionEnv.ArmyBreakDurability(),
    DebugAction = ArmyActionEnv.DebugAction(),
    ArmyWaitSiegeAssault = ArmyActionEnv.ArmyWaitSiegeAssault(),
    WaitBattle = ArmyActionEnv.WaitBattle(),
}
Army._persistent = true

---@diagnostic disable-next-line: duplicate-set-field
function Army:ctor()
    self._injuredTimerId = nil  ---@type TimerId?
end

---@diagnostic disable-next-line: duplicate-set-field
function Army:onEnterSpace()
    self:refreshAllStatus()
    self:checkInjuredTimeout()
end

function Army:isVisible()
    if self.props.inConByType[TableConst.enums.InConType.InBase] then
        return false
    end
    return true
end

function Army:isInteractable()
    if self.props.inConByType[TableConst.enums.InConType.InBase] then
        return false
    end
    if self.props.injuredTime then
        return false
    end
    if self.props.mainStatus == TableConst.enums.ArmyStatus.Retreat or self.props.mainStatus == TableConst.enums.ArmyStatus.Escape then
        return false
    end
    return true
end

function Army:CmdMarch(cmdUid, targetPos)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.March) then
        return
    end
    local moveType = TableConst.enums.MoveType.Default
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}},
    })
end

function Army:CmdOccupy(cmdUid, targetPos)
    assert(self.space)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.OccupyGrid) then
        return
    end
    local tp = self.space:GetGridType(self:GetDoubledPos2D())
    local data = GameCommon.TableDataManager:GetMapElementData(tp)
    assert(data)
    local remainTime = math.max(0, data.occupy_time)
    local moveType = TableConst.enums.MoveType.Default
    -- 交战前和倒计时结束后，通过space判断地块是否存在链接地
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}},
        {action="ArmyOccupy", params={pos=targetPos, startTime=0, remainTime=remainTime, timerId=0, occupyBattleRecord={}}},
    })
end

function Army:CmdMoveByPath(cmdUid, path)
    assert(self.space)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.March) then
        return
    end
    local actList = {}
    for idx, pos in ipairs(path) do
        local tp = self.space:GetGridType(pos.x, pos.y)
        local data = GameCommon.TableDataManager:GetMapElementData(tp)
        assert(data)
        local remainTime = math.max(0, data.occupy_time)
        local moveType = TableConst.enums.MoveType.Default
        table.insert(actList, {action="ArmyMarch", params={pos=pos, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}})
        table.insert(actList, {action="ArmyOccupy", params={pos=pos, startTime=0, remainTime=remainTime, timerId=0, occupyBattleRecord={}}})
        table.insert(actList, {action="CallbackAction", params={cb=EntCb("SetAutoPathIndex", idx)}})
    end
    table.insert(actList, {action="CallbackAction", params={cb=EntCb("ClearInAutoPath")}})
    local failedActList = {{action="CallbackAction", params={cb=EntCb("ClearInAutoPath")}}}
    local succ = self:StartCmd(cmdUid, actList, failedActList)
    if succ then
        ---@diagnostic disable-next-line: missing-fields
        self.props.inAutoPath = {
            path = path,
        }
        self:syncActorView({
            inAutoPath = self.props.inAutoPath,
        })
    end
end

function Army:SetAutoPathIndex(idx)
    self.props.inAutoPath.index = idx
    self:syncActorView({
        inAutoPath = self.props.inAutoPath,
    })
    return true
end

function Army:ClearInAutoPath()
    ---@diagnostic disable-next-line: missing-fields
    self.props.inAutoPath = {}
    self:syncActorView({
        inAutoPath = self.props.inAutoPath,
    })
    return true
end

function Army:CmdDefendGrid(cmdUid, targetPos)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.Defend) then
        return
    end
    local moveType = TableConst.enums.MoveType.Default
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}},
        {action="ArmyDefendGrid", params={pos=targetPos}},
    })
end

function Army:CmdDefendBuilding(cmdUid, targetPos, targetEid)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.Defend) then
        return
    end
    local moveType = TableConst.enums.MoveType.Default
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}},
        {action="ArmyDefendBuilding", params={entityId=targetEid}},
    })
end

function Army:CmdAttackBuilding(cmdUid, targetEid)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.AttackBuilding) then
        self:logInfo("CmdAttackBuilding not valid to attack building")
        return
    end
    local moveType = TableConst.enums.MoveType.Default
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={eid=targetEid, speed=self:GetMoveSpeedByType(moveType), moveType=moveType}},
        {action="ArmyAttackBuilding", params={entityId=targetEid, waitBreakDuraEnd=false, battleDuraDefEnd=false, duraDefenderRecordUid=false}},
    }, self:getBackToHomeActionList())
end

function Army:QueryMovePath(cmdUid, idx, pos, targetEid, moveType)
    local curPos = self:GetDoubledPos2DV()
    if curPos:Equals(pos) then
        self:onQueryMovePath(idx, pos, targetEid, moveType, true, {}, false)
        return
    end
    if targetEid ~= 0 then
        self:GetNaviPathToTargetBorder(targetEid, EntCb("onQueryMovePath", idx, pos, targetEid, moveType, false))
        return
    end
    self:GetNaviPathToPos(pos, EntCb("onQueryMovePath", idx, pos, targetEid, moveType, false))
end

function Army:onQueryMovePath(idx, pos, eid, moveType, succ, path, blockedPos)
    if not succ and #path > 0 then
        succ = true
    end
    local speed = self:GetMoveSpeedByType(moveType)
    local moveDist = MoveUtils.GetMovePathDistance(path, self:GetDoubledPos2DV())
    self:callOwnerClient("OnQueryMovePath", idx, pos, eid, succ, path, speed, moveDist, blockedPos)
end

function Army:SyncInjuredTime(injuredTime)
    if injuredTime <= ServerTimeEnv.GetServerNow() then
        injuredTime = nil
    end
    if injuredTime == self.props.injuredTime then
        return
    end
    self.props.injuredTime = injuredTime
    self:CheckInteractable()
    self:refreshCombatStatus()
    self:checkInjuredTimeout()
end

function Army:checkInjuredTimeout()
    if not self.props.injuredTime then
        return
    end
    if self._injuredTimerId then
        self:delTimer(self._injuredTimerId)
        self._injuredTimerId = nil
    end
    local delay = math.max(0.0, self.props.injuredTime - ServerTimeEnv.GetServerNow())
    self._injuredTimerId = self:addTimer(delay, 0, function ()
        self._injuredTimerId = nil
        self.props.injuredTime = nil
        self:CheckInteractable()
        self:refreshCombatStatus()
    end)
end

function Army:OnDefeated()
    self:logInfo("Army:OnDefeated")
    self:fireEntityEvent("OnArmyDefeated")
    self:ReqStartCmd(EntCb("CmdReturn", nil, true))
end

function Army:BackToHome(homeType, isDefeat)
    self:logInfo("Army:BackToHome %s %s", homeType, isDefeat)
    self:ReqStartCmd(EntCb("CmdReturn", homeType, isDefeat))
end

function Army:setStaminaDetail(staminaDetail)
    self.props.staminaDetail = staminaDetail
end

function Army:refreshAllStatus()
    self:refreshMainStatus()
    self:refreshCombatStatus()
    self:refreshDeployStatus()
    self:refreshVehicleStatus()
end

function Army:refreshMainStatus()
    -- 刷新主状态
    local status = TableConst.enums.ArmyStatus.Stay
    if self.props.inConByType[TableConst.enums.InConType.InBase] then
        status = TableConst.enums.ArmyStatus.Rest
    elseif self:IsMoving() then
        status = TableConst.enums.ArmyStatus.March
        if self.props.moveType == TableConst.enums.MoveType.Return then
            status = TableConst.enums.ArmyStatus.Retreat
        elseif self.props.moveType == TableConst.enums.MoveType.Escape then
            status = TableConst.enums.ArmyStatus.Escape
        end
    elseif self.props.inDefendGrid then
        status = TableConst.enums.ArmyStatus.DefendGrid
    elseif self.props.inDefendBuilding then
        status = TableConst.enums.ArmyStatus.DefendBuilding
    elseif self.props.occupyLand then
        status = TableConst.enums.ArmyStatus.Occupy
    elseif self.props.playingStrategy ~= 0 then
        status = TableConst.enums.ArmyStatus.Sing
        if self.props.waitContinuePlayStrategy then
            status = TableConst.enums.ArmyStatus.WaitSing
        end
    end
    assert(ProcessedTableEnv.ARMY_STATUS_GROUP.main[status])
    if status == self.props.mainStatus then
        return
    end
    self:logInfo("Army:refreshMainStatus %s %s -> %s", self.id,
        TableEnumUtilsEnv.GetEnumItemName("ArmyStatus", self.props.mainStatus), TableEnumUtilsEnv.GetEnumItemName("ArmyStatus", status))
    local oldStatus = self.props.mainStatus
    self.props.mainStatus = status
    self:syncActorView({
        mainStatus = status,
    })

    if GameCommon.TableDataManager:GetRow("common_tbvalidbehavior", status).Interactable == false or GameCommon.TableDataManager:GetRow("common_tbvalidbehavior", oldStatus).Interactable == false then
        self:CheckVisible()
        self:CheckInteractable()
    end

    self:fireEntityEvent("OnArmyMainStatusChanged", oldStatus)
end

function Army:refreshCombatStatus()
    -- 刷新战斗状态
    local status = TableConst.enums.ArmyStatus.IdleCombat
    local duration = nil
    if self:IsInCombat() then
        status = TableConst.enums.ArmyStatus.Combat
    elseif not table.isempty(self.props.impasse) then
        status = TableConst.enums.ArmyStatus.Impasse
        duration = self:getImpasseDuration()
    elseif self.props.inBreakDura then
        status = TableConst.enums.ArmyStatus.BreakDura
    elseif self:IsWaitCombat() then
        status = TableConst.enums.ArmyStatus.WaitCombat
    elseif self.props.injuredTime then
        status = TableConst.enums.ArmyStatus.Injured
        duration = self.props.injuredTime - ServerTimeEnv.GetServerNow()
    end
    assert(ProcessedTableEnv.ARMY_STATUS_GROUP.combat[status])
    if duration then
        self:SetStatueEndLeftTime(duration)
    end
    if status == self.props.combatStatus then
        return
    end
    self:logInfo("Army:refreshCombatStatus %s %s -> %s", self.id,
        TableEnumUtilsEnv.GetEnumItemName("ArmyStatus", self.props.combatStatus), TableEnumUtilsEnv.GetEnumItemName("ArmyStatus", status))
    self.props.combatStatus = status
    self:syncActorView({
        combatStatus = status,
    })
    self:fireEntityEvent("OnArmyCombatStatusChanged", status)
end

function Army:refreshDeployStatus()
    -- 刷新调动状态
    local status = TableConst.enums.ArmyStatus.IdleDeploy
    if self.props.siegeHome then
        if self.props.siegeHome.arrived then
            status = TableConst.enums.ArmyStatus.SiegeRally
        else
            status = TableConst.enums.ArmyStatus.GoSiegeRally
        end
    elseif self.props.deployHome then
        if self.props.deployHome.arrived then
            status = TableConst.enums.ArmyStatus.Deploy
        else
            status = TableConst.enums.ArmyStatus.GoDeploy
        end
    end
    assert(ProcessedTableEnv.ARMY_STATUS_GROUP.deploy[status])
    if status == self.props.deployStatus then
        return
    end
    self:logInfo("Army:refreshDeployStatus %s %s -> %s", self.id,
        TableEnumUtilsEnv.GetEnumItemName("ArmyStatus", self.props.deployStatus), TableEnumUtilsEnv.GetEnumItemName("ArmyStatus", status))
    self.props.deployStatus = status
    self:syncActorView({
        deployStatus = status,
    })
end

function Army:refreshVehicleStatus()
    local status = TableConst.enums.ArmyStatus.IdleVehicle
    if self.props.throwingStone then
        status = TableConst.enums.ArmyStatus.ThrowStone
    elseif self.props.inConByType[TableConst.enums.InConType.InVehicle] then
        local vehicle = self:getActor(self.props.inConByType[TableConst.enums.InConType.InVehicle])
        if vehicle and vehicle._actorType == TableConst.enums.ActorType.STONE_VEHICLE then
            status = TableConst.enums.ArmyStatus.InStoneVehicle
        end
    end
    self.props.vehicleStatus = status
    self:syncActorView({
        vehicleStatus = status,
    })
end


function Army:collectOwnerRegistExtraInfo()
    return {
        armySlotIdx = self.props.armySlotIdx,
        entityId = self.id,
    }
end

function Army:initActorView()
    self:firstSyncActorView({
        mainStatus = self.props.mainStatus,
        combatStatus = self.props.combatStatus,
        deployStatus = self.props.deployStatus,
        statusEndTime = self.props.statusEndTime,
        moveStartPos = self.props.moveStartPos,
        moveStartTime = self.props.moveStartTime,
        moveTargetPos = self.props.moveTargetPos,
        movePath = self.props.movePath,
        moveSpeed = self.props.moveSpeed,
        pos = self:GetDoubledPos2DV(),
        supply = self.props.supply,
        supplyMax = self.props.supplyMax,
        inAutoPath = self.props.inAutoPath,
        home = self.props.home,
        deployHome = self.props.deployHome,
        siegeHome = self.props.siegeHome,
    })
end

function Army:ChangeArmyType(armyType)
    self.props.armyType = armyType
end

function Army:onRegistOwner(accept)
    if not accept then
        self:DestroyBy(self.id, "RegistRefused", true)
        return
    end
end

function Army:GmTransferArmy(pos, callback)
    -- TODO(qun): 这个东西用Action正式化实现一下
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.March) then
        self:logWarn("GmTransferArmy not valid to move")
        self:callbackRpc(callback, false)
        return
    end
    self:StopMove(false)

    self:fireEntityEvent("onCmdStart")

    local oldX, oldY = self:SetDoubledPos2D(pos.x, pos.y)
    self:checkFollowMove(pos)

    self:OnPosChanged(oldX, oldY)

    self:allClientsRpc("OnActorTransfered")
    self:callbackRpc(callback, true)
end

-- for emmylua
return {Army=Army}