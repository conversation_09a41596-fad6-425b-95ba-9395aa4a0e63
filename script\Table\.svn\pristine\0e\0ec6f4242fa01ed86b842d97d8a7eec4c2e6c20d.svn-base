-- Excel: 模拟三国杀/公式/系数公式索引表@系数公式索引表.xlsx
-- Table Type: 列表

return
{
{name="buff_100000",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"2+2*lv",},},
{name="buff_100001",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"2+2*lv",},},
{name="buff_100002",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"2+2*lv",},},
{name="buff_100003",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"2+2*lv",},},
{name="buff_100004",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.005+0.005*lv",},},
{name="buff_100004",key="effect2",var_name="",final_min=-10000,final_max=10000,formulas={"-0.005+lv*(-0.005)",},},
{name="buff_100005",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.005+0.005*lv",},},
{name="buff_100005",key="effect2",var_name="",final_min=-10000,final_max=10000,formulas={"-0.005+lv*(-0.005)",},},
{name="buff_100006",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.005+0.005*lv",},},
{name="buff_100006",key="effect2",var_name="",final_min=-10000,final_max=10000,formulas={"-0.005+lv*(-0.005)",},},
{name="buff_100007",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.005+0.005*lv",},},
{name="buff_100007",key="effect2",var_name="",final_min=-10000,final_max=10000,formulas={"-0.005+lv*(-0.005)",},},
{name="buff_100008",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.035+0.005*lv",},},
{name="buff_100009",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.035+0.005*lv",},},
{name="buff_100010",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.035+0.005*lv",},},
{name="buff_100011",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.035+0.005*lv",},},
{name="buff_100016",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.005+lv*(-0.005)",},},
{name="buff_100017",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.005+lv*0.005",},},
{name="buff_100018",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.005+lv*(-0.005)",},},
{name="buff_100019",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.005+lv*0.005",},},
{name="buff_100020",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.02+lv*0.02",},},
{name="buff_100021",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.02+lv*(-0.02)",},},
{name="buff_10006",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.7",},},
{name="buff_100101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"5",},},
{name="buff_10016",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.2",},},
{name="buff_10020",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4*a*3/(a+b+c)",},},
{name="buff_100201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"5",},},
{name="buff_100301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"5",},},
{name="buff_100401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"5",},},
{name="buff_10100101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(20+lv*20/9)*(1+stars*0.03)",},},
{name="buff_10100201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.125-lv*0.125/9)*(1+stars*0.03)",},},
{name="buff_10100301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.015-lv*0.015/9)*(1+min(sint-240,0)/480+max(sint-240,0)/200)*(1+stars*0.03)",},},
{name="buff_10100401",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.9+lv*0.1)*(1+stars*0.03)",},},
{name="buff_10100402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"((-0.10)*(sint+350)/450)*(1+stars*0.03)",},},
{name="buff_10100501",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.3+lv*0.3/9)*(1+stars*0.03)",},},
{name="buff_10100502",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="buff_10100502",key="other2",var_name="",final_min=-10000,final_max=10000,formulas={"0.5",},},
{name="buff_10100601",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(25+lv*25/9)*(1+stars*0.03)",},},
{name="buff_10100601",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.6",},},
{name="buff_10101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8",},},
{name="buff_10180101",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+stars*0.03)",},},
{name="buff_10180201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="buff_10180201",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="buff_10180301",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="buff_10180401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="buff_10180401",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.1+lv*0.1/9)*(1+stars*0.03)",},},
{name="buff_10180501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.025-lv*0.025/9)*(1+stars*0.03)",},},
{name="buff_10180501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.1+lv*0.1/9)*(1+stars*0.03)",},},
{name="buff_10180601",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="buff_10180701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.8+lv*0.8/9)*(1+stars*0.03)",},},
{name="buff_10180801",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.175+lv*0.175/9)*(1+stars*0.03)",},},
{name="buff_10180902",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.06-lv*0.06/9)*(1+stars*0.03)",},},
{name="buff_10181002",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(30+lv*30/9)*(1+stars*0.03)",},},
{name="buff_10181002",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(-30-lv*30/9)*(1+stars*0.03)",},},
{name="buff_10181002",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.025-lv*0.025/9)*(1+stars*0.03)",},},
{name="buff_10181101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(35+lv*35/9)*(1+stars*0.03)",},},
{name="buff_10181101",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="buff_10181201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15",},},
{name="buff_10181201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.35+lv*1.35/9)*(1+stars*0.03)",},},
{name="buff_10181301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="buff_10181401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+stars*0.03)",},},
{name="buff_10181401",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.05+lv*0.05/9)*(1+stars*0.03)",},},
{name="buff_10181602",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.045+lv*0.005)*(1+stars*0.03)",},},
{name="buff_10181602",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4+lv*0.4/9)*(1+stars*0.03)",},},
{name="buff_10200301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"((-17.5-lv*17.5/9)*(sint+500)/600)*(1+stars*0.03)",},},
{name="buff_10200601",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+stars*0.03)",},},
{name="buff_10200801",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="buff_10200901",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="buff_10201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"4",},},
{name="buff_10201701",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.075+lv*0.075/9)*(1+stars*0.03)",},},
{name="buff_10201701",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.1+lv*0.1/9)*(1+stars*0.03)",},},
{name="buff_10202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.6*a",},},
{name="buff_10202001",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(40+lv*40/9)*(1+min(sint-250,0)/500+max(sint-250,0)/250)*(1+stars*0.03)",},},
{name="buff_10202501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.1+lv*1.1/9)*(1+a*0.2)*(1+stars*0.03)",},},
{name="buff_10202701",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.15+lv*1.15/9)*(1+stars*0.03)",},},
{name="buff_10280601",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.27+lv*0.03)*(1+stars*0.03)",},},
{name="buff_10280701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.08+lv*0.12)*(1+stars*0.03)",},},
{name="buff_10300101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"((-25-lv*25/9)*(satt+400)/500)*(1+stars*0.03)",},},
{name="buff_10300901",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.025+lv*0.025/9)*(1+stars*0.03)",},},
{name="buff_10301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8",},},
{name="buff_10301001",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.05",},},
{name="buff_10301101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.03-lv*0.03/9)*(1+min(satt-200,0)/400+max(satt-200,0)/250)*(1+stars*0.03)",},},
{name="buff_10400101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.3+lv*0.3/9)*(1+stars*0.03)",},},
{name="buff_10400101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(-30-lv*30/9)",},},
{name="buff_10400102",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.06-lv*0.06/9)*(1+stars*0.03)",},},
{name="buff_10400102",key="attr4",var_name="",final_min=-10000,final_max=10000,formulas={"(6+lv*6/9)",},},
{name="buff_10400302",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"((0.45+lv*0.05)+(0.1+lv*0.1/9)*a)*(1+stars*0.03)",},},
{name="buff_10400401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.075+lv*0.075/9)*(1+min(satt-200,0)/400+max(satt-200,0)/200)*(1+stars*0.03)",},},
{name="buff_10400401",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+min(sint-200,0)/400+max(sint-200,0)/250)*(1+stars*0.03)",},},
{name="buff_10400501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.2+lv*1.2/9)*(1+stars*0.03)",},},
{name="buff_10400501",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5",},},
{name="buff_10400601",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+min(sint-200,0)/400+max(sint-200,0)/350)*(1+stars*0.03)",},},
{name="buff_10400701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4+lv*0.4/9)*(1+stars*0.03)",},},
{name="buff_10400701",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4)*(1+a)*(1+stars*0.03)",},},
{name="buff_10400801",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.225+lv*0.025)*(1+min(sint-200,0)/400+max(sint-200,0)/300)*(1+stars*0.03)",},},
{name="buff_10400802",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.225+lv*0.025)*(1+stars*0.03)",},},
{name="buff_10400901",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.075+lv*0.075/9)*(1+stars*0.03)",},},
{name="buff_10400901",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.2+lv*0.2/9)*(1+a)*(1+stars*0.03)",},},
{name="buff_10401101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.1-lv*0.1/9)*(1+min(sdef-240,0)/480+max(sdef-240,0)/200)*(1+stars*0.03)",},},
{name="buff_10401101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.1-lv*0.1/9)*(1+min(sdef-240,0)/480+max(sdef-240,0)/200)*(1+stars*0.03)",},},
{name="buff_10401202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.075-lv*0.075/9)*(1+min(sdef-240,0)/480+max(sdef-240,0)/200)*(1+stars*0.03)",},},
{name="buff_10401301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="buff_10401302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+stars*0.03)",},},
{name="buff_10480101",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+stars*0.03)",},},
{name="buff_10480202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="buff_10500102",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4+lv*0.4/9)*(1+stars*0.03)",},},
{name="buff_10500201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.05+lv*0.05/9)*(1+stars*0.03)",},},
{name="buff_10500202",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.025+lv*0.025/9)*(1+stars*0.03)",},},
{name="buff_10500301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.075-lv*0.075/9)*(1+min(sdef-240,0)/480+max(sdef-240,0)/240)*(1+stars*0.03)",},},
{name="buff_10500401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.6",},},
{name="buff_10500401",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.2+lv*0.2/9)*(1+stars*0.03)",},},
{name="buff_10500402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(20+lv*20/9)*(1+stars*0.03)",},},
{name="buff_10500501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(7.5+lv*7.5/9)*(1+stars*0.03)",},},
{name="buff_10500501",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.05+lv*0.05/9)*(1+stars*0.03)",},},
{name="buff_10500601",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(7.5+lv*7.5/9)*(1+stars*0.03)",},},
{name="buff_10500601",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4+lv*0.4/9)*(1+stars*0.03)",},},
{name="buff_10600101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.225+lv*0.025)*(1+stars*0.03)",},},
{name="buff_10600101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_10600201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.04+lv*0.04/9)*(1+stars*0.03)",},},
{name="buff_10600202",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.3+lv*0.3/9)*(1+stars*0.03)",},},
{name="buff_10600203",key="heal2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.3+lv*0.3/9)*(1+stars*0.03)",},},
{name="buff_10600204",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(15+lv*15/9)*(1+stars*0.03)",},},
{name="buff_10600301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.075+lv*0.075/9)*(1+stars*0.03)",},},
{name="buff_10600302",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+stars*0.03)",},},
{name="buff_10600402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.03+lv*0.03/9)*(1+min(sint-240,0)/480+max(sint-240,0)/300)*(1+stars*0.03)",},},
{name="buff_10600403",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.125-lv*0.125/9)*(1+min(sint-240,0)/480+max(sint-240,0)/300)*(1+stars*0.03)",},},
{name="buff_10600403",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.125-lv*0.125/9)*(1+min(sint-240,0)/480+max(sint-240,0)/300)*(1+stars*0.03)",},},
{name="buff_10700102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="buff_10700102",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="buff_11100101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11100101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11100101",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11100101",key="heal2",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11100101",key="heal3",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11100102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(5+lv*5/9)*(1+min(sint-200,0)/400+max(sint-200,0)/200)",},},
{name="buff_11100103",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(5+lv*5/9)*(1+min(sint-200,0)/400+max(sint-200,0)/200)",},},
{name="buff_11100104",key="other2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.1)*(sint+700)/800",},},
{name="buff_11100105",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.2+lv*0.2/9",},},
{name="buff_11100201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+min(satt-200,0)/400+max(satt-200,0)/500)",},},
{name="buff_11100201",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.6",},},
{name="buff_11100302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-10-lv*10/9)*(1+min(sdef-300,0)/600+max(sdef-300,0)/200)",},},
{name="buff_11100303",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(10+lv*10/9)*(1+min(sdef-300,0)/600+max(sdef-300,0)/200)",},},
{name="buff_11100304",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8",},},
{name="buff_11100401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.05+lv*0.05/9)*(1+min(sint-300,0)/600+max(sint-300,0)/200)",},},
{name="buff_11100402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(7.5+lv*7.5/9)*(1+min(sint-300,0)/600+max(sint-300,0)/200)",},},
{name="buff_11100403",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(7.5+lv*7.5/9)*(1+min(sint-300,0)/600+max(sint-300,0)/200)",},},
{name="buff_11100502",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.075+lv*0.075/9",},},
{name="buff_11100502",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6)*(1+min(sint-240,0)/480+max(sint-240,0)/320)",},},
{name="buff_11100503",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.075+lv*0.075/9",},},
{name="buff_11100601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.55+lv*0.55/9",},},
{name="buff_11100701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.36+lv*0.04",},},
{name="buff_11100702",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.27+lv*0.03",},},
{name="buff_11101001",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_11101001",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+a/4000)",},},
{name="buff_11101101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.81+lv*0.09)*(a+500)/600",},},
{name="buff_11101201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4)*(1+min(satt-200,0)/400+max(satt-200,0)/250)",},},
{name="buff_11101201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4+lv*0.4/9",},},
{name="buff_11101202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.04+lv*0.04/9",},},
{name="buff_11101301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.2+lv*0.2/9)*(1+min(sdef-150,0)/300+max(sdef-150,0)/300)",},},
{name="buff_11101302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.2+lv*0.2/9",},},
{name="buff_11101401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-10-lv*10/9)*(1+min(sdef-200,0)/400+max(sdef-200,0)/200)",},},
{name="buff_11101402",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(10+lv*10/9)*(1+min(sdef-200,0)/400+max(sdef-200,0)/200)",},},
{name="buff_11101403",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.90+lv*0.1",},},
{name="buff_11101403",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="buff_11101403",key="other1",var_name="",final_min=0.5,final_max=0.8,formulas={"0.5*(1+min(sdef-200,0)/400+max(sdef-200,0)/200)",},},
{name="buff_11101501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_11101502",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(-15)*(1+min(sint-250,0)/500+max(sint-250,0)/500)",},},
{name="buff_11101503",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(15)*(1+min(sint-250,0)/500+max(sint-250,0)/500)",},},
{name="buff_11101504",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="buff_11101601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.45+lv*0.05)*(1+a/300)",},},
{name="buff_11101601",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.45+lv*0.05)*(1+a/300)",},},
{name="buff_11101602",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"5+lv*5/9",},},
{name="buff_11101701",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.7",},},
{name="buff_11101701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.2+lv*0.2/9",},},
{name="buff_11101801",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11101801",key="other1",var_name="",final_min=0,final_max=1,formulas={"0.35*(1+min(sint-300,0)/600+max(sint-300,0)/200)",},},
{name="buff_11101802",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.05",},},
{name="buff_11101802",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.05",},},
{name="buff_11101902",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.2+lv*0.2/9",},},
{name="buff_11101903",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5)*(1+min(sint-240,0)/480+max(sint-240,0)/360)",},},
{name="buff_11102001",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.54+lv*0.06",},},
{name="buff_11102102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11102103",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11102201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11102203",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.01+lv*0.01/9)*(1+min(sint-250,0)/500+max(sint-250,0)/250)",},},
{name="buff_11102301",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+a/3000)",},},
{name="buff_11102302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.075-lv*0.075/9)*(1+min(sint-250,0)/500+max(sint-250,0)/250)",},},
{name="buff_11180101",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.6+lv*0.6/9",},},
{name="buff_11180201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="buff_11180201",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11180301",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11180401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="buff_11180401",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1+lv*0.1/9",},},
{name="buff_11180501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.025-lv*0.025/9)",},},
{name="buff_11180501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1+lv*0.1/9",},},
{name="buff_11180601",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.35+lv*0.35/9",},},
{name="buff_11180701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8+lv*0.8/9",},},
{name="buff_11180801",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.175+lv*0.175/9",},},
{name="buff_11180902",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.06-lv*0.06/9)",},},
{name="buff_11181002",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"30+lv*30/9",},},
{name="buff_11181002",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(-30-lv*30/9)",},},
{name="buff_11181002",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.025-lv*0.025/9)",},},
{name="buff_11181101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"35+lv*35/9",},},
{name="buff_11181101",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.35+lv*0.35/9",},},
{name="buff_11181201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15",},},
{name="buff_11181201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"1.35+lv*1.35/9",},},
{name="buff_11181301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.35+lv*0.35/9",},},
{name="buff_11181401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15+lv*0.15/9",},},
{name="buff_11181401",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.05+lv*0.05/9",},},
{name="buff_11181501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"20+lv*20/9",},},
{name="buff_11181602",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.045+lv*0.005",},},
{name="buff_11181602",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4+lv*0.4/9",},},
{name="buff_11200101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.15",},},
{name="buff_11200201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.175+lv*0.175/9",},},
{name="buff_11200201",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6)*(1+min(sint-200,0)/400+max(sint-200,0)/250)",},},
{name="buff_11200201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1+lv*1/9",},},
{name="buff_11200301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-18-lv*2)*(sint+500)/600",},},
{name="buff_11200501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.075+lv*0.075/9",},},
{name="buff_11200701",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.075+lv*0.075/9",},},
{name="buff_11200701",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.27+lv*0.03",},},
{name="buff_11200901",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1+lv*0.1/9",},},
{name="buff_11200901",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.05+lv*0.05/9",},},
{name="buff_11201101",key="effect2",var_name="",final_min=-10000,final_max=10000,formulas={"-0.4",},},
{name="buff_11201301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-10-lv*10/9)*(1+min(satt-250,0)/500+max(satt-250,0)/200)",},},
{name="buff_11201501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.15-lv*0.15/9)*(1+min(sdef-200,0)/400+max(sdef-200,0)/250)",},},
{name="buff_11201501",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+min(sdef-200,0)/400+max(sdef-200,0)/250)",},},
{name="buff_11201501",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"(-0.15-lv*0.15/9)*(1+min(sdef-200,0)/400+max(sdef-200,0)/250)",},},
{name="buff_11201501",key="attr4",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+min(sdef-200,0)/400+max(sdef-200,0)/250)",},},
{name="buff_11201601",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.125+lv*0.0125/9",},},
{name="buff_11201601",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.125+lv*0.0125/9",},},
{name="buff_11201701",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5*(1+min(satt-250,0)/500+max(satt-250,0)/200)",},},
{name="buff_11201701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8+lv*0.8/9",},},
{name="buff_11280601",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.27+lv*0.03",},},
{name="buff_11280701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.08+lv*0.12",},},
{name="buff_11280901",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-17.5-lv*17.5/9)",},},
{name="buff_11300101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-54-lv*6)*(satt+400)/500",},},
{name="buff_11300101",key="other3",var_name="",final_min=-10000,final_max=10000,formulas={"-0.1",},},
{name="buff_11300102",key="other4",var_name="",final_min=-10000,final_max=10000,formulas={"(-50-lv*50/9)",},},
{name="buff_11300201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.05)*(1+min(satt-200,0)/400+max(satt-200,0)/300)",},},
{name="buff_11380101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(-25-lv*25/9)",},},
{name="buff_11400101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.2+lv*0.2/9)*(1+min(satt-300,0)/600+max(satt-300,0)/300)",},},
{name="buff_11400101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.2+lv*0.2/9)*(1+min(satt-300,0)/600+max(satt-300,0)/300)",},},
{name="buff_11400102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.05",},},
{name="buff_11400103",key="attr3",var_name="",final_min=-10000,final_max=10000,formulas={"-0.2",},},
{name="buff_11400201",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.035+lv*0.035/9)*(1+min(satt-300,0)/600+max(satt-300,0)/200)",},},
{name="buff_11400301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(1-0.35*(1+min(sdef-200,0)/400+max(sdef-200,0)/300))",},},
{name="buff_11400301",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.175+lv*0.175/9",},},
{name="buff_11400401",key="attr1",var_name="",final_min=0,final_max=0,formulas={},},
{name="buff_11400401",key="dam1",var_name="",final_min=0,final_max=0,formulas={},},
{name="buff_11400402",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8",},},
{name="buff_11400402",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4+lv*0.4/9",},},
{name="buff_11400501",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"a*(0.3+lv*0.3/9)*(1+min(sint-200,0)/400+max(sint-200,0)/300)",},},
{name="buff_11400501",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15+lv*0.15/9",},},
{name="buff_11400701",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+min(satt-300,0)/600+max(satt-300,0)/200)",},},
{name="buff_11400701",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.15+lv*0.15/9)*(1+min(satt-300,0)/600+max(satt-300,0)/200)",},},
{name="buff_11480101",key="effect1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15+lv*0.15/9",},},
{name="buff_11480202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="buff_11700101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.06+lv*0.06/9)*(1+min(sint-250,0)/500+max(sint-250,0)/200)",},},
{name="buff_11700201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.72+lv*0.08",},},
{name="buff_11700201",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.36+lv*0.04",},},
{name="buff_200101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"8",},},
{name="buff_200201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"8",},},
{name="buff_200301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"8",},},
{name="buff_200401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"8",},},
{name="buff_210101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.6",},},
{name="buff_21010102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.04",},},
{name="buff_21010201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.025",},},
{name="buff_21020102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"10",},},
{name="buff_21020201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.09",},},
{name="buff_210301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.04)*(sdef+300)/400",},},
{name="buff_21030101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"22",},},
{name="buff_210401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.3",},},
{name="buff_21040102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-35",},},
{name="buff_21040201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.08",},},
{name="buff_21040202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.03",},},
{name="buff_22010101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.03",},},
{name="buff_22010201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.06",},},
{name="buff_220201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_22020101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25",},},
{name="buff_22020201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.6",},},
{name="buff_220401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.04",},},
{name="buff_22040101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.04",},},
{name="buff_22040201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.03",},},
{name="buff_230101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.08",},},
{name="buff_23010102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.06",},},
{name="buff_23010202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.04",},},
{name="buff_230201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.08",},},
{name="buff_23020102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-10",},},
{name="buff_23030102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"20",},},
{name="buff_23030201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.3",},},
{name="buff_23030201",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.54+lv*0.06",},},
{name="buff_230401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4",},},
{name="buff_230401",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.3",},},
{name="buff_23040101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.03",},},
{name="buff_23040201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.06",},},
{name="buff_240101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.06",},},
{name="buff_240101",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_24010102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.1",},},
{name="buff_24010202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-20",},},
{name="buff_24010202",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"35",},},
{name="buff_24020101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4",},},
{name="buff_24020202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.04",},},
{name="buff_24020202",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.07",},},
{name="buff_240301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15",},},
{name="buff_24030102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.04",},},
{name="buff_24030201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-20",},},
{name="buff_24030201",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"0.04",},},
{name="buff_240401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.08",},},
{name="buff_240401",key="attr2",var_name="",final_min=-10000,final_max=10000,formulas={"-0.12",},},
{name="buff_24040102",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"-0.6",},},
{name="buff_24040202",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.045",},},
{name="buff_300101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"12",},},
{name="buff_300201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"12",},},
{name="buff_300301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"12",},},
{name="buff_310101",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.3",},},
{name="buff_310201",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15",},},
{name="buff_310302",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.12",},},
{name="skill_10200101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.45+lv*0.05)*(1+stars*0.03)",},},
{name="skill_10200201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="skill_10200401",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.75+lv*0.75/9)*(1+stars*0.03)",},},
{name="skill_10200501",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.54+lv*0.06)*(1+stars*0.03)",},},
{name="skill_10200601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.7+lv*0.7/9)*(1+stars*0.03)",},},
{name="skill_10200701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.25+lv*1.25/9)*(1+stars*0.03)",},},
{name="skill_10200701",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.75+lv*0.75/9)*(1+stars*0.03)",},},
{name="skill_10200901",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.65+lv*0.65/9)*(1+stars*0.03)",},},
{name="skill_10201001",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4+lv*0.4/9)*(1+stars*0.03)",},},
{name="skill_10201501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.05+lv*1.05/9)*(1+stars*0.03)",},},
{name="skill_10201501",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="skill_10201601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.8+lv*0.8/9)*(1+stars*0.03)",},},
{name="skill_10201601",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.8+lv*0.8/9)*(1+stars*0.03)",},},
{name="skill_10201701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.45+lv*0.05)*(1+stars*0.03)",},},
{name="skill_10201801",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.6+lv*1.6/9)*(1+stars*0.03)",},},
{name="skill_10201901",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.95+lv*0.95/9)*(a*0.25+0.75)*(1+stars*0.03)",},},
{name="skill_10202201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="skill_10202301",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.8+lv*0.8/9)*(1+stars*0.03)",},},
{name="skill_10202401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4+lv*0.4/9)*(1+stars*0.03)",},},
{name="skill_10202601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1+lv*1/9)-a*(0.2+lv*0.2/9)*(1+stars*0.03)",},},
{name="skill_10202701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.15+lv*1.15/9)*(1+stars*0.03)",},},
{name="skill_10202801",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.65+lv*0.65/9)*(1+stars*0.03)",},},
{name="skill_10202801",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.65+lv*0.65/9)*(1+stars*0.03)",},},
{name="skill_10202801",key="dam3",var_name="",final_min=-10000,final_max=10000,formulas={"(0.65+lv*0.65/9)",},},
{name="skill_10280101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.65+lv*0.65/9)*(1+stars*0.03)",},},
{name="skill_10280201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="skill_10280201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.25+lv*0.25/9)*(1+stars*0.03)",},},
{name="skill_10280401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.45+lv*0.05)*(1+stars*0.03)",},},
{name="skill_10280501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1+lv*1/9)*(1+stars*0.03)",},},
{name="skill_10280701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.08+lv*0.12)*(1+stars*0.03)",},},
{name="skill_10280801",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+stars*0.03)",},},
{name="skill_10300101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.2+lv*1.2/9)*(1+stars*0.03)",},},
{name="skill_10300201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="skill_10300301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="skill_10300301",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.35+lv*0.35/9)*(1+stars*0.03)",},},
{name="skill_10300501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+stars*0.03)",},},
{name="skill_10300601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+stars*0.03)",},},
{name="skill_10300701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+stars*0.03)",},},
{name="skill_10300801",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.8+lv*0.8/9)*(1+stars*0.03)",},},
{name="skill_10300901",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.25+lv*1.25/9)*(1+stars*0.03)",},},
{name="skill_10301001",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(1.1+lv*1.1/9)*(1+stars*0.03)",},},
{name="skill_10301001",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"(0.6+lv*0.6/9)*(1+stars*0.03)",},},
{name="skill_10301101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="skill_10400201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.5+lv*0.5/9)*(1+stars*0.03)",},},
{name="skill_11200101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.85+lv*0.85/9",},},
{name="skill_11200301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="skill_11200401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.5+lv*1.5/9",},},
{name="skill_11200401",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.3+lv*0.3/9",},},
{name="skill_11200601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1+lv*1/9",},},
{name="skill_11200701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="skill_11200801",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.8+lv*0.8/9",},},
{name="skill_11201001",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="skill_11201001",key="heal2",var_name="",final_min=-10000,final_max=10000,formulas={"0.6+lv*0.6/9",},},
{name="skill_11201101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.3+lv*1.3/9",},},
{name="skill_11201101",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"1.95+lv*1.95/9",},},
{name="skill_11201101",key="dam3",var_name="",final_min=-10000,final_max=10000,formulas={"2.6+lv*2.6/9",},},
{name="skill_11201101",key="other1",var_name="",final_min=-10000,final_max=10000,formulas={"0.15+lv*0.15/9",},},
{name="skill_11201201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"1.65+lv*1.65/9",},},
{name="skill_11201301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.4+lv*1.4/9",},},
{name="skill_11201401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.1+lv*1.1/9",},},
{name="skill_11201501",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.75+lv*0.75/9",},},
{name="skill_11201601",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="skill_11201601",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="skill_11201701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.3+lv*1.3/9",},},
{name="skill_11280101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.65+lv*0.65/9",},},
{name="skill_11280201",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5+lv*0.5/9",},},
{name="skill_11280201",key="heal1",var_name="",final_min=-10000,final_max=10000,formulas={"0.25+lv*0.25/9",},},
{name="skill_11280401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.45+lv*0.05",},},
{name="skill_11280501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1+lv*1/9",},},
{name="skill_11280701",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.08+lv*0.12",},},
{name="skill_11280801",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.6+lv*0.6/9",},},
{name="skill_11300101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.75+lv*0.75/9",},},
{name="skill_11300301",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"0.5*(1+min(sspe-250,0)/500+max(sspe-250,0)/300)",},},
{name="skill_11300301",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.55+lv*0.55/9)*(1+min(sspe-250,0)/500+max(sspe-250,0)/300)",},},
{name="skill_11300401",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.9+lv*0.1",},},
{name="skill_11300401",key="dam2",var_name="",final_min=-10000,final_max=10000,formulas={"0.9+lv*0.1",},},
{name="skill_11300501",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"0.4+lv*0.4/9",},},
{name="skill_11380101",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1.2+lv*1.2/9",},},
{name="skill_11400401",key="attr1",var_name="",final_min=-10000,final_max=10000,formulas={"(0.4)*(1+min(satt-250,0)/500+max(satt-250,0)/200)",},},
{name="skill_999999",key="dam1",var_name="",final_min=-10000,final_max=10000,formulas={"1",},},
}