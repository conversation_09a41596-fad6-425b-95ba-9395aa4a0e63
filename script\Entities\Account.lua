
local AvatarEntity = xrequire("Framework.Entities.AvatarEntity")
---@type ContainerInstancesComp
local ContainerInstancesComp = xrequire("Framework.Entities.Components.ContainerInstancesComp").ContainerInstancesComp
local ServerTimeCompEnv = xrequire("Entities.Components.ServerTimeComp")
---@class Account:AvatarEntity, ContainerInstancesComp<Account>
Account = DefineEntity("Account", {AvatarEntity.AvatarEntity}, {ContainerInstancesComp, ServerTimeCompEnv.ServerTimeComp})
Account.AvatarType = "Avatar"

function Account:ctor()
    self.urs = nil
end

function Account:auth(urs)
    self:logInfo("auth, urs: %s", urs)
    self.urs = urs
    self:onAuthSucc()
end

function Account:onAuthSucc()
    self:serviceRpc("RoleService", "loginRequest", self.urs, self:getMailboxStr())
end

function Account:onLoginRequest(succ, mailboxStr)
    if succ then
        if mailboxStr ~= "" then
            -- 顶号
            local avatarMailbox = LuaMailbox.createFromString(mailboxStr)
            self:giveClientToRemote(avatarMailbox, true)
        else
            -- 正常登录
            local mongoColl = self:getMongoEntityCollection(self.AvatarType)
            mongoColl:find({urs = self.urs}, {_id = 1, urs = 1}, {}, function(succ, docs) self:onQueryAvatarFromDB(succ, docs) end)
        end
    else
        self:onLoginFailed(false)
    end
end

function Account:onQueryAvatarFromDB(succ, docs)
    if succ then
        if docs[1] ~= nil then
            -- create from db
            EntityManager.createEntityFromDB(self.AvatarType, docs[1]["_id"], function (errcode, eid) self:onCreateAvatar(eid) end)
        else
            -- create locally
            self:serviceRpc("GidService", "AllocGid", self:makeCallback("OnAllocAvatarGid"))
        end
    else
        self:logError("query avatar from db failed.")
        self:onLoginFailed(true)
    end
end

function Account:OnAllocAvatarGid(succ, gid)
    if succ then
        local avatar = EntityManager.createEntity(self.AvatarType, 0, {urs = self.urs, gid = gid, name = self.urs})
        self:onCreateAvatar(avatar.id)
    else
        self:logError("alloc avatar gid failed.")
        self:onLoginFailed(true)
    end
end

function Account:onCreateAvatar(eid)
    if eid <= 0 then
        self:logError("create avatar entity failed")
        self:onLoginFailed(true)
        return
    end
    local avatar = EntityManager.getEntity(eid)
    self:giveClientTo(avatar)
    self:onLoginSucc()
end

function Account:onGiveClientToRemote(succ)
    AvatarEntity.AvatarEntity.onGiveClientToRemote(self, succ)
    if succ then
        self:onLoginSucc()
    else
        self:onLoginFailed(true)
    end
end

function Account:onLoginFailed(report)
    if report then
        self:serviceRpc("RoleService", "loginFinish", self.urs, self:getMailboxStr())
    end
    self:destroy()
end

function Account:onLoginSucc()
    self:serviceRpc("RoleService", "loginFinish", self.urs, self:getMailboxStr())
    self:destroy()
end
