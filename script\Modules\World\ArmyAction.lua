﻿local ActorCmdCompEnv = xrequire("WorldActors.CommonComps.ActorCmdComp")
local WorldConst = xrequire("Common.Const.WorldConst")
local MoveUtils = xrequire("Utils.MoveUtils")
local CoordUtils = xrequire("Common.Utils.CoordUtils")
local MapDataEnv = xrequire("Modules.World.MapData")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local EntityUtilsEnv = xrequire("Common.Utils.EntityUtils")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")
local ConstEnv = xrequire("Utils.Consts")
local ActorUtils = xrequire("Utils.ActorUtils")
local FormationUtilsEnv = xrequire("Common.Utils.FormationUtils")

---@class ArmyAction : ActionBase
ArmyMarch = DefineClass("ArmyMarch", ActorCmdCompEnv.ActionBase)
function ArmyMarch.Start(cls, army, actProp)
    assert(actProp.params.pos or actProp.params.eid, "either pos or eid must be provided")
    assert(actProp.params.moveType)
    cls:ensureParam(actProp, "speed")
    cls:NaviToTarget(army, actProp)
end

function ArmyMarch.NaviToTarget(cls, army, actProp)
    local curPos = army:GetDoubledPos2DV()
    if actProp.params.pos then
        if curPos:Equals(actProp.params.pos) then
            army:OnActionFinished()
            return
        end
        local specialNaviMask = nil
        if ActorUtils.IGNORE_OBSTACLE_MOVE[actProp.params.moveType] then
            specialNaviMask = ConstEnv.NAVI_MASK_NO_DYNAMIC_OBSTACLE
        end
        army:GetNaviPathToPos(actProp.params.pos, army:makeActionCallback(actProp, "SetPath"), specialNaviMask)
    else
        army:GetNaviPathToTargetBorder(actProp.params.eid, army:makeActionCallback(actProp, "SetPath"))
    end
end

function ArmyMarch.Stop(cls, army, actProp, success)
    army:RemoveMoveLine()
    army:StopMove()
end

function ArmyMarch.Resume(cls, army, actProp)
    local path = {}
    cls:ContinueMove(army, actProp, actProp.params.continueData.path)
    actProp.params.continueData = nil
end

function ArmyMarch.Pause(cls, army, actProp)
    army:RemoveMoveLine()
    actProp.params.continueData = army:StopMove(true)
end

---@param army Army
function ArmyMarch.SetPath(cls, army, actProp, path, blockedPos)
    if #path < 2 then
        --[[寻路重试机制
        允许在串行的Action中，配置多次寻路尝试
        需要重试的原因：
            移动Action的前一个Action导致阻挡变化，而当前寻路Action依赖该变化
            由于设置阻挡和寻路是不同线程处理的，没有串行化，存在乱序执行的可能，因此当前寻路Action可能会失败
            寻路保证了写的优先级相对较高，因此进行重试基本可以解决问题
        ]]
        if actProp.params.retryNavi and actProp.params.retryNavi > 0 then
            army:logInfo("ArmyMarch SetPath retry navi, left retryNavi=%s", actProp.params.retryNavi)
            actProp.params.retryNavi = actProp.params.retryNavi - 1
            army:waitActionCallback(actProp, 5, "NaviToTarget")
            return
        end
        army:OnActionFailed()
        return
    end
    local curPos = army:GetDoubledPos2DV()
    if not curPos:Equals(path[1]) then
        army:OnActionFailed()
        return
    end
    -- 去掉第一个点
    table.remove(path, 1)
    cls:ContinueMove(army, actProp, path)
end

---@param army Army
function ArmyMarch.ContinueMove(cls, army, actProp, path)
    local continueData = actProp.params.continueData
    local length = MoveUtils.GetMovePathDistance(path, army:GetDoubledPos2DV(), actProp.params.continueData)
    local duration = length * actProp.params.speed - (continueData and continueData.timeElapse or 0)
    army:SetStatueEndLeftTime(duration)
    army:StartMove(path, actProp.params.speed, continueData, actProp.params.moveType, actProp.params.eid, function(reachTarget, pause)
        if pause then
            return
        end
        if reachTarget then
            army:OnActionFinished()
        else
            army:OnActionFailed()
        end
    end)
    army:AddMoveLine()
end

ArmyReturn = DefineClass("ArmyReturn", ActorCmdCompEnv.FastAction)
function ArmyReturn.Start(cls, army, actProp)
    cls:ensureParam(actProp, "baseId")
    local base = army.space:getActor(actProp.params.baseId)
    if not base then
        if army.props.siegeHome and army.props.siegeHome.baseId == actProp.params.baseId then
            army:addTimer(0, 0, function()
                army:LeaveDeployHome(true, ActorUtils.DeployType.SIEGE)
            end)
        elseif army.props.deployHome and army.props.deployHome.baseId == actProp.params.baseId then
            army:addTimer(0, 0, function()
                army:LeaveDeployHome(true, ActorUtils.DeployType.DEPLOY)
            end)
        end
        army:OnActionFailed()
        return
    end
    local succ = base:dealActorEnter(army, TableConst.enums.InConType.InBase)
    if not succ then
        army:OnActionFailed()
        return
    end
    army:OnActionFinished()
end

WaitBattle = DefineClass("WaitBattle", ActorCmdCompEnv.ActionBase)
---@param army Army
function WaitBattle.Start(cls, army, actProp)
    army.props.waitBattle = {
        cb = army:makeActionCallback(actProp, "CbWaitBattle"),
    }
    army:refreshCombatStatus()
end
---@param army Army
function WaitBattle.CbWaitBattle(cls, army, actProp)
    local args = {}
    if army.props.waitBattle then
        if army.props.waitBattle.landDefender then
            args.occupyBattleRecord = army.props.waitBattle.landDefender.occupyBattleRecord
        end
        if army.props.waitBattle.duraDefender then
            args.battleDuraDefEnd = true
            args.duraDefenderRecordUid = army.props.waitBattle.duraDefender.recordUid
        end
    end
    army:OnActionFinished(args)
end
---@param army Army
function WaitBattle.Resume(cls, army, actProp)
    army:DelayCheckBattle()
end
---@param army Army
function WaitBattle.Pause(cls, army, actProp) end
---@param army Army
function WaitBattle.Stop(cls, army, actProp, success)
    army.props.waitBattle = nil
    army:refreshCombatStatus()
end

---@class ArmyOccupy : WaitAction
ArmyOccupy = DefineClass("ArmyOccupy", ActorCmdCompEnv.WaitAction)
function ArmyOccupy.Start(cls, army, actProp)
    cls:ensureParam(actProp, "pos")
    cls:ensureParam(actProp, "occupyBattleRecord")
    ActorCmdCompEnv.WaitAction.Start(cls, army, actProp)  -- TODO(qun): 这个地方可以考虑把start和resume分开调用
    cls:checkBattleWithGridDefenders(army, actProp)
end
---@param army Army
function ArmyOccupy.checkBattleWithGridDefenders(cls, army, actProp)
    assert(army.space)
    local x, y = army:GetDoubledPos2D()
    local _, _, landLevel, _ = army.space:GetGridDefender(x, y)
    local staminaCost = FormationUtilsEnv.CalcPVEStaminaCost(army.props.armyMinLevel, TableConst.enums.BattleType.PVE_LAND, {
        landLevel = landLevel,
    })
    if staminaCost > army:GetStamina() then
        army:OnActionFailed()
        return
    end
    army:WaitBattle({
        landDefender = {
            hasStaminaCost = true,
        }
    })
end
function ArmyOccupy.Resume(cls, army, actProp, args)
    if args and args.occupyBattleRecord then
        actProp.params.occupyBattleRecord = args.occupyBattleRecord
    end
    ActorCmdCompEnv.WaitAction.Resume(cls, army, actProp)
end
function ArmyOccupy.onStartWait(cls, army, actProp)
    army.props.occupyLand = true
    army:refreshMainStatus()
end
function ArmyOccupy.onPauseWait(cls, army, actProp)
    army.props.occupyLand = false
    army:refreshMainStatus()
end
---@param army Army
function ArmyOccupy.onTimeout(cls, army, actProp)
    assert(army.space)
    if not army.space:HasFriendGridAround(army.props, actProp.params.pos.x, actProp.params.pos.y, 0, false) then
        army:OnActionFailed()
        return
    end
    if not army.space:ValidOccupyGrid(actProp.params.pos.x, actProp.params.pos.y, army.props, TableConst.enums.ActorType.ARMY) then
        army:OnActionFailed()
        return
    end
    army.space:Occupy(actProp.params.pos.x, actProp.params.pos.y, army.props, army.props.name, actProp.params.occupyBattleRecord)
    army:OnActionFinished()
end

ArmyDefendGrid = DefineClass("ArmyDefendGrid", ActorCmdCompEnv.WaitBreakAction)
function ArmyDefendGrid.Start(cls, army, actProp)
    cls:ensureParam(actProp, "pos")
    local relation = army.space:GetGridRelationShip(army.props, actProp.params.pos.x, actProp.params.pos.y, false)
    if not BehaviorJudgeEnv.ValidBehaviorToByRelation(relation, army._actorType, TableConst.enums.ActorType.CHUNK, TableConst.enums.InteractiveBehavior.Defend) then
        army:OnActionFailed()
        return
    end
    if army.props.supply < Config.Slg.DEFEND_CMD_COST_SUPPLY then
        army:OnActionFailed()
        return
    end
    army:StartDefendGrid()
end
function ArmyDefendGrid.Stop(cls, army, actProp, success)
    army:StopDefendGrid()
end

ArmyDefendBuilding = DefineClass("ArmyDefendBuilding", ActorCmdCompEnv.WaitBreakAction)
function ArmyDefendBuilding.Start(cls, army, actProp)
    cls:ensureParam(actProp, "entityId")
    if army.props.supply < Config.Slg.DEFEND_CMD_COST_SUPPLY then
        army:OnActionFailed()
        return
    end
    local building = army.space:getActorByEid(actProp.params.entityId)
    if not building or not building:addDefender(army) then
        army:OnActionFailed()
        return
    end
end
function ArmyDefendBuilding.Stop(cls, army, actProp, success)
    local building = army.space:getActorByEid(actProp.params.entityId)
    if building then
        building:dealActorLeave(army)
    end
end

ArmyOperateVehicle = DefineClass("ArmyOperateVehicle", ActorCmdCompEnv.FastAction)
function ArmyOperateVehicle.Start(cls, army, actProp)
    cls:ensureParam(actProp, "entityId")
    local vehicle = army.space:getActorByEid(actProp.params.entityId)
    if not vehicle or not vehicle:addOperator(army) then
        army:OnActionFailed()
        return
    end
    army:OnActionFinished()
end

ArmyThrowStone = DefineClass("ArmyThrowStone", ActorCmdCompEnv.WaitAction)
function ArmyThrowStone.Start(cls, army, actProp)
    cls:ensureParam(actProp, "target")
    ActorCmdCompEnv.WaitAction.Start(cls, army, actProp)
end
function ArmyThrowStone.onStartWait(cls, army, actProp)
    local target = army:getActor(actProp.params.target)
    army.props.throwingStone = target:GetDoubledPos2DV()
    army:refreshVehicleStatus()
end
function ArmyThrowStone.onPauseWait(cls, army, actProp)
    army.props.throwingStone = nil
    army:refreshVehicleStatus()
end
function ArmyThrowStone.onTimeout(cls, army, actProp)
    cls.onPauseWait(cls, army, actProp)
    if not army:doThrowStone(actProp.params.target) then
        army:OnActionFailed()
    end
    army:OnActionFinished()
end

ArmyWaitSiegeAssault = DefineClass("ArmyWaitSiegeAssault", ActorCmdCompEnv.ActionBase)
function ArmyWaitSiegeAssault.Start(cls, army, actProp)
    cls:ensureParam(actProp, "campEid")
    cls:ensureParam(actProp, "gateEid")
    cls:ensureParam(actProp, "isMainForce")
    cls:Resume(army, actProp)
end
---@param army Army
function ArmyWaitSiegeAssault.Resume(cls, army, actProp)
    -- 注册监听回调，等待集结出发
    local camp = army.space:getActorByEid(actProp.params.campEid)  ---@type SiegeCamp?
    if not camp or not camp.waitForSiegeAssault then
        army:OnActionFailed()
        return
    end
    local cb = army:makeActionCallback(actProp, "CbSiegeAssault")
    camp:waitForSiegeAssault(army, actProp.params.gateEid, actProp.params.isMainForce, cb)
end
function ArmyWaitSiegeAssault.Pause(cls, army, actProp)
    -- 注销监听回调
    local camp = army.space:getActorByEid(actProp.params.campEid)  ---@type SiegeCamp?
    if camp then
        camp:cancelWaitForSiegeAssault(army)
    end
end
function ArmyWaitSiegeAssault.CbSiegeAssault(cls, army, actProps, success)
    if not success then
        army:OnActionFailed()
        return
    end
    army:OnActionFinished()
end
function ArmyWaitSiegeAssault.Stop(cls, army, actProp, success) end

ArmyAttackBuilding = DefineClass("ArmyAttackBuilding", ActorCmdCompEnv.ActionBase)
---@param army Army
function ArmyAttackBuilding.Start(cls, army, actProp)
    assert(army.space)
    cls:ensureParam(actProp, "entityId")
    cls:ensureParam(actProp, "waitBreakDuraEnd")
    cls:ensureParam(actProp, "battleDuraDefEnd")
    local building = army.space:getActorByEid(actProp.params.entityId)  ---@type OutpostActor?
    if not building or not building:AddAttacker(army) then
        army:OnActionFailed()
        return
    end
    cls:Resume(army, actProp)
end
---@param army Army
function ArmyAttackBuilding.Stop(cls, army, actProp, success)
    assert(army.space)
    local building = army.space:getActorByEid(actProp.params.entityId)  ---@type OutpostActor?
    if building then
        building:AttackerLeave(army)
    end
end
---@param army Army
function ArmyAttackBuilding.Resume(cls, army, actProp, args)
    assert(army.space)
    if args then
        if args.waitBreakDuraEnd then
            actProp.params.waitBreakDuraEnd = true
        end
        if args.battleDuraDefEnd then
            actProp.params.battleDuraDefEnd = true
        end
        if args.duraDefenderRecordUid then
            actProp.params.duraDefenderRecordUid = args.duraDefenderRecordUid
        end
    end
    local outpost = army.space:getActorByEid(actProp.params.entityId)  ---@type OutpostActor?
    if outpost and cls:CheckAttack(army, actProp, outpost) then
        return
    end
    cls:CheckResult(army, actProp)
end
---@param army Army
---@param outpost OutpostActor
---@return boolean @是否还要继续攻击
function ArmyAttackBuilding.CheckAttack(cls, army, actProp, outpost)
    if not outpost:ValidBeAttack(army) then
        return false
    end
    if outpost:GetNextBattleSysDefender(army, false) then
        army:WaitBattle({
            sysDefender = {}
        })
        return true
    end
    local npcDataId = outpost:getDuraDefender(army)  ---@type int?
    if not npcDataId then
        actProp.params.battleDuraDefEnd = true
    end
    if actProp.params.waitBreakDuraEnd and actProp.params.battleDuraDefEnd then
        -- 消耗耐久
        local duraZero = cls:doConsume(army, actProp, outpost, actProp.params.duraDefenderRecordUid)
        actProp.params.waitBreakDuraEnd = false
        actProp.params.battleDuraDefEnd = false
        actProp.params.duraDefenderRecordUid = nil
        if duraZero then
            return false
        end
    end
    if not actProp.params.battleDuraDefEnd and npcDataId then
        army:WaitBattle({
            duraDefender = {}
        })
        return true
    end
    if not actProp.params.waitBreakDuraEnd then
        army:WaitBreakDura(outpost)
        return true
    end
    army:logError("No more attack actions can be performed")
    return false
end
---@param army Army
function ArmyAttackBuilding.CheckResult(cls, army, actProp)
    assert(army.space)
    -- 判断攻占成功与否
    -- 建筑拆除、耐久0、被我方占有等情况均视为攻占成功
    local outpost = army.space:getActorByEid(actProp.params.entityId)  ---@type WorldActor|OutpostComp?
    if not outpost or EntityUtilsEnv.FRIENDLY_RELATION[EntityUtilsEnv.GetRelationShip(army.props, outpost.props)] then
        army:OnActionFinished()
        return
    end
    if outpost.props.durability <= 0 then
        -- 耐久0但是没有被我方占领，被抢了
        army:OnActionFailed()
        return
    end
    if outpost.IsInWar and not outpost:IsInWar() then
        -- 攻城结束了
        army:OnActionFailed()
        return
    end
    if army.props.inOutpost == outpost.props.uid then
        army:DelayCheckBattle()  -- 未进入Battle的拆耐久
        return
    end
    -- 否则视为攻占失败
    army:OnActionFailed()
end
function ArmyAttackBuilding.doConsume(cls, army, actProp, outpost, recordUid)
    local succ, trueConsume, duraLeft = outpost:consumeDurability(army:getSiegeValue(), army)
    if not succ then
        return false
    end
    -- 上报耐久伤害战报
    if recordUid then
        -- 增量更新造成的耐久伤害
        army:callOwner("UpdateBattleRecordFromWorld", recordUid, army:GetBattleDurabilityInfo(outpost, trueConsume, duraLeft <= 0))
    else
        -- 新建无守军战斗的耐久伤害战报
        army:GenerateConsumeDuraBattleRecord(outpost, trueConsume, duraLeft <= 0)
    end
    return duraLeft <= 0
end
function ArmyAttackBuilding.Pause(cls, army, actProp) end

ArmyBreakDurability = DefineClass("ArmyBreakDurability", ActorCmdCompEnv.WaitAction)
function ArmyBreakDurability.Start(cls, army, actProp)
    ActorCmdCompEnv.WaitAction.Start(cls, army, actProp)
end
function ArmyBreakDurability.onStartWait(cls, army, actProp)
    army.props.inBreakDura = true
    army:refreshCombatStatus()
end
function ArmyBreakDurability.onPauseWait(cls, army, actProp)
    army.props.inBreakDura = false
    army:refreshCombatStatus()
end
function ArmyBreakDurability.onTimeout(cls, army, actProp)
    cls.onPauseWait(cls, army, actProp)
    army:OnActionFinished({waitBreakDuraEnd=true})
end

--region 复杂-策牌

-- 部队吟唱
ArmySingStrategy = DefineClass("ArmySingStrategy", ActorCmdCompEnv.WaitAction)
function ArmySingStrategy.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    ActorCmdCompEnv.WaitAction.Start(cls, army, actProp)
end
function ArmySingStrategy.onStartWait(cls, army, actProp)
    army.props.playingStrategy = actProp.params.strategyId
    army:refreshMainStatus()
end
function ArmySingStrategy.onPauseWait(cls, army, actProp)
    army.props.playingStrategy = 0
    army:refreshMainStatus()
end
function ArmySingStrategy.onTimeout(cls, army, actProp)
    cls.onPauseWait(cls, army, actProp)
    army:OnActionFinished()
end

ArmyStrategySha = DefineClass("ArmyStrategySha", ActorCmdCompEnv.FastAction)
function ArmyStrategySha.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    cls:ensureParam(actProp, "strategyAddition")
    local config = GameCommon.TableDataManager:GetStrategyData(actProp.params.strategyId)
    army:AddWorldBuff(config.strategy_func.worldBuff, config.strategy_func.worldBuffTime)
    army:AddBuff(config.strategy_func.battleBuff)
    army:clearFailedActions()
    army:OnStrategySingEnd(actProp.params.strategyId, {})
    army:OnActionFinished()
end

ArmyStrategyShan = DefineClass("ArmyStrategyShan", ActorCmdCompEnv.FastAction)
function ArmyStrategyShan.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    cls:ensureParam(actProp, "strategyAddition")
    local config = GameCommon.TableDataManager:GetStrategyData(actProp.params.strategyId)
    army:AddWorldBuff(config.strategy_func.worldBuff, config.strategy_func.worldBuffTime)
    army:AddBuff(config.strategy_func.battleBuff)
    army:clearFailedActions()
    army:OnStrategySingEnd(actProp.params.strategyId, {})
    army:OnActionFinished()
end

ArmyWuZhongShengYou = DefineClass("ArmyWuZhongShengYou", ActorCmdCompEnv.FastAction)
function ArmyWuZhongShengYou.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    cls:ensureParam(actProp, "strategyAddition")
    local config = GameCommon.TableDataManager:GetStrategyData(actProp.params.strategyId)
    local curPos = army:GetDoubledPos2DV()
    local packedPos = CoordUtils.PackPos(curPos.x, curPos.y)
    local tp = MapDataEnv.StaticMapData.instance():GetElementType(packedPos)
    local info = GameCommon.TableDataManager:GetMapElementData(tp)
    if not info.production or info.productivity <= 0 then
        army:OnActionFailed()
        return
    end
    -- 只能对自己占领的土地使用
    local occupation, isBuilding = army.space:GetGridInfo(curPos.x, curPos.y)
    if isBuilding or not occupation or EntityUtilsEnv.GetRelationShip(army.props, occupation) ~= TableConst.enums.RelationShip.Self then
        army:OnActionFailed()
        return
    end
    local num = info.productivity * config.strategy_func.produceTime / 60 / 60
    local addRatio = actProp.params.strategyAddition.wuZhongShengYouAddResRatio + actProp.params.strategyAddition.resAddRatio4WuZhongShengYou[info.production] or 0
    num = math.floor(num * (1 + addRatio / 100))
    army:callOwner("AddCurrencyOnly", {[info.production]=num}, TableConst.enums.Reason.WO_ZHONG_SHENG_YOU)
    army:OnStrategySingEnd(actProp.params.strategyId, {})
    army:OnActionFinished()
end

ArmyStrategyWuGuFengDeng = DefineClass("ArmyStrategyWuGuFengDeng", ActorCmdCompEnv.FastAction)
function ArmyStrategyWuGuFengDeng.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    cls:ensureParam(actProp, "strategyAddition")
    local config = GameCommon.TableDataManager:GetStrategyData(actProp.params.strategyId)
    local curPos = army:GetDoubledPos2DV()
    local axial = CoordUtils.Axial.new(CoordUtils.Offset2Axial(CoordUtils.Doubled2Offset(curPos.x, curPos.y)))
    local gridList = {}
    local levelUpLimit = GameCommon.TableDataManager:GetCareerConst("WU_GU_MAX_LEVEL_UP")
    if actProp.params.strategyAddition.poXianLandLevelLimitTo > 0 then
        levelUpLimit = math.max(levelUpLimit, actProp.params.strategyAddition.poXianLandLevelLimitTo)
    end
    for q, r in axial:Spiral(config.strategy_func.gridRange + actProp.params.strategyAddition.tuoJiangRadiusAdd) do
        local col, row = CoordUtils.Offset2Doubled(CoordUtils.Axial2Offset(q, r))
        local occupation, isBuilding = army.space:GetGridInfo(col, row)
        local relation = occupation and EntityUtilsEnv.GetRelationShip(army.props, occupation)
        if not isBuilding and army.space:IsOccupiableGrid(col, row) and (relation == TableConst.enums.RelationShip.Self or relation == TableConst.enums.RelationShip.SameAlly) then
            local buffRatio = 0
            local buffDuration = 0
            local tp = MapDataEnv.StaticMapData.instance():GetElementType(CoordUtils.PackPos(col, row))
            buffRatio = actProp.params.strategyAddition.woYeProduceExRatio
            buffDuration = actProp.params.strategyAddition.woYeDuration
            -- 通知升级和增加产量
            army:callWorldAvatar(occupation.gid, "OnLandWatered", col, row, levelUpLimit, buffRatio, buffDuration)
            table.insert(gridList, {x=col, y=row})
        end
    end
    army:OnStrategySingEnd(actProp.params.strategyId, {gridList=gridList})
    army:OnActionFinished()
end

ArmyStrategyKaiKen = DefineClass("ArmyStrategyKaiKen", ActorCmdCompEnv.FastAction)
ArmyStrategyKaiKen.VALID_LAND_TYPE = {
    [TableConst.enums["slg.MapElementType"].Food] = true,
    [TableConst.enums["slg.MapElementType"].Stone] = true,
    [TableConst.enums["slg.MapElementType"].Wood] = true,
    [TableConst.enums["slg.MapElementType"].Iron] = true,
}
function ArmyStrategyKaiKen.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    cls:ensureParam(actProp, "landType")
    cls:ensureParam(actProp, "strategyAddition")
    if not ArmyStrategyKaiKen.VALID_LAND_TYPE[actProp.params.landType] then
        army:OnActionFailed()
        return
    end
    if army.props.allyId == 0 then
        army:OnActionFailed()
        return
    end
    local config = GameCommon.TableDataManager:GetStrategyData(actProp.params.strategyId)
    local curPos = army:GetDoubledPos2DV()
    local packedPos = CoordUtils.PackPos(curPos.x, curPos.y)
    local tp = MapDataEnv.StaticMapData.instance():GetElementType(packedPos)
    local info = GameCommon.TableDataManager:GetMapElementData(tp)
    if info.type ~= TableConst.enums["slg.MapElementType"].DevelopableResourceNode then  -- 判断info.type
        army:OnActionFailed()
        return
    end
    -- TODO(qun): 此类地块直接把他标记为城池建筑的一部分，当城池建筑的归属发生变化时，触发产出加成变动
    local newTp = ProcessedTableEnv.GetMapElementIdByTypeAndLevel(actProp.params.landType, info.level)
    if not newTp then
        army:OnActionFailed()
        return
    end
    local city = army.space:GetCity(curPos.x, curPos.y)
    if not city or not city:ValidSetDevGridType(packedPos) then
        army:OnActionFailed()
        return
    end
    local newConfig = GameCommon.TableDataManager:GetMapElementData(newTp)
    -- 通知联盟产出加成
    local produce = newConfig.productivity
    local production = newConfig.production
    army:callAlly(army.props.allyId, "OnAddCityLandProduce", packedPos, {[production]=produce})
    army.space:Occupy(curPos.x, curPos.y, {allyId = army.props.allyId}, "", {})
    city:SetDevGridType(packedPos, actProp.params.landType)
    army:OnStrategySingEnd(actProp.params.strategyId, {})
    army:OnActionFinished()
end

ArmyStrategyJiShenJiDianBase = DefineClass("ArmyStrategyJiShenJiDianBase", ActorCmdCompEnv.ActionBase)
-- 处理加入祭典后的一些通用逻辑
function ArmyStrategyJiShenJiDianBase.Start(cls, army, actProp)
    cls:ensureParam(actProp, "strategyId")
    cls:ensureParam(actProp, "strategyAddition")
    cls:ensureParam(actProp, "jiDianActorId")
end
function ArmyStrategyJiShenJiDianBase.Stop(cls, army, actProp, success)
    local jiDianActor = army.space:getActor(actProp.params.jiDianActorId)
    if jiDianActor then
        jiDianActor:OnMemberLeaveJiDian(army)
    end
    army.props.playingStrategy = 0
    army.props.waitContinuePlayStrategy = false
    army:refreshMainStatus()
end
function ArmyStrategyJiShenJiDianBase.JoinJiDian(cls, army, actProp, jiDianActor)
    jiDianActor:AddMember(army, army:makeActionCallback(actProp, "onJiDianFinished"))
    army.props.playingStrategy = actProp.params.strategyId
    army:refreshMainStatus()
    army:clearFailedActions()
end
function ArmyStrategyJiShenJiDianBase.onJiDianFinished(cls, army, actProp)
    if actProp.params.strategyAddition.jiKuiResAdd > 0 then
        -- 参与者根据各自天赋解锁情况获得奖励
        army:callOwner("OnJiDianReward", actProp.params.strategyAddition.jiKuiResAdd)
    end
    army:OnActionFinished()
end
function ArmyStrategyJiShenJiDianBase.Pause(cls, army, actProp)
    -- 祭典中暂停，一般是进入战斗中
    local jiDianActor = army.space:getActor(actProp.params.jiDianActorId)
    if jiDianActor then
        jiDianActor:OnMemberPaused(army)
    end
end
function ArmyStrategyJiShenJiDianBase.Resume(cls, army, actProp)
    -- 恢复祭典
    local jiDianActor = army.space:getActor(actProp.params.jiDianActorId)
    if jiDianActor then
        jiDianActor:OnMemberResumed(army)
    end
end
function ArmyStrategyJiShenJiDianBase.GmSetWaitTime(cls, actor, actProp, time)
    local actor = actor:getActor(actProp.params.jiDianActorId)
    if not actor then
        return
    end
    actor:GmSetWaitTime(time)
end

ArmyStrategyStartJiShenJiDian = DefineClass("ArmyStrategyStartJiShenJiDian", ArmyStrategyJiShenJiDianBase)
function ArmyStrategyStartJiShenJiDian.Start(cls, army, actProp)
    ArmyStrategyJiShenJiDianBase.Start(cls, army, actProp)
    if army.props.allyId == 0 then
        army:OnActionFailed()
        return
    end
    local config = GameCommon.TableDataManager:GetStrategyData(actProp.params.strategyId)
    local curPos = army:GetDoubledPos2DV()
    local validJiDian = nil
    for _, actor in ipairs(army.space:GetActorAround(curPos.x, curPos.y, config.strategy_func.radius, TableConst.enums.ActorType.JI_SHEN_JI_DIAN)) do
        if EntityUtilsEnv.GetRelationShip(army.props, actor.props) ~= TableConst.enums.RelationShip.SameAlly then
            validJiDian = actor
            break
        end
    end
    if not validJiDian then
        validJiDian = army.space:createActor("JiShenJiDianActor", {
            allyId = army.props.allyId,
            jiDianEndTime = config.strategy_func.singTime + ServerTimeEnv.GetServerNow(),
        }, curPos)
    end
    assert(validJiDian)
    actProp.params.jiDianActorId = validJiDian.props.uid
    validJiDian:RestartJiDianTimer()
    cls:JoinJiDian(army, actProp, validJiDian)
end

ArmyStrategyJoinJiShenJiDian = DefineClass("ArmyStrategyJoinJiShenJiDian", ArmyStrategyJiShenJiDianBase)
function ArmyStrategyJoinJiShenJiDian.Start(cls, army, actProp)
    ArmyStrategyJiShenJiDianBase.Start(cls, army, actProp)
    if army.props.allyId == 0 then
        army:OnActionFailed()
        return
    end
    local target = army:getActor(actProp.params.jiDianActorId)
    if EntityUtilsEnv.GetRelationShip(army.props, target.props) ~= TableConst.enums.RelationShip.SameAlly then
        army:OnActionFailed()
        return
    end
    cls:JoinJiDian(army, actProp, target)
end

--endregion

--region Debug

---@class DebugAction : ActionBase
DebugAction = DefineClass("DebugAction", ActorCmdCompEnv.ActionBase)
function DebugAction.Start(cls, actor, actProp)
    actor:logInfo("DebugAction Start %s", actProp.params.msg)
end
function DebugAction.Stop(cls, actor, actProp, success)
    actor:logInfo("DebugAction Stop %s", actProp.params.msg)
end
function DebugAction.Pause(cls, actor, actProp)
    actor:logInfo("DebugAction Pause %s", actProp.params.msg)
end
function DebugAction.Resume(cls, actor, actProp)
    actor:logInfo("DebugAction Resume %s", actProp.params.msg)
end

--endregion

return {
    ArmyMarch = ArmyMarch,
    ArmyReturn = ArmyReturn,
    WaitBattle = WaitBattle,
    ArmyOccupy = ArmyOccupy,
    ArmyDefendGrid = ArmyDefendGrid,
    ArmyDefendBuilding = ArmyDefendBuilding,
    ArmyOperateVehicle = ArmyOperateVehicle,
    ArmyThrowStone = ArmyThrowStone,
    ArmyWaitSiegeAssault = ArmyWaitSiegeAssault,
    ArmyAttackBuilding = ArmyAttackBuilding,
    ArmyBreakDurability = ArmyBreakDurability,
    ArmySingStrategy = ArmySingStrategy,
    ArmyStrategySha = ArmyStrategySha,
    ArmyStrategyShan = ArmyStrategyShan,
    ArmyWuZhongShengYou = ArmyWuZhongShengYou,
    ArmyStrategyWuGuFengDeng = ArmyStrategyWuGuFengDeng,
    ArmyStrategyKaiKen = ArmyStrategyKaiKen,
    ArmyStrategyStartJiShenJiDian = ArmyStrategyStartJiShenJiDian,
    ArmyStrategyJoinJiShenJiDian = ArmyStrategyJoinJiShenJiDian,
    DebugAction = DebugAction,
}