{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "115"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}}}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "30"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "0.18+0.02*lv"}, "value": {"Type": "number", "Value": "0"}}}, "3": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "4": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "string", "Value": ""}, "targetIds": [], "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "2"}, "sourceId": {"Type": "string", "Value": ""}, "tacticId": {"Type": "string", "Value": ""}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "damageHeroIds": [], "damagePackageIds": [], "missHeroIds": []}}, "5": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "BlackboardValue": "DamageValue"}, "formula": {"Type": "string", "Value": ""}, "formulaItems": [[{"Type": "string", "Value": "a*0.8"}, {"Type": "number", "Value": 0.0}, {"Type": "boolean", "Value": false}]], "list": [], "vlist": [], "leftBoundary": {"Type": "number", "Value": "0"}, "noLeftBoundary": {"Type": "boolean", "Value": "False"}, "noRightBoundary": {"Type": "boolean", "Value": "True"}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "6": {"Type": "RandomNode", "Field": {"randomType": {"Type": "number", "Value": "1"}, "useFormula": {"Type": "boolean", "Value": "True"}, "probability": {"Type": "number", "Value": "0"}, "probabilityFormula": {"Type": "string", "Value": "0.6"}, "record": {"Type": "boolean", "Value": "False"}, "integer": {"Type": "boolean", "Value": "False"}, "min": {"Type": "number", "Value": "0"}, "max": {"Type": "number", "Value": "0"}, "minFormula": {"Type": "string", "Value": ""}, "maxFormula": {"Type": "string", "Value": ""}, "numberResult": {"Type": "number", "Value": "0"}, "byWeight": {"Type": "boolean", "Value": "False"}, "idList": [], "weights": [], "count": {"Type": "number", "Value": "1"}, "result": []}}}, "Links": {"0": {"onLayerIncreased": ["1.prev"], "AfterDodgeSuccessNode": ["3.prev"]}, "3": {"next": ["6.prev"]}, "6": {"next": ["4.prev"]}}, "DataFlows": {"3": {"targetIds": ["4.targetIds"]}, "5": {"result": ["4.damageInputString"]}}}