{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"consts": {"extraValue1": {"VariableRangeType": 1, "ResetOnExecute": false, "Type": "number", "Value": 0.0}}, "event1": {"Type": "number", "Value": "5"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "10"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "-0.07"}, "value": {"Type": "number", "Value": "-0.07"}}}, "3": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "5": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "BlackboardValue": "Round"}, "op": {"Type": "number", "Value": "5"}, "number2": {"Type": "number", "Value": "2"}}}, "6": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "16"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": "-0.07"}, "value": {"Type": "number", "Value": "-0.07"}}}, "7": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "False"}, "tacticId": {"Type": "number", "Value": "0"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "True"}, "armyTypeHeroId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "armyTypes": [{"Type": "number", "Value": "1"}]}}}, "Links": {"0": {"RoundPrepareNode": ["7.prev"]}, "1": {"next": ["6.prev"]}, "3": {"next": ["1.prev"]}, "5": {"next": ["3.prev"]}, "7": {"next": ["5.prev"]}}, "DataFlows": {}}