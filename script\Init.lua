
-- local success, result = pcall(function ()
-- 		package.cpath = package.cpath .. ';C:/Users/<USER>/.vscode/extensions/tangzx.emmylua-0.8.20-win32-x64/debugger/emmy/windows/x86/?.dll'
-- 		local dbg = require('emmy_core')
-- 		dbg.tcpConnect('localhost', 9966)
-- 	end )

local scriptPath = EZE.getScriptPath()
package.path = package.path .. ";" .. scriptPath .. "/?.lua"
package.path = package.path .. ";" .. scriptPath .. "/Libs/?.lua"
local ext = package.cpath:match("%.([dll|so|dylib]+)") or "so"
package.cpath = package.cpath .. ";" .. scriptPath .. "/Libs/?." .. ext

-- chapter服务端调试开关
CHAPTER_DEBUG = true

require "Common.XRequire"
DeclareAndSetGlobal("IsServer", true)
xrequire("Common.DefineClass")
xrequire("Utils.EntityEventApp")

-- init EZEngine
xrequire "Framework.InitEZ"
local Config = xrequire(EZFPath .. ".Utils.Config")
DeclareAndSetGlobal("clusterId", Config.getNodeConfig("cluster"))
xrequire("Utils.TelnetShortcuts")

-- init DevLog
local DevLoggerEnv = xrequire("Common.Utils.DevLogger")
DevLoggerEnv.InitFromConfig()

-- declare global env
xrequire("Common.GameCommon")
require("Common.Utilities")
xrequire("Common.Utils.TextUtils").LoadTableText()
require("Common.Utils.Codec")
require ("Common.Utils.DebugUtil")
xrequire("Utils.EntityUtils")
xrequire("Utils.TableChecker").CheckTable()

-- init global callback
xrequire "Bootstrap.GlobalCallbackApp"

-- entities
xrequire "Entities.Account"
xrequire "Entities.Avatar"
xrequire "Entities.WorldAvatar"
xrequire "Services.RoleService"
xrequire "Services.BattleService"
xrequire "Services.WorldService"
xrequire "Services.GidService"
xrequire "Services.ActorCallerService"
xrequire "Services.NaviService"
xrequire "Services.AllyService"
xrequire "Services.AmbitionsService"
xrequire "Entities.Ally.Ally"

-- world entities
xrequire "Entities.World.World"
xrequire "Entities.World.WorldSpace"
xrequire "Entities.World.Army"
xrequire "Entities.World.City"
xrequire "Entities.World.Chunk"
xrequire "Entities.World.MainCity"
xrequire "Entities.World.JiShenJiDianActor"

-- test entities
xrequire "Test.NaviTest"

local Switches = xrequire("Common.Switches")
if not Switches.IsRelease then
    local ErrorUtils = xrequire("Utils.ErrorUtils")
    ErrorUtils.DbgHookErrorAndBoardcastClients()
end
