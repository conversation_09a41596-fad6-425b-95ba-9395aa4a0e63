jit.off()
-- local success, result = pcall(function ()
-- 		package.cpath = package.cpath .. ';C:/Users/<USER>/.vscode/extensions/tangzx.emmylua-0.8.20-win32-x64/debugger/emmy/windows/x86/?.dll'
-- 		local dbg = require('emmy_core')
-- 		dbg.tcpConnect('localhost', 9966)
-- 	end )

local scriptPath = EZE.getScriptPath()
package.path = package.path .. ";" .. scriptPath .. "/?.lua"
package.path = package.path .. ";" .. scriptPath .. "/Libs/?.lua"
local ext = package.cpath:match("%.([dll|so|dylib]+)") or "so"
package.cpath = package.cpath .. ";" .. scriptPath .. "/Libs/?." .. ext

-- chapter服务端调试开关
CHAPTER_DEBUG = true

require "Common.XRequire"
DeclareGlobal("IsServer"); _G.IsServer = true
DeclareGlobal("IsStress"); _G.IsStress = LuaNodeConfig.getConfig("stress")
DeclareGlobal("IsRelease"); _G.IsRelease = LuaNodeConfig.getConfig("release")
xrequire("Common.DefineClass")
xrequire("Utils.EntityEventApp")

-- init EZEngine
xrequire "Framework.InitEZ"
xrequire "Utils.ScheduleRecTag"
local Config = xrequire(EZFPath .. ".Utils.Config")
DeclareGlobal("clusterId"); _G.clusterId = Config.getNodeConfig("cluster")
xrequire("Utils.TelnetShortcuts")

-- init DevLog
local DevLoggerEnv = xrequire("Common.Utils.DevLogger")
DevLoggerEnv.InitFromConfig()

-- declare global env
xrequire("Common.GameCommon")
local levelId = LuaNodeConfig.getConfig("fyc.level_id")
GameCommon.TableDataManager:SetLevelId(levelId)
EZGlobal.LEVEL_INFO = GameCommon.TableDataManager:GetMapLevel(levelId)
xrequire("Common.Utilities")
xrequire("Common.Utils.TextUtils")
xrequire("Common.Utils.Codec")
xrequire ("Common.Utils.DebugUtil")
xrequire("Utils.EntityUtils")
xrequire("Utils.TableChecker").CheckTable()

-- init global callback
xrequire "Bootstrap.GlobalCallbackApp"

-- entities
xrequire "Entities.Account"
xrequire "Entities.Avatar"
xrequire "Entities.WorldAvatar"
xrequire "Services.RoleService"
xrequire "Services.BattleService"
xrequire "Services.WorldService"
xrequire "Services.ActorCallerService"
xrequire "Services.NaviService"
xrequire "Services.AllyService"
xrequire "Services.AmbitionsService"
xrequire "Entities.Ally"
xrequire "Services.AvatarInfoService"
xrequire "Services.MailService"
xrequire "Services.ClanService"
xrequire "Services.ClanInviteService"
xrequire "Entities.Clan"
xrequire "Services.LogicService"
xrequire "Entities.LogicProxy"
xrequire "Services.WorldChannelService"
xrequire "Services.PayService"
xrequire "Entities.PrefectureSync"
xrequire "Services.MonitorService"
xrequire "Entities.SiegeBoard"
xrequire "Entities.SiegeDetailBoard"

-- world entities
xrequire "WorldActors.World"
xrequire "WorldActors.WorldSpace"
xrequire "Utils.ActorCollector"

-- test entities
xrequire "Test.NaviTest"

-- balance test
xrequire "Entities.BalanceBattle"
xrequire "Services.BalanceBattleService"
xrequire "Services.BalanceRunnerService"

local Switches = xrequire("Common.Switches")
if not Switches.IsRelease then
    local ErrorUtils = xrequire("Utils.ErrorUtils")
    local GlobalEntityMgr = xrequire("Framework.Utils.GlobalEntityMgr")
    GlobalEntityMgr.instance:registerReadyCb("RoleService", "HookError", function()
        ErrorUtils.DbgHookErrorAndBoardcastClients()
        return false
    end)
end
