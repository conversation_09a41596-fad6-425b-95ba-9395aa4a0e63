﻿local TimeEnv = xrequire("Framework.Utils.Time")

function GetServerNow()
    return TimeEnv.getServerNow()
end

function GetServerNowInt()
    return TimeEnv.getServerNowInt()
end

function GetDay(t)
    t = t or GetServerNowInt()
    return math.floor(t / 86400)
end

function GetWeek(t)
    t = t or GetServerNowInt()
    return math.floor(t / ( 86400 * 7 ))
end

function IsSameDay(t1, t2)
    return GetDay(t1) == GetDay(t2)
end

function IsSameWeek(t1, t2)
    return GetWeek(t1) == GetWeek(t2)
end

return {
    GetServerNow = GetServerNow,
    GetServerNowInt = GetServerNowInt,
    GetDay = GetDay,
    GetWeek = GetWeek,
    IsSameDay = IsSameDay,
    IsSameWeek = IsSameWeek,
}