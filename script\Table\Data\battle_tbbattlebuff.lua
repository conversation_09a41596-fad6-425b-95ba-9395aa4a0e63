-- Excel: 模拟三国杀/Buff列表@战斗buff.xlsx
-- Table Type: 映射表
-- Index: id

return
{
[10001] = {["adjunct"]=false,["can_stack"]=true,["desc"]="负面状态，大回合结束时受到灼烧层数*50%的伤害（受buff施加者 施加时的智力计算）",["dispellable"]=true,["effect_id"]=10001,["effect_socket"]="Foot",["groupTypeList"]={},["icon_path"]="icon-buff4",["id"]=10001,["is_debuff"]=true,["json_file"]="buff_10001",["jump_on_add"]="添加灼烧",["jump_on_remove"]="移除灼烧",["max_combat"]=0,["max_stack_count"]=10,["name"]="灼烧",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=1,["total_round"]=3,["type"]=0,},
[10002] = {["adjunct"]=false,["can_stack"]=false,["desc"]="负面状态，无法使用主动战法",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff2",["id"]=10002,["is_debuff"]=true,["json_file"]="buff_10002",["jump_on_add"]="添加乐",["jump_on_remove"]="移除乐",["max_combat"]=0,["max_stack_count"]=1,["name"]="计穷",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10003] = {["adjunct"]=false,["can_stack"]=false,["desc"]="无法普攻",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff3",["id"]=10003,["is_debuff"]=true,["json_file"]="buff_10003",["jump_on_add"]="添加缴械",["jump_on_remove"]="移除缴械",["max_combat"]=0,["max_stack_count"]=1,["name"]="缴械",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10004] = {["adjunct"]=false,["can_stack"]=false,["desc"]="无法行动",["dispellable"]=true,["effect_id"]=10010,["effect_socket"]="HpPoint",["groupTypeList"]={},["icon_path"]="icon-buff1",["id"]=10004,["is_debuff"]=true,["json_file"]="buff_10004",["jump_on_add"]="添加震慑",["jump_on_remove"]="移除震慑",["max_combat"]=0,["max_stack_count"]=1,["name"]="震慑",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10005] = {["adjunct"]=false,["can_stack"]=false,["desc"]="负面状态，统率-50",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff6",["id"]=10005,["is_debuff"]=true,["json_file"]="buff_10005",["jump_on_add"]="添加水攻",["jump_on_remove"]="移除水攻",["max_combat"]=0,["max_stack_count"]=1,["name"]="水攻",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10006] = {["adjunct"]=false,["can_stack"]=false,["desc"]="负面状态，造成伤害减少70%",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=10006,["is_debuff"]=true,["json_file"]="buff_10006",["jump_on_add"]="添加粮",["jump_on_remove"]="移除粮",["max_combat"]=0,["max_stack_count"]=1,["name"]="虚弱",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10007] = {["adjunct"]=false,["can_stack"]=false,["desc"]="负面状态，受到治疗效果降低70%",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=10007,["is_debuff"]=true,["json_file"]="buff_10007",["jump_on_add"]="添加粮",["jump_on_remove"]="移除粮",["max_combat"]=0,["max_stack_count"]=1,["name"]="断粮",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10008] = {["adjunct"]=false,["can_stack"]=false,["desc"]="强制攻击目标",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=10008,["is_debuff"]=true,["json_file"]="buff_10008",["jump_on_add"]="添加嘲讽",["jump_on_remove"]="移除嘲讽",["max_combat"]=0,["max_stack_count"]=1,["name"]="嘲讽",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10009] = {["adjunct"]=false,["can_stack"]=true,["desc"]="受到的下一次伤害减少70%-100%  可叠加2层",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=10009,["is_debuff"]=false,["json_file"]="buff_10009",["jump_on_add"]="添加抵御",["jump_on_remove"]="移除抵御",["max_combat"]=0,["max_stack_count"]=2,["name"]="抵御",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=1,["total_round"]=8,["type"]=0,},
[10010] = {["adjunct"]=false,["can_stack"]=true,["desc"]="下一次主动或追击战法目标优先选择Buff来源且造成伤害降低35%",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=10010,["is_debuff"]=true,["json_file"]="buff_10010",["jump_on_add"]="添加威慑",["jump_on_remove"]="移除威慑",["max_combat"]=0,["max_stack_count"]=1,["name"]="威慑",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=1,["total_round"]=1,["type"]=0,},
[12333] = {["adjunct"]=false,["can_stack"]=false,["desc"]="在考虑额外军职时视为主公",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=12333,["is_debuff"]=true,["json_file"]="buff_12333",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="额外军职-主公",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[20001] = {["adjunct"]=false,["can_stack"]=false,["desc"]="伤害分摊",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="icon-buff5",["id"]=20001,["is_debuff"]=false,["json_file"]="buff_20001",["jump_on_add"]="免死",["jump_on_remove"]="移除粮",["max_combat"]=0,["max_stack_count"]=1,["name"]="不屈",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100000] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加武力",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100000,["is_debuff"]=false,["json_file"]="buff_100000",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加武力",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100001] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加智力",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100001,["is_debuff"]=false,["json_file"]="buff_100001",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加智力",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100002] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加统帅",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100002,["is_debuff"]=false,["json_file"]="buff_100002",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加统帅",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100003] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加速度",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100003,["is_debuff"]=false,["json_file"]="buff_100003",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加速度",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100004] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加盾兵增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100004,["is_debuff"]=false,["json_file"]="buff_100004",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加盾兵增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100005] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加弓兵增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100005,["is_debuff"]=false,["json_file"]="buff_100005",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加弓兵增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100006] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加骑兵增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100006,["is_debuff"]=false,["json_file"]="buff_100006",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加骑兵增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100007] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技增加枪兵增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100007,["is_debuff"]=false,["json_file"]="buff_100007",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技增加枪兵增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100008] = {["adjunct"]=false,["can_stack"]=false,["desc"]="魏国武将增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100008,["is_debuff"]=false,["json_file"]="buff_100008",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="魏国武将增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100009] = {["adjunct"]=false,["can_stack"]=false,["desc"]="蜀国武将增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100009,["is_debuff"]=false,["json_file"]="buff_100009",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="蜀国武将增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100010] = {["adjunct"]=false,["can_stack"]=false,["desc"]="吴国武将增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100010,["is_debuff"]=false,["json_file"]="buff_100010",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="吴国武将增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100011] = {["adjunct"]=false,["can_stack"]=false,["desc"]="群国武将增减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100011,["is_debuff"]=false,["json_file"]="buff_100011",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="群国武将增减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[100012] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技盾兵十级特殊效果",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100012,["is_debuff"]=false,["json_file"]="buff_100012",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="第2回合，我方盾兵受到伤害降低7%",["overlay_rule"]=2,["record"]=true,["remove_timing"]=2,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[100013] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技弓兵十级特殊效果",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100013,["is_debuff"]=false,["json_file"]="buff_100013",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="第3回合，我方弓兵会心和奇谋几率提升10%",["overlay_rule"]=2,["record"]=true,["remove_timing"]=2,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=3,["type"]=0,},
[100014] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技骑兵十级特殊效果",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100014,["is_debuff"]=false,["json_file"]="buff_100014",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="首回合，我方骑兵规避率提升5%",["overlay_rule"]=2,["record"]=true,["remove_timing"]=2,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[100015] = {["adjunct"]=false,["can_stack"]=false,["desc"]="城建科技枪兵十级特殊效果",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=100015,["is_debuff"]=false,["json_file"]="buff_100015",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="第4回合，我方枪兵破甲和看破提升8%",["overlay_rule"]=2,["record"]=true,["remove_timing"]=2,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=4,["type"]=0,},
[200001] = {["adjunct"]=false,["can_stack"]=false,["desc"]="策牌-杀",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=200001,["is_debuff"]=false,["json_file"]="",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=1,["max_stack_count"]=1,["name"]="策牌-杀",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=4,["type"]=0,},
[200002] = {["adjunct"]=false,["can_stack"]=false,["desc"]="策牌-闪",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=200002,["is_debuff"]=false,["json_file"]="",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=1,["max_stack_count"]=1,["name"]="策牌-闪",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=4,["type"]=0,},
[999001] = {["adjunct"]=false,["can_stack"]=false,["desc"]="兵种适应性属性加成",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=999001,["is_debuff"]=false,["json_file"]="buff_999001",["jump_on_add"]="兵种适应性",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="兵种适应性",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[7980001] = {["adjunct"]=false,["can_stack"]=false,["desc"]="加10点武力",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=7980001,["is_debuff"]=false,["json_file"]="buff_7980001",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="装备词条加武力",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[7980002] = {["adjunct"]=false,["can_stack"]=false,["desc"]="仅关羽发动率增加",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=7980002,["is_debuff"]=false,["json_file"]="buff_7980002",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="装备词条武圣",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[9990021] = {["adjunct"]=false,["can_stack"]=false,["desc"]="第2回合，我方盾兵受到伤害降低7%",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=9990021,["is_debuff"]=false,["json_file"]="buff_9990021",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="城建科技盾兵十级特殊效果",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10100101] = {["adjunct"]=false,["can_stack"]=false,["desc"]="忠勇加属性",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100101,["is_debuff"]=false,["json_file"]="buff_10100101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="忠勇[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=2,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10100201] = {["adjunct"]=false,["can_stack"]=false,["desc"]="援护减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100201,["is_debuff"]=false,["json_file"]="buff_10100201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="援护[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=3,["type"]=0,},
[10100301] = {["adjunct"]=false,["can_stack"]=true,["desc"]="亲民减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100301,["is_debuff"]=false,["json_file"]="buff_10100301",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=4,["name"]="亲民减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10100302] = {["adjunct"]=false,["can_stack"]=false,["desc"]="亲民减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100302,["is_debuff"]=false,["json_file"]="buff_10100302",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="亲民[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10100401] = {["adjunct"]=false,["can_stack"]=false,["desc"]="五禽戏[预备]",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100401,["is_debuff"]=false,["json_file"]="buff_10100401",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="五禽戏[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10100402] = {["adjunct"]=false,["can_stack"]=false,["desc"]="五禽戏减伤",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100402,["is_debuff"]=false,["json_file"]="buff_10100402",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="五禽戏减伤",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10100501] = {["adjunct"]=false,["can_stack"]=false,["desc"]="请君入瓮[预备]",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100501,["is_debuff"]=false,["json_file"]="buff_10100501",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="请君入瓮[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10100502] = {["adjunct"]=false,["can_stack"]=false,["desc"]="请君入瓮控制",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10100502,["is_debuff"]=false,["json_file"]="buff_10100502",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="请君入瓮",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10200301] = {["adjunct"]=false,["can_stack"]=true,["desc"]="散谣减属性",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10200301,["is_debuff"]=true,["json_file"]="buff_10200301",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=2,["name"]="散谣[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10200601] = {["adjunct"]=false,["can_stack"]=false,["desc"]="扬威加暴击",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10200601,["is_debuff"]=false,["json_file"]="buff_10200601",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="扬威[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10200801] = {["adjunct"]=false,["can_stack"]=false,["desc"]="自愈回血",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10200801,["is_debuff"]=false,["json_file"]="buff_10200801",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="自愈",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10200901] = {["adjunct"]=false,["can_stack"]=false,["desc"]="洛水惊鸿​",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10200901,["is_debuff"]=false,["json_file"]="buff_10200901",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="洛水惊鸿​",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10300101] = {["adjunct"]=false,["can_stack"]=false,["desc"]="短兵减统率",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10300101,["is_debuff"]=true,["json_file"]="buff_10300101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="短兵[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[10400101] = {["adjunct"]=false,["can_stack"]=false,["desc"]="血战加连击",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10400101,["is_debuff"]=false,["json_file"]="buff_10400101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="血战[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10400102] = {["adjunct"]=false,["can_stack"]=true,["desc"]="血战每回合减连击",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10400102,["is_debuff"]=false,["json_file"]="buff_10400102",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=10,["name"]="血战衰减[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10500101] = {["adjunct"]=false,["can_stack"]=false,["desc"]="检测buff",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10500101,["is_debuff"]=false,["json_file"]="buff_10500101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="元戎弩兵[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=4,["type"]=0,},
[10500102] = {["adjunct"]=false,["can_stack"]=false,["desc"]="加暴击",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10500102,["is_debuff"]=false,["json_file"]="buff_10500102",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="元戎弩兵-暴击[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[10500201] = {["adjunct"]=false,["can_stack"]=false,["desc"]="闪避提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10500201,["is_debuff"]=false,["json_file"]="buff_10500201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="闪避增加[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10500202] = {["adjunct"]=false,["can_stack"]=false,["desc"]="闪避提升（前3回合）",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10500202,["is_debuff"]=false,["json_file"]="buff_10500202",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="前三回合闪避增加[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=3,["type"]=0,},
[10600101] = {["adjunct"]=false,["can_stack"]=false,["desc"]="虎翼阵先锋增伤",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10600101,["is_debuff"]=false,["json_file"]="buff_10600101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="虎翼阵[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=3,["type"]=0,},
[10600102] = {["adjunct"]=false,["can_stack"]=false,["desc"]="虎翼阵主将减伤",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10600102,["is_debuff"]=false,["json_file"]="buff_10600102",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="虎翼阵[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=3,["type"]=0,},
[10600201] = {["adjunct"]=false,["can_stack"]=false,["desc"]="发动率提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10600201,["is_debuff"]=false,["json_file"]="buff_10600201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="胭脂阵发动率增加[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10600202] = {["adjunct"]=false,["can_stack"]=false,["desc"]="胭脂阵治疗",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10600202,["is_debuff"]=false,["json_file"]="buff_10600202",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="胭脂阵回血[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10600203] = {["adjunct"]=false,["can_stack"]=false,["desc"]="胭脂阵先锋回血",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10600203,["is_debuff"]=false,["json_file"]="buff_10600203",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="胭脂阵先锋回血[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[10600204] = {["adjunct"]=false,["can_stack"]=false,["desc"]="胭脂阵属性增加",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=10600204,["is_debuff"]=false,["json_file"]="buff_10600204",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="胭脂阵属性增加[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=false,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100101] = {["adjunct"]=false,["can_stack"]=true,["desc"]="仁德加属性",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100101,["is_debuff"]=false,["json_file"]="buff_11100101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=2,["name"]="仁德[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11100102] = {["adjunct"]=false,["can_stack"]=false,["desc"]="邀请普攻",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100102,["is_debuff"]=false,["json_file"]="buff_11100102",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="激将[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100201] = {["adjunct"]=false,["can_stack"]=false,["desc"]="破军预备",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100201,["is_debuff"]=false,["json_file"]="buff_11100201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="破军[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100302] = {["adjunct"]=false,["can_stack"]=true,["desc"]="奸雄减属性",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100302,["is_debuff"]=false,["json_file"]="buff_11100302",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=2,["name"]="奸雄",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11100303] = {["adjunct"]=false,["can_stack"]=true,["desc"]="奸雄加属性",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100303,["is_debuff"]=false,["json_file"]="buff_11100303",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=2,["name"]="奸雄",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11100401] = {["adjunct"]=false,["can_stack"]=false,["desc"]="鬼才监听",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100401,["is_debuff"]=false,["json_file"]="buff_11100401",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="鬼才[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100402] = {["adjunct"]=false,["can_stack"]=true,["desc"]="鬼才加属性（非军）",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100402,["is_debuff"]=false,["json_file"]="buff_11100402",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=8,["name"]="鬼才[属性]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100403] = {["adjunct"]=false,["can_stack"]=true,["desc"]="鬼才加属性（军）",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100403,["is_debuff"]=false,["json_file"]="buff_11100403",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=12,["name"]="鬼才[属性]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100501] = {["adjunct"]=false,["can_stack"]=false,["desc"]="黄天[预备]",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100501,["is_debuff"]=false,["json_file"]="buff_11100501",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="黄天[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100502] = {["adjunct"]=false,["can_stack"]=false,["desc"]="雷击[预备]",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100502,["is_debuff"]=false,["json_file"]="buff_11100502",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="雷击[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11100503] = {["adjunct"]=false,["can_stack"]=true,["desc"]="黄天[预备]",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11100503,["is_debuff"]=false,["json_file"]="buff_11100503",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=2,["name"]="黄天[预备]",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11200101] = {["adjunct"]=false,["can_stack"]=true,["desc"]="概率降低",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11200101,["is_debuff"]=false,["json_file"]="buff_11200101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=5,["name"]="洛神[预备]",["overlay_rule"]=2,["record"]=false,["remove_timing"]=2,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11200201] = {["adjunct"]=false,["can_stack"]=false,["desc"]="闪避提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11200201,["is_debuff"]=false,["json_file"]="buff_11200201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="流离加闪避",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11200301] = {["adjunct"]=false,["can_stack"]=false,["desc"]="奇才BUFF",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11200301,["is_debuff"]=false,["json_file"]="buff_11200301",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="集智",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11200302] = {["adjunct"]=false,["can_stack"]=false,["desc"]="闪避提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11200302,["is_debuff"]=false,["json_file"]="buff_11200302",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=99,["name"]="奇才概率降低",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11200501] = {["adjunct"]=false,["can_stack"]=false,["desc"]="追击战法发动率提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11200501,["is_debuff"]=false,["json_file"]="buff_11200501",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="离间",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11300101] = {["adjunct"]=false,["can_stack"]=true,["desc"]="奇袭",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11300101,["is_debuff"]=false,["json_file"]="buff_11300101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="奇袭",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11300102] = {["adjunct"]=false,["can_stack"]=false,["desc"]="奇袭",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11300102,["is_debuff"]=true,["json_file"]="buff_11300102",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="奇袭-统率",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11300201] = {["adjunct"]=false,["can_stack"]=false,["desc"]="闪避提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11300201,["is_debuff"]=false,["json_file"]="buff_11300201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=10,["name"]="弓腰姬",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11400101] = {["adjunct"]=false,["can_stack"]=false,["desc"]="自己回合普攻追击",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400101,["is_debuff"]=false,["json_file"]="buff_11400101",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="咆哮[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11400102] = {["adjunct"]=false,["can_stack"]=false,["desc"]="兵刃伤害提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400102,["is_debuff"]=false,["json_file"]="buff_11400102",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="咆哮[预备]",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11400103] = {["adjunct"]=false,["can_stack"]=true,["desc"]="兵刃伤害提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400103,["is_debuff"]=false,["json_file"]="buff_11400103",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=9,["name"]="咆哮[预备]",["overlay_rule"]=2,["record"]=false,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11400201] = {["adjunct"]=false,["can_stack"]=true,["desc"]="破甲提升",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400201,["is_debuff"]=false,["json_file"]="buff_11400201",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=4,["name"]="武圣[破甲]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11400202] = {["adjunct"]=false,["can_stack"]=true,["desc"]="武圣-武力 主动发动率提升",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400202,["is_debuff"]=false,["json_file"]="buff_11400202",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=4,["name"]="武魂[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=1,["type"]=0,},
[11400203] = {["adjunct"]=false,["can_stack"]=false,["desc"]="水攻buff",["dispellable"]=true,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400203,["is_debuff"]=true,["json_file"]="buff_11400203",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="武圣[水攻]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=2,["type"]=0,},
[11400204] = {["adjunct"]=false,["can_stack"]=false,["desc"]="武圣预备",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400204,["is_debuff"]=false,["json_file"]="buff_11400204",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="武圣[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11400301] = {["adjunct"]=false,["can_stack"]=true,["desc"]="不屈预备",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400301,["is_debuff"]=false,["json_file"]="buff_11400301",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=13,["name"]="不屈[预备]",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=false,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
[11400501] = {["adjunct"]=false,["can_stack"]=false,["desc"]="闪避提升",["dispellable"]=false,["effect_id"]=0,["effect_socket"]="",["groupTypeList"]={},["icon_path"]="",["id"]=11400501,["is_debuff"]=false,["json_file"]="buff_11400501",["jump_on_add"]="",["jump_on_remove"]="",["max_combat"]=0,["max_stack_count"]=1,["name"]="受治疗提升",["overlay_rule"]=2,["record"]=true,["remove_timing"]=1,["show"]=true,["sourceDeadClear"]=true,["stack_rule"]=2,["total_round"]=9,["type"]=0,},
}