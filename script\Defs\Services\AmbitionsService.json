{"implements": ["ScheduleComp", "S1IdentityComp", "RankHostComp"], "properties": [{"name": "processId", "type": "int", "default": 0, "persistent": true, "flag": "ALL_CLIENTS"}, {"name": "targets", "type": "AmbitionTarget", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "views", "type": "AmbitionTargetView", "persistent": true, "flag": "ALL_CLIENTS"}, {"name": "allyRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "prosperityRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "meritRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "meritWeekRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "firstOccupyRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "clanProsperityRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "clanMeritRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "clanMeritWeekRank", "type": "Rank", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "firstOccupyRankMaxLevel", "type": "int", "persistent": true, "flag": "ALL_CLIENTS"}, {"name": "cityOccupation", "type": "CityOccupation", "persistent": true, "flag": "ALL_CLIENTS"}, {"name": "allyBrief", "type": "AllyBrief", "persistent": true, "flag": "ALL_CLIENTS"}, {"name": "middleRankSettle", "type": "RankSettleResult", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "finalRankSettle", "type": "RankSettleResult", "persistent": true, "flag": "SERVER_ONLY"}, {"name": "tmpSlgSummary", "type": "TmpSlgSummary", "persistent": false, "flag": "ALL_CLIENTS"}], "server_methods": [{"name": "ObserveAmbitions", "args": ["string"]}, {"name": "UnobserveAmbitions", "args": ["string"]}, {"name": "OnOccupyLand", "args": ["int"]}, {"name": "UpdateCityOccupier", "args": ["CityId", "AllyId"]}, {"name": "SyncAllyInfo", "args": ["AllyId", "AllyBriefInfo"]}, {"name": "OnGetRankSettleData", "args": ["int", "bool", "int", "any"]}, {"name": "GetRankSettleData", "args": ["int", "AvatarGid", "Callback"]}, {"name": "gmSetAmbitionsTaskProgress", "args": ["int", "int"]}, {"name": "UpdatePersonalProsperity", "args": ["AvatarGid", "Career", "int"]}, {"name": "UpdatePersonalMerit", "args": ["AvatarGid", "Career", "int", "int", "int"]}, {"name": "OnFirstOccupiedLand", "args": ["AvatarGid", "int", "OccupyBattleRecord"]}, {"name": "OnClanInfluenceChanged", "args": ["ClanId", "int"]}, {"name": "OnClanMeritChanged", "args": ["ClanId", "int", "int", "int"]}, {"name": "OnClanDismissed", "args": ["ClanId"]}, {"name": "OnAllyDismissed", "args": ["AllyId"]}]}