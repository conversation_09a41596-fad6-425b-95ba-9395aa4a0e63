----------------------- WARNING -----------------------
---     目前coroutine有风险；游戏正式模块请勿使用      ---
----------------------- WARNING -----------------------

---@alias AeId integer | string
---@alias HandleResume fun(ar?: AsyncResult, ...): AsyncResult, ... @自定义处理resume返回值的方法
---@alias HookResume fun(ar?: AsyncResult, ...): AsyncResult, ... @自定义处理resume参数的方法
---@alias AsyncFunc fun(...):...

---约定的异步返回状态
---nil：默认情况
---ERROR：协程执行过程中出现了未处理的异常，需要注意逻辑层的容错、回退等处理
---TIMEOUT：异步处理出现超时，目前只用作resume的参数
---IDLE：上一个任务执行完成；配合协程复用模式使用，表示该协程已被回收并将等待下一次任务分配
---@alias AsyncResult nil | "'ERROR'" | "'TIMEOUT'" | "'IDLE'"


---@class AsyncExecutor
---@field aeId AeId
---@field co thread
---@field new fun(aeId: AeId, fun: AsyncFunc, handleResume: HandleResume, hookResume: HookResume): AsyncExecutor
---@field doResume fun(ar?: AsyncResult, ...): AsyncResult, ...
---@field asyncTimerId TimerId
AsyncExecutor = DefineClass("AsyncExecutor")

---@param aeId AeId
---@param fun AsyncFunc
---@param handleResume HandleResume
---@param hookResume HookResume
function AsyncExecutor:ctor(aeId, fun, handleResume, hookResume)
    self.aeId = aeId
    self.co = coroutine.create(function(ar, ...)
        return fun(...)
    end)
    if handleResume and hookResume then
        self.doResume = function(...) handleResume(self:resumeWarpper(coroutine.resume(self.co, hookResume(...)))) end
    elseif handleResume then
        self.doResume = function(...) handleResume(self:resumeWarpper(coroutine.resume(self.co, ...))) end
    elseif hookResume then
        self.doResume = function(...) self:resumeWarpper(coroutine.resume(self.co, hookResume(...))) end
    else
        self.doResume = function(...) self:resumeWarpper(coroutine.resume(self.co, ...)) end
    end
end

function AsyncExecutor:clear()
    if self.asyncTimerId then
        AsyncExecutorMgr.timer:delTimer(self.asyncTimerId)
        self.asyncTimerId = nil
    end
end

---@return nil | string
function AsyncExecutor:status()
    return coroutine.status(self.co)
end

---@param ar? AsyncResult
---@param timeout? number @在yield的情况下生效，默认10秒，0表示没有超时限制
---@param ... any
---@return AsyncResult | nil, ...
function AsyncExecutor:yield(ar, timeout, ...)
    timeout = timeout or 10
    if timeout > 0 then
        self.asyncTimerId = AsyncExecutorMgr.timer:addTimer(timeout, 0, function(...)
            self.asyncTimerId = nil
            self:resume("TIMEOUT")
        end)
    end
    return coroutine.yield(ar, ...)
end

---@param ar? AsyncResult
---@return AsyncResult, ...
function AsyncExecutor:resume(ar, ...)
    assert(AsyncExecutor.isMainThread(), string.format("current running coroutine(%s %s) is not nil", coroutine.running()))
    if self.asyncTimerId then
        AsyncExecutorMgr.timer:delTimer(self.asyncTimerId)
        self.asyncTimerId = nil
    end
    return self.doResume(ar, ...)
end

---@param res boolean
---@param ar? AsyncResult | any @可能是错误信息
---@return AsyncResult, ...
function AsyncExecutor:resumeWarpper(res, ar, ...)
    if not res then
        local msg = string.format("AsyncExecutor[%s] resume failed: %s", self.aeId, ar)
        local trace = EZE.traceback(self.co, string.format("The error coroutine(%s.%s):", self.aeId, self.co))
        ErrorLog(EZE.traceback(string.format("%s\n%s\nThe main thread(%s):", msg, trace, coroutine.running()), 1))
        ar = "ERROR"
    end
    return ar, ...
end

function AsyncExecutor.isMainThread()
    local thread, main = coroutine.running()
    return main
end

-- for emmylua
---@class AsyncExecutorEnv
local AsyncExecutorEnv = {
    AsyncExecutor=AsyncExecutor,
}
return AsyncExecutorEnv
