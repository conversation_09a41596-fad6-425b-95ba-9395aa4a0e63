{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "111"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}}}, "1": {"Type": "RandomNode", "Field": {"randomType": {"Type": "number", "Value": "1"}, "useFormula": {"Type": "boolean", "Value": "False"}, "probability": {"Type": "number", "Value": "0.3"}, "probabilityFormula": {"Type": "string", "Value": ""}, "record": {"Type": "boolean", "Value": "False"}, "integer": {"Type": "boolean", "Value": "False"}, "min": {"Type": "number", "Value": "0"}, "max": {"Type": "number", "Value": "0"}, "minFormula": {"Type": "string", "Value": ""}, "maxFormula": {"Type": "string", "Value": ""}, "numberResult": {"Type": "number", "Value": "0"}, "byWeight": {"Type": "boolean", "Value": "False"}, "idList": [], "weights": [], "count": {"Type": "number", "Value": "1"}, "result": []}}, "2": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "False"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}}}, "4": {"Type": "PlainAttackNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetId": {"Type": "string", "Value": ""}, "ignoreCrowdControl": {"Type": "boolean", "Value": "False"}}}, "5": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "2"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "8": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "other2"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "仁德邀战概率，a=智力"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"AfterPlainAttackProcessNode": ["5.prev"]}, "1": {"next": ["2.prev"]}, "2": {"next": ["4.prev"]}, "5": {"next": ["8.prev"]}, "8": {"next": ["1.prev"]}}, "DataFlows": {"2": {"targetIds": ["4.sourceId"]}, "5": {"attributeValues": ["8.a"]}, "8": {"result": ["1.probability"]}}}