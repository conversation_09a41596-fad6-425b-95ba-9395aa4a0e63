local EZE = xrequire "EZE"

DeclareGlobal("DebugLog")
DeclareGlobal("InfoLog")
DeclareGlobal("WarnLog")
DeclareGlobal("ErrorLog")

_G.DebugLog = DebugLog or function(...)
    EZE.log.debug(nil, ...)
end

_G.InfoLog = InfoLog or function(...)
    EZE.log.info(nil, ...)
end

_G.WarnLog = WarnLog or function(...)
    EZE.log.warn(nil, ...)
end

_G.ErrorLog = ErrorLog or function(...)
    EZE.log.error(nil, ...)
end

lxpcall = DeclareAndSetGlobal("lxpcall", function(func, ...)
    return xpcall(func, function(errormsg)
        errormsg = EZE.traceback(errormsg, 2)
        ErrorLog(errormsg)
        return errormsg
    end, ...)
end)

DeclareAndSetGlobal("P<PERSON>", LuaFormatter.new)
