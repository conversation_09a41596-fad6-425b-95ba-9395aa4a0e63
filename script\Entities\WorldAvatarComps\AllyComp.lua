local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local GlobalDataMgrEnv = xrequire("Framework.Utils.GlobalDataMgr")
local AvatarConst = xrequire("Common.Const.AvatarConst")
local WorldCityDataEnv = xrequire("Modules.World.WorldCityData")
local CoordUtils = xrequire("Common.Utils.CoordUtils")

---@class (partial) WorldAvatar
---@class AllyComp: ComponentBase
AllyComp = DefineClass("AllyComp", ComponentBaseEnv.ComponentBase)

function AllyComp:ctor()
    self:checkSelfInAlly()
end

--region 联盟状态同步

function AllyComp:OnSyncAlly(allyId)
    assert(allyId ~= 0)
    -- 加入or创建联盟
    if self.props.allyId == allyId then
        return
    end
    if self.props.allyId ~= 0 then
        self:forceLeaveAlly()
    end
    self:logInfo("[qun] OnSyncAlly %s", allyId)
    self.props.allyId = allyId
    -- TODO(qun): 临时处理，将Avatar的出生州和联盟同步
    local globalData = GlobalDataMgrEnv.instance:get("ambitions")
    if globalData and globalData.allyBrief[allyId] then
        self.props.birthplace = globalData.allyBrief[allyId].birthplace
    end
    self:broadcastAlly()
    self:TryConnectAlly()
    self:onSetAmbitionsScore()
end

function AllyComp:OnLeaveAlly(allyId)
    -- 离开联盟
    if self.props.allyId ~= allyId then
        return
    end
    self:logInfo("[qun] OnLeaveAlly %s", allyId)
    self:TryDisconnectAlly()
    self.props.allyId = 0
    self:broadcastAlly()
end

function AllyComp:broadcastAlly()
    self:broadcastOwning("OnSyncAlly", self.props.allyId)
    self:callAvatarChunks("OnSyncRelationProps", self:GetRelationProps())
    self:serviceRpc("AvatarInfoService", "UpdateDiffAvatarInfo", self.props.gid, AvatarConst.AvatarInfoField.allyId, self.props.allyId)
end

function AllyComp:checkSelfInAlly()
    if self.props.allyId == 0 then
        return
    end
    self:callAlly(self.props.allyId, "CheckMemberInAlly", self.props.gid, self:makeCallback("OnCheckMemberInAllyCB", self.props.allyId))
end

function AllyComp:OnCheckMemberInAllyCB(allyId, isInside)
    if allyId ~= self.props.allyId then
        return
    end
    if not isInside then
        self:OnLeaveAlly(allyId)
    end
end

function AllyComp:CheckAllyMember(allyId, callback)
    if self.props.allyId ~= 0 and self.props.allyId ~= allyId then
        self:logError("[qun] CheckAllyMember failed, not in ally %s, in other ally %s", allyId, self.props.allyId)
        self:callbackRpc(callback, false)
        return
    end
    if self.props.allyId == 0 then
        self:logInfo("[qun] CheckAllyMember %s not in any ally", self.props.gid)
        self:OnSyncAlly(allyId)
    end
    self:callbackRpc(callback, true)
end

function AllyComp:TryConnectAlly()
    if self.props.allyId == 0 then
        return
    end

    local client = self:getClient()
    if not client then
        return
    end

    self:callAlly(self.props.allyId, "MemberObserve", client)
end

function AllyComp:TryDisconnectAlly(clientSessionId)
    if self.props.allyId == 0 then
        return
    end

    clientSessionId = clientSessionId or self:getClientSession()
    if not clientSessionId then
        return
    end

    self:callAlly(self.props.allyId, "MemberUnobserve", clientSessionId)
end

--endregion

--region 加入退出联盟

function AllyComp:doCreateAlly(allyName)
    if self.props.allyId ~= 0 then
        return
    end
    self:serviceRpc("AllyService", "CreateAlly", self.props.gid, allyName, self.props.birthplace, self:makeCallback("OnCreateAllyCB"))
end

function AllyComp:OnCreateAllyCB(succ)
    self:logInfo("[qun] OnCreateAllyCB %s", succ)
end

function AllyComp:doJoinAlly(allyName)
    if self.props.allyId ~= 0 then
        return
    end
    self:serviceRpc("AllyService", "JoinAlly", self.props.gid, allyName, self:makeCallback("OnJoinAllyCB"))
end

function AllyComp:OnJoinAllyCB(succ)
    self:logInfo("[qun] OnJoinAllyCB %s", succ)
end

function AllyComp:forceLeaveAlly()
    self:doLeaveAlly()
end

function AllyComp:doLeaveAlly()
    if self.props.allyId == 0 then
        return
    end
    self:callAlly(self.props.allyId, "LeaveAlly", self.props.gid, self:makeCallback("OnLeaveAllyCB"))
end

function AllyComp:OnLeaveAllyCB(succ)
    self:logInfo("[qun] OnLeaveAllyCB %s", succ)
end

--endregion

--region GM

function AllyComp:gmCreateAlly(allyName)
    self:doCreateAlly(allyName)
end

function AllyComp:gmJoinAlly(allyName)
    self:doJoinAlly(allyName)
end

function AllyComp:gmLeaveAlly()
    self:doLeaveAlly()
end

--endregion

--region 联盟资源产出

function AllyComp:OnSyncAllyCityLandProduce(adds)
    self:SetResProduceAddition(TableConst.enums.ProduceSrcType.CityLand, adds)
end

function AllyComp:OnSyncAllyCityProduce(adds)
    self:logInfo("OnSyncAllyCityProduce. adds: %v", adds)
    self:SetResProduceAddition(TableConst.enums.ProduceSrcType.City, adds)
end

--endregion

--region 赛季玩法

function AllyComp:ChooseRoleForBirthplace(cardIdx)
    self:callAlly(self.props.allyId, "ChooseRoleForBirthplace", self.props.gid, cardIdx)
end

function AllyComp:SuggestRoleForBirthplace(cardIdx)
    self.avatar.props.s1IdentitySuggest = cardIdx
    self:serviceRpc("AmbitionsService", "SuggestRoleForBirthplace", self.props.gid, self.props.birthplace, cardIdx)
end

function AllyComp:gmAllyFoundNation()
    self:callAlly(self.props.allyId, "GmCommand", "gmAllyFoundNation", {})
end

function AllyComp:onSetAmbitionsScore()
    self:callAlly(self.props.allyId, "UpdatePersonalScore", self.props.gid, self.props.career, self.props.ambitionsScore)
end

--endregion

--region 攻城

function AllyComp:AnnounceSiege(siegeCampEid, hourIdx, minIdx)
    if not self:ValidModifySiege() then
        self:logInfo("AnnounceSiege failed, no permission")
        return
    end
    self:callActorByEid(siegeCampEid, "AnnounceSiege", self.props.allyId, hourIdx, minIdx)
end

function AllyComp:CancelAnnounceSiege(siegeCampEid)
    if not self:ValidModifySiege() then
        self:logInfo("CancelAnnounceSiege failed, no permission")
        return
    end
    self:callActorByEid(siegeCampEid, "CancelAnnounceSiege", self.props.allyId)
end

function AllyComp:gmSetAnnounceTimeout(sec)
    self:NotifyGmListenEvent(TableConst.enums.GMEvent.SetAnnounceTimeout)
    coroutine.yield(self:callAlly(self.props.allyId, "GmCommand", "gmSetAnnounceTimeout", {sec, self:makeCallback("OnGmSetAnnounceTimeout")}))
end

function AllyComp:OnGmSetAnnounceTimeout(code)
    self:NotifyGmEventEnd(TableConst.enums.GMEvent.SetAnnounceTimeout, code)
end

function AllyComp:gmSetSiegeTimeout(sec)
    self:callAlly(self.props.allyId, "GmCommand", "gmSetSiegeTimeout", {sec})
end

function AllyComp:OnSiegeFinish(success, isFirstOccupy, lastHit, killRank, duraRank)
    self:safeClientRpc("OnSiegeFinish", success, isFirstOccupy, lastHit, killRank, duraRank)
end

function AllyComp:gmOccupyCity(cityInstanceId)
    local config = WorldCityDataEnv.WORLD_CITY[cityInstanceId]
    if not config then
        self:logError("gmOccupyCity failed, invalid cityInstanceId %s", cityInstanceId)
        return
    end
    local x, y = CoordUtils.UnpackPos(config.pos)
    self:callWorldSpace({x = x, y = y}, "GmOccupyCity", cityInstanceId, self:GetRelationProps())
end

function AllyComp:gmOccupyBuilding(actorEid)
    self:callActorByEid(actorEid, "GmOccupyBuilding", self:GetRelationProps())
end

--endregion

function AllyComp:QueryFirstOccupy(cityEntityId)
    self:callActorByEid(cityEntityId, "QueryCityDetail", {"firstOccupy"}, self:getMailboxStr(), "OnQueryFirstOccupy")
end

function AllyComp:QueryOrgCityDetailPannel(cityEntityId)
    self:callActorByEid(cityEntityId, "QueryCityDetail", {"deployNum"}, self:getMailboxStr(), "OnQueryOrgCityDetailPannel")
end
