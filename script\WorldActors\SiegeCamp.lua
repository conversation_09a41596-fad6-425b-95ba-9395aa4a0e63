local WorldActorEnv = xrequire("WorldActors.WorldActor")
local ActorContainerCompEnv = xrequire("WorldActors.CommonComps.ActorContainerComp")
local WorldBuildingCompEnv = xrequire("WorldActors.CommonComps.WorldBuildingComp")
local OutpostCompEnv = xrequire("WorldActors.CommonComps.OutpostComp")
local ActorContentCompEnv = xrequire("WorldActors.CommonComps.ActorContentComp")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local AllyOwningViewCompEnv = xrequire("WorldActors.CommonComps.AllyOwningViewComp")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")
local InGridComp = xrequire("WorldActors.CommonComps.InGridComp")
local CityAnnexCompEnv = xrequire("WorldActors.CommonComps.CityAnnexComp")
local EntityUtils = xrequire("Utils.EntityUtils")

---@class (partial) SiegeCamp : WorldActor, ActorContainerComp, WorldBuildingComp, OutpostComp, ActorContentComp, AllyOwningViewComp, InGridOutpostComp, CityAnnexComp
---@field props SiegeCampProps
SiegeCamp = DefineEntity("SiegeCamp", {WorldActorEnv.WorldActor}, {
    ActorContainerCompEnv.ActorContainerComp,  ---@type ActorContainerComp
    WorldBuildingCompEnv.WorldBuildingComp,  ---@type WorldBuildingComp
    OutpostCompEnv.OutpostComp,  ---@type OutpostComp
    ActorContentCompEnv.ActorContentComp,  ---@type ActorContentComp
    AllyOwningViewCompEnv.AllyOwningViewComp,  ---@type AllyOwningViewComp
    InGridComp.InGridOutpostComp,  ---@type InGridOutpostComp
    CityAnnexCompEnv.CityAnnexComp,  ---@type CityAnnexComp
})
SiegeCamp._actorType = TableConst.enums.ActorType.SIEGE_CAMP
SiegeCamp._persistent = true

function SiegeCamp:ctor()
    self.announceTimer = nil  ---@type TimerId|nil
    self.preorderTimer = nil  ---@type TimerId|nil
    self.barracksTimer = nil  ---@type TimerId|nil
    self.siegeTimer = nil  ---@type TimerId|nil
    self.announcePreorder = {}  ---@type table<string, boolean>
    self.inAssault = nil  ---@type table?
end

function SiegeCamp:OnActorDestroy()
    self:CancelAnnounceSiege(self.props.allyId)
end

---@return CityGate?
function SiegeCamp:getGate()
    local city = self:getCity()
    if not city then
        return
    end
    return city.siegeGroups[self.props.siegeGroupIdx][TableConst.enums.ActorType.CITY_GATE]
end

---@return InnerCityGate?
function SiegeCamp:getInnerGate()
    local city = self:getCity()
    if not city then
        return
    end
    return city.siegeGroups[self.props.siegeGroupIdx][TableConst.enums.ActorType.INNER_CITY_GATE]
end

--region 倒计时

function SiegeCamp:AnnounceSiege(allyId, timeIdx, minIdx)
    local city = self:getCity()
    if not city then
        self:logInfo("AnnounceSiege failed, no city")
        return
    end
    if allyId ~= self.props.allyId or allyId == "" or city.props.allyId == allyId then
        self:logInfo("AnnounceSiege failed, invalid allyId Avatar(%s) Camp(%s) City(%s)", allyId, self.props.allyId, city.props.allyId)
        return
    end
    if self:IsInBuilding() then
        self:logInfo("AnnounceSiege failed, in building")
        return
    end
    if self.props.announceTimeout then
        return
    end
    -- 倒计时开战
    local delayHour = GameCommon.TableDataManager:GetSiegeConst("ANNOUNCE_DELAY_HOUR")[timeIdx]
    local delayMin = GameCommon.TableDataManager:GetSiegeConst("ANNOUNCE_DELAY_MINUTE")[minIdx]
    if not delayHour or not delayMin then
        return
    end
    local timeout = ServerTimeEnv.GetServerNow() + delayHour * 60 * 60 + delayMin * 60
    -- 如果正在建造中，等建造完再加上剩余时间
    if self:IsInBuilding() then
        timeout = timeout + math.max(0, self.props.processTimeout - ServerTimeEnv.GetServerNow())
    end
    local siegeUid = EZE.genUUID()
    self:logInfo("AnnounceSiege success, allyId %s, timeout %s, siegeUid:%s", allyId, timeout, siegeUid)
    self.props.siegeUid = siegeUid
    self.props.announceTimeout = timeout
    self:restartSiegeTimer()
    self:DelaySyncAllyView()
end

function SiegeCamp:CancelAnnounceSiege(allyId)
    if allyId ~= self.props.allyId or allyId == "" then
        self:logInfo("CancelAnnounceSiege failed, invalid allyId %s", allyId)
        return
    end
    -- 取消倒计时
    if not self.props.announceTimeout and not self.props.siegeTimeout then
        return
    end
    self:logInfo("CancelAnnounceSiege success, allyId %s", allyId)
    self:OnSiegeCancelled()
end

function SiegeCamp:restartSiegeTimer()
    if self.announceTimer then
        self:delTimer(self.announceTimer)
        self.announceTimer = nil
    end
    if self.preorderTimer then
        self:delTimer(self.preorderTimer)
        self.preorderTimer = nil
    end
    if self.barracksTimer then
        self:delTimer(self.barracksTimer)
        self.barracksTimer = nil
    end
    if self.siegeTimer then
        self:delTimer(self.siegeTimer)
        self.siegeTimer = nil
    end
    if self.props.announceTimeout then
        local city = self:getCity()
        if not city then
            self:logWarn("restartSiegeTimer failed, no city")
            return
        end
        self.announceTimer = self:addTimer(math.max(0, self.props.announceTimeout - ServerTimeEnv.GetServerNow()), 0, function()
            if self.announceTimer then
                self:delTimer(self.announceTimer)
                self.announceTimer = nil
            end
            if not self.props.announceTimeout then
                return
            end
            self:onAnnounceTimeout()
        end)
        city:SetAllyPreWar(self.props.allyId, self.props.siegeGroupIdx)
        local preorderGoTimeout = self.props.announceTimeout - GameCommon.TableDataManager:GetSiegeConst("PREORDER_GO_BEFORE_SEC")
        self.preorderTimer = self:addTimer(math.max(0, preorderGoTimeout - ServerTimeEnv.GetServerNow()), 0, function()
            if self.preorderTimer then
                self:delTimer(self.preorderTimer)
                self.preorderTimer = nil
            end
            if not self.props.announceTimeout then
                return
            end
            self:onPreorderTimeout(GameCommon.TableDataManager:GetSiegeConst("PREORDER_GO_CHECK_CNT"))
        end)
        local gate = self:getGate()
        if not gate then
            return
        end
        local barracksSupportDefenderTimeout = self.props.announceTimeout - GameCommon.TableDataManager:GetSiegeConst("BarrackTempArmyStartTime")
        self.barracksTimer = self:addTimer(math.max(0, barracksSupportDefenderTimeout - ServerTimeEnv.GetServerNow()), 0, function()
            if self.barracksTimer then
                self:delTimer(self.barracksTimer)
                self.barracksTimer = nil
            end
            if not self.props.announceTimeout then
                return
            end
            local gate = self:getGate()
            if not gate then
                self:logWarn("barracksTimer failed, no gate")
                return
            end
            gate:StartBarracksSupportDefender()
        end)
    elseif self.props.siegeTimeout then
        self.siegeTimer = self:addTimer(math.max(0, self.props.siegeTimeout - ServerTimeEnv.GetServerNow()), 0, function()
            if self.siegeTimer then
                self:delTimer(self.siegeTimer)
                self.siegeTimer = nil
            end
            if not self.props.siegeTimeout then
                return
            end
            self:onSiegeTimeout()
        end)
    end
end

function SiegeCamp:onAnnounceTimeout()
    local siegeTimeout = GameCommon.TableDataManager:GetSiegeConst("SIEGE_DURATION")
    self:logInfo("onAnnounceTimeout, siegeTimeout %s", siegeTimeout)
    self.props.announceTimeout = nil
    self.props.siegeTimeout = siegeTimeout + ServerTimeEnv.GetServerNow()
    self:restartSiegeTimer()
    self:siegeStartAssault()
    local gate = self:getGate()
    if gate then
        gate:SetInWar(self.props.allyId)
    end
    local innerGate = self:getInnerGate()
    if innerGate then
        innerGate:SetInWar(self.props.allyId)
    end
    self:DelaySyncAllyView()
end

function SiegeCamp:onPreorderTimeout(checkCount)
    self:logInfo("onPreorderTimeout, checkCount %s", checkCount)
    for actorUid, isMainForce in pairs(self.announcePreorder) do
        self:callActor(actorUid, "OnArmyPreorderSiegeTimeout", self.id, isMainForce)
    end
    checkCount = checkCount - 1
    if checkCount <= 0 then
        self.announcePreorder = {}
        return
    end
    self.preorderTimer = self:addTimer(GameCommon.TableDataManager:GetSiegeConst("PREORDER_GO_CHECK_DELAY"), 0, function()
        if self.preorderTimer then
            self:delTimer(self.preorderTimer)
            self.preorderTimer = nil
        end
        if not self.props.announceTimeout then
            return
        end
        self:onPreorderTimeout(checkCount)
    end)
end

function SiegeCamp:onSiegeTimeout()
    self:logInfo("onSiegeTimeout")
    self:addTimer(0, 0, function()
        -- 延迟一帧判断，保证城门有无守军的情况的时序都是一样的
        local gate = self:getGate()
        if gate.props.allyId == self.props.allyId then
            gate = self:getInnerGate()
        end
        if gate then
            gate:SetInWar(nil)
            gate:settleWaitAllAttackerLeave()
        else
            self:OnTimeoutOccupyFail()
        end
    end)
    self:DelaySyncAllyView()
end

function SiegeCamp:fullSyncAllyView()
    local city = self:getCity()
    if not city then
        self:logWarn("fullSyncAllyView failed, no city")
        return
    end
    self:SyncAllyView({
        -- 静态数据
        city = city.props.instanceId,
        cityPos = city:GetDoubledPos2DV(),
        pos = self:GetDoubledPos2DV(),
        buildStatus = self.props.buildStatus,
        processTimeout = self.props.processTimeout,
        -- 动态数据
        siegeUid = self.props.siegeUid,
        announceTimeout = self.props.announceTimeout,
        siegeTimeout = self.props.siegeTimeout,
        mainForceNum = #self.props.mainForce,
        duraForceNum = #self.props.duraForce,
    }, true)
end

function SiegeCamp:gmSetAnnounceTimeout(sec, callback)
    if not self.props.announceTimeout then
        self:logWarn("not in announce stage, gm set failed")
        self:callbackRpc(callback, false)
        return
    end

    self.props.announceTimeout = sec + ServerTimeEnv.GetServerNow()
    self:logInfo("gmSetAnnounceTimeout, sec %s", sec)
    self:restartSiegeTimer()
    self:DelaySyncAllyView()
    self:callbackRpc(callback, true)
end

function SiegeCamp:gmSetSiegeTimeout(sec)
    if not self.props.siegeTimeout then
        self:logWarn("not in siege stage, gm set failed")
        return
    end
    self.props.siegeTimeout = sec + ServerTimeEnv.GetServerNow()
    self:logInfo("gmSetSiegeTimeout, sec %s", sec)
    self:restartSiegeTimer()
    self:DelaySyncAllyView()
end

--endregion

--region 集结

function SiegeCamp:ReqPreorderSiege(actorUid, relation, actorType, isMainForce, rpcCb)
    if not BehaviorJudgeEnv.ValidActorContainer(relation, self.props, actorType, self._actorType, TableConst.enums.InConType.InBase) then
        self:logInfo("ReqPreorderSiege, invalid actor container")
        return
    end
    if self:IsInBuilding() then
        self:logInfo("ReqPreorderSiege, in building")
        return
    end
    if not self.props.announceTimeout then
        self:logInfo("ReqPreorderSiege, not in announce stage")
        return
    end
    self:logInfo("ReqPreorderSiege, actorUid %s, isMainForce %s", actorUid, isMainForce)
    self.announcePreorder[actorUid] = isMainForce
    self:callbackRpc(rpcCb, self.id, isMainForce, self.props.announceTimeout - GameCommon.TableDataManager:GetSiegeConst("PREORDER_GO_BEFORE_SEC"))
end

---@param actor Army
function SiegeCamp:onContentEnter(actor, inConType)
    if inConType ~= TableConst.enums.InConType.InBase then
        return
    end
    if not actor.props.siegeHome or actor.props.siegeHome.baseId ~= self.props.uid then
        return
    end
    if actor.props.siegeHome.isMainForce then
        EntityUtils.InsertPropIfNotExist(self.props.mainForce, actor.props.uid)
    else
        EntityUtils.InsertPropIfNotExist(self.props.duraForce, actor.props.uid)
    end
    self:DelaySyncAllyView()
end

function SiegeCamp:onContentLeave(actor, inConType)
    if inConType ~= TableConst.enums.InConType.InBase then
        return
    end
    local toRemove = nil
    for idx, val in ipairs(self.props.mainForce) do
        if val == actor.props.uid then
            toRemove = idx
            break
        end
    end
    if toRemove then
        self.props.mainForce:remove(toRemove)
    end
    local toRemove = nil
    for idx, val in ipairs(self.props.duraForce) do
        if val == actor.props.uid then
            toRemove = idx
            break
        end
    end
    if toRemove then
        self.props.duraForce:remove(toRemove)
    end
    self:DelaySyncAllyView()
end

--endregion

--region 攻城冲锋移动

function SiegeCamp:siegeStartAssault()
    self.inAssault = {}
    local city = self:getCity()
    if not city then
        self:logWarn("siegeStartAssault failed, no city")
        return
    end
    city:SetAllyInWar(self.props.allyId, self.props.siegeGroupIdx)
    local gate = self:getGate()
    if not gate then
        self:logWarn("siegeStartAssault failed, no gate")
        return
    end
    self:setSiegeAssaultStart(gate.id, true)
    --TODO(qun): 先直接各自寻路前往城门上墙，等后续迭代行军排阵规则优化寻路消耗
    self:doAssault(self.props.mainForce:copy(), GameCommon.TableDataManager:GetSiegeConst("SIEGE_MAIN_MOVE_PATCH"), function()
        local gate = self:getGate()
        if not gate then
            self:logWarn("siegeStartAssault finish callback failed, no gate")
            return
        end
        gate:CheckAllSysDefenderDie()
    end)
end

---@param gate CityGate|InnerCityGate
function SiegeCamp:OnCityGateSysDefenderZero(gate)
    self:setSiegeAssaultStart(gate.id, false)
    if gate == self:getGate() then
        self:doAssault(self.props.duraForce:copy(), GameCommon.TableDataManager:GetSiegeConst("SIEGE_SUB_MOVE_PATCH"), function()
            self:logInfo("CityGate duraForce assault complete")
        end)
    end
end

function SiegeCamp:doAssault(actorList, assaultOneTime, completeCb)
    local hasMemberWaiting = false
    local assaultThisTime = assaultOneTime
    local gate = self:getGate()
    if not gate then
        self:logWarn("doAssault failed, no gate")
        return
    end
    for _, actorUid in ipairs(actorList) do
        local actor = self:getActor(actorUid)  ---@type Army|nil
        self:logInfo("doAssault, actorUid %s, assaultThisTime %s, %s %s %s", actorUid, assaultThisTime, actor, actor and actor:ValidAssault(self), actor and actor:IsExecuteAction())
        if actor and actor:ValidAssault(self) and not actor:IsExecuteAction() then
            if assaultThisTime > 0 then
                assaultThisTime = assaultThisTime - 1
                actor:StartSiegeAssault(self)
            else
                hasMemberWaiting = true
                break
            end
        end
    end
    if hasMemberWaiting then
        self:addTimer(GameCommon.TableDataManager:GetSiegeConst("SIEGE_MOVE_PATCH_DELAY"), 0, function()
            self:doAssault(actorList, assaultOneTime)
        end)
        return
    end
    if completeCb then
        completeCb()
    end
end

function SiegeCamp:setSiegeAssaultStart(gateEid, isMainForce)
    if not self.inAssault then
        self:logError("setSiegeAssaultStart failed, no inAssault")
        return
    end
    if not self.inAssault[gateEid] then
        self.inAssault[gateEid] = {}
    end
    local cbMap = self.inAssault[gateEid] and self.inAssault[gateEid][isMainForce]
    if cbMap == true then
        return
    end
    self.inAssault[gateEid][isMainForce] = true
    if table.isnilorempty(cbMap) then
        return
    end
    for armyUid, cb in pairs(cbMap) do
        local army = self:getActor(armyUid)  ---@type Army|nil
        if army then
            army:entityCallback(cb, true)
        end
    end
end

---@param army Army
---@param gateEid EntityId
---@param isMainForce boolean
function SiegeCamp:waitForSiegeAssault(army, gateEid, isMainForce, cb)
    if not self.inAssault then
        army:addTimer(0, 0, function()
            army:entityCallback(cb, false)
        end)
        return
    end
    if self.inAssault[gateEid] and self.inAssault[gateEid][isMainForce] == true then
        army:addTimer(0, 0, function()
            army:entityCallback(cb, true)
        end)
        return
    end
    if not self.inAssault[gateEid] then
        self.inAssault[gateEid] = {}
    end
    if not self.inAssault[gateEid][isMainForce] then
        self.inAssault[gateEid][isMainForce] = {}
    end
    self.inAssault[gateEid][isMainForce][army.props.uid] = cb
end

---@param army Army
function SiegeCamp:cancelWaitForSiegeAssault(army, gateEid, isMainForce)
    if not self.inAssault then
        return
    end
    if not self.inAssault[gateEid] then
        return
    end
    local cbMapOrBool = self.inAssault[gateEid][isMainForce]  ---@type table|boolean?
    if cbMapOrBool == nil or cbMapOrBool == true or cbMapOrBool == false then
        return
    end
    cbMapOrBool[army.props.uid] = nil
end

function SiegeCamp:IsMainForceAssaultStarted()
    local gate = self:getGate()
    return gate and self.inAssault
end

function SiegeCamp:IsDuraForceAssaultStarted()
    local gate = self:getGate()
    return gate and self.inAssault and self.inAssault[gate.id] and self.inAssault[gate.id][false]
end

--endregion

--region 结算

function SiegeCamp:OnCityOccupiedByOther()
    -- 城市被其他势力占领了，需要结算攻城失败
    -- 需要中断攻城和宣战
    if self.props.announceTimeout then
        self:failedClearAnnounceSiege()
    elseif self.props.siegeTimeout then
        self:failedClearSiege()
    end
end

function SiegeCamp:OnOutpostDurabilityZero()
    -- 攻城大营被拆掉了，需要结算攻城失败
    if self.props.announceTimeout then
        self:failedClearAnnounceSiege()
    elseif self.props.siegeTimeout then
        self:failedClearSiege()
    end
end

function SiegeCamp:OnSiegeCancelled()
    -- 宣战被取消，所有的攻城部队已经离开城墙
    if self.props.announceTimeout then
        self:failedClearAnnounceSiege()
    elseif self.props.siegeTimeout then
        self:failedClearSiege()
    else
        self:logError("OnSiegeCancelled failed, not in announce or siege stage")
    end
end

function SiegeCamp:OnTimeoutOccupyFail()
    -- 超时攻占失败，所有的攻城部队已经离开城墙
    self:failedClearSiege()
end

function SiegeCamp:OnGateDuraZero()
    -- 城门耐久被打到0，攻城胜利
    if not self.props.siegeTimeout then
        self:logError("OnGateDuraZero failed, not in siege stage")
        return
    end
    self:successClearSiege()
end

function SiegeCamp:failedClearAnnounceSiege()
    assert(self.props.announceTimeout)
    -- 宣战阶段失败，清理宣战相关
    -- 清理城门的兵营系统守军
    -- 清理宣战的状态
    self:doSiegeEnd()
end

function SiegeCamp:failedClearSiege()
    assert(self.props.siegeTimeout)
    -- 攻城阶段失败，清理攻城相关
    -- 清理城门的兵营系统守军
    -- 清理攻城的状态
    self:doSiegeEnd()
    self:doSiegeFailed()
end

function SiegeCamp:successClearSiege()
    -- 攻城阶段胜利，清理攻城相关
    -- 清理城门的兵营系统守军
    -- 清理攻城的状态
    self:doSiegeEnd()
    self:doSiegeSuccess()
end

function SiegeCamp:doSiegeEnd()
    -- NOTE: 先执行end，再执行success或failed，保证时序；End清理各类状态，failed和success基于清理后的结果做结算
    -- 终止宣战和攻城的定时器
    self.props.siegeUid = nil
    self.props.announceTimeout = nil
    self.props.siegeTimeout = nil
    self:restartSiegeTimer()
    -- 清理集结触发的数据
    self.inAssault = nil
    -- 触发城门清理宣战和集结结束的清理数据流程
    local city = self:getCity()
    city:doSiegeEnd(self.props.siegeGroupIdx, self.props.allyId)
    self:DelaySyncAllyView()
end

function SiegeCamp:doSiegeFailed()
    -- 攻城失败流程
    ---@type City|nil
    local city = self:getCity()
    assert(city)
    city:getSiegeRecords(self.props.siegeGroupIdx)
    city:ClearOccupyOnFailed(self.props.siegeGroupIdx, self.props.allyId)
    -- TODO(quN): 攻城Action依赖的挂载在outpost和发起指令时清理有耦合，要支持一些Action的可重入
    self:addTimer(GameCommon.TableDataManager:GetSiegeConst("CLEAR_INSIDER_REPEAT_INTERNAL"), 0, function()
        city:ClearInsideAttackerOnFailed(self.props.allyId, GameCommon.TableDataManager:GetSiegeConst("CLEAR_INSIDER_REPEAT_COUNT"))  -- 依赖先清理了攻城中的状态
    end)
    self:broadcastAllyClients(self.props.allyId, "OnSiegeFailed", city.props.instanceId)
    self:callAlly(self.props.allyId, "SendSiegeMail", city.props.instanceId, false, {}, false)
end

function SiegeCamp:doSiegeSuccess()
    local city = self:getCity()
    assert(city)
    -- 攻城排行
    local lastHit, duraRank, killRank, participate = city:getSiegeRecords(self.props.siegeGroupIdx)
    city:SetOccupyOnSuccess(self.props.siegeGroupIdx, self.props.allyId)
    -- 首占记录
    local isFirstOccupy = false
    if not city.props.firstOccupy then
        isFirstOccupy = true
    end
    self:serviceRpc("AvatarInfoService", "FillSiegeResultDetails", self.props.allyId, city.props.instanceId, isFirstOccupy, lastHit, killRank, duraRank)
    if isFirstOccupy then
        city.props.firstOccupy = {
            allyId = self.props.allyId,
            dura = duraRank,
            kill = killRank,
        }
    end
    city:save()  -- 触发保存首占信息
    self:callAlly(self.props.allyId, "SendSiegeMail", city.props.instanceId, true, participate, isFirstOccupy)
end

--endregion
