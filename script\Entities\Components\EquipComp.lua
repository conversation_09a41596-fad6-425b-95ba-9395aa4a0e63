local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local ChooseEnv = xrequire("Common.Choose")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local enum_details = xrequire("Table.Defines.enum_details")

---@class EquipComp: Avatar
EquipComp = DefineClass("EquipComp", ComponentBaseEnv.ComponentBase)

function EquipComp:ctor()
    self:checkWaitHorseTraining()
end

function EquipComp:onSetTime()
    self:checkWaitHorseTraining()
end

--region 装备坐骑背包

function EquipComp:addEquip(equipList, reason)
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    local logDirectAdd, logMailAdd, mailadd = {}, {}, {}
    local bagSize = 0
    for equipUid, _ in pairs(self.props.equipBag) do
        bagSize = bagSize + 1
    end
    for _, equip in ipairs(equipList) do
        local equipConfig = GameCommon.TableDataManager:GetEquipData(equip.id)
        if not equipConfig then
            self:logError("addEquip failed, equip id: %s not found", equip.id)
        else
            local equipUid = EZE.genUUID()
            if bagSize >= GameCommon.TableDataManager:GetItemConst("EQUIP_BAG_CAP") then
                table.insert(logMailAdd, equipUid)
                mailadd[equipUid] = equip
            else
                table.insert(logDirectAdd, equipUid)
                self.props.equipBag[equipUid] = equip
                bagSize = bagSize + 1
            end
        end
    end
    self:logInfo("addEquip success, num: %s, reason: %s, directAdd: %v, mailAdd: %v", #equipList, reasonDetail.Name, logDirectAdd, logMailAdd)
    self:clientRpc("OnNewEquip", logDirectAdd, mailadd)
end

function EquipComp:delEquip(equipUidList, reason)
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    self:logInfo("delEquip, equipUidList: %v, reason: %s", equipUidList, reasonDetail.Name)
    for _, equipUid in ipairs(equipUidList) do
        assert(self.props.equipBag[equipUid])
        self.props.equipBag[equipUid] = nil
    end
end

function EquipComp:addHorse(horse, reason)
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    local horseConfig = GameCommon.TableDataManager:GetHorseData(horse.id)
    if not horseConfig then
        self:logError("addHorse failed, horse id: %s not found %s", horse.id, reasonDetail.Name)
        return
    end
    local bagSize = 0
    for equipUid, _ in pairs(self.props.equipBag) do
        bagSize = bagSize + 1
    end
    local horseUid = EZE.genUUID()
    self:logInfo("addHorse, horse: %s %v %s", horseUid, horse, reasonDetail.Name)
    if bagSize >= GameCommon.TableDataManager:GetItemConst("HORSE_BAG_CAP") then
        self:clientRpc("OnNewHorse", {}, { [horseUid] = horse })
        return
    end
    self.props.horseBag[horseUid] = horse
    self:clientRpc("OnNewHorse", { horseUid }, {})
end

function EquipComp:delHorse(horseUidList, reason)
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    self:logInfo("delHorse, horseUid: %v, reason: %s", horseUidList, reasonDetail.Name)
    for _, horseUid in ipairs(horseUidList) do
        assert(self.props.horseBag[horseUid])
        self.props.horseBag[horseUid] = nil
    end
end

function EquipComp:getEquip(equipUid)
    if self.props.equipBag[equipUid] then
        return self.props.equipBag[equipUid], GameCommon.TableDataManager:GetEquipData(self.props.equipBag[equipUid].id)
    elseif self.props.horseBag[equipUid] then
        return self.props.horseBag[equipUid], GameCommon.TableDataManager:GetHorseData(self.props.horseBag[equipUid].id)
    end
    return nil, nil
end

--endregion

--region 装备坐骑穿戴

function EquipComp:WearEquip(equipUid, equipSlotId, heroId)
    local slotEquipType = GameCommon.TableDataManager:GetEquipConst("EQUIP_SLOTS")[equipSlotId]
    local equip, equipConfig = self:getEquip(equipUid)
    if not equip then
        self:logError("WearEquip failed, equipUid: %s not found", equipUid)
        return
    end
    if equip.heroId ~= 0 then
        self:logError("WearEquip failed, equipUid: %s already worn by heroId: %s", equipUid, equip.heroId)
        return
    end
    if equipConfig.equip_type ~= slotEquipType then
        self:logError("WearEquip failed, equipUid: %s type mismatch, expected: %s, got: %s", equipUid, slotEquipType, equipConfig.equip_type)
        return
    end
    local hero = self:getHeroCard(heroId)
    if not hero then
        self:logError("WearEquip failed, heroId: %s not found", heroId)
        return
    end
    local armySlotIdx = self.worldAvatar:GetArmyByUid(hero.inArmy)
    if armySlotIdx and not self.worldAvatar:validBehavior(armySlotIdx, TableConst.enums.BehaviorType.ModifyArmy, true) then
        self:logError("WearEquip failed, heroId: %s cannot wear equip", heroId)
        return
    end
    if hero.equipSlots[equipSlotId] then
        self:doUnWearEquip(hero, equipSlotId)
    end
    self:logInfo("WearEquip success, equipUid: %s, equipSlotId: %s, heroId: %s", equipUid, equipSlotId, heroId)
    hero.equipSlots[equipSlotId] = equipUid
    equip.heroId = heroId
    self:doSyncHeroToArmy(heroId)
    self.worldAvatar:refreshArmyBuff(hero.inArmy)
end

function EquipComp:UnWearEquip(equipSlotId, heroId)
    local hero = self:getHeroCard(heroId)
    if not hero then
        self:logError("UnWearEquip failed, heroId: %s not found", heroId)
        return
    end
    local equipUid = hero.equipSlots[equipSlotId]
    if not equipUid then
        self:logError("UnWearEquip failed, equipSlotId: %s not equip", equipSlotId)
        return
    end
    local armySlotIdx = self.worldAvatar:GetArmyByUid(hero.inArmy)
    if armySlotIdx and not self.worldAvatar:validBehavior(armySlotIdx, TableConst.enums.BehaviorType.ModifyArmy, true) then
        self:logError("WearEquip failed, heroId: %s cannot wear equip", heroId)
        return
    end
    self:doUnWearEquip(hero, equipSlotId)
    self:doSyncHeroToArmy(heroId)
    self.worldAvatar:refreshArmyBuff(hero.inArmy)
end

function EquipComp:doUnWearEquip(hero, equipSlotId)
    self:logInfo("UnWearEquip success, equipUid: %s, equipSlotId: %s, heroId: %s", equipUid, equipSlotId, heroId)
    local equipUid = hero.equipSlots[equipSlotId]
    if not equipUid then
        self:logError("doUnWearEquip failed, equipSlotId: %s not equip", equipSlotId)
        return
    end
    local equip, equipConfig = self:getEquip(equipUid)
    if not equip then
        self:logError("doUnWearEquip failed, equipUid: %s not found", equipUid)
        return
    end
    equip.heroId = 0
    hero.equipSlots[equipSlotId] = nil
end

--endregion

--region 装备打造&驯马

EquipComp.VALID_BUILD_EQUIP = {
    [TableConst.enums.EquipType.Weapon] = true,
    [TableConst.enums.EquipType.Defense] = true,
}

function EquipComp:BuildEquip(equipType, buildCnt, blueprint)
    assert(self.VALID_BUILD_EQUIP[equipType])
    if buildCnt <= 0 or buildCnt >= GameCommon.TableDataManager:GetEquipConst("MAX_CONTINUOUS_BUILD") then
        self:logError("BuildEquip failed, invalid buildCnt: %s", buildCnt)
        return
    end
    if blueprint ~= 0 then
        local bpConfig = GameCommon.TableDataManager:GetItemData(blueprint)
        if not bpConfig or bpConfig.use_func._type_ ~= "EquipBlueprint" then
            self:logError("BuildEquip failed, invalid blueprint: %s", blueprint)
            return
        end
    end
    local itemNeed = {}
    for coinId, num in pairs(GameCommon.TableDataManager:GetEquipConst("BUILD_EQUIP_COST_CURRENCY")) do
        itemNeed[coinId] = num * buildCnt
    end
    for itemId, num in pairs(GameCommon.TableDataManager:GetEquipConst("BUILD_EQUIP_COST_ITEM")) do
        itemNeed[itemId] = num * buildCnt
    end
    if blueprint ~= 0 then
        itemNeed[blueprint] = buildCnt
    end
    if not self:ValidConsumeAll(itemNeed, TableConst.enums.Reason.BUILD_EQUIP) then
        self:logError("BuildEquip failed, not enough resources")
        return
    end
    local equipList = {}
    for i = 1, buildCnt do
        local equip = self:doBuildEquip(equipType, blueprint)
        table.insert(equipList, equip)
    end
    self:logInfo("BuildEquip success, equipType: %s, buildCnt: %s, blueprint: %s", equipType, buildCnt, blueprint)
    self:ConsumeFromBag(itemNeed, TableConst.enums.Reason.BUILD_EQUIP)
    self:addEquip(equipList, TableConst.enums.Reason.BUILD_EQUIP)
end

function EquipComp:doBuildEquip(equipType, blueprint)
    local equip = {}
    equip.id = ChooseEnv.RandomChoose(ProcessedTableEnv.ALL_EQUIP_IDS[equipType])
    equip.quality = ChooseEnv.RandomChooseByWeight(
        GameCommon.TableDataManager:GetTable("common_tbequipqualityratio"),
        function(v) return v.base_build_ratio end
    )
    self:generateEntriesAndEffects(equip, equipType, equip.quality)
    return equip
end

function EquipComp:TrainHorse(extraItems)
    for itemId, _ in pairs(extraItems) do
        local itemConfig = GameCommon.TableDataManager:GetItemData(itemId)
        if not itemConfig or itemConfig.use_func._type_ ~= "HorseTrain" then
            self:logError("TrainHorse failed, invalid item: %s", itemId)
            return
        end
    end
    local itemNeed = {}
    for coinId, num in pairs(GameCommon.TableDataManager:GetEquipConst("TRAIN_HORSE_COST_CURRENCY")) do
        itemNeed[coinId] = num
    end
    for itemId, num in pairs(extraItems) do
        itemNeed[itemId] = num
    end
    if not self:ValidConsumeAll(itemNeed, TableConst.enums.Reason.TRAIN_HORSE) then
        self:logError("TrainHorse failed, not enough currency")
        return
    end
    if self.props.horseTraining.trainingEndTime ~= 0 then
        self:logError("TrainHorse failed, already training")
        return
    end
    local trainTime = GameCommon.TableDataManager:GetEquipConst("TRAIN_HORSE_COST_TIME")
    local horse = self:doTrainHorse(extraItems)
    self:logInfo("TrainHorse success, horse: %v, trainTime: %s", horse, trainTime)
    self:ConsumeFromBag(itemNeed, TableConst.enums.Reason.TRAIN_HORSE)
    self.props.horseTraining = {
        trainingEndTime = ServerTimeEnv.GetServerNow() + trainTime,
        horse = horse,
    }
end

function EquipComp:doTrainHorse(extraItems)
    local horse = {}
    horse.id = ChooseEnv.RandomChoose(ProcessedTableEnv.ALL_EQUIP_IDS[TableConst.enums.EquipType.Horse])
    local horseConfig = GameCommon.TableDataManager:GetHorseData(horse.id)
    self:generateEntriesAndEffects(horse, TableConst.enums.EquipType.Horse, horseConfig.quality)
    return horse
end

function EquipComp:checkWaitHorseTraining()
    if self.props.horseTraining.trainingEndTime == 0 then
        return
    end
    if self._waitHorseTrainingTimer then
        self:delTimer(self._waitHorseTrainingTimer)
        self._waitHorseTrainingTimer = nil
    end
    self._waitHorseTrainingTimer = self:addTimer(math.max(0, self.props.horseTraining.trainingEndTime - ServerTimeEnv.GetServerNow()), 0, function()
        if self.props.horseTraining.trainingEndTime == 0 then
            self:logInfo("Horse training already completed")
            return
        end
        local horse = self.props.horseTraining.horse
        self.props.horseTraining.horse = {}  -- 手动删除horse的parent
        self.props.horseTraining = {}
        self:addHorse(horse, TableConst.enums.Reason.TRAIN_HORSE)
    end)
end

function EquipComp:generateEntriesAndEffects(equip, equipType, quality)
    local config = nil
    if equipType == TableConst.enums.EquipType.Horse then
        config = GameCommon.TableDataManager:GetHorseData(equip.id)
    else
        config = GameCommon.TableDataManager:GetEquipData(equip.id)
    end
    assert(config)
    local mainEntryId = nil
    if config.main_prop and config.main_prop > 0 then
        mainEntryId = config.main_prop
    end
    -- 随机词条
    local qualityRatioConfig = GameCommon.TableDataManager:GetEquipQualityRatio(quality)
    local entryType = ChooseEnv.RandomChooseByWeight(qualityRatioConfig.build_entry_ratio)
    equip.entries = {}
    if entryType == TableConst.enums.EquipEntriesType.Single then
        equip.entries[1] = self:generateEquipEntry(quality, mainEntryId)
    elseif entryType == TableConst.enums.EquipEntriesType.Double then
        equip.entries[1] = self:generateEquipEntry(quality, mainEntryId)
        equip.entries[2] = self:generateEquipEntry(quality, nil, nil, equip.entries[1].id)
    elseif entryType == TableConst.enums.EquipEntriesType.ReverseDouble then
        equip.entries[1] = self:generateEquipEntry(quality, mainEntryId, false)
        equip.entries[2] = self:generateEquipEntry(quality, nil, true, equip.entries[1].id)
    end
    -- 随机特效
    local effectList = {}
    for i = 1, 2 do
        local effectQuality = ChooseEnv.RandomChooseByWeight(qualityRatioConfig.build_effect_ratio)
        if effectQuality ~= TableConst.enums.EquipQuality.None then
            table.insert(effectList, self:generateEquipEffect(equipType, equip.id, effectQuality))
        end
    end
    equip.effects = effectList
end

function EquipComp:generateEquipEntry(quality, mainEntryId, negativeOrPositive, excludeId)
    local entryId = mainEntryId
    if not entryId then
        local validEntries = {}
        for _, id in ipairs(GameCommon.TableDataManager:GetEquipConst("EQUIP_PROPS_TYPE")) do
            if id ~= excludeId then
                table.insert(validEntries, id)
            end
        end
        entryId = ChooseEnv.RandomChoose(validEntries)
    end
    local value = GameCommon.TableDataManager:GetEquipConst("ENTRIES_BASE_VALUE")[entryId]
    -- 根据品质调整属性值
    value = value * GameCommon.TableDataManager:GetEquipConst("QUALITY_TO_PROPS_ADD")[quality] / 100
    -- 根据正负调整属性值
    if negativeOrPositive == true then
        value = value * GameCommon.TableDataManager:GetEquipConst("DOUBLE_REVERSE_NEGATIVE_ADD") / 100
    elseif negativeOrPositive == false then
        value = value * GameCommon.TableDataManager:GetEquipConst("DOUBLE_REVERSE_POSITIVE_ADD") / 100
    end
    -- 根据初始进度配置随机
    local initRatio = GameCommon.TableDataManager:GetEquipConst("INIT_ENTRY_VALUE_RATIO")
    local value = self:getEquipEntryValue(entryId, quality, negativeOrPositive, math.random(initRatio[1], initRatio[2]) / 100)
    return {
        id = entryId,
        value = value,
    }
end

function EquipComp:getEquipEntryValue(entryId, quality, negativeOrPositive, ratio)
    local value = GameCommon.TableDataManager:GetEquipConst("ENTRIES_BASE_VALUE")[entryId]
    -- 根据品质调整属性值
    value = value * GameCommon.TableDataManager:GetEquipConst("QUALITY_TO_PROPS_ADD")[quality] / 100
    -- 根据正负调整属性值
    if negativeOrPositive == true then
        value = value * GameCommon.TableDataManager:GetEquipConst("DOUBLE_REVERSE_NEGATIVE_ADD") / 100
    elseif negativeOrPositive == false then
        value = value * GameCommon.TableDataManager:GetEquipConst("DOUBLE_REVERSE_POSITIVE_ADD") / 100
    end
    -- TODO(qun) 属性需要保留几位小数点？
    return ratio and math.floor(value * ratio / 100) or value
end

function EquipComp:generateEquipEffect(equipType, equipId, effectQuality, excludeId)
    local validEffects = {}
    for _, effectId in ipairs(ProcessedTableEnv.ALL_QUALITY_TO_EFFECT[effectQuality]) do
        local effectConfig = GameCommon.TableDataManager:GetEquipEffectData(effectId)
        assert(effectConfig)
        if effectId == excludeId then
            -- 排除特定特效
        elseif not effectConfig.equip or effectConfig.equip == 0 then
            -- 全部装备都适用的特效
            table.insert(validEffects, effectId)
        elseif effectConfig.equip == equipId and equipType ~= TableConst.enums.EquipType.Horse then
            table.insert(validEffects, effectId)
        end
    end
    if #validEffects == 0 then
        self:logError("generateEquipEffect failed, no valid effects for equipType: %s, equipId: %s, effectQuality: %s", equipType, equipId, effectQuality)
        return {}
    end
    return {
        id = ChooseEnv.RandomChoose(validEffects),
    }
end

--endregion

--region 分解&出售

function EquipComp:DecomposeEquip(equipUidList)
    local totalReward = {}
    for _, equipUid in ipairs(equipUidList) do
        local equip, equipConfig = self:getEquip(equipUid)
        assert(equipConfig.equip_type ~= TableConst.enums.EquipType.Horse, "DecomposeEquip cannot decompose horse")
        if not equip then
            self:logError("DecomposeEquip failed, equipUid: %s not found", equipUid)
            return
        end
        if equip.heroId ~= 0 then
            self:logError("DecomposeEquip failed, equipUid: %s is worn by heroId: %s", equipUid, equip.heroId)
            return
        end
        local reward = GameCommon.TableDataManager:GetEquipConst("DECOMPOSE_EQUIP_REWARD")[equip.quality]
        if not reward then
            self:logError("DecomposeEquip failed, no reward for quality: %s", equip.quality)
            return
        end
        for k, v in pairs(reward) do
            if not totalReward[k] then
                totalReward[k] = 0
            end
            totalReward[k] = totalReward[k] + v
        end
    end
    self:logInfo("DecomposeEquip success, equipUid: %v, reward: %v", equipUidList, totalReward)
    self:delEquip(equipUidList, TableConst.enums.Reason.DECOMPOSE_EQUIP)
    self:AddToBag(totalReward, TableConst.enums.Reason.DECOMPOSE_EQUIP)
    return
end

function EquipComp:SoldHorseReward(horseUidList)
    local totalReward = {}
    for _, horseUid in ipairs(horseUidList) do
        local horse, horseConfig = self:getEquip(horseUid)
        assert(horseConfig.equip_type == TableConst.enums.EquipType.Horse, "SoldHorseReward for horse only")
        if not horse then
            self:logError("SoldHorseReward failed, horseUid: %s not found", horseUid)
            return
        end
        if horse.heroId ~= 0 then
            self:logError("SoldHorseReward failed, horseUid: %s is worn by heroId: %s", horseUid, horse.heroId)
            return
        end
        local reward = GameCommon.TableDataManager:GetEquipConst("SOLD_HORSE_REWARD_CURRENCY")[horseConfig.quality]
        if not reward then
            self:logError("SoldHorseReward failed, no reward for quality: %s", horseConfig.quality)
            return
        end
        for k, v in pairs(reward) do
            if not totalReward[k] then
                totalReward[k] = 0
            end
            totalReward[k] = totalReward[k] + v
        end
    end
    self:logInfo("SoldHorseReward success, horseUid: %v, reward: %v", horseUidList, totalReward)
    self:delHorse(horseUidList, TableConst.enums.Reason.SOLD_HORSE)
    self:AddToBag(totalReward, TableConst.enums.Reason.SOLD_HORSE)
end

--endregion

--region 锻造

EquipComp.VALID_FORGE = {
    [TableConst.enums.EquipType.Weapon] = true,
    [TableConst.enums.EquipType.Defense] = true,
}

function EquipComp:ForgeEquip(equipUid)
    local equip, equipConfig = self:getEquip(equipUid)
    if not equip then
        self:logError("ForgeEquip failed, equipUid: %s not found", equipUid)
        return
    end
    assert(EquipComp.VALID_FORGE[equipConfig.equip_type], "ForgeEquip invalid equip type")
    if equip.heroId ~= 0 then
        self:logError("ForgeEquip failed, equipUid: %s is worn by heroId: %s", equipUid, equip.heroId)
        return
    end
    if not table.isempty(equip.forge.waitChooseEntries) then
        self:logError("ForgeEquip failed, equipUid: %s has choose waiting", equipUid)
        return
    end
    local forgeCostItem = GameCommon.TableDataManager:GetEquipConst("FORGE_COST_ITEM")
    if not self:ConsumeFromBag(forgeCostItem, TableConst.enums.Reason.FORGE_EQUIP) then
        self:logError("ForgeEquip failed, not enough items")
        return
    end
    self:doForgeEquip(equip, equipConfig)
end

function EquipComp:doForgeEquip(equip, equipConfig)
    --[[锻造规则
    1、锻造达到制定此术后触发保底机制。
    2、触发保底时，锻造随机范围偏移值由配表指定。
    3、否则正常锻造，偏移值由配表指定。
    4、随机范围不能小于初始最小值和最大值100。
    5、双词条锻造，先用绝对值和算总值，随后限制两者差距小于指定值时随机分配数值。
    ]]
    -- 正常锻造的属性值范围
    local minRatio = GameCommon.TableDataManager:GetEquipConst("INIT_ENTRY_VALUE_RATIO")[1]
    local rangeLow = minRatio
    local rangeHigh = equip.forge.historyBest +GameCommon.TableDataManager:GetEquipConst("FORGE_NORMAL_UP_RANGE")
    -- 保底锻造的属性值范围
    if equip.forge.continuousDecCnt >= GameCommon.TableDataManager:GetEquipConst("FORGE_GUARANTEE_TIME") then
        rangeLow = equip.forge.historyBest + GameCommon.TableDataManager:GetEquipConst("FORGE_GUARANTEE_UP_RANGE")[1]
        rangeHigh = equip.forge.historyBest + GameCommon.TableDataManager:GetEquipConst("FORGE_GUARANTEE_UP_RANGE")[2]
    end
    -- 确认值在合理范围内
    rangeLow = math.max(minRatio, rangeLow)
    rangeHigh = math.min(100, rangeHigh)
    local totalRatio = math.random(rangeLow, rangeHigh)
    local max1 = 0
    local max2 = 0
    if equip.entries[1] then
        local negativeOrPositive1 = nil
        local negativeOrPositive2 = nil
        if equip.entries[2] then
            if equip.entries[2].value < 0 then
                negativeOrPositive1 = true
                negativeOrPositive2 = false
            end
            max2 = self:getEquipEntryValue(equip.entries[2].id, equip.quality, negativeOrPositive2)
        end
        max1 = self:getEquipEntryValue(equip.entries[1].id, equip.quality, negativeOrPositive1)
    end
    local totalValue = math.floor((max1 + max2) * totalRatio / 100)
    local min1 = math.floor(max1 * minRatio / 100)
    local min2 = math.floor(max2 * minRatio / 100)
    if equip.entries[2] then
        -- 保证两词条差距小于指定值
        local maxDiff = GameCommon.TableDataManager:GetEquipConst("FORGE_DOUBLE_MAX_DIST")
        min1 = math.max(min1, (totalValue - maxDiff) / 2)
        max1 = math.min(max1, (totalValue + maxDiff) / 2)
    end
    local val1 = math.random(math.max(min1, totalValue - max2), math.min(totalValue - min2, max1))
    local val2 = math.max(min2, math.min(max2, totalValue - val1))
    local newEntries = {}
    if equip.entries[1] then
        newEntries[1] = {
            id = equip.entries[1].id,
            value = val1,
        }
        if equip.entries[2] then
            if equip.entries[2].value < 0 then
                -- 如果第二个词条是负值，则第一个词条必须是正值
                val2 = -val2
            end
            newEntries[2] = {
                id = equip.entries[2].id,
                value = val2,
            }
        end
    end
    if totalRatio < equip.forge.historyBest then
        equip.forge.continuousDecCnt = equip.forge.continuousDecCnt + 1
    else
        equip.forge.historyBest = totalRatio
        equip.forge.continuousDecCnt = 0
    end
    equip.forge.waitChooseEntries = newEntries
end

function EquipComp:ChooseForgeResult(equipUid, useNew)
    local equip, equipConfig = self:getEquip(equipUid)
    if not equip then
        self:logError("ChooseForgeResult failed, equipUid: %s not found", equipUid)
        return
    end
    assert(EquipComp.VALID_FORGE[equipConfig.equip_type], "ForgeEquip invalid equip type")
    if equip.heroId ~= 0 then
        self:logError("ChooseForgeResult failed, equipUid: %s is worn by heroId: %s", equipUid, equip.heroId)
        return
    end
    if table.isempty(equip.forge.waitChooseEntries) then
        self:logError("ChooseForgeResult failed, equipUid: %s has no waitChooseEntries", equipUid)
        return
    end
    if useNew then
        local newEntries = equip.forge.waitChooseEntries
        equip.forge.waitChooseEntries = {}  -- 释放parent
        equip.entries = newEntries
    else
        equip.forge.waitChooseEntries = {}
    end
end

--endregion

--region 重塑

function EquipComp:RebuildEquip(equipUid)
    local equip, equipConfig = self:getEquip(equipUid)
    if not equip then
        self:logError("RebuildEquip failed, equipUid: %s not found", equipUid)
        return
    end
    assert(EquipComp.VALID_FORGE[equipConfig.equip_type], "RebuildEquip invalid equip type")
    if equip.heroId ~= 0 then
        self:logError("RebuildEquip failed, equipUid: %s is worn by heroId: %s", equipUid, equip.heroId)
        return
    end
    if not table.isempty(equip.rebuild.waitChooseEffect) then
        self:logError("RebuildEquip failed, equipUid: %s has choose waiting", equipUid)
        return
    end
    if table.isempty(equip.effects) then
        self:logError("RebuildEquip failed, equipUid: %s has no effects", equipUid)
        return
    end
    local allRebuildRatio = GameCommon.TableDataManager:GetEquipConst("REBUILD_QUALITY_RATIO")
    assert(allRebuildRatio[equip.quality], "RebuildEquip invalid quality: %s", equip.quality)
    local rebuildCostItem = GameCommon.TableDataManager:GetEquipConst("REBUILD_COST_ITEM")
    if not self:ConsumeFromBag(rebuildCostItem, TableConst.enums.Reason.REBUILD_EQUIP) then
        self:logError("RebuildEquip failed, not enough items")
        return
    end
    self:doRebuildEquip(equip, equipConfig)
end

function EquipComp:doRebuildEquip(equip, equipConfig)
    --[[重塑规则
    重塑不会增加词条数量，只会重新随机品质和具体词条
    ]]
    local rebuildRatio = GameCommon.TableDataManager:GetEquipConst("REBUILD_QUALITY_RATIO")[equip.quality]
    local newEffects = {}
    local excludeId = nil
    for i = 1, #equip.effects do
        local effectQuality = ChooseEnv.RandomChooseByWeight(rebuildRatio)
        table.insert(newEffects, self:generateEquipEffect(equip.equip_type, equip.id, effectQuality, excludeId))
        excludeId = newEffects[#newEffects].id  -- 避免重复特效
    end
    equip.rebuild.waitChooseEffect = newEffects
end

function EquipComp:ChooseRebuildResult(equipUid, useNew)
    local equip, equipConfig = self:getEquip(equipUid)
    if not equip then
        self:logError("ChooseRebuildResult failed, equipUid: %s not found", equipUid)
        return
    end
    assert(EquipComp.VALID_FORGE[equipConfig.equip_type], "RebuildEquip invalid equip type")
    if equip.heroId ~= 0 then
        self:logError("ChooseRebuildResult failed, equipUid: %s is worn by heroId: %s", equipUid, equip.heroId)
        return
    end
    if table.isempty(equip.rebuild.waitChooseEffect) then
        self:logError("ChooseRebuildResult failed, equipUid: %s has no waitChooseEffect", equipUid)
        return
    end
    if useNew then
        local newEffects = equip.rebuild.waitChooseEffect
        equip.rebuild.waitChooseEffect = {}  -- 释放parent
        equip.effects = newEffects
    else
        equip.rebuild.waitChooseEffect = {}
    end
end

--endregion
