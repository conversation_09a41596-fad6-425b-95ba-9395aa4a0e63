{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode", "Field": {"event1": {"Type": "number", "Value": "4"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "2": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "buffId": {"Type": "number", "Value": "11100201"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}}, "Links": {"0": {"FormationTacticsOtherNode": ["2.prev"]}}, "DataFlows": {}}