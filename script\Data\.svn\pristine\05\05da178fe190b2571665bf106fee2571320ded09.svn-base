{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "101"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}, "event2": {"Type": "number", "Value": "5"}, "eventPriority2": {"Type": "number", "Value": "0"}, "onlySelf2": {"Type": "boolean", "Value": "False"}}}, "2": {"Type": "RemoveBuffNode", "Field": {"targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "random": {"Type": "boolean", "Value": "False"}, "count": {"Type": "number", "Value": "1"}, "isDebuff": {"Type": "boolean", "Value": "False"}, "byId": {"Type": "boolean", "Value": "True"}, "buffId": {"Type": "number", "Value": "10500102"}, "byType": {"Type": "boolean", "Value": "False"}, "buffType": [], "byBuffGroupType": {"Type": "boolean", "Value": "False"}, "buffGroupType": []}}, "4": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "buffId": {"Type": "number", "Value": "10500102"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "6": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "BlackboardValue": "Round"}, "op": {"Type": "number", "Value": "2"}, "number2": {"Type": "number", "Value": "4"}}}, "8": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "False"}, "tacticId": {"Type": "number", "Value": "0"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [], "checkDamage": {"Type": "boolean", "Value": "True"}, "damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "checkIsCrit": {"Type": "boolean", "Value": "True"}, "isCrit": {"Type": "boolean", "Value": "True"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "BlackboardValue": "DamageType"}, "damageTypes": [{"Type": "number", "Value": "1"}], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}}, "Links": {"0": {"AfterDamageNode": ["8.prev"], "RoundPrepareNode": ["6.prev"]}, "6": {"next": ["4.prev"]}, "8": {"next": ["2.prev"]}}, "DataFlows": {}}