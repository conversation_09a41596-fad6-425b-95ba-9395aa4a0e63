local CompatEnv = xrequire("Common.Actor.Compat")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")
local FormationUtilsEnv = xrequire("Common.Utils.FormationUtils")
local ServerTimeEnv = xrequire("Utils.ServerTime")

--[[双端通用部队概念类
]]

---@class CommonArmy: ComponentBase, CommonWorldActor
---@field props ArmyProps
---@diagnostic disable-next-line: assign-type-mismatch
CommonArmy = DefineClass("CommonArmy", CompatEnv.ComponentBaseEnv.ComponentBase)

function CommonArmy:GetStamina()
    return FormationUtilsEnv.CalcStaminaRecover(self.props.staminaDetail, self.props.staminaDetail.max, ServerTimeEnv.GetServerNow())
end

---@return boolean
function CommonArmy:ValidStartBehavior(behaviorType)
    local behaviorConfig = GameCommon.TableDataManager:GetSingleBehaviorConfig(behaviorType)
    if behaviorConfig and behaviorConfig.need_stamina then
        if self:GetStamina() <= 0 then
            return false
        end
    end
    return BehaviorJudgeEnv.ValidStartBehavior(self.props, behaviorType)
end

---@param vehicle CommonStoneVehicle
---@param target CommonOutpost
function CommonArmy:ValidThrowStone(vehicle, target)
    if not target then
        self:logInfo("CmdThrowStone: target actor not found")
        return false
    end
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.ThrowStone) then
        self:logInfo("CmdThrowStone: invalid behavior")
        return false
    end
    local dist = self:GetDistanceFrom(target)
    if dist > GameCommon.TableDataManager:GetSlgBuildingConst("STONE_VEHICLE_ATTACK_RANGE") then
        self:logInfo("CmdThrowStone: target is too far, dist: %s", dist)
        return false
    end
    if not vehicle then
        self:logInfo("CmdThrowStone: not in vehicle")
        return false
    end
    if not BehaviorJudgeEnv.ValidBehaviorTo(vehicle.props, target.props, vehicle._actorType, target._actorType, TableConst.enums.InteractiveBehavior.ThrowStone) then
        return false
    end
    if not target:ValidBeAttack(self) then
        return false
    end
    return true
end

return {
    CommonArmy = CommonArmy,
}