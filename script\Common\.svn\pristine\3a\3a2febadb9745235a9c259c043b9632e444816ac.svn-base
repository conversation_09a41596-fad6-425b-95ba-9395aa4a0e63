﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local BattleRecorderEnv = xrequire("Common.Battle.BattleRecorder")
local BattleHeroEnv = xrequire("Common.Battle.BattleHero")
local BattleFormulasEnv = xrequire("Common.Battle.BattleFormulas")
local BattleGameEventEnv = xrequire("Common.Battle.BattleGameEvent")
local AbilitySystemManagerServerEnv = xrequire("Common.Battle.AbilitySystem.Core.AbilitySystemManagerServer")
local BattleCheckerEnv = xrequire("Common.Battle.BattleChecker")
local DetailRecordType = TableConst.enums.DetailRecordType

local Camps = {TableConst.enums.Camp.A, TableConst.enums.Camp.B}

---@class BattleGame
local BattleGame = DefineClass("BattleGame")

function BattleGame:ctor(battleTeamA, battleTeamB, battleInfo, abilitySystemManagerClient)
    self.success = false
    BattleCheckerEnv.CheckInputTeam(Camps[1], battleTeamA)
    BattleCheckerEnv.CheckInputTeam(Camps[2], battleTeamB)

    self.battleInfo = battleInfo or {}

    self._damagePackageIdGenerator = 0
    self._healPackageIdGenerator = 0

    self.round = 0
    self.battleData = {}
    self.battleEnd = false
    self.winCamp = TableConst.enums.Camp.None
    self.battleInfoBuffer = {}

    self.recorder = BattleRecorderEnv.BattleRecorder.new(self)

    self.battleTeamA = battleTeamA
    self.battleTeamB = battleTeamB

    self.heroes = {
        [TableConst.enums.Camp.A] = {},
        [TableConst.enums.Camp.B] = {},
    }
    self.heroUniqueIdMap = {}
    for index, heroData in pairs(battleTeamA.battleArmyData.heroes) do
        if heroData.attributes[TableConst.enums.BattleAttributeType.Health] > 0 then
            local hero = BattleHeroEnv.BattleHero.new(self, TableConst.enums.Camp.A, index, heroData, battleTeamA)
            self.heroes[TableConst.enums.Camp.A][index] = hero
            self.heroUniqueIdMap[hero.uniqueId] = hero
        end
    end
    for index, heroData in pairs(battleTeamB.battleArmyData.heroes) do
        if heroData.attributes[TableConst.enums.BattleAttributeType.Health] > 0 then
            local hero = BattleHeroEnv.BattleHero.new(self, TableConst.enums.Camp.B, index, heroData, battleTeamB)
            self.heroes[TableConst.enums.Camp.B][index] = hero
            self.heroUniqueIdMap[hero.uniqueId] = hero
        end
    end

    self.orderedHeroes = {}

    self._uidGenerator = 0

    self.damagePackageMap = {}
    self.healPackageMap = {}

    self.curActionHeroUid = nil

    self.skillVariables = {}
    self.skillVariables.global = {}
    self.skillVariables.tactic = {}
    self.skillVariables.buff = {}
    self.skillVariables.hero = {}
    self.battleGameEvent = BattleGameEventEnv.BattleGameEvent.new(self)
    self.abilitySystemManager = abilitySystemManagerClient or AbilitySystemManagerServerEnv.AbilitySystemManagerServer.instance()

    self.buffsToRemoveAfterRoundEnd = {}
end

function BattleGame:GenerateUid()
    self._uidGenerator = self._uidGenerator + 1
    return self._uidGenerator
end

function BattleGame:GetTacticByUid(tacticUid)
    for _, hero in pairs(self.heroUniqueIdMap) do
        for _, tactic in ipairs(hero.tactics) do
            if tactic.uid == tacticUid then
                return tactic
            end
        end
    end
    return nil
end

function BattleGame:GetHeroByUid(heroUid)
    for _, hero in pairs(self.heroUniqueIdMap) do
        if hero.uniqueId == heroUid then
            return hero
        end
    end
    return nil
end

function BattleGame:ApplyArmyTypeQualificationModify(tableIndex, ownerHero)
    if not tableIndex then
        return
    end
    local data = GameCommon.TableDataManager:GetArmyTypeQualificationModifyData(tableIndex)
    local targets = {}
    if data.target == TableConst.enums.ArmyTypeQualificationTarget.Self then
        table.insert(targets, ownerHero)
    end
    local attributeType = TableConst.enums.BattleAttributeType.ArmyTypeQualification
    local attributeData = GameCommon.TableDataManager:GetAttributeData(attributeType)
    for _, hero in ipairs(targets) do
        local modifyValue = data.modify_map[hero.armyType]
        if modifyValue then
            if data.fix then
                hero.attributes[attributeType] = math.clamp(hero.attributes[attributeType] + modifyValue, attributeData.min_value, attributeData.max_value)
            else
                hero:AddAttributeModifier(attributeType, modifyValue, BattleConstEnv.AttributeModifierType.Fix)
            end
        end
    end
end

function BattleGame:CheckBattleEnd()
    if self.battleEnd then
        return true
    end

    local campALose = not self:GetCampStatus(TableConst.enums.Camp.A)
    local campBLose = not self:GetCampStatus(TableConst.enums.Camp.B)

    if campALose and campBLose then
        self.winCamp = TableConst.enums.Camp.None
    elseif campALose then
        self.winCamp = TableConst.enums.Camp.B
    elseif campBLose then
        self.winCamp = TableConst.enums.Camp.A
    else
        self.winCamp = TableConst.enums.Camp.None
    end

    self.battleEnd = campALose or campBLose

    return self.battleEnd
end

function BattleGame:CampCaptainDead(camp)  -- 主公死亡 
    local campHeroes = self.heroes[camp]
    local pioneer = campHeroes[TableConst.enums.Position.Pioneer]
    local adviser = campHeroes[TableConst.enums.Position.Adviser]
    local scale = Config.Battle.CaptainDeadChangeToRecoverableHealthScale
    if pioneer and pioneer:IsAlive() then
        pioneer:ModifyHealth(nil, -pioneer:GetAttribute(TableConst.enums.BattleAttributeType.Health) * scale)
    end
    if adviser and adviser:IsAlive() then
        adviser:ModifyHealth(nil, -adviser:GetAttribute(TableConst.enums.BattleAttributeType.Health) * scale)
    end
end

function BattleGame:GetCampStatus(checkCamp)
    local campHeroes = self.heroes[checkCamp]
    if campHeroes then
        return campHeroes[1]:IsAlive()
    end
    return false
end

function BattleGame:Reset()
    self.round = 0
    self.curActionHeroUid = nil
    for _, hero in pairs(self.heroUniqueIdMap) do
        hero:SetAttribute(TableConst.enums.BattleAttributeType.RecoverableHealth, 0) --进入战斗 伤兵清零
        if hero:IsAlive() then
            hero:Reset()
        end
    end

    -- 阵型战法的兵种适应性加成
    for _, hero in pairs(self.heroUniqueIdMap) do
        for _, tactic in ipairs(hero.tactics) do
            if tactic.tacticType == TableConst.enums.TacticType.ArmyType or tactic.tacticType == TableConst.enums.TacticType.Formation then
                local tacticTableData = GameCommon.TableDataManager:GetTacticData(tactic.tacticId)
                self:ApplyArmyTypeQualificationModify(tacticTableData.army_type_qualification_id, hero)
            end
        end
    end

    self.recorder:InitStatistic()
end

function BattleGame:CalculateHeroSpeed()
    self.orderedHeroes = {}
    for _, hero in pairs(self.heroUniqueIdMap) do
        if hero:IsAlive() then
            table.insert(self.orderedHeroes, hero)
        end
    end
    table.sort(self.orderedHeroes, function(hero1, hero2)
        return hero1:GetAttribute(TableConst.enums.BattleAttributeType.Speed) > hero2:GetAttribute(TableConst.enums.BattleAttributeType.Speed)
    end)
end

function BattleGame:SimulateBattleAndGetRecord()
    self.recorder:Reset()
    self:Reset()
    self:SimulateBattle()
    --xpcall(self._SimulateBattleAndGetRecord, ErrorLog, self)
    return self.recorder:GetRecord()
end

--function BattleGame:_SimulateBattleAndGetRecord()
--    self:Reset()
--    self:SimulateBattle()
--end

function BattleGame:SimulateBattle()
    self:BattleStart()
    while not self:CheckBattleEnd() and self.round < Config.Battle.MaxRound do
        self:SimulateRound()
    end
    self:BattleResult()
end

function BattleGame:SimulateRound()
    self.round = self.round + 1
    self:RoundPrepare()
    if self:CheckBattleEnd() then
        return
    end
    for order, hero in ipairs(self.orderedHeroes) do
        if hero:IsAlive() then
            self.curActionHeroUid = hero.uniqueId
            hero:SimulateHeroTurn(order)
            if self:CheckBattleEnd() then
                return
            end
        end
    end
    self:RoundEnd()
end

----------- battle stages -----------

function BattleGame:BattleStart()
    local recordArgs = {}
    -- 角色数据
    recordArgs.heroes = {}
    for _, hero in pairs(self.heroUniqueIdMap) do
        if hero:IsAlive() then
            table.insert(recordArgs.heroes, hero:Dump())
        end
    end
    self:CalculateHeroSpeed()
    recordArgs.order = {}
    for _, hero in ipairs(self.orderedHeroes) do
        if hero:IsAlive() then
            table.insert(recordArgs.order, hero.uniqueId)
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStart, self.orderedHeroes)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.Initialize, {})
    -- 布阵阶段顺序：
    self:BattleStartStageMorale()
    self:BattleStartStageArmyType()
    self:BattleStartStageTechnology()
    self:BattleStartStageEquipment()
    self:BattleStartStageTactics()
end

function BattleGame:BattleStartStageMorale()--士气
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationMorale, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartMorale)
end

function BattleGame:BattleStartStageArmyType()--兵种适应性
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationArmyType, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartArmyType)
end

function BattleGame:BattleStartStageTechnology()--科技加成
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationTechnology, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartTechnology)
end

function BattleGame:BattleStartStageEquipment()--装备
    for _, camp in ipairs(Camps) do
        local campHeroes = self.heroes[camp]
        for i = 1, 3 do
            local hero = campHeroes[i]
            if hero and hero:IsAlive() then
                self.battleGameEvent:FireHeroBattleEvent(hero, TableConst.enums.BattleEvent.FormationEquipment, {}, hero.uniqueId)
            end
        end
    end
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartEquipment)
end

function BattleGame:BattleStartStageTactics()
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationTacticsFormation, {})--战法-阵型
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationTacticsArmyType, {})--战法-兵种
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.FormationTacticsOther, {})--战法-其他
    self.recorder:DetailBattleStart(DetailRecordType.BattleStartTactics)
end

function BattleGame:RoundPrepare()
    local recordArgs = {
        round = self.round,
    }
    self:CalculateHeroSpeed()

    self.recorder:DetailRoundStart(self.round, self.orderedHeroes)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.RoundPrepare, recordArgs, true)
end

function BattleGame:RoundEnd()
    self:ModifyHeroesRecoverableHealth(nil, -Config.Battle.RoundEndChangeToRecoverableHealthScale) --大回合结束伤兵转换为死兵
    local recordArgs = {
        round = self.round
    }
    self.recorder:DetailRoundEnd(self.round)
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.RoundEnd, recordArgs, true)

    self:RemoveBuffsAfterRoundEnd()
    -- 清理伤害和治疗数据包，目前不会跨回合调用
    self.damagePackageMap = {}
    self.healPackageMap = {}
end

function BattleGame:RemoveBuffsAfterRoundEnd()
    -- 有的buff配置了移除时延时到在大回合结束时才移除
    for i = #self.buffsToRemoveAfterRoundEnd, 1, -1 do
        local buff = self.buffsToRemoveAfterRoundEnd[i]
        buff.hero:RemoveBuff(buff)
        table.remove(self.buffsToRemoveAfterRoundEnd, i)
    end
end


function BattleGame:ModifyHeroesRecoverableHealth(value, scale)
    for _, hero in ipairs(self.orderedHeroes) do
        hero:ModifyRecoverableHealth(value, scale)
    end
end

function BattleGame:BattleResult()
    self:ModifyHeroesRecoverableHealth(nil, -Config.Battle.BattleEndChangeToRecoverableHealthScale) --战斗结束伤兵转换为死兵
    self.recorder:DetailResult(self.winCamp)
    self.success = true
    self.recorder:OnBattleFinished()
end

function BattleGame:PushDamagePackage(damagePackage)
    self._damagePackageIdGenerator = self._damagePackageIdGenerator + 1
    local index = self._damagePackageIdGenerator
    damagePackage.damagePackageId = index
    self.damagePackageMap[index] = damagePackage
    return index
end

function BattleGame:SubmitDamage(graphData, damagePackage)
    local index = self:PushDamagePackage(damagePackage)
    local target = self.heroUniqueIdMap[damagePackage.targetId]
    local randomNumbers = {math.random(), math.random()} -- 用几个生成几个

    local commonArgs = {
        Index = index,
        InputTacticId = damagePackage.tacticId,
        InputBuffId = damagePackage.buffId,
        TargetId = damagePackage.targetId,
        EventSourceId = damagePackage.casterId,
        DamageRange = damagePackage.damageRange,
        DamageType = damagePackage.damageType,
        IsInevitableDamage = damagePackage.additionalModification.isInevitableDamage,
        DamagePackage = damagePackage
    }

    -- NOTICE 先计算伤害结果，可能跟实际伤害时的结果有差异，因为伤害前事件可能会修改属性导致再计算时伤害结果变化
    --        跟策划商量后，这里由策划来保证，策划需要合理配置伤害前事件的优先级来规避二者结果差异带来问题
    local damageResult = self:GetDamageResult(damagePackage, randomNumbers)

    -- 伤害前事件
    local beforeDamageArgs = table.clone(commonArgs)
    beforeDamageArgs.DamageValue = damageResult.damageValue
    beforeDamageArgs.IsCrit = damageResult.crit
    local nestIds = {}
    local handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeDamage, beforeDamageArgs, damagePackage.casterId)
    if handled then
        nestIds[TableConst.enums.BattleEvent.BeforeDamage] = beforeDamageArgs.nestId
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBeDamaged, beforeDamageArgs, damagePackage.targetId)

    -- 闪避
    local targetDodgeRate = target:GetAttribute(TableConst.enums.BattleAttributeType.DodgeRate)
    local dodgeResult
    if targetDodgeRate > 0 then --只有闪避率大于0的角色会触发闪避前后事件
        local beforeDodgeArgs = table.clone(commonArgs)
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeDodge, beforeDodgeArgs, damagePackage.targetId)
        targetDodgeRate = target:GetAttribute(TableConst.enums.BattleAttributeType.DodgeRate) --闪避前事件中可能修改闪避率
        dodgeResult = targetDodgeRate > math.random()
        if dodgeResult then
            local afterDodgeSuccessArgs = table.clone(commonArgs)
            afterDodgeSuccessArgs.DamageValue = damageResult.damageValue
            afterDodgeSuccessArgs.IsCrit = damageResult.crit
            self.recorder:DetailDodge(target.uniqueId, math.floor(damageResult.damageValue), damageResult.debug)
            self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterDodgeSuccess, afterDodgeSuccessArgs, damagePackage.targetId)
            return { casterId = damagePackage.casterId, targetId = damagePackage.targetId, accept = false }
        end
    end

    damagePackage = self.damagePackageMap[index]
    damageResult = self:GetDamageResult(damagePackage, randomNumbers)
    local beforeFatalDamageArgs = table.clone(commonArgs)
    beforeFatalDamageArgs.DamageValue = damageResult.damageValue
    beforeFatalDamageArgs.IsCrit = damageResult.crit
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeFatalDamage, beforeFatalDamageArgs, damagePackage.targetId)

    damagePackage = self.damagePackageMap[index]
    -- 伤害无效（免疫等）
    if damagePackage.disable then
        return { damagePackageId = index, casterId = damagePackage.casterId, targetId = damagePackage.targetId, nestIds = nestIds, accept = false }
    end

    local caster = self.heroUniqueIdMap[damagePackage.casterId]

    damageResult = self:GetDamageResult(damagePackage, randomNumbers)

    local finalDamage = damageResult.damageValue or 0
    if dodgeResult == false then --闪避失败后
        local afterDodgeFailArgs = table.clone(commonArgs)
        afterDodgeFailArgs.DamageValue = damageResult.damageValue
        afterDodgeFailArgs.IsCrit = damageResult.crit
        self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterDodgeFail, afterDodgeFailArgs, damagePackage.targetId)
    end

    local rdv, recoverable = target:ModifyHealth(graphData, -finalDamage)
    damageResult.damageValue = -rdv
    if graphData then
        graphData:RecordBrief(rdv, recoverable, target.camp)
    end
    self.recorder:DetailDamage(caster.uniqueId, target.uniqueId, damageResult.damageType, damageResult.damageValue, recoverable, damageResult.crit, damagePackage.tacticId, damageResult.debug)
    if target:GetAttribute(TableConst.enums.BattleAttributeType.Health) <= 0 then
        target:Die()
    end

    local result = table.clone(damagePackage)
    local afterDamageArgs = table.clone(commonArgs)
    afterDamageArgs.DamageValue = damageResult.damageValue
    afterDamageArgs.IsCrit = damageResult.crit
    afterDamageArgs.nestId = self.recorder:GetNestId()
    handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterDamage, afterDamageArgs, damagePackage.casterId)
    if handled then
        nestIds[TableConst.enums.BattleEvent.AfterDamage] = afterDamageArgs.nestId
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBeDamaged, afterDamageArgs, damagePackage.targetId)
    result.health = target:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    result.nestIds = nestIds
    result.accept = true
    result.recoverable = recoverable
    return result
end

function BattleGame:GetDamageResult(damagePackage, randomNumbers)
    local damageResult
    local caster = self.heroUniqueIdMap[damagePackage.casterId]
    local target = self.heroUniqueIdMap[damagePackage.targetId]
    local alwaysCrit = damagePackage.alwaysCrit
    local neverCrit = damagePackage.neverCrit
    local finalDamageCoefficient
    if damagePackage.additionalModification.isInevitableDamage then --必中忽略格挡
        finalDamageCoefficient = 1
    else
        finalDamageCoefficient = 1 - target:GetAttribute(TableConst.enums.BattleAttributeType.FinalDamageCoefficient)
    end
    if damagePackage.additionalModification.isFixedDamage then
        damageResult = {
            damageType = damagePackage.damageType,
            casterId = caster.uniqueId,
            damageValue = damagePackage.additionalModification.fixedDamageValue,
            crit = true
        }
    elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Attack then
        damageResult = BattleFormulasEnv.AttackDamage(randomNumbers, caster, target, damagePackage.damageFactor, alwaysCrit, neverCrit, damagePackage.additionalModification, damagePackage.overwriteArgs)
        damageResult.damageValue = damageResult.damageValue * damagePackage.globalFactor * finalDamageCoefficient
    elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Intelligence then
        damageResult = BattleFormulasEnv.IntelligenceDamage(randomNumbers, caster, target, damagePackage.damageFactor, alwaysCrit, neverCrit, damagePackage.additionalModification, damagePackage.overwriteArgs)
        damageResult.damageValue = damageResult.damageValue * damagePackage.globalFactor * finalDamageCoefficient
    elseif damagePackage.damageType == TableConst.enums.BattleDamageType.Chain or damagePackage.damageType == TableConst.enums.BattleDamageType.Share then
        damageResult = {}
        damageResult.damageValue = damagePackage.additionalModification.fixedDamageValue * damagePackage.globalFactor
    end
    damagePackage.damage = damageResult
    return damageResult
end

function BattleGame:PushHealPackage(healPackage)
    self._healPackageIdGenerator = self._healPackageIdGenerator + 1
    local index = self._healPackageIdGenerator
    healPackage.healPackageId = index
    self.healPackageMap[index] = healPackage
    return index
end

function BattleGame:SubmitHeal(graphData, healPackage)
    local index = self:PushHealPackage(healPackage)
    local target = self.heroUniqueIdMap[healPackage.targetId]
    local randomNumbers = {math.random()} -- 用几个生成几个
    local commonArgs = {
        Index = index,
        InputTacticId = healPackage.tacticId,
        InputBuffId = healPackage.buffId,
        TargetId = healPackage.targetId,
        EventSourceId = healPackage.casterId,
        HealPackage = healPackage
    }
    local beforeArgs = table.clone(commonArgs)
    beforeArgs.nestId = self.recorder:GetNestId()
    local nestIds = {}
    local handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeHeal, beforeArgs, healPackage.casterId)
    if handled then
        nestIds[TableConst.enums.BattleEvent.BeforeHeal] = beforeArgs.nestId
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.BeforeBeHealed, beforeArgs, healPackage.targetId)

    healPackage = self.healPackageMap[index]

    if healPackage.disable then
        return { healPackageId = index, casterId = healPackage.casterId, targetId = healPackage.targetId, nestIds = nestIds, accept = false }
    end

    local caster = self.heroUniqueIdMap[healPackage.casterId]
    local healResult
    if healPackage.additionalModification.isFixedHeal then
        healResult = {
            casterId = caster.uniqueId,
            heal = healPackage.additionalModification.fixedHealValue,
        }
    else
        healResult = BattleFormulasEnv.Heal(randomNumbers, caster, target, healPackage.healFactor, healPackage.overwriteArgs)
    end
    healPackage.heal = healResult.heal
    healPackage.heal = healPackage.heal * healPackage.globalFactor
    local finalHeal = healPackage.heal

    local rdv, recoverable = target:ModifyHealth(graphData, finalHeal)
    healPackage.heal = rdv
    if graphData then
        graphData:RecordBrief(rdv, recoverable, target.camp, caster.camp)
    end
    self.recorder:DetailHeal(healPackage.casterId, healPackage.targetId, healPackage.heal, healResult.debug)
    if target:GetAttribute(TableConst.enums.BattleAttributeType.Health) <= 0 then
        target:Die()
    end

    local result = table.clone(healPackage)
    local afterArgs = table.clone(commonArgs)
    afterArgs.HealValue = healPackage.heal
    afterArgs.nestId = self.recorder:GetNestId()
    handled = self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterHeal, afterArgs, healPackage.casterId)
    if handled then
        nestIds[TableConst.enums.BattleEvent.AfterHeal] = afterArgs.nestId
    end
    self.battleGameEvent:FireBattleEvent(TableConst.enums.BattleEvent.AfterBeHealed, afterArgs, healPackage.targetId)
    result.health = target:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    result.nestIds = nestIds
    result.accept = true
    result.recoverable = recoverable
    return result
end

function BattleGame:GetTeamHealth(camp)
    local heroes = self.heroes[camp]
    if not heroes then
        return 0
    end

    local health = 0
    for _, hero in pairs(heroes) do
        health = health + hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    end

    return health
end

function BattleGame:GetTeamHeroesHealth(camp)
    local heroesHealth = {}

    local heroes = self.heroes[camp]
    if not heroes then
        return heroesHealth
    end

    for idx, hero in ipairs(heroes) do
        heroesHealth[idx] = hero:GetAttribute(TableConst.enums.BattleAttributeType.Health)
    end

    return heroesHealth
end

function BattleGame:GetBattleResult()
    if self.success then
        return {success = self.success, winCamp = self.winCamp, heroesHealth = {
            [TableConst.enums.Camp.A] = self:GetTeamHeroesHealth(TableConst.enums.Camp.A),
            [TableConst.enums.Camp.B] = self:GetTeamHeroesHealth(TableConst.enums.Camp.B),
        }}
    else
        return {success = false, winCamp = TableConst.enums.Camp.None, heroesHealth = {}}
    end
end