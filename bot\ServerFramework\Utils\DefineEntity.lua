﻿require("table.clear")

---@type EntityEvent
local EntityEvent = xrequire(EZFPath .. ".Utils.EntityEvent").EntityEvent
---@type SEntityManagerEnv | CEntityManagerEnv
local EntityManager = xrequire(EZFPath .. ".Utils.EntityManager")

---@class LuaEntityClass:LuaClass
local LuaEntityClass = {}

function LuaEntityClass:fireEntityEvent() end

---@protected
function LuaEntityClass:ctor(...) end

EntityIntefaceCache = EntityIntefaceCache or {}
EntityMroCache = EntityMroCache or {}

function ClearEntityCache()
    EntityIntefaceCache = {}
    for cls, mroCache in pairs(EntityMroCache) do
        local cache, visited = unpack(mroCache)
        table.clear(cache)
        table.clear(visited)
    end
end
DeclareAndSetGlobal("ClearEntityCache", ClearEntityCache)

local function _getClsInterface(cls, name)
    -- cache
    local cache = EntityIntefaceCache
    local clsCache = cache[cls]
    local methodsCache = clsCache and clsCache[name]
    if methodsCache then
        return methodsCache
    end

    if not clsCache then
        clsCache = {}
        cache[cls] = clsCache
    end
    if not methodsCache then
        methodsCache = {}
        clsCache[name] = methodsCache
    end

    local methodsMap = {}

    for _, super in ipairs(cls.__supers or {}) do
        local suMethods = _getClsInterface(super, name)
        for _, f in ipairs(suMethods) do
            if not methodsMap[f] then
                methodsMap[f] = true
                table.insert(methodsCache, f)
            end
        end
    end

    local f = rawget(cls, name)
    if f and not methodsMap[f] then 
        methodsMap[f] = true
        table.insert(methodsCache, f)
    end

    return methodsCache
end

local function _callClsInterface(cls, name, ...)
    local methods = _getClsInterface(cls, name)
    for _, f in ipairs(methods) do
        lxpcall(f, ...)
    end
end

local function _getAllSupers(cls)
    local supers = {}
    for _, super in ipairs(cls.__supers or {}) do
        for _, c in ipairs(_getAllSupers(super)) do
            table.insert(supers, c)
        end
    end
    return supers
end

local function _checkSupers(cls, supers)
    supers = supers or {}

    local count = 0
    for _, super in pairs(supers) do
        count = count + 1
    end
    assert(count == #supers, string.format("class %s got a invalid supers, len: %s, count: %s.", cls.__cname, #supers, count))

    for _, super in ipairs(supers) do
        local superType = type(super)
        assert(superType == "table", string.format('define entity "%s" with invalid super class type "%s"', cls.__cname, superType))
    end

    local superMap = {}
    for _, super in ipairs(supers) do
        for _, c in ipairs(_getAllSupers(super)) do
            assert(not superMap[c], 
                string.format("class %s has duplicate super class %s", cls.__cname, c.__cname))
            superMap[c] = true
        end
        superMap[super] = true
    end

    return supers
end

function IsSubclass(cls, subclass)
    if cls == subclass then
        return true
    end
    for _, super in ipairs(subclass.__supers or {}) do
        if IsSubclass(cls, super) then
            return true
        end
    end
    return false
end
DeclareAndSetGlobal("IsSubclass", IsSubclass)

local function _getAllComponents(cls)
    local components = {}
    for _, super in ipairs(cls.__supers or {}) do
        for _, c in ipairs(_getAllComponents(super)) do
            table.insert(components, c)
        end
    end
    for _, comp in ipairs(cls.__comps or {}) do
        table.insert(components, comp)
    end
    return components
end

local function _checkComponents(cls, comps)
    comps = comps or {}

    local count = 0
    for _, comp in pairs(comps) do
        count = count + 1
    end
    assert(count == #comps, string.format("class %s got a invalid comps, len: %s, count: %s.", cls.__cname, #comps, count))  

    local isParentOrChild = function(l, r)
        return IsSubclass(l, r) or IsSubclass(r, l)
    end

    local allcomps = {}
    for _, super in ipairs(cls.__supers or {}) do
        for _, c in ipairs(_getAllComponents(super)) do
            for _, comp in ipairs(allcomps) do
                assert(not isParentOrChild(c, comp),
                    string.format("class %s has duplicate component %s", cls.__cname, comp.__cname)
                )
            end
        end
    end
    for _, c in ipairs(comps) do
        for _, comp in ipairs(allcomps) do
            assert(not isParentOrChild(c, comp),
                string.format("class %s has duplicate component %s", cls.__cname, comp.__cname)
            )
        end
    end

    -- 同名方法检查
    local attrMap = {}
    for _, comp in ipairs(comps) do
        assert(comp.__cname)
        for k, v in pairs(comp) do
            -- 双下划线开头被认为是metatable或者扩展lua所使用的，不进行拷贝
            if k:sub(1, 2) ~= "__" then
                -- LuaClass中定义的方法与Entity接口方法不进行拷贝
                if EntityEvent[k] == nil and LuaClass[k] == nil then
                    if (cls[k] or attrMap[k]) and not XReloading then
                        error(string.format('%s RegistComponent have same attr %s, please check', comp.__cname,k))
                    end
                    attrMap[k] = v
                end
            end
        end
    end

    return comps
end

local function _initClsSupersAndComponents(name, cls, supers, comps)
    cls.__supers = _checkSupers(cls, supers)
    cls.__comps = _checkComponents(cls, comps)

    for _, comp in ipairs(cls.__comps) do
        -- 放入supers，component与super同等处理，但有额外的检查
        table.insert(cls.__supers, comp)

        if comp.onComposed == nil then
            error(string.format('Component %s must inherit ComponentBase', comp.__cname))
        end
        comp.onComposed(comp, cls)
    end
end

local function _getClsMroCache(cls)
    local cache = {}
    local visited = {}
    EntityMroCache[cls] = {cache, visited}
    setmetatable(cache, {__index = function(cache, key)
        local v = nil
        if EntityEvent[key] ~= nil or LuaClass[key] ~= nil then
            goto found
        end

        v = rawget(cls, key)
        if v ~= nil then
            goto found
        end
        for _, c in ipairs(cls.__supers) do
            v = c[key]
            if v ~= nil then
                goto found
            end
        end

        ::found::
        visited[key] = true
        cache[key] = v
        return v
    end})
    return cache
end

--- 自定义oop类，可以完成继承
---@generic T, COMP
---@overload fun(name: string, supers: T[])
---@param name string
---@param supers T[]
---@param components COMP[]
---@return LuaEntityClass
function DefineEntity(name, supers, components)
    local env
    if LUA_VERSION_NUM < 502 then
        env = getfenv(2)  -- caller's env
        assert(env ~= _G, "class %s must be defined in an environment other than _G", name)
    else
        env = _ENV
    end
    
    if env[name] == nil then
        env[name] = {__cname = name, __full_name = env.__module_name .. name}
    end

    ---@type LuaEntityClass
    local cls = env[name]

    -- init supers & components
    _initClsSupersAndComponents(name, cls, supers, components)

    -- init class method
    local cache = _getClsMroCache(cls)
    setmetatable(cls, {__index = cache})
    cls.__index = cache
    cls.__tostring = function(instance)
        return instance.__info_str or string.format("%s(%u)", instance.__entity_name, instance.id)
    end
    cls.__class = cls
    cls.__call_ctor = function(instance, ...)
        _callClsInterface(cls, "ctor", instance, ...)
    end

    cls.fireEntityEvent = function(inst, event, ...)
        if EntityEvent[event] == nil then
            error(string.format("calling %s which is not a valid event", event))
        end
        _callClsInterface(cls, event, inst, ...)
    end

    -- reload不重新注册
    if not XReloading then
        EntityManager.EntityManager.registEntity(name, cls, cls.Migratable or false)
    end

    return cls
end
DeclareAndSetGlobal("DefineEntity", DefineEntity)
