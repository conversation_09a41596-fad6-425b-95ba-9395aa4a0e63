﻿local NodeBaseServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.NodeBaseServer")
local CommonMethodsEnv = xrequire("Common.Battle.AbilitySystem.Core.CommonMethods")

---@class CompareNodeServer : NodeBaseServer
local CompareNodeServer = DefineClass("CompareNodeServer", NodeBaseServerEnv.NodeBaseServer)

function CompareNodeServer:Execute(graphData)
    CommonMethodsEnv.CompareNodeExecute(self, graphData)
end