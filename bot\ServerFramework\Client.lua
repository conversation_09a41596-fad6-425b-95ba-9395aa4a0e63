---@class Client: LuaClass
---@field urs Urs
---@field gateClient LuaGateClient
---@field mainEntity CAvatarEntity | nil
---@field mainEntities table
---@field cryptoType string
Client = DefineClass("Client")
Client.cryptoPubKey = [[
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7Qdtm/8vo5Lpxpw4wRQUTFfAH
FBMQ90b/rWmNaFR/5cNzNs2pvfJBYHX+tbUrtaMgyroUQZWtJOd0CpgBxwAewold
NlThLPJq881bWXt+yJNcXmyfEPf3WV1ShybwVKTBWgrHDPKfnmIdVOYeK7L0vuPM
4ZIvEb0FSgxYwT9ULwIDAQAB
-----E<PERSON> PUBLIC KEY-----
]]

---@param urs Urs
function Client:ctor(urs)
    self.urs = urs
    self.gateClient = LuaGateClient.new()
    self.gateClient:setCryptoPubKey(Client.cryptoPubKey)
    self.cryptoType = "ChaCha20"
    self.mainEntity = nil
    self.mainEntities = {}
end

---@param host string
---@param port int
---@param protocol ProtocolType
---@param cryptoType? string
function Client:connect(host, port, protocol, cryptoType)
    InfoLog("connect %s %s:%s %s", protocol, host, port, cryptoType)
    self.cryptoType = cryptoType or self.cryptoType
    self.gateClient:connect(host, port, protocol, self)
end

function Client:onConnect(errcode)
    if errcode ~= 0 then
        ErrorLog("Client connect failed")
        return
    end
    InfoLog("Client connected")
    self.gateClient:handshake(self.cryptoType)
end

function Client:onDisconnect(errcode)
    InfoLog("Client(%s) disconnected, err(%s)", self.urs, errcode)
end

function Client:onHandshake(errcode)
    if errcode ~= 0 then
        -- 1: checksum_mismatch
        -- 2: crypto_err
        ErrorLog("onHandshake failed(%s)", errcode)
        return
    end
    self.gateClient:login()
end

function Client:onCreateOwnedEntity(ent)
    table.insert(self.mainEntities, ent)
    if self.mainEntity == nil then
        self:setMainEntity(ent)
    end
end

function Client:onDestroyOwnedEntity(ent)
    for i, ownedEnt in pairs(self.mainEntities) do
        if ownedEnt == ent then
            table.remove(self.mainEntities, i)
            break
        end
    end
    if self.mainEntity == ent then
        self:setMainEntity(self.mainEntities[1])
    end
end

function Client:createEntity(entityName, entityId, initData, owned)
    ---@type CEntity
    local ent = EntityManager.createEntity(entityName, entityId, initData)
    ent:setClient(self)
    if owned then
        self:onCreateOwnedEntity(ent)
    end
end

function Client:destroyEntity(entityId)
    local ent = EntityManager.getEntity(entityId)
    if ent ~= nil then
        if ent:owned() then
            self:onDestroyOwnedEntity(ent)
        end
        ent:destroy()
    else
        WarnLog("can not find Entity(%s) when receive destroy entity from server", entityId)
    end
end

---@param ent CAvatarEntity | nil
function Client:setMainEntity(ent)
    self.mainEntity = ent
end

-- for emmylua
---@class ClientEnv
local ClientEnv = {Client=Client}
return ClientEnv
