{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "100"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "True"}}}, "2": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "True"}, "tacticId": {"Type": "number", "BlackboardValue": "InputTacticId"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [{"Type": "number", "Value": "5"}], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}, "3": {"Type": "ModifyDamageNode", "Field": {"damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "immune": {"Type": "boolean", "Value": "False"}, "modifyTarget": {"Type": "boolean", "Value": "False"}, "newTargetId": {"Type": "string", "Value": ""}, "modifyCritAttr": {"Type": "boolean", "Value": "False"}, "alwaysCrit": {"Type": "boolean", "Value": "False"}, "neverCrit": {"Type": "boolean", "Value": "False"}, "damageFactor": {"Type": "number", "Value": "1"}, "modifyComboAttr": {"Type": "boolean", "Value": "False"}, "isComboDisabled": {"Type": "boolean", "Value": "False"}}}, "4": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": ""}, "varName": {"Type": "string", "Value": "0"}, "desc": {"Type": "string", "Value": "徐盛普攻伤害增加（独立乘区）a=武力"}, "result": {"Type": "number", "Value": "0"}}}, "5": {"Type": "OverwriteBattleFormulaNode", "Field": {"formulaType": {"Type": "number", "Value": "0"}, "damageArgs": [{"Type": "string", "Value": "G:2:0.75"}], "healArgs": [], "output": {"Type": "nil", "Value": "null"}}}, "6": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "2"}, "number2": {"Type": "number", "Value": "0"}}}, "7": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "EventSourceId"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "20"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "8": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "TargetId"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "20"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "9": {"Type": "ModifyDamageNode", "Field": {"damagePackageId": {"Type": "number", "BlackboardValue": "Index"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "immune": {"Type": "boolean", "Value": "False"}, "modifyTarget": {"Type": "boolean", "Value": "False"}, "newTargetId": {"Type": "string", "Value": ""}, "modifyCritAttr": {"Type": "boolean", "Value": "False"}, "alwaysCrit": {"Type": "boolean", "Value": "False"}, "neverCrit": {"Type": "boolean", "Value": "False"}, "damageFactor": {"Type": "number", "Value": "1"}, "modifyComboAttr": {"Type": "boolean", "Value": "False"}, "isComboDisabled": {"Type": "boolean", "Value": "False"}}}, "10": {"Type": "OverwriteBattleFormulaNode", "Field": {"formulaType": {"Type": "number", "Value": "0"}, "damageArgs": [{"Type": "string", "Value": "C4:1:0.6"}], "healArgs": [], "output": {"Type": "nil", "Value": "null"}}}, "11": {"Type": "GetAttributeNode", "Field": {"heroIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": []}}, "13": {"Type": "PrintNode", "Field": {"prefix": {"Type": "string", "Value": "目标兵力"}, "printTarget": {"Type": "string", "Value": ""}}}, "14": {"Type": "PrintNode", "Field": {"prefix": {"Type": "string", "Value": "徐盛兵力"}, "printTarget": {"Type": "string", "Value": ""}}}}, "Links": {"0": {"BeforeDamageNode": ["7.prev", "2.prev"]}, "2": {"next": ["5.prev"]}, "5": {"next": ["3.prev"]}, "6": {"next": ["10.prev"]}, "7": {"next": ["8.prev"]}, "8": {"next": ["13.prev"]}, "10": {"next": ["9.prev"]}, "11": {"next": ["4.prev"]}, "13": {"next": ["14.prev"]}, "14": {"next": ["6.prev"]}}, "DataFlows": {"5": {"output": ["3.overwrite<PERSON><PERSON>s"]}, "7": {"attributeValues": ["6.number1", "14.<PERSON><PERSON><PERSON><PERSON>"]}, "8": {"attributeValues": ["6.number2", "13.<PERSON><PERSON><PERSON><PERSON>"]}, "10": {"output": ["9.overw<PERSON><PERSON><PERSON>s"]}, "11": {"attributeValues": ["4.a"]}}}