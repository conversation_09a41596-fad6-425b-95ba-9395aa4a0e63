----------------------- WARNING -----------------------
---     目前coroutine有风险；游戏正式模块请勿使用      ---
----------------------- WARNING -----------------------

---@type AsyncExecutorEnv
local AsyncExecutor = xrequire(EZFPath .. ".Async.AsyncExecutor")

---@class AsyncExecutorMgr
---@field executors table<AeId, AsyncExecutor>
---@field timer Entity
AsyncExecutorMgr = DefineSingletonClass("AsyncExecutorMgr")

function AsyncExecutorMgr:ctor()
    self.executors = {}
    self.timer = EntityManager.createEntity("Entity", 0, {})
end

---@param aeId AeId
---@param fun AsyncFunc
---@param handleResume? HandleResume
---@param hookResume? HookResume
---@return AsyncExecutor | nil
function AsyncExecutorMgr:create(aeId, fun, handleResume, hookResume)
    if self:remove(aeId) then
        local ae = AsyncExecutor.AsyncExecutor.new(aeId, fun, handleResume, hookResume)
        self.executors[aeId] = ae
        return ae
    end
    ErrorLog("AsyncExecutorMgr: aeId[%s] recreate failed!", aeId)
    return nil
end

---@param aeId AeId
---@param force? boolean
---@return boolean
function AsyncExecutorMgr:remove(aeId, force)
    ---@type AsyncExecutor | nil
    local ae = self.executors[aeId]
    if not ae then
        return true
    end
    local status = ae:status()
    if force or status == "dead" then
        self.executors[aeId] = nil
        return true
    end
    ErrorLog("AsyncExecutorMgr: aeId[%s] remove failed! status is %s", aeId, status)
    return false
end

---@param aeId AeId
---@return AsyncExecutor | nil
function AsyncExecutorMgr:get(aeId)
    return self.executors[aeId]
end

DeclareAndSetGlobal("AsyncExecutorMgr", AsyncExecutorMgr.instance())

-- for emmylua
---@class AsyncExecutorMgrEnv
local AsyncExecutorMgrEnv = {AsyncExecutorMgr=AsyncExecutorMgr}
return AsyncExecutorMgrEnv
