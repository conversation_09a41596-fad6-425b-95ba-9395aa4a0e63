local CompatEnv = xrequire("Common.Actor.Compat")

--[[城门组件
概念：归属城池、关口、渡口，需要通过攻城流程攻打的Actor
]]


---@class CommonCityGate: ComponentBase, CommonWorldActor
---@field props CityGateCompProps
---@diagnostic disable-next-line: assign-type-mismatch
CommonCityGate = DefineClass("CommonCityGate", CompatEnv.ComponentBaseEnv.ComponentBase)
CommonCityGate.HasCommonCityGate = true  ---@type boolean

function CommonCityGate:IsInWar()
    return self.props.inWarWith ~= nil
end

---@param allyId AllyId
function CommonCityGate:IsInWarWith(allyId)
    return self.props.inWarWith == allyId
end

return {
    CommonCityGate = CommonCityGate,
}