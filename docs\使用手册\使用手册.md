# 使用手册

# 概述

## 设计目标

- 高性能、高可用、可扩展
- 简单易用
- 可热更新

## 架构简介

### 物理架构

EZEngine采用统一的进程EZServer提供服务。EZServer可以对外部玩家提供服务，也可以只提供对内网服务。EZServer进程之间通过内网联通，并可通过内网连接进行通信。

![Untitled](物理架构.png)

### 进程架构

EZEngine采用多进程多线程的集群架构，整体集群架构图如下

![Untitled](进程架构.png)

架构中，主要包含以下部分

- Node，即EZServer进程，是Container的载体。同时为Container提供网络、消息队列、集群通信等基础设施。
- Container，游戏逻辑的载体，在每个Container中都会提供独立的lua虚拟机。Container之间采用消息队列通信。
- 消息队列，为Container之间提供线程安全的通信方式。
- Network，提供网络基础设施，比如TcpServer、TcpClient、TelnetServer等，后续会持续增加KcpServer、KcpClient等。
- Sidecar，集群管理的基础设施。负责集群内服务发现、消息转发、数据共享等。

### 逻辑架构

EZEngine中所有通信都需要以Entity为主体，所以相关的数据、逻辑都建议以Entity的方式进行组织，更统一、也方便维护。

Entity的载体为Container，所有Entity都需要创建在Cointainer中，同一时刻从属且只从属于某个Container。EntityId是由NodeId、ContainerId与自增Id构成，EntityId也是Entity间通信的唯一地址。

Entity间通信主要有两种方式

- Container内部可以直接通过对象访问调用方法，也可以通过Rpc通信。
- Container之间可以通过Rpc通信。

![Untitled](Rpc通信.png)

### 功能

为了适应不同的应用场景与需求，将引擎被分为了两层`EZCore`与`EZFramework` 。

`EZCore`目标是提供最基础的功能，比如网络、消息队列、集群管理、Entity管理等。并提供良好的扩展性，可以根据需求自行扩展。

`EZFramework`目标是提供更丰富的功能集，尽量达到即插即用，避免重复早轮子。相比与`EZCore`增加了，客户端连接网关-Gate、数据库接口-DBMgr、Entity定义、Entity自动存盘、Entity属性系统等。

## 集群管理

### 集群简介

目前引擎已初步支持由多个Node组建集群。
- 其中一部分Node包含Raft模块，组成一个更小Raft集群，主要负责维护、存储、广播集群消息。
- 所有Node都包含ClusterMgr模块，主要负责收发和处理集群消息。

![Untitled](集群.png)

### 集群配置

集群需要知晓Raft相关信息，这部分内容都需要在配置文件中指定。
```json
{
	"#raft_cluster": "raft集群配置；id是nodeId",
	"raft_cluster": [
		{"id": 1, "host": "127.0.0.1", "port": 1001, "client_port": 1101},
		{"id": 2, "host": "127.0.0.1", "port": 1002, "client_port": 1102},
		{"id": 3, "host": "127.0.0.1", "port": 1003, "client_port": 1103}
	]
}
```
被指定的Node会启用Raft模块，互相之间会组成Raft集群。需要注意的是，如果出现容灾等异常情况，需要重启Node时，需要加上`--recover`参数。

### 集群和Node

当有Node加入或离开集群时，回调脚本如下：
```lua
function GlobalCallback:onNodeRegistered(nodeId, uid, containers)
end

function GlobalCallback:onNodeRemoved(nodeId)
end
```

当自身Node和集群连上或断开时，回调脚本如下：
```lua
-- 第一次连接会创建container；后面重连才有回调
function GlobalCallback:onAttachCluster()
end

function GlobalCallback:onDetachCluster()
end
```

### 集群和Global Entity

Global Entity是在集群内开放访问的对象，比较典型的应用就是Service。

其注册、注销接口如下：
```lua
EZE.registerGlobalEntity(name, eid, ready, instance_data)

EZE.unregisterGlobalEntity(eid)
```

当Global Entity注册时，ready表示是否已经初始化完成；instance_data是该实例的数据，会跟随GlobalEntity广播给所有container，是打包好的二进制数据，可以用来指定分片等。

当Global Entity注册加入集群时，只是表示可以接收和执行rpc了，但并不意味着其初始化已经完成（有可能其本身的初始化就依赖rpc）。
对于初始化过程中不需要rpc的，在初始化完成后注册，ready传参为true。
对于初始化过程中需要rpc的，可先在适当时先以ready为false的方式注册，等初始化完成再调用以下接口：
```lua
EZE.setGlobalEntityReady(eid)
```

当有Global Entity加入或离开集群时，回调脚本如下：
```lua
function GlobalCallback:onGlobalEntityRegistered(name, mailbox_str, ready, instance_data)
end

function GlobalCallback:onGlobalEntityRemoved(name, mailbox_str)
end
```

当有Global Entity初始化完成时，回调脚本如下：
```lua
function GlobalCallback:onGlobalEntityReady(name, mailbox_str)
end
```

- 当container初始化时，会先执行`onContainerInited`；随后是已有GlobalEntity的注册；最后是`onContainerReady`。其中已有GlobalEntity的注册依赖的是引擎缓存的历史数据；只有一份最新的数据，没有变化过程，且各个GlobalEntity的注册顺序不保序。

GlobalEntity需要在配置中指定：
```json
{
	"global_entities":
	{
		"DBMgr": {"instances_required": 1, "dependence": [], "strategy": "random_once"},
		"RoleService": {},
		"PersistentService": {"dependence": ["DBMgr", "AsyncReadyService"]},
		"AsyncReadyService": {}
	},
}
```

其中：
- instances_required表示起服需要的最小实例数量，默认值是1。这个不仅是起服的需求，也是创建其他有依赖关系的对象的需求。
- dependence表示依赖哪些GlobalEntity，当这些GlobalEntity初始化完成（ready）且数量满足需求之后才会创建自身。
- strategy是在多实例的情况下选择rpc目标的策略。这个值完全由脚本去定义和使用，可以参考GlobalEntitySelector.lua。

### 集群和GlobalData

集群内有数据共享和广播的需求，都可以通过GlobalData实现

GlobalData接口：
```lua
-- 写入数据
EZE.pushGlobalData(key, val)

-- 更新数据
function GlobalCallback:onPushedGlobalData(key, val)
    ...
end
```
- 其中key支持`string`；val支持任意基础类型`boolean | integer | float | string | list | table<string | integer, table>`，支持table的嵌套
- `pushGlobalData`是异步的，只有当被集群确认之后才会更新回调
- 和`GlobalEntity`一样，初始化container时的执行时序是`onContainerInited`->`onPushedGlobalData`->`onContainerReady`。在onContainerReady之前调用的都是历史数据，历史数据只保证最终一致性且不保序

#### GlobalDataMgr

GlobalDataMgr主要对数据存取和更新回调分发做了支持：
```lua
-- 写入数据
---@param key string @global data key
---@param val nil|boolean|number|string|table @global data value
GlobalDataMgr:push(key, val)

-- 读取数据
---@param key string @global data key
---@return nil|boolean|number|string|table
GlobalDataMgr:get(key)

-- 注册更新回调
---@param key string @global data key
---@param cbKey string @回调的唯一id
---@param cb fun(val: nil|boolean|number|string|table):boolean @回调函数，返回true表示在回调后注销回调；注意在回调时不要再进行注册或注销操作
GlobalDataMgr:registerCallback(key, cbKey, cb)

-- 注销更新回调
---@param key string @global data key
---@param cbKey string @回调的唯一id
GlobalDataMgr:unregisterCallback(key, cbKey)
```

- 需要注意历史数据只有最终一致性且不保序，可能会对回调产生影响。可以考虑在`onContainerReady`先处理历史数据再注册回调以规避这个问题

## 内建Entity

### ContainerEntity [`EZCore`]

ContainerEntity是每个Container在创建之后必须会创建的一个Entity，每个Container中有且仅有一个。

ContainerEntity主要用来进行全局相关的回调，以及在非Entity作为通信发起方时，作为引擎内部通信的一个载体（比如：`createEntityFromDB`）。服务器启动时的回调（`onContainerInited`，`onContainerReady`），客户端登录后的回调（`clientLogin`）都是通过ContainerEntity执行的。

ContainerEntity目前是一个C Entity，对Lua脚本透明。

### LuaEntityBase [`EZCore`]

所有Lua Entity的基类，提供Entity的基础功能，包括：注册、创建、销毁、收发rpc等。

### LuaGameEntityBase [`EZFramwork`]

`EZFramwork`  中对LuaEntityBase的扩展，新增了对Entity定义、自动存盘、数据库接口、绑定客户端、属性系统等功能。

在使用`EZFramwork` 时，推荐作为所有脚本定义Entity的基类。

在Lua脚本中，封装了两个基于LuaGameEntityBase的基类，`Entity`与`AvatarEntity`。`AvatarEntity`相比于`Entity`增加了对客户端Entity绑定关系的管理，即具备了客户端绑定该Entity的能力。

当服务器绑定某个Entity到客户端时，会自动在客户端创建同名Entity，并且确定两个Entity之间的绑定关系。两个Entity之间除了类型名一致外，还具有相同的EntityId，且可以在两个Entity之间发送rpc与属性同步。

### LuaClientEntityBase [`EZFramwork`]

LuaGameEntityBase在客户端对应的Entity类型，接收来自服务端的rpc与属性同步，也支持向服务器发送rpc。

### LuaGateEntityBase [`EZFramwork`]

客户端连接服务器的网关，管理所有客户端连接。所有在客户端与服务器之间的rpc都需要经过LuaGateEntityBase转发。

![Untitled](Gate逻辑架构.png)

LuaGateEntityBase支持创建多个进行水平扩容，提升承载。客户端可以根据项目需求指定Gate选择策略，一般情况下随机即可。但同一Container中，不建议创建多个GateEntity。

GateEntity为Lua脚本中对LuaGateEntityBase的封装。

### LuaDBMgr [`EZFramwork`]

Entity与数据库通信的中间件，目前主要支持MongoDB，后续考虑添加对Redis的支持。

目前数据库的请求主要分为两类

1. Entity存盘相关。
    1. 从数据库中加载Entity数据
    2. 将Entity数据存入数据库
    3. 从数据库中删除Entity
2. 直接操作MongoDB collection。比如MongoDB的find、find_one、insert_one等。

LuaDBMgr会创建工作线程处理数据库请求，可以通过配置文件中的`worker_thread_num`配置。会创建MongoDB连接线程池，线程池大小由配置控制`max_pool_size`。

DBMgr为Lua脚本中对LuaDBMgr的封装。

## 主要流程

### 服务器启动

#### 进程启动

启动进程之后，会进行读取配置文件、创建node、尝试连接/构建Raft等操作。

#### 创建和初始化Container

连接上Raft之后，Node会根据配置文件，创建对应类型与数量的Container。

对于每个Container来说，都会经历
1) 创建container对象
2) Lua虚拟机初始化，并执行Init.lua脚本。
3) 回调`onContainerInited`，container创建成功。
4) 回调`onContainerReady`，container初始化完成。

一般在这个过程中创建GlobalEntity。

#### 集群Ready（正常运行）

当所有的GlobalEntity都完成初始化且数量满足需求之后，集群就被设置为Ready，进入正常运行状态了。同时有以下回调：
```lua
function GlobalCallback:onClusterReady()
end
```

由前文的叙述可知，`onGlobalEntityReady`和`onContainerReady`都早于`onClusterReady`。

### 服务器关闭

以下接口可以向集群发送关服请求：
```lua
EZE.startShutdown()
```
目前的关服脚本都是通过telnet发送该命令实现的。

当关服请求并被集群确认接受之后，就正式进入了关服流程。整个关服过程会经过若干个阶段，下文每个阶段处理的业务内容只是一个示例，并不强制，项目可以自由定制。

#### 开始关服
脚本回调：
```lua
function GlobalCallback:onClusterClosing()
end
```
一般在这一阶段可以进行一些关服准备工作，如果所有准备工作都已完成，就可以调用以下接口通知集群：
```lua
EZE.continueShutdown()
```
当集群确认所有Node的所有Container都调用了该接口，整个集群就可以进入下一个阶段了。

注意该接口在关服过程中是通用的，当前阶段处理完成都需要调用该接口，后文中不再赘述。

#### 关闭网络
一般在此时关闭gate；断开和客户端的连接，回调如下：
```lua
function GlobalCallback:onClusterDisconnectClient()
end
```

#### 预存储
一般在此时处理一些可以提前存储和删除的对象，回调如下：
```lua
function GlobalCallback:onClusterPreSave()
end
```

#### 全对象存储
一般在此时存储和删除大部分对象，回调如下：
```lua
function GlobalCallback:onClusterSaving()
end
```

#### 后置存储
一般在此时存储和删除剩余的对象（如service等），回调如下：
```lua
function GlobalCallback:onClusterPostSave()
end
```

#### 关服成功
此时关服就已经成功了，一般输出一些日志即可。回调如下：
```lua
function GlobalCallback:onClusterClosed()
end
```
这里同样需要调用`EZE.continueShutdown()`，随后引擎会释放资源、停止各个模块的运行。
引擎在关服成功后不会退出进程，也不会做其他操作了。

### 客户端登录

1. 客户端连接GateEntity监听的端口，建立连接。
2. 客户端发送登录请求到GateEntity，GateEntity会生成唯一的SessionId用来标记这个客户端连接，且从可接受客户端连接的Container中随机选择一个，发送clientLogin。
3. Container中收到clientLogin后，回调到全局回调中。Lua脚本负责创建对应的登录Entity，并绑定客户端。
4. 绑定客户端后，会在客户端自动创建同名Entity。此时引擎中的客户端登录流程基本完成。
5. 客户端向服务器发送账号登录请求。
6. 服务端检查账号是否已经登录。如果已登录则执行顶号流程。
7. 如果未登录则从数据库中加载玩家数据，并创建对应的Entity。若玩家数据不存在，则直接创建新的Entity。
8. 服务端绑定新创建的Entity。
9. 客户端自动创建玩家Entity。

## 容灾恢复

### service容灾恢复

#### 主备模式（active-standby）

主备模式是指以一主一（多）备的形式创建多个service实例的模式。其中主实例负责处理所有的业务逻辑，并将所有属性（properties）的变更推送到备实例上；备实例平时不做任何处理，只在主实例失效时尝试转换为主实例。

以`PersistentService`为例，启用主备模式需要做以下修改

1. Service继承`ActiveStandbyService`
2. 配置多个Service实例（一般配置在不同的Node中）
3. 在`global_entities`中标注`active_standby`为`true`，表示使用主备模式；
   并指定“主”+“备”的实例总数。
    ```json5
    "global_entities": {
        "PersistentService": {"active_standby": true, "instances_required": 2}
    },
    ```

除了以上修改，使用时仍需注意：

- service只会恢复defs中定义的属性（properties），其他数据会被丢弃
- 主备切换时可能丢失数据，需做好相关处理
- 主备模式下，只有一个主实例处于正常服务状态；配置中的`strategy`只能配置为`active_standby`或省略。

#### rpc恢复数据模式

该模式的核心思路是在现存的Node中搜集数据，并以rpc的形式上报给重新拉起的Service实例。
以下以RoleService举例说明rpc恢复数据模式

1. Service继承`RpcRecoverService`
2. Service添加容灾rpc，注意须设置为允许初始化期间发送。（相应的，在目标Service尚未ready的情况下，未设置该标记的rpc将会被丢弃）
    ```json
	{
		"server_methods": [
			{"name": "avatarRecover", "args": ["int", "StrStrDict"], "flag": ["RPC_FLAG.INIT"]}
		]
	}
	```
3. Service实现搜集数据的方法
   - 该方法名称固定为`recoverRpc`；必须是类方法
   - 该方法将在容灾发生时所有可用的container中执行
   - 该方法的作用是收集本地（contianer）的数据，并上报Service
    ```lua
    ---@param eid EntityId @new service entity id
    ---@param prevEid EntityId @old service entity id
    function RoleService.recoverRpc(eid, prevEid)
        -- 将本地的avatar同步给RoleService，考虑到玩家数量较多，需要做分批处理
        local Avatar = xrequire("Entities.Avatar")
        local avts = {}
        local cnt = 0
        for _, avt in pairs(Avatar.instances) do
            avts[avt.props.urs] = avt:getMailboxStr()
            cnt = cnt + 1
            if cnt > 1000 then
                EZGlobal.Entity:serviceRpc("RoleService", "avatarRecover", containerId, avts)
                avts = {}
                cnt = 0
            end
        end
        if cnt > 0 then
            EZGlobal.Entity:serviceRpc("RoleService", "avatarRecover", containerId, avts)
        end
        -- 正在登陆中account，保险起见全部销毁重登
        local Account = xrequire("Entities.Account")
        for _, acc in pairs(Account.Account.instances) do
            acc:destroy()
        end
    end
	 ```

# 接入

引擎CI地址：ftp://ez@************/

可以从上述地址中下载引擎编译好的包。每个版本的文件夹中包含以下4个压缩包，分别对应不通平台的编译结果

- package_linux_release.zip
- package_windows_release.zip
- package_android_release.zip
- package_ios_release.zip

其中Windows平台包括引擎对应的Lua Framework，以及Demo。

## 客户端SDK

客户端SDK接入说明以Unity为例，其他引擎也类似。

#### 目录

1. SDK

   - Assets/Plugins/x86_64/ezclient.dll，Windows平台引擎Sdk

   - Assets/Plugins/Android/libs/arm64-v8a/libezclient.so，Android平台引擎Sdk

   - Assets/Plugins/iOS/libezclient.a，iOS平台引擎Sdk

2. Defs，客户端与服务端的逻辑协议

   - Assets/ServerDefs，与服务端的Defs一致，一般采用svn外链的方式。

3. Lua

   - ServerEntities，客户端中与服务器通信的Entities

   - ServerFramework，引擎脚本框架

#### SDK接入

SDK暴露出来的接口如下

```c++
/**
 * 使用外部传入的Lua虚拟机作为引擎的Lua虚拟机。
 * 
 * @param L 外部传入的Lua虚拟机
 */
DLL_API int clientsdk_init_luastate(lua_State *L);

/**
 * 初始化SDK，需要在设置外部Lua虚拟机后调用。
 * 
 * @param script_root 脚本根目录，非必须
 */
DLL_API int clientsdk_init(const char *script_root);

/**
 * 启动SDK后台线程与服务。
 */
DLL_API int clientsdk_start();

/**
 * 关闭SDK后台线程与服务。
 */
DLL_API void clientsdk_stop();

/**
 * 在主线程中处理消息，包括rpc。
 */
DLL_API int clientsdk_poll();

/**
 * 取消设置引擎日志Handler
 */
DLL_API void clientsdk_clear_log_writter();

/**
 * 设置引擎日志输出Handler，方便将SDK的日志输出到Unity等客户端引擎中。
 * 
 * @param log_writter 日志Handler，签名为void handler(int level, const char* msg, size_t msglen)
 */
DLL_API void clientsdk_set_log_writter(void (*log_writter)(int, const char*, size_t len));
```

Unity中的接入示例如下

```c#
using System;
using System.Runtime.InteropServices;
using LuaInterface;
using UnityEngine;

public class ServerSComponent : ISComponent
{
#if !UNITY_EDITOR && UNITY_IPHONE
    const string CLIENTSDKDLL = "__Internal";
#else
    const string CLIENTSDKDLL = "ezclient";
#endif

    public void OnUpdate()
    {
        clientsdk_poll();
    }

    public void OnAwake()
    {
        clientsdk_set_log_writter(LogMessageFromCpp);
#if !MULTI_STATE
        LuaState state = LuaState.Get(IntPtr.Zero);
        clientsdk_init_luastate(state.L);
#else
        // TODO
#endif

#if UNITY_EDITOR
        clientsdk_init(LuaConst.luaDir);
#else
        clientsdk_init("");
#endif
        clientsdk_start();
    }

    public void OnDestroy()
    {
        clientsdk_stop();
        clientsdk_clear_log_writter();
    }

    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern int clientsdk_init_luastate(IntPtr L);
    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern int clientsdk_init(string script_root);
    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern int clientsdk_start();
    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern int clientsdk_stop();
    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern int clientsdk_poll();


    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    private delegate void LogDelegate(int level, IntPtr message, int iSize);

    [MonoPInvokeCallback(typeof(LogDelegate))]
    private static void LogMessageFromCpp(int level, IntPtr message, int iSize)
    {
        try
        {
            switch (level)
            {
                case 0:
                case 1:
                case 2:
                    Debug.Log(Marshal.PtrToStringAnsi(message, iSize));
                    break;
                case 3:
                    Debug.LogWarning(Marshal.PtrToStringAnsi(message, iSize));
                    break;
                default:
                    Debug.LogError(Marshal.PtrToStringAnsi(message, iSize));
                    break;
            }
        }
        catch
        {
            // 中文的log可能会导致这种trace，例如在windows环境下连接失败 boost 的e.message() 可能就是中文
            return;
        }
    }

    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern void clientsdk_set_log_writter(LogDelegate log);

    [DllImport(CLIENTSDKDLL, CallingConvention = CallingConvention.Cdecl)]
    private static extern void clientsdk_clear_log_writter();
}
```

#### Lua接入

引擎启动后，会在Lua全局表中搜索InitEZClient方法，并调用该方法进行Lua中的初始化。

Lua中的初始化主要包括

1. 引入ServerFramework.InitEZ模块。
2. 引入各个Entity，引入的Entity会自动注册到引擎中，方便后续创建。
3. 解析Defs。需要考虑手机包资源管理方式。

```lua
function InitEZClient()
    -- init ezclient
    xrequire "ServerFramework.InitEZ"

    -- entities
    xrequire "ServerEntities.Account"
    xrequire "ServerEntities.Avatar"

    -- parse def
    EZE.parseDef(scriptPath .. "/../common/Defs")
end
```

## 服务端

推荐目录结构见：引擎更新指引.md

#### 运行Demo

如果要在Windows中运行Demo，需要先安装Windows Terminal。

1. 解压demo后，复制config文件夹中的conf_template.json到conf.json，修改其中的cluster为任意随机值，避免与其他人重复。
2. 执行shell/win中的server.bat，启动demo服务端。
3. 执行shell/win中的bot.bat，启动demo客户端机器人。

#### 服务端接入

以按照推荐目录结构组织服务端、Windows平台为例

1. 修改启动脚本环境，shell/win/environment.bat

   ```bat
   @echo off
   
   set ROOT=%~dp0..\..
   set BIN_SERVER=%ROOT%\bin
   set BIN_CLIENT=%ROOT%\bin
   set SCRIPT_ROOT_SERVER=%ROOT%\script\server @REM 改为服务端脚本的根目录，按照推荐目录为%ROOT%\script
   set SCRIPT_ROOT_CLIENT=%ROOT%\script\client @REM 改为机器人脚本的根目录，按照推荐目录为%ROOT%\bot
   set LOG=%ROOT%\log
   ```

2. 部署项目MongoDB

3. 生成项目加密密钥

4. 修改配置文件

   引擎支持配置文件继承，有了继承的支持，可以更好的组织项目、引擎的配置文件。

   开发期可以考虑采用conf.json -> conf_proj.json -> conf_default.json的继承方式。其中conf_default.json是引擎默认的配置文件，conf_proj.json是项目的默认配置文件，conf.json为机器（个人）的配置文件。

   修改MongoDB地址，dbmgr.mongo_cluster.game.hosts

   修改项目加密密钥，gate.rsa_private_key

# 编码指引

## Entity

Entity相关内容主要包括Def定义与脚本逻辑。

### Def定义

主要存在于Defs目录下，与Entity同名的json文件中。

Def定义主要包括

- 属性定义。定义Entity所具有的属性，包括属性的类型、存盘与否等。
- 客户端Rpc定义。定义服务端可以调用的客户端Rpc。
- 服务端Rpc定义。定义服务端可以调用的服务端Rpc与客户端可以调用的服务端Rpc。
- 数据库索引定义。定义数据库表索引，会在启动时自动创建。
- 继承其他Def。继承其他Def，方便复用。继承的逻辑等同于将继承的Def展开。

一个简单的示例如下

```json
{
    "implements": [
        "AvatarEntity"
    ],

    "properties": [
        {"name": "account", "type": "string", "flag": "OWN_CLIENT", "persistent": true}
    ],

    "client_methods": [
        {"name": "onGetControl"},
        {"name": "onLoginFail", "args": ["int"]}
    ],

    "server_methods": [
        {"name": "auth", "args": ["string"], "exposed": true},
        {"name": "onLoginRequest", "args": ["bool", "string"]}
    ]
}
```

- implements即继承，该Entity继承了AvatarEntity的Def。
- properties即属性，该Entity包含一个名为account的属性。
- client_methods即客户端Rpc。
- server_methods即服务端Rpc。

### 脚本逻辑

定义一种新的Entity时，需要定义新的Entity类型，并将其注册到引擎中。

```lua

local AvatarEntity = require "ServerFramework.Entities.AvatarEntity"
local Account = DefineClass("Account", AvatarEntity)

function Account:ctor()
    self.urs = nil
end

function Account:auth(urs)
    self:logInfo("auth, urs: %s", urs)
    self.urs = urs
    self:onAuthSucc()
end

EntityManager.registEntity("Account", Account)

return Account
```

如上述代码所示，

1. 通过DefineClass定义了一种新的类型名为Account，并继承AvatarEntity。
2. 通过EntityManager.registEntity接口，将新的Entity类型注册到引擎中。

这样后续就可以通过创建Entity的接口进行创建使用。

```lua
local ent = EntityManager.createEntity("Account", 0, {})
```

## Rpc

目前，引擎支持

- 同一Entity内
    - 客户端向服务器发送Rpc
    - 服务端向客户端发送Rpc
- 不同Entity之间
    - 服务端向服务端发送Rpc
    - 服务端向服务发送Rpc
    - 广播Rpc

### 同一Entity内发送Rpc

- 服务端向客户端发送Rpc，可以通过`self:clientRpc(rpcName, …)`
- 客户端向服务端发送Rpc，可以通过`self:serverRpc(rpcName, …)`

self都为该Entity的某一实例。

### Mailbox

当需要在不同Entity间发送Rpc时，为了确定目标Entity地址，引入了Mailbox的概念。

Mailbox中保存目标Entity的信息

- 确定目标地址
- 根据定义对rpc进行校验

对于某个Entity来说，可以通过`self:getMailbox()`拿到该Entity的Mailbox，这样就可以给其他Entity，让其他Entity给该Entity发送Rpc。

如果要通过Rpc发送Mailbox时，则需要对Mailbox进行序列化，以string的方式进行传输。

- 可以直接获取Entity的序列化好的Mailbox，`self:getMailboxStr()`
- 也可以对Mailbox进行序列化，`mailbox:serializeToString()`

在目标收到Mailbox后，如果需要使用，需要进行反序列化，`Mailbox.createFromString(mailboxStr)`

### 不同Entity之间发送Rpc

- 服务端向服务端发送Rpc，`self:serverRpc(mailbox, rpcName, …)`
- 服务端向服务发送Rpc，`self:serviceRpc(serviceName, rpcName, …)`

### Rpc定义

如Entity Def定义示例中所示，无论是客户端发往服务器的Rpc、服务器发往客户端的Rpc还是服务端之间的Rpc，定义的方式基本相同。

```json
{
	"client_methods": [
	    {"name": "onLoginFail", "args": ["int"]}
	],
	
	"server_methods": [
	    {"name": "auth", "args": ["string"], "exposed": true},
	]
}
```

对于单个Rpc的定义，都需要包括Rpc名（name）和参数列表（args）。引擎会根据定义中的信息，对Rpc进行校验

- Rpc是否存在
- Rpc参数是否正确

参数列表中，除了基础类型外，还可以使用复杂类型，详见属性系统。

对于客户端发往服务器的Rpc，为了安全考虑区别于其他Rpc的定义，需要将exposed置为true。非exposed的Rpc不能被客户端调用。

#### 自定义RpcFlag

为了方便业务扩展，rpc也支持自定义标记

- 在const.json中定义RpcFlag
    ```json
    {
        "RPC_FLAG":
        {
            "INIT": 1
        }
    }
    ```

- 在定义rpc时配置flag
    ```json
    {
        "server_methods":
        [
            {"name": "avatarRecover", "args": ["int", "StrStrDict"], "flag": ["RPC_FLAG.INIT"]}
        ]
    }
    ```
	flag是数组格式，支持配置多个flag。

- lua接口
    ```lua
    ---检查某个rpc是否包含某个RpcFlag
    ---@param entity_name string @entity名称
    ---@param rpc_name string @rpc名称
    ---@param flag_value int @RpcFlag值
    ---@return boolean @是否包含flag
    EZE.hasRpcFlagValue(entity_name, rpc_name, flag_value)
    
    ---检查某个rpc是否包含某个RpcFlag
    ---@param entity_name string @entity名称
    ---@param rpc_name string @rpc名称
    ---@param flag_name string @在const.json中的Flag路径
    ---@return boolean @是否包含flag
    EZE.hasRpcFlagName(entity_name, rpc_name, flag_name)
    ```

## 属性系统

属性系统目的是让Entity具有自动存盘、属性自动同步等功能而产生的。有上述需求的属性才需要定义，并不是所有属性都需要定义。但也建议尽量Entity的属性都进行定义，相比于脚本语言的灵活，有定义更容易维护，且在属性赋值时有类型检查也更容易查错。

### 内嵌类型

基础类型：

| 类型名 | C类型 | 其他说明 |
| --- | --- | --- |
| int | int64 |  |
| float | double |  |
| string |  |  |
| binary |  |  |
| bool | bool |  |
| any |  | any用来存储任意lua类型，不做额外检查。 |

复杂类型：

| 类型名 | 说明 |
| --- | --- |
| array | 需要指定element类型 |
| dict | 需要指定key类型与value类型，key只支持int或string类型。 |
| struct | 与C中的结构体类似，需要指定每个字段名与类型。 |

内建类型：

| 类型名  | 引擎类型   | 说明 |
| ------- | ---------- | ---- |
| mailbox | LuaMailbox |      |

复杂类型都支持嵌套。

### 类型定义

类型定义主要分为两种方式

- 一种定义于alias.json中，在使用时直接使用类型别名即可。
- 一种未匿名定义，在使用的地方直接定义类型。

后续讨论都为在alias.json中的定义，匿名定义只需要去掉name字段即可，不做额外赘述。

**定义基础类型别名**

```json
[
	{"name": "EntityId", "type": "int"}
]
```

**定义复杂类型别名**

```json
[
  {"name": "TestList", "type": "list", "element": "float"},
  {"name": "TestDict", "type": "dict", "key": "string", "value": "string"},
  {"name": "TestStruct", "type": "struct", "elements": [
    {"name": "hp", "type": "int", "flag": "OWN_CLIENT", "default": 100},
    {"name": "mp", "type": "string", "flag": "OWN_CLIENT", "default": "", "persistent": true}
	]}
]
```

- list，指定element类型，通过element字段指定。
- dict，需要指定key类型与value类型。key类型通过key字段指定，value类型通过value字段指定。key类型只能为int或者string类型，以及这些类型的别名。
- struct，需要通过elements字段指定每个element的名字与类型，定义方式与属性定义方式相同。

**定义复杂类型嵌套**

```json
[
	{"name": "TestDictOfList", "type": "dict", "key": "string", "value": "TestList"}
	{"name": "TestDictOfListEmbed", "type": "dict", "key": "string", "value": {"type": "list", "element": "float"}}
]
```

定义后的类型别名即可在属性定义与Rpc定义中直接使用。

### 属性定义

如Entity Def定义示例中所示，Entity属性定义为一个json array，每个属性是一个json object。主要包括：

- name，属性名。
- type，属性类型。如果为字符串，则在类型别名中查找，如果是json object则为匿名类型。
- flag，同步类型。目前只支持`SERVER_ONLY`，`OWN_CLIENT` 。可选字段，默认为`SERVER_ONLY` 。
- persistent，是否存盘。可选字段，默认为false。
- default，默认值。可选字段，每个类型的默认值不同。

```json
{
    "properties": [
        {"name": "account", "type": "string", "flag": "OWN_CLIENT", "persistent": true, "default": "lb"}
    ]
}
```

定义后的属性，可以通过`self.props.name`的方式获取到对应的属性。

**每种类型支持的default与default的默认值**

| 类型名  | 支持类型（Json）  | 默认值(Lua) |
| ------- | ----------------- | ----------- |
| int     | `null`, `integer` | 0           |
| float   | `null`, `number`  | 0.0         |
| string  | `null`, `string`  | ""          |
| binary  | `null`, `string`  | ""          |
| bool    | `null`, `boolean` | false       |
| any     | `null`            | nil         |
| array   | `null`            | nil         |
| dict    | `null`            | nil         |
| struct  | `null`            | nil         |
| mailbox | `null`            | nil         |

### 属性同步

属性同步即在客户端Entity创建时与属性发生变化时，自动将属性同步到客户端。

目前，同步标记只支持

- `SERVER_ONLY` ，该属性不进行同步。
- `OWN_CLIENT` ，在客户端Entity创建时与属性发生变化时，都会进行同步。即在客户端可以通过`self.props.name`取到属性当前的值。

不只是基础类型会同步，复杂类型也会同步。

引擎是通过hook `__newindex`的方式监听属性变化，并生成同步指令的。所以，对于复杂类型来说，直接通过self.props.xxx是无法拿到实际的Lua table的，需要使用`self.props.xxx:raw()`。

**优化同步性能**

- 引擎对于复杂类型的同步采用同步操作/变化的方式进行，需要使用引擎提供的方式对复杂类型进行操作。
  
    ```lua
    -- list
    -- def {"name": "testList", "type": {"type": "list", "element": "float"}, "flag": "OWN_CLIENT"}
    self.props.testList[1] = 1.0 -- set
    self.props.testList:insert([pos,] value) -- insert element
    -- self.props.testList[1] = nil -- remove list element illegal
    self.props.testList:remove([pos]) -- remove element
    self.props.testList:sort([comp]) -- sort list
    
    -- dict
    -- def {"name": "testDict", "type": {"type": "dict", "key": "int", "value": "string"}, "flag": "OWN_CLIENT"},
    self.props.testDict[100] = "lalala" -- add or replace
    self.props.testDict[100] = nil -- remove
    
    -- struct
    ```
    
- 引擎在同步复杂类型时，会自动过滤/填充默认值。

**同步类型嵌套**

属性支持部分同步，比如对于如下定义的属性

```json
// alias.json
[
  {"name": "TestStruct", "type": "struct", "elements": [
    {"name": "hp", "type": "int", "flag": "OWN_CLIENT"},
    {"name": "mp", "type": "string", "flag": "SERVER_ONLY"}
	]}
]

// entity
[
	"properties": [
	  {"name": "testStruct", "type": "TestStruct", "flag": "OWN_CLIENT", "persistent": true}
    ]
]
```

其中`testStruct.hp`会被同步到客户端，而`testStruct.mp`不会。

当属性嵌套时，当前子属性是否同步，取决于**父属性同步标记 & 自属性同步标记**，结果包含`OWN_CLIENT` 则会同步，否则不会。Entity可以视为一个特殊的struct，flags为`OWN_CLIENT` 。

比如：

```json
// alias.json
[
  {"name": "TestStruct", "type": "struct", "elements": [
    {"name": "hp", "type": "int", "flag": "OWN_CLIENT"},
    {"name": "mp", "type": "string", "flag": "SERVER_ONLY"}
	]}
]

// entity
[
	"properties": [
	  {"name": "testStruct1", "type": "TestStruct", "flag": "OWN_CLIENT", "persistent": true},
	  {"name": "testStruct2", "type": "TestStruct", "flag": "SERVER_ONLY", "persistent": true}
    ]
]
```

`testStruct1.hp`会被同步，而`testStruct2.hp`不会被同步。

### 自动存盘

自动存盘，即将persistent为true的属性存入到数据库中，在数据库中保持与属性定义相同的结构。

如下定义的Entity

```json
// alias
[
  {"name": "TupleAttr1", "type": "struct", "elements": [
    {"name": "hp", "type": "int", "flag": "OWN_CLIENT", "default": 100},
    {"name": "mp", "type": "string", "flag": "OWN_CLIENT", "default": "", "persistent": true}
  ]},
  {"name": "TestDictIntKey", "type": "dict", "key": "int", "value": "string"}
]
// entity 
{
  "properties": [
    {"name": "testStr", "type": "string", "flag": "SERVER_ONLY", "persistent": true},
    {"name": "testInt", "type": "int", "flag": "SERVER_ONLY", "persistent": true},
    {"name": "testFloat", "type": "float", "flag": "SERVER_ONLY", "persistent": true},
    {"name": "testList", "type": {"type": "list", "element": "float"}, "flag": "SERVER_ONLY", "persistent": true},
    {"name": "testDict", "type": "TestDictIntKey", "flag": "SERVER_ONLY", "persistent": true},
    {"name": "testStruct", "type": "TupleAttr1", "flag": "SERVER_ONLY", "persistent": true}
  ]
}
```

在数据库（MongoDB）中存储为

```jsx
{
  _id: 'ZmLE8PANqlecbH0w',
  _term: 7,
  _term_index: 0,
  testStr: '',
  testInt: 0,
  testFloat: 0,
  testList: [],
  testDict: {},
  testStruct: {
    mp: ''
  }
}
```

其中下划线开头的几个字段为引擎维护字段。

- _id为唯一ID，又名DatabaseId或者DBID。在Entity首次存盘时自动生成。Lua中可以通过`self:getDBID()`获取。
- _term与_term_index为了避免内存中两个相同Entity数据相互覆盖。Lua中可以通过`self:getTerm()`与`self:getTermIndex()`获取。

**存盘标记嵌套**

persistent标记与属性同步标记相同，都会遇到嵌套的情况。在发生嵌套时，与属性同步计算规则相同。比如：

```json
// alias.json
[
  {"name": "TestStruct", "type": "struct", "elements": [
    {"name": "hp", "type": "int", "flag": "SERVER_ONLY", "persistent": true},
    {"name": "mp", "type": "string", "flag": "SERVER_ONLY", "persistent": false}
	]}
]

// entity
[
	"properties": [
	  {"name": "testStruct1", "type": "TestStruct", "flag": "SERVER_ONLY", "persistent": true},
	  {"name": "testStruct2", "type": "TestStruct", "flag": "SERVER_ONLY", "persistent": false}
    ]
]
```

`testStruct1.hp`会存盘，而`testStruct1.mp`、`testStruct2.hp`、`testStruct2.mp` 都不会。

**避免数据相互覆盖**

引擎不保证相同DatabaseId的Entity不会创建多个，也就是说如果不加以限制，可能一个玩家对应的Entity会同时存在两份。这两个Entity数据存盘时，就会相互覆盖数据，导致玩家数据丢失。所以一方面需要在脚本层处理相关逻辑，保证不会存在两份，同时也要提供保底措施，一旦内存中存在两个相同DatabaseId的Entity，数据存盘时及时报错不要相互覆盖。term和term_index就是为了解决这个问题而产生的。

1. 从数据库创建Entity时，term会自增1。
2. 每次Entity存盘时，term_index自增1。
3. Entity存盘时会比较Entity上的term和数据库中的term，只有在`entity_term == db_term && entity_term_index > db_term_index`成立才会成功存盘。

这样就可以保证，只有一个Entity会存盘成功，并且如果存盘操作乱序，最新数据不会被旧数据覆盖。

## 数据库

除Entity自动存盘外，引擎还提供对数据库的直接操作。目标支持MongoDB，后续考虑增加对Redis的支持。

使用方法：

```lua
local coll = entity:mongoCollection(dbmgrId, db, collection)
coll:find({urs = "lb0001"}, {_id = 1, urs = 1}, {}, function(succ, docs) end)
```

1. 通过Entity上的`mongoCollection`创建collection的代理
2. 调用collection代理的接口操作数据库，并用callback监听结果

目前支持操作

```lua
-- @params options 支持sort、limit、skip
-- @params callback callback(succ, docs)
function MongoCollection:find(filter, projection, options, callback) end

-- @params options 同find
-- @params callback callback(succ, docs)
function MongoCollection:find_one(filter, projection, options, callback) end

-- @params options 支持upsert、projection
-- @params callback callback(succ, docs)
function MongoCollection:find_one_and_update(filter, update, options, callback) end

-- @params options 不支持
-- @params callback callback(succ)
function MongoCollection:insert_one(doc, options, callback) end

-- @params operation_options 不支持
-- @params callback callback(succ, index_doc)
function MongoCollection:create_index(keys, index_options, operation_options, callback) end
```

操作的具体参数可以参考MongoDB文档。

## 双端Entity绑定

EZEngine中逻辑、通信、数据主要是以Entity为载体进行设计与开发的。客户端连接到服务器之后，会自动在客户端创建对应Entity，并于服务端Entity进行绑定。

1. 客户端与服务端Entity同名
2. 客户端Entity与服务端Entity具有相同的EntityId
3. 客户端Entity只能与绑定的服务端Entity进行Rpc通信。
4. 服务端Entity属性变化后会自动同步到绑定的客户端Entity上。

以经典的玩家登录为例，简要说明。玩家登录过程中设计两种Entity，Account和Avatar。Account主要负责做登录验证，Avatar则主要为玩家数据逻辑。

```mermaid
sequenceDiagram
	participant Client
	participant Server
	participant CAccount
	participant SAccount
	participant CAvatar
	participant SAvatar
	Client->>Server: 客户端连接
	Server->>Server: 服务端创建Account
	SAccount->>SAccount: 服务端Account绑定到客户端
	SAccount->>Client: 客户端创建Account
	CAccount->>SAccount: 账号验证
	SAccount->>SAccount: 验证成功，创建Avatar
	SAvatar->>SAvatar: 服务端Avatar绑定到客户端
	SAvatar->>Client: 客户端创建Avatar
	CAvatar->>SAvatar: 游戏逻辑
	SAvatar->>CAvatar: 游戏逻辑
```

绑定关系支持

- 绑定到客户端，`entity:connectClient(gateId, sessionId)`
- 从客户端解绑，`entity:detachClient()`
- 客户端断开连接，`entity:disconnectClient()`

通过登录的例子可以看出，Entity与客户端的绑定是多对一的关系，也就是多个Entity可以绑定到同一个客户端，但是同一Entity只能绑定到一个客户端。

在绑定新的Entity时，可以通过原有Entity的接口`entity:getClient()`获取与当前Entity绑定的客户端信息，可以直接用于调用`connectClient`。

## 调试

### Telnet

引擎每个container都会开放一个telnet端口，用来进行调试。调试端口配置在`container.telnet_server`中。如：

```json
{
	"container_gate": {
		"tags": ["Gate"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 10000
		}
	}
}
```

可以通过telnet工具连接，即可执行lua指令进行调试。以win10为例

```powershell
telnet localhost 10000
```

连接成功后，有如下提示

```powershell
Welcome to eze...(server)
>>>
```

之后就可以像写lua代码一样进行调试了，举几个常用的例子

```
-- 获取并打印Entity
>>> print(EntityManager.getEntity(281483566645250))
table: 0x01c9fe9b8e98

-- 打印Entity定义的属性
>>> print(EntityManager.getEntity(281483566645250).props.testInt)
0

-- 执行Entity的方法
>>> EntityManager.getEntity(281483566645250):testHttpGet()
```

### EmmyLua

- server `script/server/ServerFramework/Utils/Annotation.lua`
- client `script/client/ServerFramework/Utils/Annotation.lua`

## Kcp

GateEntity同时支持Tcp与Kcp。对于如下配置的GateEntity，会同时监听Tcp与Udp的30000端口，同时接受来自Tcp与Kcp的连接。

```json
"gate": {
    "host": "0.0.0.0",	// 监听的地址
    "port":  30000		// 监听的端口
}
```

客户端在连接时，可以选择使用Tcp还是Kcp连接到服务器。

```lua
local client = Client.Client.new("test")
client:connect("127.0.0.1", 30000, "kcp")	-- kcp or tcp
```

可以根据项目的需求选择使用Tcp还是Kcp。同时也可以在Kcp连接失败时，回退到使用Tcp连接的方式，避免玩家连接不上的问题。

目前Kcp主要支持以下参数的配置，以服务器的配置方式为例

```json
"gate": {
    "kcp": {
        // 第一部分
        "mtu": 1400,	// mtu
        "nodelay": 1,	// 包重传延迟策略，0：线性增长，1：0.5倍指数增长，2：根据当前的rto的0.5倍增长
        "interval": 10,	// flush刷新间隔，单位ms
        "resend": 2,	// 触发快速重传的重复ack个数
        "nocwnd": 1,	// 取消拥塞控制
        "sndwnd": 32,	// 发送窗口大小
        "rcvwnd": 128,	// 接收窗口大小
        "stream": 1,

        // 第二部分
        "timeout": 30000,			// 超时时间，单位ms，超时后链接断开
        "heartbeat": 6000,			// 客户端发送心跳包的间隔，单位ms
        "syn_retry_interval": 200,	// 客户端建立Kcp连接过程中Udp数据包丢包重试间隔，单位ms
        "syn_max_retry": 25,		// 客户端建立Kcp连接过程中Udp数据包丢包重试最大次数

        // 第三部分
        "fec_encoder": {			// Forward Error Correction(FEC)，通过发送冗余包，达到即使存在数据包丢失的情况下，也可以通过冗余包进行恢复，避免重传延迟
            "algorithm": "none",	// FEC算法，目前支持：none、xor
            "data_shard": 9,		// 数据包数量，即每n个数据包发送k个冗余包
            "parity_shard": 1		// 冗余包数量，xor目前只支持1个冗余包
        }
    }
}
```

第一部分的配置是Kcp的原生配置，每个参数的意义可以参考注释，具体功能可以查看[kcp源码](https://github.com/skywind3000/kcp)。

第二部分的配置是Kcp连接相关配置。syn_retry_interval与syn_max_retry与客户端连接服务器相关，syn_retry_interval*syn_max_retry毫秒后连接超时失败。客户端与服务器都会检查当前连接是否超市，如果在timeout毫秒内没有收到数据包则判定连接超时断开连接。客户端如果在heartbeat毫秒内没有向服务器发送数据包，则发送心跳包避免连接超时。

第三部分的配置是Kcp包的前向校验（FEC）。目前只支持XOR算法，即将data_shard个包进行异或操作，得到parity_shard个冗余包并发送。这样data_shard + parity_shard中如果有parity_shard个包发生丢包，可以通过FEC算法恢复而不用等待重传。发送冗余包有额外的带宽与性能开销，非延迟敏感项目不建议使用。

## Http

在服务端支持HttpServer，且已经在脚本层封装为HttpServerComp。可以通过使用该组件实现HttpServer。

使用HttpServer

- Entity创建后，需要调用registHttpHandler注册http handler。并调用startHttpServer方法开启http server。注意为了支持reload，现在的registHttpHandler接口是classmethod

  ```lua

  HttpServerTest.registHttpHandler(HttpServerTest, "/test", "test")

  function HttpServerTest:ctor()
      self:registHttpHandler("/test", "test")
      self:startHttpServer("0.0.0.0", 12345)
  end
  ```

- http请求会查找注册的http path，并在Entity找到并执行对应方法，参数为sessionId与request。如果没有找到方法，则返回http not found。

简单示例可以参考如下代码

```lua
local HttpServerCompEnv = xrequire "Framework.Entities.Components.HttpServerComp"
local HttpServerTest = DefineEntity("HttpServerTest", {}, {HttpServerCompEnv.HttpServerComp})

HttpServerTest.registHttpHandler(HttpServerTest, "/test", "test")

function HttpServerTest:ctor()
    self:startHttpServer("0.0.0.0", 12345)
end

function HttpServerTest:test(sessionId, request)
    self:httpResponse(sid, HttpServerCompEnv.HttpStatus.OK, {["Content-Type"] = "text/plain"}, "hello world")
end
```

测试结果如下

```shell
$ curl http://127.0.0.1:12345/test -s
hello world
$ curl http://127.0.0.1:12345/abc -s
'/abc' was not found.
```

## API References

### LuaEntityBase[`EZCore`]

```
/**
 * 销毁Entity，销毁不代表立即析构，只是进行清理，也不再接受rpc。
 * 
 * @return 无返回值
 */
entity:destroy()

/**
 * 向目标Entity发送rpc。
 * 
 * @param destination 目标EntityId
 * @param msgtype 消息类型值域[0, 15]，0、1、2为引擎保留类型。一般传0即可。
 * @param rpcname rpc名，对应rpc处理函数名。
 * @param args rpc参数。
 * @return 发送失败抛出异常，无返回值
 */
entity:send_message(destination, msgtype, rpcname, ...)

/**
 * 在Entity上启动新的定时器
 * 
 * @param delay 定时器第一次触发时间间隔，单位为秒
 * @param repeat 定时器第一次触发后，后续每次触发的时间间隔，单位为秒。如果为单次计时器则传0。
 * @param callback 定时器到时间后的回调，callback(timer_id)
 * @return 定时器id（timer_id），int64
 */
entity:addTimer(delay, repeat, callback)

/**
 * 删除Entity上已启动的定时器
 * 
 * @param timer_id addTimer返回的定时器id
 * @return 无
 */
entity:delTimer(timer_id)
```

### LuaClientEntityBase[`EZFramework`] [`Client`]

LuaClientEntityBase继承自LuaEntityBase。

```
/**
 * 向该Entity的服务端发送Rpc
 * 
 * @param method_name rpc名，对应rpc处理函数名。
 * @param args rpc参数。
 * @return 
 */
entity:serverRpc(method_name, ...)
```

### LuaGameEntityBase[`EZFramework`] [`Server`]

LuaGameEntityBase继承自LuaEntityBase。

```
/**
 * 绑定Entity到客户端连接，会自动在客户端创建该Entity。
 * 
 * @param gate_id 客户端连接所属的gate entity id
 * @param session_id 客户端连接的session id
 * @return lua中返回bool，表示操作是否成功
 */
entity:connectClient(gate_id, session_id)

/**
 * Entity与客户端连接解除绑定，会自动在客户端销毁该Entity。
 * 
 * @return 无
 */
entity:detachClient()

/**
 * 断开与Entity绑定的客户端的网络连接。
 * 
 * @return 无
 */
entity:disconnectClient()

/**
 * 增加观察者，观察者的客户端会创建此Entity，并收到后续所有ALL_CLIENTS属性的同步以及所有allClient的rpc。
 * 
 * @param client 客户端连接信息，包括gate_id,session_id, addr等
 * @return 操作是否成功
 */
entity:connectObserver(client)

/**
 * 解除观察关系，会自动在观察者客户端销毁该Entity。
 * 
 * @param session_id 观察者客户端的sessionId
 * @return 无
 */
entity:detachObserver(session_id)

/**
 * 解除所有观察关系。
 * 
 * @return 无
 */
entity:detachAllObservers()

/**
 * 是否存在某个观察者
 * 
 * @param session_id 可选参数，不传入该参数则表示是否存在观察者，传入参数表示sessionId的观察者是否存在。
 * @return 是否存在观察者
 */
entity:hasObserver(session_id)

/**
 * 获取所有观察者.
 * 
 * @return 所有观察者sessionId的列表
 */
entity:getObservers()

/**
 * 向该Entity的客户端发送rpc
 * 
 * @param method_name rpc名，对应rpc处理函数名。
 * @param args rpc参数。
 * @return 发送成功lua返回true，失败lua返回nil
 */
entity:clientRpc(method_name, ...)

/**
 * 向其他服务端Entity发送rpc.
 * 
 * @param mailbox 目标Entity的mailbox
 * @param method_name rpc名，对应rpc处理函数名。
 * @param args rpc参数。
 * @return 发送成功lua返回true，失败lua返回nil
 */
entity:serverRpc(mailbox, method_name, ...)

/**
 * 获取Entity的mailbox
 * 
 * @return 该Entity的mailbox，LuaMailbox类型
 */
entity:getMailbox()

/**
 * 获取Entity是否已经与客户端连接绑定
 * 
 * @return lua bool
 */
entity:hasClient()

/**
 * 获取与Entity绑定的客户端连接信息
 * 
 * @return gate_id, session_id. 两个返回值
 */
entity:getClient()

/**
 * 在Entity上启动新的定时器
 * 
 * @param delay 定时器第一次触发时间间隔，单位为秒
 * @param repeat 定时器第一次触发后，后续每次触发的时间间隔，单位为秒。如果为单次计时器则传0。
 * @param callback 定时器到时间后的回调，callback(timer_id)
 * @return 定时器id（timer_id），int64
 */
entity:addTimer(delay, repeat, callback)

/**
 * 删除Entity上已启动的定时器
 * 
 * @param timer_id addTimer返回的定时器id
 * @return 无
 */
entity:delTimer(timer_id)

/**
 * 获取mongo collection代理，LuaMongoCollection。用来执行mongo操作。
 * 
 * @param dbmgr dbmgr entity id，后续mongo操作将在该dbmgr上执行。
 * @param db mongodb db名
 * @param collection mongodb collection名
 * @return LuaMongoCollection
 */
entity:mongoCollection(dbmgr, db, collection)

/**
 * Entity存盘
 * 
 * @param dbmgr dbmgr entity id，存盘操作将在该dbmgr上执行。
 * @param db 存储到的mongodb database名
 * @param collection 存储到的mongodb collection名
 * @param callback[optional] 存盘的回调，callback(succ)。参数succ表示存盘是否成功。
 * @return 无
 */
entity:saveTo(dbmgr, db, collection, callback)

/**
 * 获取Entity的DatabaseId，DatabaseId为Entity在数据库中的唯一ID，存储在mongodb中的_id字段。
 * 
 * @return DatabaseId
 */
entity:getDBID()

/**
 * Entity term
 * 
 * @return term
 */
entity:getTerm()

/**
 * Entity term index.
 * 
 * @return term index
 */
entity:getTermIndex(lua_State* L)
```

### LuaGateEntityBase[`EZFramework`] [`Server`]

LuaGateEntityBase继承自LuaEntityBase。

```
/**
 * 开始对外提供服务，监听端口，等待客户端连接。
 * 
 * @param host 监听的域名或ip
 * @param port 监听的端口
 * @param backlog 建立中的连接的最大数量
 * @param thrnum 网络线程数，默认2线程
 */
entity:start(host, port, backlog, thrnum)

/**
 * 停止对外服务，断开所有连接。
 *
 */
entity:stop()
```

### LuaEntityManager[`EZCore`]

```
/**
 * 注册Entity，所有Entity类型在注册后才可以使用。
 * 
 * @param entname Entity类型名
 * @param entcls Entity class，lua table
 * @return 成功返回entcls，失败返回nil
 */
LuaEntityManager.registEntity(entname, entcls)

/**
 * 创建Entity
 * 
 * @param entname Entity类型名
 * @param eid 创建Entity的ID，填0由引擎自动生成，非0则直接作为EntityId使用。一般都填0。
 * @param props 用来初始化创建Entity的table。defs中定义的属性会用来初始化entity.props，其他key会放在entity中。
 * @return 返回创建好的entity，如果非Lua Entity则返回EntityId。
 */
LuaEntityManager.createEntity(entname, eid, props)
```

### LuaProp[`EZFramework`] [`Server`]

```
/**
 * 获取存储数据的lua table。
 * 
 * @return 存储数据的table
 */
prop:raw()

/**
 * 向list中插入元素
 * 
 * @param pos[optional] 插入的位置，不传入则默认插入list尾
 * @param value 待插入的元素
 * @return 
 */
prop:insert([pos,] value)

/**
 * 从list中删除元素
 * 
 * @param pos[optional] 待删除元素的位置，不传入则默认删除list最后一个
 * @return 
 */
prop:remove([pos])

/**
 * 将list中的元素排序
 * 
 * @param comp[optional] 排序比较函数，不传入则使用<。
 * @return 
 */
prop:sort([comp])
```

### LuaTcpClient[`EZCore`]

```
/**
 * 创建TcpClient
 * 
 * @return LuaTcpClient
 */
LuaTcpClient.new()

/**
 * 建立连接
 * 
 * @param host 目标域名或ip
 * @param port 目标端口
 * @param handler 连接回调，主要包括
 *					handler:handleConnected()		连接建立回调
 *					handler:handleClosed(reason)	连接断开回调
 *					handler:handleData(reason)		收到数据回调
 * @return true
 */
tcpclient:connect(host, port, handler)

/**
 * 断开连接
 * 
 * @return 无
 */
tcpclient:disconnect()

/**
 * 发送数据
 * 
 * @param data 发送数据，字符串类型。
 * @return 发送请求成功返回true，失败返回nil
 */
tcpclient:send(data)
```

### LuaTcpServer[`EZCore`]

```
/**
 * 创建TcpServer
 * 
 * @return LuaTcpServer
 */
LuaTcpServer.new()

/**
 * 监听端口
 * 
 * @param host 监听域名或ip
 * @param port 监听端口
 * @param backlog 建立中的连接的最大数量
 * @param handler 连接回调，包括
 * 					handler:handleNewConnnection(connId)			连接建立回调
 * 					handler:handleCloseConnection(connId, reason)	连接断开回调
 * 					handler:handleConnectionData(connId, data)		从连接收到数据回调
 * @return 成功返回true，失败返回nil
 */
tcpserver:start(host, port, backlog, handler);

/**
 * 停止监听，不影响已经建立的连接
 * 
 * @return 无
 */
tcpserver:stop();

/**
 * 向连接发送数据
 * 
 * @param connection_id 连接id，连接建立回调的id。
 * @param data 发送数据，字符串。
 * @return 成功返回true，失败返回nil
 */
tcpserver:send(connection_id, data);
```

### LuaHttpClient[`EZCore`]

```
/**
 * 异步http请求，支持http与https，支持指定headers与body，请求完成后会通过callback方式返回。
 * 
 * @param method http method，支持GET,POST,PUT,DELETE
 * @param host ip或者域名
 * @param port 端口
 * @param path 请求路径，如果请求根目录则传'/'
 * @param body http body，字符串
 * @param headers http headers table，key为header名，value为值
 * @param timeout 请求超时时间，单位为秒
 * @param is_ssl http传入false，https传入true
 * @param callback lua回调函数，回调参数为(code, msg, headers, body)
 * @return 出错抛出异常，无返回值
 */
LuaHttpClient.request(method, host, port, path, body, headers, timeout, is_ssl, callback)

/**
 * 异步http post请求.
 * 
 * @param host ip或者域名
 * @param port 端口
 * @param path 请求路径，如果请求根目录则传'/'
 * @param body http body，字符串
 * @param headers http headers table，key为header名，value为值
 * @param timeout 请求超时时间，单位为秒
 * @param callback lua回调函数，回调参数为(code, msg, headers, body)
 * @return 出错抛出异常，无返回值
 */
LuaHttpClient.http_post(host, port, path, body, headers, timeout, callback)

/**
 * 异步http post请求.
 *
 * @param host ip或者域名
 * @param port 端口
 * @param path 请求路径，如果请求根目录则传'/'
 * @param headers http headers table，key为header名，value为值
 * @param timeout 请求超时时间，单位为秒
 * @param callback lua回调函数，回调参数为(code, msg, headers, body)
 * @return 出错抛出异常，无返回值
 */
LuaHttpClient.http_get(host, port, path, headers, timeout, callback)
 
/**
 * 异步https post请求.
 *
 * @param host ip或者域名
 * @param port 端口
 * @param path 请求路径，如果请求根目录则传'/'
 * @param body http body，字符串
 * @param headers http headers table，key为header名，value为值
 * @param timeout 请求超时时间，单位为秒
 * @param callback lua回调函数，回调参数为(code, msg, headers, body)
 * @return 出错抛出异常，无返回值
 */
LuaHttpClient.https_post(host, port, path, body, headers, timeout, callback)

/**
 * 异步https post请求.
 *
 * @param host ip或者域名
 * @param port 端口
 * @param path 请求路径，如果请求根目录则传'/'
 * @param headers http headers table，key为header名，value为值
 * @param timeout 请求超时时间，单位为秒
 * @param callback lua回调函数，回调参数为(code, msg, headers, body)
 * @return 出错抛出异常，无返回值
 */
LuaHttpClient.https_get(host, port, path, headers, timeout, callback)
```

### LuaHttpServer[`EZCore`]

```
/**
 * 启动http server，开始监听tcp，并解析http请求。
 * 
 * @param host ip地址
 * @param port 端口
 * @param handler http请求解析回调。函数签名handler(response, request)
 *					sessionId - 会话唯一标识（整型），发送response的时候需要传递该参数
 *					request - lua table，包括
 *						version: number
 *						method: string
 *						path: string
 *						params: string
 *						headers: table of {name, value}
 *						body: string
 * @return 无
 */
LuaHttpServer:start(host, port, handler)

/**
 * 关闭http server，断开所有连接
 * 
 * @return 无
 */
LuaHttpServer:stop()

/**
 * 设置http request处理超时时间，即收到请求到发送response的时间。
 * 
 * @param timeout_ms 超时时间，毫秒
 * @return 无
 */
LuaHttpServer:set_timeout(timeout_ms)

/**
 * 设置http request body的最大长度
 * 
 * @param limit 最大长度，字节
 * @return 无
 */
LuaHttpServer:set_body_limit(limit)

/**
 * 发送http response
 *
 * @param sessionId 会话标识
 * @param status http status code
 * @param headers http headers，table类型，key、value都是string类型
 * @param body http response body，string类型
 * @return 是否成功
 */
 LuaHttpServer:response(sessionId, status, headers, body)
```

### LuaUdpLogger[`EZCore`]

```
/**
 * 初始化udp logger，主要创建logger并初始化udp socket。
 *
 * @param host 目标udp地址。
 * @param port 目标udp端口。
 * @return 成功返回true，出错抛出异常。
 */
LuaUdpLogger.init(host, port);

/**
 * 向udp logger中写入日志，日志内容与字符串。
 *
 * @param content 日志内容，字符串。
 * @return 无。
 */
LuaUdpLogger.write(content);
```



### LuaTelnetServer[`EZCore`]

```
/**
 * 创建TelnetServer
 * 
 * @return LuaTelnetServer
 */
LuaTelnetServer.new()

/**
 * 启动telnet server，并监听
 * 
 * @param host 监听域名或ip
 * @param port 监听端口
 * @param handler command回调，handler:onCmd(connId, cmd, func)
 * @return 成功返回true，失败返回nil
 */
telnetsvr:start(host, port, handler)

/**
 * 关闭监听，断开连接。
 * 
 * @return 无
 */
telnetsvr:stop()
```

### LuaNodeConfig[`EZCore`]

```
/**
 * 获取全局配置
 * 
 * @param path 配置路径
 * @return 配置信息
 */
LuaNodeConfig.getConfig(path)

/**
 * 获取container配置.
 * 
 * @param container_type container类型
 * @param path 配置路径，从container的root开始算。
 * @return 配置信息
 */
LuaNodeConfig.getContainerConfig(container_type, path)
```

### LuaFormatter[`EZCore`]

```lua
/**
 * 创建LuaFormatter实例，实现了__tostring元方法，用来格式化table等复杂类型。
 * 
 * @param obj 需要格式化的对象
 * @param depth 可选，默认为5。格式化输出深度，超过深度的数据简化为{...}
 * @param pretty 可选，默认为true。是否换行
 * @return LuaFormatter实例
 */
LuaFormatter.new(obj, depth, pretty);

/**
 * 设置格式化输出深度
 * 
 * @param depth 深度
 * @return self
 */
formatter:depth(depth);

/**
 * 设置格式化输出是否换行
 * 
 * @param L 
 * @param pretty 是否换行
 * @return self
 */
formatter:pretty(pretty);
```

### LuaMailbox[`EZFramework`] [`Server`]

```
/**
 * 将mailbox信息格式化为LuaMailbox(entity_id)
 * 
 * @return 格式化后的字符串
 */
mailbox:toString()

/**
 * 获取mailbox对应的EntityId
 * 
 * @return EntityId
 */
mailbox:getEntityId()

/**
 * 将mailbox序列化为字符串
 *
 * @return 序列化后的字符串
 */
mailbox:serializeToString()

/**
 * 从mailbox序列化的字符串反序列化并创建mailbox
 * 
 * @param data 序列化后的字符串
 * @return mailbox
 */
LuaMailbox.createFromString(data)
```

### LuaGateClient[`EZFramework`] [`Client`]

```
/**
 * 创建GateClient
 * 
 * @return LuaGateClient
 */
LuaGateClient.new()

/**
 * 建立连接
 * 
 * @param host 目标域名或ip
 * @param port 目标端口
 * @param protocol 协议，目前只支持tcp，后续增加kcp的支持。
 * @param handler lua table，连接回调以及服务端调用。
 *					handler:onConnect(errcode)								连接建立回调
 *					handler:onDisconnect(errcode)							连接断开回调
 *					handler:createEntity(entityName, entityId, initData)	服务端通知客户端创建Entity
 *					handler:destroyEntity(entityId)							服务端通知客户端销毁Entity
 * @return 成功返回true，失败返回nil
 */
gateclient:connect(host, port, protocol, handler);

/**
 * 断开连接
 * 
 * @return 成功返回true，失败返回nil
 */
gateclient:disconnect();

/**
 * 向服务器发送登录请求，连接成功建立后发送该请求，发送登录请求后才会创建Account。
 * 
 * @return 成功发送返回true，失败返回nil
 */
gateclient:login();
```

/**
 * 同步session、加密等信息，需要在login之前
 * 
 * @param crypto_type 加密算法名称
 * @return 成功发送返回true，失败返回false
 */
    gateclient:handshake(crypto_type);
```

/**
 * 设置加密公钥
 * 
 * @param pubkey 公钥
 * @return 成功返回true，失败返回false
 */
gateclient:setCryptoPubKey(pubkey);
```

### EZE.log[`EZCore`]

TODO

### EZE[`EZCore`]

```
/**
 * 获取script dir
 * 
 * @return script dir
 */
EZE.getScriptPath()

/**
 * 获取当前设置的global callback
 * 
 * @return global callback table
 */
EZE.getGlobalCallback()

/**
 * 设置global callback
 * 
 * @param cb global callback table
 * @return 无
 */
EZE.setGlobalCallback(cb)

/**
 * 获取当前设置的日志分级
 * 
 * @return 日志分级
 */
EZE.getLogLevel()

/**
 * 设置日志分级.
 * 
 * @param lv
 *			debug	= 0,
 *			info	= 1,
 *			warning	= 3,
 *			error	= 4,
 * @return 无
 */
EZE.setLogLevel(lv)

/**
 * 获取时间戳（毫秒）
 * 
 * @return 时间戳（毫秒）
 */
EZE.getTimestampMS()

/**
 * 获取时间戳（微秒）
 * 
 * @return 时间戳（微秒）
 */
EZE.getTimestampUS()

/**
 * 生成全局唯一的uuid，长度为16字节的字符串。
 * 
 * @return uuid
 */
EZE.genUUID()

/**
 * 获取当前的container id
 * 
 * @return container id
 */
EZE.getContainerId()

/**
 * 获取当前的container类型
 * 
 * @return container类型
 */
EZE.getContainerType()

/**
 * 使用msgpack对lua类型进行序列化
 * 
 * @param val 待序列化的lua数据，支持nil/boolean/number/string/table
 * @return 序列化后的二进制
 */
EZE.msgpackPack(val)

/**
 * 使用msgpack反序列化为lua table
 * 
 * @param bin 序列化后的二进制
 * @return lua table，如果序列化失败则抛出异常。
 */
EZE.msgpackUnpack(bin)

/**
 * 设置rpc发送回调，在发送前调用，用来监控rpc消息。
 *
 * @param hook 回调函数，参数: header，rpc参数。
 *		header参数为table类型，包含：
 *			source: 发送rpc的EntityId
 *			destination: 接收rpc的EntityId
 *			rpcname: rpc函数名
 * @return 无
 */
EZE.setRpcSendHook(hook)

/**
 * 设置rpc接收回调，在执行前调用，用来监控rpc消息。
 *
 * @param hook 回调函数，参数包括：header，rpc参数
 *		header参数为table类型，包含：
 *			source: 发送rpc的EntityId
 *			destination: 接收rpc的EntityId
 *			rpcname: rpc函数名
 * @return 无
 */
EZE.setRpcRecvHook(hook)

/**
 * 输出调用栈以及调用栈中每个函数的局部变量，替代debug.traceback。
 * 
 * @param thread 可选，默认为当前线程。要获取调用栈的线程
 * @param msg 可选，默认为空。错误信息
 * @param level 可选，默认为1。调用栈的起始层级
 * @return 调用栈信息
 */
EZE.tracebackWithLocals([thread,] [message [,level])

/**
 * 根据OPTION(traceback_with_locals)的值，确定调用栈中是否包含locals信息。
 * 
 * @param thread 可选，默认为当前线程。要获取调用栈的线程
 * @param msg 可选，默认为空。错误信息
 * @param level 可选，默认为1。调用栈的起始层级
 * @return 调用栈信息
 */
EZE.traceback([thread,] [message [,level])
```

### EZE[`EZFramework`]

/**
 * 检查某个rpc是否包含某个RpcFlag
 *
 * @param entity_name string entity名称
 * @param rpc_name string rpc名称
 * @param flag_value int RpcFlag值
 * @return 是否包含flag
 */
    EZE.hasRpcFlagValue(entity_name, rpc_name, flag_value)

/**
 * 检查某个rpc是否包含某个RpcFlag
 *
 * @param entity_name string entity名称
 * @param rpc_name string rpc名称
 * @param flag_name string 在const.json中的Flag路径
 * @return 是否包含flag
 */
    EZE.hasRpcFlagName(entity_name, rpc_name, flag_name)

### EZE[`EZFramework`] [`Client`]

```
/**
 * 解析Def定义文件
 * 
 * @param path 定义文件所在路径
 * @return 无，解析失败抛出异常
 */
EZE.parseDef(path)

/**
 * 添加Def文件内容。
 * 由于手机端无法dir整个目录，所以需要打包手机Def文件信息，并通过此接口将所有Defs文件内容导入，
 * 再执行parseDefByContent一次性解析所有Def。
 * 
 * @param name Def文件名
 * @param content 文件内容
 * @return 无，失败抛出异常
 */
EZE.addDefContent(name, content)

/**
 * 将添加的Def文件内容进行解析。
 * 
 * @return 无，解析失败抛出异常
 */
EZE.parseDefByContent()
```

### EZE[`EZFramework`] [`Server`]

```
/**
 * 从DB读取Entity数据，并创建Entity
 * 
 * @param dbmgr 从该dbmgr读取Entity数据
 * @param db Entity数据所在的mongo db名
 * @param collection Entity数据所在的mongo collection名
 * @param dbid 读取Entity的DatabaseId
 * @param ename Entity类型名
 * @param callback 回调函数，callback(errcode, entity_id)。
 *			errcode(0) 成功
 *			errcode(1) Entity不存在
 *			errcode(2) 超时
 *			errcode(其他) 其他错误
 * @return 无
 */
EZE.createEntityFromDB(dbmgr, db, collection, dbid, ename, callback)

/**
 * 根据Entity的Def定义，创建对应索引
 * 
 * @param dbmgr 将创建索引请求发送到该dbmgr
 * @param db mongo database
 * @return 无
 */
EZE.createEntitiesIndexes(dbmgr, db)

/**
 * 向集群注册global entity
 * 
 * @param name 唯一名
 * @param eid global entity的entity id
 * @param ready 是否已经ready
 * @param instance_data GlobalEntity实例附带的数据，如指定的分片等，这里已经打包好了
 * @return 无
 */
EZE.registerGlobalEntity(name, eid, ready, instance_data)

/**
 * 向集群注销global entity
 * 
 * @param eid global entity的entity id
 * @return 无
 */
EZE.unregisterGlobalEntity(eid)

/**
 * 通知集群global entity已经初始化完成了
 * 
 * @param eid global entity的entity id
 * @return 无
 */
EZE.setGlobalEntityReady(eid)

/**
 * 向集群发送关服请求
 *
 * @return 请求是否发送成功
 */
EZE.startShutdown()

/**
 * 告知集群当前container可以进入下阶段了
 *
 * @return 是否成功
 */
EZE.continueShutdown()

/**
 * 从ContainerId中获取NodeId
 *
 * @param cid ContainerId
 * @return NodeId
 */
EZE.containerIdToNodeId(cid)

/**
 * 从EntityId中获取NodeId
 *
 * @param eid EntityId
 * @return NodeId
 */
EZE.entityIdToNodeId(eid)

/**
 * 从EntityId中获取ContainerId
 *
 * @param eid EntityId
 * @return ContainerId
 */
EZE.entityIdToContainerId(eid)

/**
 * 将一个lua基础类型推送到GlobalData
 *
 * @param key string 数据的key
 * @param bin 打包好的二进制数据
 * @return 无
 */
EZE.pushGlobalData(key, bin)

/**
 * 请求删除一个GlobalData数据
 *
 * @param key string 数据的key
 * @return 无
 */
EZE.popGlobalData(key)
```

# 运维指引

## 服务器日志

服务器日志一般分位服务器运行日志与埋点日志。

- 运行日志，是指进程运行时产出的一般需要输出到标准输出的日志，会包含Debug、Info、Warning、Error等分级，用来监控进程运行状态或者方便错误定位的日志。
- 埋点日志，是指游戏在运行中产生的与玩家行为相关的日志，多用于玩家行为分析、投放监控等。又多称为运营日志。

引擎提供UdpLogger，主要用来将日志通过Udp发送到日志收集系统中。

引擎强制ip为127.0.0.1，所以需要将日志接收系统部署在本地，并监听localhost的端口。这样的部署要求主要是因为使用了udp协议，避免udp丢包导致日志丢失。

使用udp协议发送日志的主要原因

- 通过使用udp的同步发送，可以保证日志在进程crash时也基本不会丢失。
- 并发性能高，每个线程都有独立的udp logger，日志记录时没有锁竞争。

运行日志只需要在服务器配置中修改`log.output_udp_port`即可指定发送目标的Udp端口号。

埋点日志则需要在逻辑中使用LuaUdpLogger记录。LuaUdpLogger支持指定目标地址和端口。

日常开发时，引擎提供了filebeat的可执行文件与配置（在log目录下），直接执行引擎的启动脚本就会自动开启filebeat接收运行日志。

正式部署时，推荐在Linux下使用rsyslog，性能会比filebeat好很多。rsyslog配置大致如下，需要使用imudp module，并配置监听端口。同时，需要配置rcvbufSize，将udp的接收缓存尽量调大避免发生丢包。同时监控lo网卡的udp错误情况。

```
module(load="imudp")
input(type="imudp" port="19000" rcvbufSize="100m")
```



# 配置说明

```json
{
	"#name": "Node名，相当于一个标签，没有实际作用",
	"name": "server",
	"#cluster": "集群唯一id",
	"cluster": 1000,
	"#log_level": "日志等级，可选[debug/info/warn]",
	"log_level": "debug",
	"#res_path": "资源路径，是基于root的相对路径，默认为res",
	"res_path": "",
	"#def_path": "defs文件路径",
	"def_path": "script/common/Defs",
	"#script_path": "脚本路径，在没有使用启动参数指定脚本路径的情况下，会使用配置中的值",
	"script_path": "",
	"#node": "node id",
	"node": 1,

	"#global_entities": "设置GlobalEntity。instances_required表示起服需要的最小实例数量，默认值是1；dependence是自身依赖的GlobalEntity，表示这些GlobalEntity初始化完成（ready）且数量满足需求之后才会创建自身，默认是[]；strategy是在多实例的情况下选择rpc目标的策略，默认是random_once。",
	"global_entities": {
		"DBMgr": {"instances_required": 1, "dependence": [], "strategy": "random_once"},
		"RoleService": {},
		"PersistentService": {"dependence": ["DBMgr", "AsyncReadyService"]},
		"AsyncReadyService": {}
	},

	"#container": "container通用配置",
	"container": {
		"global": {
			"enable_login": false // 玩家能否登录到这个container
		},
		"login_entity": "Account",
		"#database": "数据库中的database名，实际使用时会将{cluster}替换成集群id，即配置中的cluster；建议配置为'项目代号_{cluster}'",
		"database": "proj_{cluster}",
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 0
		}
	},

	"#containers": "key是containerType，value是数量",
	"containers": {
		"gate": 1,
		"game": 1,
		"dbmgr": 1
	},

	"#container_gate": "containerType为gate的专属配置，规则是containerType前加前缀'container_'",
	"container_gate": {
		"#tags": "container的标签，一般起标识功能的作用。目前引擎会用Avatar去判断可否创建Account/Avatar，其他都由业务层自己处理",
		"tags": ["Gate"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 12001
		}
	},
	
	"container_dbmgr": {
		"tags": ["DBMgr"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 13001
		},
		"#create_entities_indexes": "为true时，初始化时回去创建索引",
		"create_entities_indexes": true
	},

	"container_game": {
		"global": {
			"enable_login": true
		},
		"tags": ["RoleService", "lbdebug"],
		"telnet_server": {
			"host": "127.0.0.1",
			"port": 14001
		}
	},

	"gate": {
		"#host": "公网地址",
		"host": "0.0.0.0",
		"#port": "公网端口",
		"port":  30000,
		"kcp": {
			"mtu": 1400,
			"nodelay": 1,
			"interval": 10,
			"resend": 2,
			"nocwnd": 1,
			"sndwnd": 32,
			"rcvwnd": 128,

			"timeout": 30000,
			"heartbeat": 6000,
			"syn_retry_interval": 200,
			"syn_max_retry": 25,

			"fec": {
				"algorithm": "xor",
				"data_shard": 3,
				"parity_shard": 1
			}
		},
		"#crypto_types": "支持的加密算法，如果允许不加密，可以加上'NONE'；客户端handshak时，需要从中指定一个加密算法",
		"crypto_types": ["RC4", "ChaCha20", "AES-128-CFB", "AES-192-CFB","AES-256-CFB", "AES-128-OFB", "AES-192-OFB", "AES-256-OFB"],
		"rsa_private_key": "../config/rsa_private_key.pem"
	},

	"#dbmgr": "dbmgr相关配置",
	"dbmgr": {
		"mongo_cluster": {
			"game": {
				"hosts": [
					"**************:27017"
				],
				"username": "liubo03",
				"password": "yueying",
				"auth_source": "admin",
				"max_pool_size": 10
			}
		},
		"mongo_database": {
			"#proj_{cluster}": "指定每个database所使用的数据库实例",
			"proj_{cluster}": "game"
		},
		"worker_thread_num": 4
	},

	"#sidecar": "sidecar配置",
	"sidecar": {
		"host": "127.0.0.1",
		"port": 20001
	},

	"#raft_cluster": "raft集群配置；id是nodeId",
	"raft_cluster": [
		{"id": 1, "host": "127.0.0.1", "port": 1001, "client_port": 1101},
		{"id": 2, "host": "127.0.0.1", "port": 1002, "client_port": 1102},
		{"id": 3, "host": "127.0.0.1", "port": 1003, "client_port": 1103}
	]
}
```
