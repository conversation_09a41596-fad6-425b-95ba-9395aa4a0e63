{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "100"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "2": {"Type": "ModifyDamageNode", "Field": {"damagePackageId": {"Type": "number", "Value": "0"}, "overwriteArgs": {"Type": "nil", "Value": "null"}, "immune": {"Type": "boolean", "Value": "False"}, "modifyTarget": {"Type": "boolean", "Value": "False"}, "newTargetId": {"Type": "string", "Value": ""}, "modifyCritAttr": {"Type": "boolean", "Value": "False"}, "alwaysCrit": {"Type": "boolean", "Value": "False"}, "neverCrit": {"Type": "boolean", "Value": "False"}, "damageFactor": {"Type": "number", "Value": "0.3"}, "modifyComboAttr": {"Type": "boolean", "Value": "False"}, "isComboDisabled": {"Type": "boolean", "Value": "False"}}}, "3": {"Type": "DecreaseBuffLayerNode", "Field": {"targetId": {"Type": "string", "Value": ""}, "buffId": {"Type": "number", "Value": "10009"}, "count": {"Type": "number", "Value": "1"}}}, "4": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "TargetId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "5": {"Type": "BranchNode", "Field": {"condition": {"Type": "boolean", "BlackboardValue": "IsInevitableDamage"}}}}, "Links": {"0": {"BeforeDamageNode": ["5.prev"]}, "2": {"next": ["3.prev"]}, "4": {"next": ["2.prev"]}, "5": {"elseNode": ["4.prev"], "next": ["3.prev"]}}, "DataFlows": {"4": {"targetIds": ["3.targetId"]}}}