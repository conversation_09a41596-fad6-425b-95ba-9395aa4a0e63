{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode"}, "1": {"Type": "ModifyAttributeNode", "Field": {"attributeType": {"Type": "number", "Value": "27"}, "modifierType": {"Type": "number", "Value": "1"}, "valueFormula": {"Type": "string", "Value": ""}, "value": {"Type": "number", "Value": "0"}}}, "2": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "varName": {"Type": "string", "Value": ""}, "desc": {"Type": "string", "Value": "离间追击战法发动率增加"}, "result": {"Type": "number", "Value": "0"}}}}, "Links": {"0": {"onLayerIncreased": ["2.prev"]}, "2": {"next": ["1.prev"]}}, "DataFlows": {"2": {"result": ["1.value"]}}}