---@class ClientHotfixUtils
ClientHotfixUtils = DefineClass("ClientHotfixUtils")
DeclareAndSetGlobal("ClientHotfixUtils", ClientHotfixUtils)

-- hotfixData包含了所有的内容，需满足幂等性
function ClientHotfixUtils.hotfix(hotfixData)
    InfoLog("hotfix md5: %s", EZE.md5hexa(hotfixData))
    local hotfix 
    if LUA_VERSION_NUM < 502 then
        hotfix = loadstring(hotfixData)
    else
        hotfix = load(hotfixData)
    end
    if hotfix then
        xpcall(hotfix, function(error)
            ErrorLog(EZE.traceback(nil, error))
        end)
    else
        ErrorLog(EZE.traceback(nil, error))
    end
end

-- for emmylua
return {ClientHotfixUtils=ClientHotfixUtils}
