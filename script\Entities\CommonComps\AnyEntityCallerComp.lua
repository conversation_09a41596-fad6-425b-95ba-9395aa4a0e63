local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local GlobalDataMgrEnv = xrequire("Framework.Utils.GlobalDataMgr")
local GlobalEntityMgr = xrequire("Framework.Utils.GlobalEntityMgr")

CallLoadProxyStrategy = {
    Random = 1,
    LoadBalance = 2
}

---@class AnyEntityCallerComp : ComponentBase, SEntity
AnyEntityCallerComp = DefineClass("AnyEntityCallerComp", ComponentBaseEnv.ComponentBase)

function AnyEntityCallerComp:ctor()
    self._lastQueryId = 0  ---@type int
    self.loadRecords = {}
end

---调用各类实体的方法集合，一般通过Service转发

--region Actor in Map

---@param actorUid string Actor的Uid
---@param funcName string Actor的方法名
---@param ... any Actor方法的参数
function AnyEntityCallerComp:callActor(actorUid, funcName, ...)
    assert(actorUid ~= "")
    self:serviceRpc("ActorCallerService", "callActor", actorUid, funcName, {...})
end

function AnyEntityCallerComp:callActorByEid(eid, funcName, ...)
    -- TODO(qun): 如果指令中包含eid，可能导致起服恢复指令时报错，考虑先完成替换
    assert(eid ~= 0)
    self:serviceRpc("ActorCallerService", "callActorByEid", eid, funcName, {...})
end

-- endregion

--region Map

AnyEntityCallerComp.WORLD_MAILBOX = nil

---@param position Position 格子的坐标
---@param funcName string 地图的方法名
---@param ... any 地图的方法参数
function AnyEntityCallerComp:callWorldSpace(position, funcName, ...)
    ---@diagnostic disable-next-line: unnecessary-assert
    assert(position.x and position.y)
    if not AnyEntityCallerComp.WORLD_MAILBOX then
        local worldInfo = GlobalDataMgrEnv.instance:get("world")
        assert(worldInfo and worldInfo.mailbox, "World mailbox not set")
        AnyEntityCallerComp.WORLD_MAILBOX = LuaMailbox.createFromString(worldInfo.mailbox)
    end
    self:serverRpc(AnyEntityCallerComp.WORLD_MAILBOX, funcName, ...)
end

--endregion

--region 联盟

function AnyEntityCallerComp:callAlly(allyId, funcName, ...)
    if not allyId or allyId == 0 then
        return
    end
    self:serviceRpc("AllyService", "callAlly", allyId, funcName, {...}, {})
end

function AnyEntityCallerComp:callAllyIfExist(allyId, notExistCallback, funcName, ...)
    if not allyId or allyId == 0 then
        return
    end
    self:serviceRpc("AllyService", "callAlly", allyId, funcName, {...}, notExistCallback)
end

function AnyEntityCallerComp:broadcastAllyAvatars(allyId, funcName, ...)
    self:callAlly(allyId, "ReqBroadcastAvatar", funcName, {...})
end

function AnyEntityCallerComp:broadcastAllyWorldAvatars(allyId, funcName, ...)
    self:callAlly(allyId, "ReqBroadcastWorldAvatar", funcName, {...})
end

function AnyEntityCallerComp:broadcastAllyClients(allyId, funcName, ...)
    self:callAlly(allyId, "ReqBroadcastClient", funcName, {...})
end

--endregion

--region 世族

function AnyEntityCallerComp:findClanMbByClanId(clanId)
    local clanData = GlobalDataMgrEnv.instance:get("ClanData") or { clans = {}, clanName2clanId = {}, clanPref2clanIdList = {}, clanComm2clanIdList = {} }
    return clanData.clans[clanId] and LuaMailbox.createFromString(clanData.clans[clanId])
end

function AnyEntityCallerComp:findClanMbByClanName(clanName)
    local clanData = GlobalDataMgrEnv.instance:get("ClanData") or { clans = {}, clanName2clanId = {}, clanPref2clanIdList = {}, clanComm2clanIdList = {} }
    local clanId = clanData.clanName2clanId[clanName]
    return clanId and self:findClanMbByClanId(clanId)
end

function AnyEntityCallerComp:WaitClanData(cb)
    if GlobalDataMgrEnv.instance:get("ClanData") then
        cb()
    else
        self:registerGlobalDataCb("ClanData", EZE.genUUID(), function()
            cb()
            return true  -- callback后解除注册
        end)
    end
end

function AnyEntityCallerComp:callClan(clanId, errCb, funcName, ...)
    local clanMb = self:findClanMbByClanId(clanId)
    if not clanMb and (self.IsWorldAvatar or self.IsAvatar) then
        local worldAvatar = self.IsWorldAvatar and self or self.worldAvatar
        if clanId == worldAvatar.props.clanId then
            clanMb = worldAvatar.clanMb
        end
    end
    if not clanMb then
        self:logInfo("callClan Fail. Cannot Find ClanMailbox. clanId: %s, funcName: %s", clanId, funcName)
        if errCb then
            errCb()
        end
        return
    end

    self:serverRpc(clanMb, funcName, ...)
end

--endregion

--region Avatar

function AnyEntityCallerComp:callAvatar(avatarGid, funcName, ...)
    self:serviceRpc("RoleService", "callAvatar", avatarGid, funcName, {...})
end

function AnyEntityCallerComp:callWorldAvatar(avatarGid, funcName, ...)
    self:serviceRpc("RoleService", "callWorldAvatar", avatarGid, funcName, {...}, {})
end

function AnyEntityCallerComp:callWorldAvatarIfExist(avatarGid, notExistCallback, funcName, ...)
    self:serviceRpc("RoleService", "callWorldAvatar", avatarGid, funcName, {...}, notExistCallback)
end

function AnyEntityCallerComp:broadcastAvatarClient(funcName, ...)
    self:serviceRpc("RoleService", "broadcastAvatarClient", funcName, {...})
end

--endregion

--region Chunk

function AnyEntityCallerComp:callAvatarGrids(avatarGid, funcName, ...)
    -- 调用WorldSpace上的 funcName(avatarGid, packedPosList, ...)
    -- 由于存在多个WorldSpace，有归属目标Avatar的格子的WorldSpace都会被调用，对应的packedPosList都为处于该WorldSpace的目标Avatar的格子
    -- 对应的方法建议实现在 WorldSpace的ChunkMgrComp中
    self:serviceRpc("RoleService", "callWorldAvatar", avatarGid, "callAvatarChunks", funcName, {...}, {})
end

--endregion

--region 客户端

function AnyEntityCallerComp:safeClientRpc(funcName, ...)
    if self:hasClient() then
        self:clientRpc(funcName, ...)
    end
end

--endregion

--region Tools

--- 生成一个int类型的id，用于单次RPC查询回调有效性的校验
function AnyEntityCallerComp:genQueryId()
    self._lastQueryId = self._lastQueryId + 1
    if self._lastQueryId >= 0xFFFF then
        self._lastQueryId = 1
    end
    return self._lastQueryId
end

--endregion

--region LogicProxy

function AnyEntityCallerComp:WaitGlobalLogicProxy(cb)
    if GlobalDataMgrEnv.instance:get("GlobalLogicProxy") then
        cb()
    else
        self:registerGlobalDataCb("GlobalLogicProxy", EZE.genUUID(), function()
            cb()
            return true  -- callback后解除注册
        end)
    end
end

function AnyEntityCallerComp:acquireLogicProxyMb(tag, strategy, strategyParam)
    local globalLogicProxy = GlobalDataMgrEnv.instance:get("GlobalLogicProxy")
    if not globalLogicProxy then
        self:logError("acquireLogicProxyMb. Cannot Find Logic Proxy List In GlobalData. tag: %s, strategy: %s, strategyParam: %v", tag, strategy, strategyParam)
        return
    end

    local logicProxyList = globalLogicProxy.tag2logicProxyList[tag]
    if not logicProxyList or #logicProxyList == 0 then
        self:logError("acquireLogicProxyMb. Cannot Find Specific Tag Logic Proxy List. tag: %s, strategy: %s, strategyParam: %v", tag, strategy, strategyParam)
        return
    end

    local mailbox
    if strategy == CallLoadProxyStrategy.Random then
        local index = EZE.hash(strategyParam.hashKey) % #logicProxyList + 1
        mailbox = globalLogicProxy.logicProxyMap[logicProxyList[index]].mailbox
    elseif strategy == CallLoadProxyStrategy.LoadBalance then
        if not self.loadRecords[tag] then
            self.loadRecords[tag] = {}
        end
        local loadRecord = self.loadRecords[tag]

        local currContainerId = -1
        local currLoad = -1
        for _, containerId in ipairs(logicProxyList) do
            local load = loadRecord[containerId] or 0
            if currContainerId == -1 or load < currLoad then
                currContainerId = containerId
                currLoad = load
                if load == 0 then
                    break
                end
            end
        end

        loadRecord[currContainerId] = currLoad + 1
        mailbox = globalLogicProxy.logicProxyMap[currContainerId].mailbox
    else
        self:logError("acquireLogicProxyMb. Strategy Error. tag: %s, strategy: %s, strategyParam: %v", tag, strategy, strategyParam)
        return
    end

    self:logInfo("acquireLogicProxyMb. tag: %s, strategy: %s, mailbox: %s, strategyParam: %v", tag, strategy, mailbox, strategyParam)
    return LuaMailbox.createFromString(mailbox)
end

function AnyEntityCallerComp:callLogicProxy(tag, strategy, strategyParam, errCb, funcName, ...)
    local mailbox = self:acquireLogicProxyMb(tag, strategy, strategyParam)
    if not mailbox then
        if errCb then
            errCb()
        end

        return
    end

    self:serverRpc(mailbox, funcName, ...)
end

-- todo lzy 负载均衡策略下, 记得把销毁的entity也都清掉

--endregion

--region PrefectureSync

function AnyEntityCallerComp:WaitPrefectureSync(cb)
    if GlobalDataMgrEnv.instance:get("PrefectureSync") then
        cb()
    else
        self:registerGlobalDataCb("PrefectureSync", EZE.genUUID(), function()
            cb()
            return true  -- callback后解除注册
        end)
    end
end

function AnyEntityCallerComp:callPrefectureSync(prefecture, errCb, funcName, ...)
    local PrefectureSyncData = GlobalDataMgrEnv.instance:get("PrefectureSync")
    if not PrefectureSyncData then
        self:logInfo("callPrefectureSync Fail. Cannot Find Prefecture Sync Data In GlobalData. prefecture: %s", prefecture)
        if errCb then
            errCb()
        end

        return
    end

    local mailbox = LuaMailbox.createFromString(PrefectureSyncData.prefectureSyncs[prefecture])
    self:serverRpc(mailbox, funcName, ...)
end

--endregion

--region Safe Service Rpc

function AnyEntityCallerComp:safeWaitCallService(serviceName, method, ...)
    local info = GlobalEntityMgr.instance:getGlobalEntityInfo(serviceName, self:shardingKey())
    if info then
        self:serviceRpc(serviceName, method, ...)
        return
    end
    local args = {...}
    GlobalEntityMgr.instance:registerReadyCb(serviceName, EZE.genUUID(), function()
        self:serviceRpc(serviceName, method, table.unpack(args))
        return false
    end)
end

function AnyEntityCallerComp:safeWaitServiceCallback(serviceName, waitKey, cb)
    local info = GlobalEntityMgr.instance:getGlobalEntityInfo(serviceName, self:shardingKey())
    if info then
        cb()
        return
    end
    GlobalEntityMgr.instance:registerReadyCb(serviceName, waitKey, function()
        cb()
        return false
    end)
end

--endregion