--region GlobalDataType

---@class GlobalDataType
---@field names table<int, string>
GlobalDataType = {
    BASE = 0,
    ACK = 1,
    CAS = 2,
}

local names = {}
for name, i in pairs(GlobalDataType) do
    names[i] = name
end
GlobalDataType.names = names

GlobalDataType.types = {}
function GlobalDataType:register(type, cls)
    self.types[type] = cls
end

GlobalDataType.supportNil = {
    [GlobalDataType.BASE] = true,
}

GlobalDataType.supportCas = {
    [GlobalDataType.CAS] = true,
}

---@alias GlobalData GlobalDataBase | GlobalDataAck | GlobalDataCas
---@param key string
---@param val any
---@param type? int
---@return GlobalData
function GlobalDataType:create(key, val, type, ...)
    return self.types[type].new(key, val, type, ...)
end

DeclareAndSetGlobal("GlobalDataType", GlobalDataType)

--endregion GlobalDataType


--region GlobalDataBase

---基础的GlobalData数据结构
---@class GlobalDataBase
---@field key string
---@field val GlobalDataVal
---@field type int
GlobalDataBase = DefineClass("GlobalDataBase")
GlobalDataType:register(GlobalDataType.BASE, GlobalDataBase)

function GlobalDataBase:ctor(key, val, type, ...)
    self.key = key
    self.val = val
    self.type = type
    self:initDataEx(...)
end

-- 每个派生结构初始化自己的数据
function GlobalDataBase:initDataEx(...)
end

function GlobalDataBase:prevCallback(key, prev)
end

function GlobalDataBase:postCallback(key, prev)
end

--endregion GlobalDataBase


--region GlobalDataAck

---有确认回调GlobalData数据结构
---@class GlobalDataAck: GlobalDataBase
---@field src EntityId
---@field node NodeId
GlobalDataAck = DefineClass("GlobalDataAck", GlobalDataBase)
GlobalDataType:register(GlobalDataType.ACK, GlobalDataAck)

---@param src EntityId
---@param node NodeId
function GlobalDataAck:initDataEx(src, node)
    self.src = src
    self.node = node
end

function GlobalDataAck:prevCallback(key, prev)
    if EZE.entityIdToContainerId(self.src) == containerId and self.node == EZE.nodeUid() then
        local entity = EntityManager.getEntity(self.src)
        if entity then
            entity:ensureGlobalDataAckNodes(key)
        end
    end
end

function GlobalDataAck:postCallback(key, prev)
    if EZGlobal.ContainerReady or (EZE.entityIdToContainerId(self.src) == containerId and self.node == EZE.nodeUid()) then
        EZGlobal.Entity:send_message(self.src, 0, "onGlobalDataAck", key, containerId)
    end
end

--endregion GlobalDataAck


--region GlobalDataCas

---GlobalData原子操作基础款
---@class GlobalDataCas: GlobalDataBase
---@field inc int @cas的自增量
GlobalDataCas = DefineClass("GlobalDataCas", GlobalDataBase)
GlobalDataType:register(GlobalDataType.CAS, GlobalDataCas)

---@param inc int @cas的自增量；保持在参数列表最后，是自动生成不需要初始化时传参；这里做校验用
function GlobalDataCas:initDataEx(inc)
    local data = instance.data[self.key]
    assert(not data or (data and (data.inc + 1) == inc), string.format("pre inc: %s, cur inc:%s", data and data.inc, inc))
    self.inc = inc
end

function GlobalDataCas:pack()
    return EZE.msgpackPack({self.val, self.type, self.inc})
end

function GlobalDataCas:raw()
    return {self.val, self.type, self.inc}
end

--endregion


--region GlobalDataMgr

---@alias GlobalDataVal nil|boolean|number|string|table
---@alias GlobalDataCB fun(val: GlobalDataVal, prevVal: GlobalDataVal):boolean

---@class GlobalDataMgr
---@field data table<string, GlobalData>
---@field callbacks table<string, table<string, GlobalDataCB>>
GlobalDataMgr = DefineClass("GlobalDataMgr")

function GlobalDataMgr:ctor()
    self.data = {}
    self.callbacks = {}
end

---写入数据。
---注意这个过程是异步的，要等更新之后才能获取到新的值。
---可以注册更新回调。
---支持原子操作（cas），不过需要业务层保证数据的唯一性（不同的container有不同的值）
---@param key string @global data key
---@param val GlobalDataVal @global data value
---@param type? int @global data type
function GlobalDataMgr:push(key, val, type, ...)
    type = type or GlobalDataType.BASE
    if val == nil then
        if not GlobalDataType.supportNil[type] then
            error("GlobalDataType[%s] not support nil value", type)
        end
        EZE.popGlobalData(key)
    elseif GlobalDataType.supportCas[type] then
        local prev = self.data[key]
        local data = {val, type, ...}
        data[#data + 1] = prev and (prev.inc + 1) or 1  -- 注意自增量是通过计算而非传参获得
        local bin = EZE.msgpackPack(data)
        EZE.pushGlobalData(key, bin, true, prev and prev:pack() or "")
    else
        EZE.pushGlobalData(key, EZE.msgpackPack({val, type, ...}), false, "")
    end
end

-- 读取数据
---@param key string @global data key
---@return GlobalDataVal
function GlobalDataMgr:get(key)
    return self.data[key] and self.data[key].val
end

-- 更新数据并执行回调
---@param key string @global data key
---@param bin string @global data bin
function GlobalDataMgr:onPushed(key, bin)
    ---@type GlobalData
    local data = GlobalDataType:create(key, unpack(EZE.msgpackUnpack(bin)))
    local prev = self.data[key]
    self.data[key] = data
    self.data[key]:prevCallback(key, prev)
    self:updateCallback(key, data.val, prev and prev.val)
    self.data[key]:postCallback(key, prev)
    if GlobalDataType.supportCas[data.type] then
    end
end

-- 删除数据并执行回调
---@param key string @global data key
function GlobalDataMgr:onPopped(key)
    local prev = self.data[key]
    self.data[key] = nil
    self:updateCallback(key, nil, prev and prev.val)
end

-- 更新后的回调
---@param key string @global data key
---@param val GlobalDataVal @global data val
---@param prevVal GlobalDataVal @prev val
function GlobalDataMgr:updateCallback(key, val, prevVal)
    local cbs = self.callbacks[key]
    if cbs and next(cbs) then
        self.callbacks[key] = {}
        for cbk, cb in pairs(cbs) do
            local suc, res = xpcall(function() return cb(val, prevVal) end, function(err) ErrorLog(EZE.traceback(err, 2)) end)
            if suc and not res then
                self.callbacks[key][cbk] = cb
            end
        end
    end
end

-- 注册更新回调
---@param key string @global data key
---@param cbKey string @回调的唯一id
---@param cb GlobalDataCB @回调函数，返回true表示在回调后注销回调
---@return boolean @是否注册成功
function GlobalDataMgr:registerCallback(key, cbKey, cb)
    if not cb then
        ErrorLog("registerCallback [%s.%s] but callback not exist", key, cbKey)
        return false
    end
    local cbs = self.callbacks[key]
    if not cbs then
        cbs = {}
        self.callbacks[key] = cbs
    end
    if cbs[cbKey] then
        WarnLog("registerCallback [%s.%s] but already exist, replace it", key, cbKey)
    end
    cbs[cbKey] = cb
    return true
end

-- 注销更新回调
---@param key string @global data key
---@param cbKey string @回调的唯一id
---@param silent? boolean @某些情况下不输出告警
function GlobalDataMgr:unregisterCallback(key, cbKey, silent)
    local cbs = self.callbacks[key]
    if cbs then
        if cbs[cbKey] then
            cbs[cbKey] = nil
        elseif not silent then
            WarnLog("unregisterCallback but %s.%s is not exist", key, cbKey)
        end
    elseif not silent then
        WarnLog("unregisterCallback but %s is not exist", key)
    end
end

-- 注册更新回调且已有数据时立刻进行一次回调
---@param key string @global data key
---@param cbKey string @回调的唯一id
---@param cb GlobalDataCB @回调函数，返回true表示在回调后注销回调
---@return boolean @注册是否成功且回调仍然生效
function GlobalDataMgr:ensureCallback(key, cbKey, cb)
    if not cb then
        ErrorLog("ensureCallback [%s.%s] but callback not exist", key, cbKey)
        return false
    end
    local data = self.data[key]
    if data then
        local suc, res = xpcall(function() return cb(self:get(key), nil) end, function(err) ErrorLog(EZE.traceback(err, 2)) end)
        if suc and not res then
            -- del self, just return false
            return false
        end
    end
    return self:registerCallback(key, cbKey, cb)
end

---@type GlobalDataMgr
instance = instance or GlobalDataMgr.new()

--endregion GlobalDataMgr

-- for emmylua
---@class GlobalDataMgrEnv
local GlobalDataMgrEnv = {
    GlobalDataType=GlobalDataType,
    GlobalDataBase=GlobalDataBase,
    GlobalDataAck=GlobalDataAck,
    GlobalDataCas=GlobalDataCas,
    GlobalDataMgr=GlobalDataMgr,
    ---@type GlobalDataMgr
    instance=instance,
}
return GlobalDataMgrEnv
