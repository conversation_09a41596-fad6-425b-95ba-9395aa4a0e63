# 调用环境设置脚本
& ".\environment.bat"

# 设置日志目录路径
$logDir = If ($env:LOG) {"$env:LOG\log"} Else {"$env:EZLOG\log"}

while ($true) {
    # 查找最新的日志文件
    $latestLog = Get-ChildItem -Path $logDir -Filter *.ndjson | Sort-Object LastWriteTime -Descending | Select-Object -First 1

    if ($latestLog) {
        # 获取当前时间
        $now = Get-Date

        # 获取文件的修改时间
        $fileCreateTime = $latestLog.CreationTime

        # 计算修改时间与当前时间的差值（秒）
        $timeDiff = ($now - $fileCreateTime).TotalSeconds

        # 判断是否在最近3秒内修改
        if ($timeDiff -le 3)
        {
            Write-Host "logtail: $($latestLog.Name) $timeDiff"

            # 使用 Get-Content 持续输出日志，只过滤错误和警告
            Get-Content -Path $latestLog.FullName -Wait | ForEach-Object {
                if ($_ -match "- E -") {
                    Write-Host $_ -ForegroundColor Red
                }
                elseif ($_ -match "- W -") {
                    Write-Host $_ -ForegroundColor Yellow
                }
            }
            break
        }
    }

    # 如果没有找到日志文件，则等待1秒钟再重试
    Start-Sleep -Seconds 1
}