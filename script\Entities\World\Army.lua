local WorldActorEnv = xrequire("Entities.World.WorldActor")
local MoveCompEnv = xrequire("Entities.World.Components.MoveComp")
local ActorCmdCompEnv = xrequire("Entities.World.Components.ActorCmdComp")
local AvatarOwningCompEnv = xrequire("Entities.World.Components.AvatarOwningComp")
local ArmyCombatCompEnv = xrequire("Entities.World.Components.ArmyCombatComp")
local ArmyBuffCompEnv = xrequire("Entities.World.Components.ArmyBuffComp")
local ActorContentCompEnv = xrequire("Entities.World.Components.ActorContentComp")
local enum_details = xrequire("Table.Defines.enum_details")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")
local NaviCompEnv = xrequire("Entities.World.Components.NaviComp")
local ArmyStrategyCompEnv = xrequire("Entities.World.Components.ArmyStrategyComp")

local WorldConst = xrequire("Common.Const.WorldConst")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local ArmyActionEnv = xrequire("Modules.World.ArmyAction")
local MoveUtils = xrequire("Utils.MoveUtils")
local CoordUtils = xrequire("Common.Utils.CoordUtils")

---@class Army : WorldActor, MoveComp, AvatarOwningComp, ArmyCombatComp, ArmyBuffComp, NaviComp
---@field props any
Army = DefineEntity("Army", {WorldActorEnv.WorldActor}, {
    MoveCompEnv.MoveComp,
    ActorCmdCompEnv.ActorCmdComp,
    AvatarOwningCompEnv.AvatarOwningComp,
    ArmyCombatCompEnv.ArmyCombatComp,
    ArmyBuffCompEnv.ArmyBuffComp,
    ActorContentCompEnv.ActorContentComp,
    NaviCompEnv.NaviComp,
    ArmyStrategyCompEnv.ArmyStrategyComp,
})
Army._actorType = TableConst.enums.ActorType.ARMY
Army._actionClass = {
    ArmyMarch = ArmyActionEnv.ArmyMarch(),
    ArmyReturn = ArmyActionEnv.ArmyReturn(),
    ArmyBattleNpc = ArmyActionEnv.ArmyBattleNpc(),
    ArmyBattle = ArmyActionEnv.ArmyBattle(),
    ArmyOccupy = ArmyActionEnv.ArmyOccupy(),
    ArmySingStrategy = ArmyActionEnv.ArmySingStrategy(),
    RpcCallbackAction = ActorCmdCompEnv.RpcCallbackAction(),
    ArmyStrategySha = ArmyActionEnv.ArmyStrategySha(),
    ArmyStrategyShan = ArmyActionEnv.ArmyStrategyShan(),
    ArmyWuZhongShengYou = ArmyActionEnv.ArmyWuZhongShengYou(),
    ArmyStrategyWuGuFengDeng = ArmyActionEnv.ArmyStrategyWuGuFengDeng(),
    ArmyStrategyKaiKen = ArmyActionEnv.ArmyStrategyKaiKen(),
    ArmyStrategyStartJiShenJiDian = ArmyActionEnv.ArmyStrategyStartJiShenJiDian(),
    ArmyStrategyJoinJiShenJiDian = ArmyActionEnv.ArmyStrategyJoinJiShenJiDian(),
}

function Army:ctor()
    self:refreshAllStatus()
end

function Army:isVisible()
    if self.props.inConByType[TableConst.enums.InConType.InBase] then
        return false
    end
    return true
end

function Army:isInteractable()
    if self.props.inConByType[TableConst.enums.InConType.InBase] then
        return false
    end
    if self.props.injured then
        return false
    end
    -- 这里还有重伤之类的情况，所以和visible区分开来
    return true
end

function Army:ValidStartBehavior(behaviorType)
    return BehaviorJudgeEnv.ValidStartBehavior(self.props, behaviorType)
end

function Army:GetMoveSpeedByType()
    return self:GetMoveSpeed()
end

function Army:CmdMarch(cmdUid, targetPos)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.March) then
        return
    end
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType()}},
    })
end

function Army:CmdOccupy(cmdUid, targetPos)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.OccupyGrid) then
        return
    end
    local tp = self.space:GetGridType(self:GetDoubledPos2D())
    local data = GameCommon.TableDataManager:GetMapElementData(tp)
    local remainTime = math.max(0, data.occupy_time)
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=targetPos, speed=self:GetMoveSpeedByType()}},
        {action="ArmyBattleNpc", params={attType=TableConst.enums.InteractiveBehavior.Occupy}},
        {action="ArmyOccupy", params={pos=targetPos, startTime=0, remainTime=remainTime, timerId=0}},
    })
end

function Army:CmdReturn(cmdUid)
    if not self:ValidStartBehavior(TableConst.enums.BehaviorType.Return) then
        return
    end
    self:StartCmd(cmdUid, {
        {action="ArmyMarch", params={pos=self.props.home.pos, speed=self:GetMoveSpeedByType()}},
        -- TODO(qun) 这里先临时直接用一下主堡的uid
        {action="ArmyReturn", params={baseId=self.props.home.baseId}},
    })
end

function Army:QueryMovePath(cmdUid, idx, pos)
    self:GetNaviPathToPos(pos, EntCb("onQueryMovePath", {idx=idx, pos=pos}))
end

function Army:onQueryMovePath(userdata, path)
    local timeCost = self:GetMoveSpeedByType() * MoveUtils.GetMovePathDistance(path, self:GetDoubledPos2DV())
    self:callOwnerClient("OnQueryMovePath", userdata.idx, userdata.pos, path, timeCost)
end

function Army:ActionBattle()
    self:InsertAction({action="ArmyBattle", params={}})
end

function Army:OnDefeated()
    self:logInfo("Army:OnDefeated")
    self.props.injured = true
    self:CheckInteractable()
    self:CmdReturn(EZE.genUUID())
end

-- function Army:OnRecvOccupyBattleResult(winCamp, rounds)
--     self:EnsureAction(WorldConst.ArmyActionType.BattleNpc)
--     self:GetCurAction():OnGetBattleResult(winCamp, rounds)
-- end

function Army:refreshAllStatus()
    self:refreshMainStatus()
    self:refreshCombatStatus()
    self:refreshDeployStatus()
end

function Army:refreshMainStatus()
    -- 刷新主状态
    local status = TableConst.enums.ArmyStatus.Stay
    if self.props.inConByType[TableConst.enums.InConType.InBase] then
        status = TableConst.enums.ArmyStatus.Rest
    elseif self:IsMoving() then
        status = TableConst.enums.ArmyStatus.March
    elseif self.props.occupyLand then
        status = TableConst.enums.ArmyStatus.Occupy
    elseif self.props.playingStrategy ~= 0 then
        status = TableConst.enums.ArmyStatus.Sing
        if self.props.waitContinuePlayStrategy then
            status = TableConst.enums.ArmyStatus.WaitSing
        end
    end
    assert(ProcessedTableEnv.ARMY_STATUS_GROUP.main[status])
    if status == self.props.mainStatus then
        return
    end
    self:logInfo("Army:refreshMainStatus %s %s -> %s", self.id,
        enum_details.GetEnumItemName("ArmyStatus", self.props.mainStatus), enum_details.GetEnumItemName("ArmyStatus", status))
    local oldStatus = self.props.mainStatus
    self.props.mainStatus = status
    self:syncActorView({
        mainStatus = status,
    })
    -- TODO(qun) 临时处理，考虑把判断丢到导表内
    if status == TableConst.enums.ArmyStatus.Rest or oldStatus == TableConst.enums.ArmyStatus.Rest then
        self:CheckVisible()
        self:CheckInteractable()  -- 这个应该还可以考虑重伤不进格子
    end

    self:fireEntityEvent("OnArmyMainStatusChanged", oldStatus)
end

function Army:refreshCombatStatus()
    -- 刷新战斗状态
    local status = TableConst.enums.ArmyStatus.IdleCombat
    if self:IsInCombat() then
        status = TableConst.enums.ArmyStatus.Combat
    elseif self:IsWaitCombat() then
        status = TableConst.enums.ArmyStatus.WaitCombat
    end
    assert(ProcessedTableEnv.ARMY_STATUS_GROUP.combat[status])
    if status == self.props.combatStatus then
        return
    end
    self:logInfo("Army:refreshCombatStatus %s %s -> %s", self.id,
        enum_details.GetEnumItemName("ArmyStatus", self.props.combatStatus), enum_details.GetEnumItemName("ArmyStatus", status))
    self.props.combatStatus = status
    self:syncActorView({
        combatStatus = status,
    })
end

function Army:refreshDeployStatus()
    -- 刷新调动状态
    local status = TableConst.enums.ArmyStatus.IdleDeploy
    assert(ProcessedTableEnv.ARMY_STATUS_GROUP.deploy[status])
    if status == self.props.deployStatus then
        return
    end
    self:logInfo("Army:refreshDeployStatus %s %s -> %s", self.id,
        enum_details.GetEnumItemName("ArmyStatus", self.props.deployStatus), enum_details.GetEnumItemName("ArmyStatus", status))
    self.props.deployStatus = status
    self:syncActorView({
        deployStatus = status,
    })
end

function Army:OnPosChanged(oldX, oldY)
    local x, y = self:GetDoubledPos2D()
    -- self:callOwner("OnArmyPosChanged", self.id, x, y)
    WorldActorEnv.WorldActor.OnPosChanged(self, oldX, oldY)
end

function Army:collectOwnerRegistExtraInfo()
    return {
        armySlotIdx = self.props.armySlotIdx,
        entityId = self.id,
    }
end

function Army:initActorView()
    self:syncActorView({
        mainStatus = self.props.mainStatus,
        combatStatus = self.props.combatStatus,
        deployStatus = self.props.deployStatus,
        statusEndTime = self.props.statusEndTime,
        moveStartPos = self.props.moveStartPos,
        moveStartTime = self.props.moveStartTime,
        moveTargetPos = self.props.moveTargetPos,
        movePath = self.props.movePath,
        moveSpeed = self.props.moveSpeed,
        pos = self:GetDoubledPos2DV(),
    })
end

function Army:ChangeArmyType(armyType)
    self.props.armyType = armyType
end

-- for emmylua
return {Army=Army}