--- @class TelnetServer : LuaClass
TelnetServer = DefineClass("TelnetServer")

TelnetServer.shortcuts = {}

function TelnetServer:ctor()
    self.server = LuaTelnetServer:new()
    self.feedbackBuffer = {}
    if LUA_VERSION_NUM < 502 then
        -- TODO support lua 5.2
        self:initEnv()
    end
end

function TelnetServer:initEnv()
    local orig_print = print
    local orig_InfoLog = InfoLog
    local orig_ErrorLog = ErrorLog
    
    local function env_print(...)
        orig_print(...)
        local output = {...}
        for i = 1, select("#", ...) do
            output[i] = tostring(select(i, ...))
        end
        self:feedback(table.concat(output, "\t"))
    end

    local function env_InfoLog(...)
        orig_InfoLog(...)
        self:feedback(EZE.log.get_last_log())
    end

    local function env_ErrorLog(...)
        orig_ErrorLog(...)
        self:feedback(EZE.log.get_last_log())
    end

    self.env = {
        print = env_print,
        InfoLog = env_InfoLog,
        ErrorLog = env_ErrorLog
    }

    for k, v in pairs(TelnetServer.shortcuts) do
        if type(v) == "function" then
            setfenv(v, self.env)
        end
        self.env[k] = v
    end
    setmetatable(self.env, {__index=_G})
end

function TelnetServer:start(ip, port)
    InfoLog("telnet server listening %s:%s", ip, port)
    return self.server:start(ip, port, self)
end

function TelnetServer:port()
    return self.server:port()
end

function TelnetServer:onCmd(connId, cmd, func)
    -- print("telnet server onCmd connId: %s, cmd: %s", connId, cmd)

    setfenv(func, self.env)

    self.env.print(select(2, xpcall(func, function(errormsg)
        errormsg = EZE.traceback(errormsg, 2)
        self:feedback(errormsg)
        return errormsg
    end)))

    local ret = table.concat(self.feedbackBuffer, '\r\n')

    -- clear buffer
    for i = 1, #self.feedbackBuffer do
        self.feedbackBuffer[i] = nil
    end

    return ret
end

function TelnetServer:feedback(msg)
    local telnet_msg = string.gsub(msg, "([^\r])\n", "%1\r\n")
    self.feedbackBuffer[#self.feedbackBuffer + 1] = telnet_msg
end

-- for emmylua
---@class TelnetServerEnv
local TelnetServerEnv = {TelnetServer=TelnetServer}
return TelnetServerEnv
