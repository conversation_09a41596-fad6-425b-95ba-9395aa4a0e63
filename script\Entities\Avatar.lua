
local AvatarEntity = xrequire("Framework.Entities.AvatarEntity")
---@type ContainerInstancesComp
local ContainerInstancesCompEnv = xrequire("Framework.Entities.Components.ContainerInstancesComp")
---@type AvatarBattleComponent
local AvatarBattleComponentEnv = xrequire("Entities.Components.AvatarBattleComponent")
local AvatarGMCompEnv = xrequire("Entities.Components.AvatarGMComp")
local AvatarWorldViewportCompEnv = xrequire("Entities.Components.AvatarWorldViewportComp")
local ServerTimeCompEnv = xrequire("Entities.Components.ServerTimeComp")
local AvatarBattleRecordCompEnv = xrequire("Entities.Components.AvatarBattleRecordComp")
local EventComponentEnv = xrequire("Entities.Components.EventComponent")
local AvatarHeroPackCompEnv = xrequire("Entities.Components.AvatarHeroPackComp")
local AvatarTacticPackCompEnv = xrequire("Entities.Components.AvatarTacticPackComp")
local AnyEntityCallerCompEnv = xrequire("Entities.Components.AnyEntityCallerComp")
local AvatarHomeCompEnv = xrequire("Entities.Components.AvatarHomeComp")
local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")  --临时代码
local TaskComponentEnv = xrequire("Entities.Components.TaskComponent")
local AvatarCareerStrategyCompEnv = xrequire("Entities.Components.AvatarCareerStrategyComp")
local ScheduleCompEnv = xrequire("Entities.Components.Common.ScheduleComp")
local EquipCompEnv = xrequire("Entities.Components.EquipComp")
local BagCompEnv = xrequire("Entities.Components.BagComp")
local GlobalDataSwitchEnv = xrequire("Modules.CustomGlobalData.GlobalDataSwitch")
local Switches = xrequire("Common.Switches")
local Schedule = xrequire("Modules.Schedule.Schedule")

---@class Avatar: AvatarEntity, ContainerInstancesComp<Avatar>, AvatarHeroPackComp, AvatarTacticPackComp, AnyEntityCallerComp, AvatarHomeComp, EventComponent, TaskComponent, EquipComp, BagComp
---@field worldAvatar WorldAvatar
---@field props any
Avatar = DefineEntity("Avatar", {AvatarEntity.AvatarEntity}, {
    ContainerInstancesCompEnv.ContainerInstancesComp, 
    AvatarBattleComponentEnv.AvatarBattleComponent,
    AvatarGMCompEnv.AvatarGMComp,
    AvatarWorldViewportCompEnv.AvatarWorldViewportComp,
    ServerTimeCompEnv.ServerTimeComp,
    AvatarBattleRecordCompEnv.AvatarBattleRecordComp,
    AvatarHeroPackCompEnv.AvatarHeroPackComp,
    AvatarTacticPackCompEnv.AvatarTacticPackComp,
    AnyEntityCallerCompEnv.AnyEntityCallerComp,
    AvatarHomeCompEnv.AvatarHomeComp,
    EventComponentEnv.EventComponent,
    TaskComponentEnv.TaskComponent,
    AvatarCareerStrategyCompEnv.AvatarCareerStrategyComp,
    ScheduleCompEnv.ScheduleComp,
    EquipCompEnv.EquipComp,
    BagCompEnv.BagComp,
})

function Avatar:ctor()
    self:logDebug("Avatar.ctor urs: %s, nonprop: %s", self.props.urs, self.notprop)
    self:serviceRpc("RoleService", "avatarCreated", self.props.urs, self:getMailboxStr())
    if self:getDBID() == "" then
        self:addTimer(0, 0, function() self:save() end)
    end

    self.worldAvatar = nil
    self:fireEntityEvent("onCreate")
end

function Avatar:onCreate()
    self:GetOrCreateWorldAvatar()
end

function Avatar:onDestroy()
    self:serviceRpc("RoleService", "avatarDestroyed", self.props.urs, self:getMailboxStr())
    if self.worldAvatar then
        self.worldAvatar:OnLoseAvatar()
        self.worldAvatar = nil
    end
end
function Avatar:GetOrCreateWorldAvatar()
    local worldAvatar = self:tmpGetWorldAvatar()
    if worldAvatar == nil then
        ---@type WorldAvatar
        worldAvatar = EntityManager.createEntity("WorldAvatar", 0, {
            gid = self.props.gid,
            urs = self.props.urs,
            name = self.props.name
        })
    end
    self.worldAvatar = worldAvatar
    worldAvatar:OnGetAvatar(self)
    if self:hasClient() then
        self:giveClientToRemote(worldAvatar:getMailbox(), false)
    end
    -- TODO(qun): 创建Avatar的事件
    self:initHomeBuilding()
    self:initAcceptFirstTask()
    self:initStrategyPack()
    -- TODO(qun): 初始化Avatar的事件
    self:loadAllTask()
    self:syncHomeBuffToArmy()
end

function Avatar:tmpGetWorldAvatar()
    --获取WorldAvatar的id用于绑定，返回nil为未创建需要新创建
    --TODO(qun): 非常临时的方法，后续迭代，现用于将WorldAvatar和Avatar绑定相互引用
    local info = GlobalEntityMgr.instance:getGlobalEntityInfo("WorldService")
    assert(info and info.ready)
    local serviceId = info.mailbox:getEntityId()
    local service = EntityManager.getEntity(serviceId)
    local worldAvatarMailbox = service.worldAvatars[self.props.gid]
    if not worldAvatarMailbox then
        return nil
    end
    local worldAvatar = EntityManager.getEntity(worldAvatarMailbox:getEntityId())
    assert(worldAvatar)
    return worldAvatar
end

function Avatar:fireAvatarEvent(event, id, ...)
    self:fireEvent(event, id, ...)
    if self.worldAvatar then
        self.worldAvatar:fireEvent(event, id, ...)
    end
end

function Avatar:HasWorldAvatar()
    return self.worldAvatar ~= nil
end

function Avatar:onGetClient()
    self:SyncSwitches()
    AvatarEntity.AvatarEntity.onGetClient(self)
    if self.worldAvatar then
        self:giveClientToRemote(self.worldAvatar:getMailbox(), false)
    end
    self:serviceRpc("AmbitionsService", "ObserveAmbitions", self:getClient())
end

function Avatar:gmOfflineNow()
    self:destroy()
end

function Avatar:gmOneClickGrowth()
    self:gmAddHero(0, 1)
    self:gmSetHeroLevel(0, 50, 0)
    self:gmSetHeroStars(0, 5)
    self:gmAddTactic(0, 1)
    self:gmSetTacticLevel(0, 10)
    self:gmSetTacticStars(0, 5)
    self:gmUpgradeHomeBuilding(0, -1)
    self:gmUpgradeHomeTech(0, -1)
    self.worldAvatar:gmSetCurrency(0, 100000000)
end

function Avatar:gmSetSwitch(name, value)
    GlobalDataSwitchEnv.SetSwitch(name, value)
end

function Avatar:OnSetSwitch(name, value)
    self:safeClientRpc("OnSetSwitch", name, value)
end

function Avatar:SyncSwitches()
    self:clientRpc("SyncSwitches", GlobalDataSwitchEnv.ModifiedSwitches)
end

function Avatar:OnCommonDailySchedule(userdata, nowCsv)
    local lastCsv = self:GetCsv(TableConst.enums.ScheduleType.CommonDaily) or nowCsv
    local lastCycle = Schedule.fromScheduleCSV(lastCsv)
    local nowCycle = Schedule.fromScheduleCSV(nowCsv)
    self:logInfo("OnCommonDailySchedule csv(%s) %s -> %s", nowCsv, lastCycle, nowCycle)
    self:OnCareerStrategyDailySchedule(lastCycle, nowCycle)
end

function Avatar:GmSetTime(year, month, day, hour, minute, second)
    local ServerTimeEnv = xrequire("Utils.ServerTime")
    local GlobalDataMgrEnv = xrequire("Framework.Utils.GlobalDataMgr")
    local nowTS = ServerTimeEnv.GetServerNow()
    local t = os.date("*t", nowTS)
    local timeTuple = {
        (year and year >= 0) and year or t.year,
        (month and month >= 0) and month or t.month,
        (day and day >= 0) and day or t.day,
        (hour and hour >= 0) and hour or t.hour,
        (minute and minute >= 0) and minute or t.min,
        (second and second >= 0) and second or t.sec,
    }
    GlobalDataMgrEnv.instance:push("broadcast", EZE.msgpackPack({
        method = "timeproxy",
        args = timeTuple,
        callback = "GmBroadcastCallback",
        mailboxStr = self:getMailboxStr(),
        uid = "timeproxy_" .. EZE.genUUID(),
    }))
end

function Avatar:GmBroadcastCallback(cid, uid, succ, res)
    if not succ then
        self:logError("GmBroadcastCallback failed %s %s", cid, uid)
    end
end