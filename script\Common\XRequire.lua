local stringLen = string.len
local stringSub = string.sub
local stringGsub = string.gsub
local stringFormat = string.format

local major, minor = _VERSION:match("Lua (%d+)%.(%d+)")
assert(major and minor)
LUA_VERSION_NUM = major * 100 + minor

local function startswith(str, prefix)
    return stringSub(str, 1, stringLen(prefix)) == prefix
end

-- x require

local blackModuleList = {
    bson = 1,
    jit = 1,
    bit = 1,
    ffi = 1,
    EZE = 1,
    cjson = 1,
}

XModuleLoaded = XModuleLoaded or {}
XModuleLoadedByOrder = XModuleLoadedByOrder or {}

local function _XLoadModule(moduleName, env)
    local ret, msg
    if tolua ~= nil then
        -- unity
        local filepath = string.gsub(moduleName, "%.", "/")
        if filepath then
            if LUA_VERSION_NUM < 502 then
                ret, msg = loadfile(filepath)
                if ret then
                    setfenv(ret, env)
                end
            else
                ret, msg = loadfile(filepath, nil, env)
            end
        else
            ret, msg = nil, "no file"
        end
    else
        -- non-unity
        local filepath = package.searchpath(moduleName, package.path)
        if filepath then
            if LUA_VERSION_NUM < 502 then
                ret, msg = loadfile(filepath)
                if ret then
                    setfenv(ret, env)
                end
            else
                ret, msg = loadfile(filepath, nil, env)
            end
        else
            ret, msg = nil, "no file"
        end
    end
    if ret == nil then
        error(msg .. " -> " .. moduleName)
    end
    return ret
end

---@overload fun(moduleName: string): table
---@param moduleName string
---@param isReloading? boolean
---@return table
function xrequire(moduleName, isReloading)
    if blackModuleList[moduleName] ~= nil or startswith(moduleName, "jit.") then
        return raw_require(moduleName)
    end

    if XModuleLoaded[moduleName] == nil or isReloading then
        if XModuleLoaded[moduleName] == nil then
            local moduleEnv = setmetatable({__module_name = moduleName}, {
                __index = _G
            })
            moduleEnv.__xdata = _XLoadModule(moduleName, moduleEnv)()
            XModuleLoaded[moduleName] = moduleEnv
            XModuleLoadedByOrder[#XModuleLoadedByOrder + 1] = moduleName
            return moduleEnv
        else
            local moduleEnv = setmetatable(XModuleLoaded[moduleName], {
                __index = _G
            })
            moduleEnv.__xdata = _XLoadModule(moduleName, moduleEnv)()
            return moduleEnv
        end
    else
        return XModuleLoaded[moduleName]
    end
end

raw_require = raw_require or require
--require = xrequire

-- x reload

---@param module table
---@param reloadAll boolean
local function onModuleReload(module, reloadAll)
    if module and type(module) == "table" then
        for objname, obj in pairs(module) do
            if type(obj) == "table" and objname:sub(1, 2) ~= "__" and obj.onReload then
                obj:onReload(reloadAll)
            end
        end
    end
end

_G.XReloading = false

function ReloadAll()
    _G.XReloading = true
    collectgarbage("stop")
    if ClearEntityCache then ClearEntityCache() end
    for _, v in ipairs(XModuleLoadedByOrder) do
        onModuleReload(xrequire(v, true), true)
    end
    _G.XReloading = false
    collectgarbage("restart")
end

function ReloadModule(moduleName)
    if not XModuleLoaded[moduleName] then
        print("there is no need to reload, no module loaded " .. moduleName)
        return
    end
    print("reload module " .. moduleName)
    _G.XReloading = true
    if ClearEntityCache then ClearEntityCache() end
    onModuleReload(xrequire(moduleName, true), false)
    _G.XReloading = false
end

-- x strict
local _G_mt = getmetatable(_G)
if _G_mt == nil then
    _G_mt = {}
    setmetatable(_G, _G_mt)
end
_G_mt.__declared = _G_mt.__declared or {}

-- 锁定全局变量，声明后才可以设置
function LockGlobal()
    local getinfo, error, rawset, rawget = debug.getinfo, error, rawset, rawget

    local function what()
        local d = getinfo(3, "S")
        return d and d.what or "C"
    end

    local mt = getmetatable(_G)
    mt.__newindex = function(t, n, v)
        if not mt.__declared[n] then
            local w = what()
            if w ~= "C" then
                error("assign to undeclared variable '" .. n .. "'", 2)
            end
            mt.__declared[n] = true
        end
        rawset(t, n, v)
    end
end

-- 声明全局变量，声明后才可以设置
---@param k string 全局变量名
---@param try boolean? 尝试声明，如果已经声明也不会报错
function DeclareGlobal(k, try)
    local mt = getmetatable(_G)
    if not mt.__declared[k] then
        mt.__declared[k] = true
    elseif not XReloading and not try then
        error("global variable " .. k .. " has been declared")
    end
end

-- 声明并设置全局变量
---@generic T
---@param k string 全局变量名
---@param v T 全局变量值
---@param try bool? 尝试声明，如果已经声明也不会报错
---@return T @全局变量值
function DeclareAndSetGlobal(k, v, try)
    DeclareGlobal(k, try)
    rawset(_G, k, v)
    return v
end

DeclareAndSetGlobal("LUA_VERSION_NUM", LUA_VERSION_NUM)
