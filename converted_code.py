import struct
import numpy as np
import os

def load_map_data(self, filename):
    """
    将Lua代码转换为Python版本
    读取二进制地图文件并处理数据
    """
    try:
        # 打开文件
        with open(filename, "rb") as file:
            # 读取整个文件内容
            data = file.read()
    except IOError:
        return False, f"open file {filename} failed"
    
    # 创建缓冲区（Python中直接使用bytes）
    buf = data
    offset = 0
    
    # 读取地图尺寸并校验
    # 使用struct.unpack读取int16_t（小端序）
    x_size = struct.unpack('<h', buf[offset:offset+2])[0]
    offset += 2  # sizeof(int16_t) = 2
    
    y_size = struct.unpack('<h', buf[offset:offset+2])[0]
    offset += 2  # sizeof(int16_t) = 2
    
    # 校验地图尺寸
    assert x_size == self.x_size and y_size == self.y_size, \
           f"map size mismatch {x_size} {y_size}"
    
    # 读取地图数据
    # 创建numpy数组替代FFI数组
    valid_map_shape = (self.max_q - self.min_q, self.max_r - self.min_r)
    score_map_shape = (self.max_q - self.min_q, self.max_r - self.min_r)
    
    # 初始化地图数组
    self.valid_map = np.ones(valid_map_shape, dtype=np.int8)  # 填充1
    self.score_map = np.zeros(score_map_shape, dtype=np.int8)  # 填充0
    
    return True, None

# 如果需要更精确地模拟FFI的行为，可以使用ctypes
import ctypes

def load_map_data_with_ctypes(self, filename):
    """
    使用ctypes更精确地模拟FFI行为
    """
    try:
        with open(filename, "rb") as file:
            data = file.read()
    except IOError:
        return False, f"open file {filename} failed"
    
    # 创建ctypes缓冲区
    buf = ctypes.create_string_buffer(data)
    offset = 0
    
    # 读取int16_t数据
    x_size = ctypes.c_int16.from_buffer(buf, offset).value
    offset += ctypes.sizeof(ctypes.c_int16)
    
    y_size = ctypes.c_int16.from_buffer(buf, offset).value
    offset += ctypes.sizeof(ctypes.c_int16)
    
    # 校验
    assert x_size == self.x_size and y_size == self.y_size, \
           f"map size mismatch {x_size} {y_size}"
    
    # 创建二维数组类型
    valid_rows = self.max_q - self.min_q
    valid_cols = self.max_r - self.min_r
    score_rows = self.max_q - self.min_q  
    score_cols = self.max_r - self.min_r
    
    # 定义数组类型
    ValidMapType = (ctypes.c_int8 * valid_cols) * valid_rows
    ScoreMapType = (ctypes.c_int8 * score_cols) * score_rows
    
    # 创建并初始化数组
    self.valid_map = ValidMapType()
    self.score_map = ScoreMapType()
    
    # 填充数组
    for i in range(valid_rows):
        for j in range(valid_cols):
            self.valid_map[i][j] = 1
            
    for i in range(score_rows):
        for j in range(score_cols):
            self.score_map[i][j] = 0
    
    return True, None
