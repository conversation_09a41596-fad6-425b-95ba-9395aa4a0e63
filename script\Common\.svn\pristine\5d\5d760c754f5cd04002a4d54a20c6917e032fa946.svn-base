local CompatEnv = xrequire("Common.Actor.Compat")
local CoordUtils = xrequire("Common.Utils.CoordUtils")


-- 双端Entity的一些通用方法，在CommonActor里面用到的接口，在这里声明一下
---@class CommonEntity
---@field id integer
---@field anyRpc fun(self: CommonEntity, arg: any)
---@field logDebug fun(self: CommonEntity, format: string, ...: any)
---@field logInfo fun(self: CommonEntity, format: string, ...: any)
---@field logWarn fun(self: CommonEntity, format: string, ...: any)
---@field logError fun(self: CommonEntity, format: string, ...: any)
---@field entityCallback fun(self: CommonEntity, cb: EntityCallback, ...: any)
---@field getPos2D fun(self: CommonEntity): number, number


---@class CommonWorldActor: CommonEntity
---@field props WorldActorProps
---@field _actorType integer @ActorType枚举
---@diagnostic disable-next-line: assign-type-mismatch
CommonWorldActor = DefineClass("CommonWorldActor", CompatEnv.ComponentBaseEnv.ComponentBase)

function CommonWorldActor:GetDoubledPos2D()
    return CoordUtils.Offset2Doubled(self:getPos2D())
end

function CommonWorldActor:GetDoubledPos2DV()
    return Vector2.New(CoordUtils.Offset2Doubled(self:getPos2D()))
end

---@param actor CommonWorldActor
function CommonWorldActor:GetDistanceFrom(actor)
    return CoordUtils.GetDistance(self:GetDoubledPos2DV(), actor:GetDoubledPos2DV())
end

return {
    CommonWorldActor = CommonWorldActor,
}