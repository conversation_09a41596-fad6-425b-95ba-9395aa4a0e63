﻿xrequire("Framework.Async.Require")
---@type ComponentBaseEnv
local ComponentBase = xrequire(EZFPath .. ".Entities.Components.ComponentBase")


---@class AsyncComp: Entity, ComponentBase
---@field running table<AeId, AsyncExecutor>
AsyncComp = DefineClass("AsyncComp", ComponentBase.ComponentBase)

function AsyncComp:ctor()
    self.running = {}
end

---@param f fun(aeId: AeId):void
---@param aeId? AeId
function AsyncComp:genAsyncExecutor(f, aeId)
    assert(Async.AsyncExecutor.isMainThread(), "MUST run in main thread!")
    aeId = aeId or EZE.genUUID()
    local ae = AsyncExecutorMgr:create(aeId, function() f(aeId) end)
    self.running[ae.aeId] = ae
    ae:resume()
end

-- 发送serverRpc并等待返回
---@param ae AsyncExecutor
---@param mailbox LuaMailbox
---@param method string
function AsyncComp:awaitServerRpc(ae, mailbox, method, ...)
    assert(not Async.AsyncExecutor.isMainThread(), "do NOT run in main thread!")
    self:serverRpc(mailbox, method, ...)
    return ae:yield(nil, 30)
end

-- 发送serviceRpc并等待返回
---@param ae AsyncExecutor
---@param serviceName string
---@param method string
function AsyncComp:awaitServiceRpc(ae, serviceName, method, ...)
    assert(not Async.AsyncExecutor.isMainThread(), "do NOT run in main thread!")
    self:serviceRpc(serviceName, method, ...)
    return ae:yield(nil, 30)
end

---@param cmdProxy CmdProxy
---@param res any
function AsyncComp:asyncFeedback(cmdProxy, res)
    assert(Async.AsyncExecutor.isMainThread(), "MUST run in main thread!")
    if not self.running[cmdProxy.aeId] then
        return self:logError("async executor is not exist, aeId: %s", cmdProxy.aeId)
    end
    self.running[cmdProxy.aeId]:resume(nil, res)
end

-- for emmylua
---@class AsyncCompEnv
local AsyncCompEnv = {
    AsyncComp=AsyncComp,
}
return AsyncCompEnv
