---@type ComponentBaseEnv
local ComponentBase = xrequire(EZFPath .. ".Entities.Components.ComponentBase")
---@type NodeMgrEnv
local NodeMgr = xrequire(EZFPath .. ".Utils.NodeMgr")
---@type GlobalDataMgrEnv
local GlobalDataMgr = xrequire(EZFPath .. ".Utils.GlobalDataMgr")
---@type GlobalEntityMgrEnv
local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")


---@class GlobalDataAckHolder
---@field key string
---@param ensureContainers boolean
---@field pendingContainers table<ContainerId, boolean>
---@param finishContainers table<ContainerId, boolean>
---@field cb fun(GlobalDataAckHolder):void
---@field rpcBuffer table[]
GlobalDataAckHolder = DefineClass("GlobalDataAckHolder")

---@param key string
---@param ensureContainers boolean
---@param pendingContainers table<ContainerId, boolean>
---@param finishContainers table<ContainerId, boolean>
---@param cb fun(GlobalDataAckHolder):void
function GlobalDataAckHolder:ctor(key, ensureContainers, pendingContainers, finishContainers, cb)
    self.key = key
    self.ensureContainers = ensureContainers
    self.pendingContainers = pendingContainers
    self.finishContainers = finishContainers
    self.cb = cb
    self.rpcBuffer = {}
end


---@class BroadcastRes
---@field succ ContainerId[]
---@field failed ContainerId[]
---@field miss ContainerId[]
---@field allowed table<ContainerId, boolean>
---@field blocked ContainerId[]


-- 包含确认应答的广播组件
---@class GlobalDataAckComp: ComponentBase, Entity
---@field acks table<string, GlobalDataAckHolder>
---@field broadcastRes table<uid, BroadcastRes>
---@field recoverRpc nil | fun(eid:EntityId):void @有容灾需求的Service须重写；将在各个container调用，负责上传恢复数据给Service
GlobalDataAckComp = DefineClass("GlobalDataAckComp", ComponentBase.ComponentBase)

function GlobalDataAckComp:ctor()
    self.acks = {}
    self.broadcastRes = {}
    self.recoverKey = self.__cname .. ".recover"
end

---@param cid ContainerId
function GlobalDataAckComp:onDelContainer(cid)
    for key, hd in pairs(self.acks) do
        if hd.pendingContainers[cid] then
            self:onGlobalDataAck(key, cid)
        end
    end
end

-- 推送一个GlobalData并注册一个广播完成之后的回调
---@param key string @global data key
---@param val nil|boolean|number|string|table @global data value
---@param cb fun(GlobalDataAckHolder):void @global data broadcast done cb
function GlobalDataAckComp:pushGlobalDataWithAck(key, val, cb)
    if self.acks[key] then
        ErrorLog("%s %s GlobalDataAckComp.push %s is already pending now: %v", containerType, self.id, key, self.acks[key].pendingContainers)
        return false
    end
    GlobalDataMgr.instance:push(key, val, GlobalDataType.ACK, self.id, EZE.nodeUid())
    self.acks[key] = GlobalDataAckHolder.new(key, false, {}, {}, cb)
    InfoLog("%s GlobalDataAckComp.pushGlobalDataWithAck %s", containerType, key)
    return true
end

-- 确认广播发送前已有的Nodes和Containers
---@param key string @global data key
---@param nodes list<NodeId> @广播发送前已有的Nodes
function GlobalDataAckComp:ensureGlobalDataAckNodes(key)
    local hd = self.acks[key]
    if not hd then
        ErrorLog("%s %s GlobalDataAckComp.ensure %s is not exist", containerType, self.id, key)
        return
    end
    InfoLog("GlobalDataAckComp.pushed, key: %s", key)
    hd.ensureContainers = true
    for _, info in pairs(NodeMgr.instance.nodes) do
        for cid, _ in pairs(info.containers) do
            if not hd.finishContainers[cid] then
                hd.pendingContainers[cid] = true
            end
        end
    end
    self:tryFinish(key, hd)
end

-- 确认广播发送前已有的Containers
---@param key string @global data key
---@param cid ContainerId @已处理的node
function GlobalDataAckComp:onGlobalDataAck(key, cid)
    local hd = self.acks[key]
    if not hd then
        ErrorLog("%s %s GlobalDataAckComp.ack %s is not exist", containerType, self.id, key)
        return
    end
    hd.finishContainers[cid] = true
    hd.pendingContainers[cid] = nil
    self:tryFinish(key, hd)
end

-- 检查是否完成
function GlobalDataAckComp:tryFinish(key, hd)
    if hd.ensureContainers and not next(hd.pendingContainers) then
        -- 全部处理完成
        hd.cb(hd)
        self.acks[key] = nil
        InfoLog("GlobalDataAckComp.onGlobalDataAck done, key: %s", key)
    end
end

-- 开始恢复流程
function GlobalDataAckComp:startRecover()
    -- 如果已经注册，直接发起恢复广播
    local info = GlobalEntityMgr.instance:getGlobalEntityInfoById(self.__cname, self.id)
    if info then
        assert(not info.ready or GlobalEntityMgr.instance:isActive(self.__cname, self.id), string.format("%s is ready", self.__info_str))
        self:setRpcHook("startRpcBuffer")
        return self:pushGlobalDataWithAck(self.recoverKey, self.id, Functor("_finishRecover", self))
    end
end

-- 恢复成功
function GlobalDataAckComp:_finishRecover()
    self:finishRpcBuffer()
    self:finishRecover()
end

-- 恢复成功回调
function GlobalDataAckComp:finishRecover()
    error("NotImplementedError")
end

-- 恢复期间开启rpc缓存
---@param source EntityId
---@param rpc string
function GlobalDataAckComp:startRpcBuffer(source, rpcType, cbid, rpc, ...)
    local holder = self.acks[self.recoverKey]
    if holder then
        if EZE.hasRpcFlagValue(self.__cname, rpc, DefsConst.RPC_FLAG.INIT) then
            self[rpc](self, ...)
        elseif holder.finishContainers[EZE.entityIdToContainerId(source)] then
            holder.rpcBuffer[#holder.rpcBuffer + 1] = {rpc, ...}
            self:logInfo("push rpc buffer: %v", holder.rpcBuffer[#holder.rpcBuffer])
        else
            self:logInfo("drop rpc: %v", {rpc, ...})
        end
    else
        self:setRpcHook("")
        self:logError("recover(%s) is not exist and recv rpc(%s)", self.recoverKey, rpc)
    end
end

-- 恢复成功后rpc缓存的处理
function GlobalDataAckComp:finishRpcBuffer()
    self:setRpcHook("")
    local holder = self.acks[self.recoverKey]
    if holder then
        self:logWarn("exec rpc in buffer(%s)", #holder.rpcBuffer)
        for _, rpc in ipairs(holder.rpcBuffer) do
            self[rpc[1]](self, unpack(rpc, 2, table.maxn(rpc)))
        end
    else
        self:logError("recover(%s) is not exist", self.recoverKey)
    end
end

---@param uid AnyId
---@param allowList int[]
---@param method string
---@param args any[]
---@param cb fun(GlobalDataAckHolder): void
---@param ignoreLogRes? bool
function GlobalDataAckComp:broadcast(uid, allowList, method, args, cb, ignoreLogRes)
    local allowed = nil
    if allowList and #allowList then
        allowed = {}
        for _, cid in ipairs(allowList) do
            allowed[cid] = true
        end
    end

    if self:pushGlobalDataWithAck(
        "broadcast", 
        EZE.msgpackPack({method=method, args=args, mailboxStr=self:getMailboxStr(), callback="broadcastCallback", uid=uid, allowed=allowed, ignoreLogRes=ignoreLogRes}),
        function (hd)
            cb(hd)
            self.broadcastRes[uid] = nil
        end) then
        self.broadcastRes[uid] = {succ={}, failed={}, miss={}, allowed=allowed, blocked={}}
    end
end

---@param cid ContainerId
---@param uid AnyId
---@param succ boolean
---@param res any
function GlobalDataAckComp:broadcastCallback(cid, uid, succ, res)
    local cache = self.broadcastRes[uid]
    if not cache then
        return
    end
    if succ then
        cache.succ[cid] = res or true
    else
        cache.failed[cid] = res or false
    end
end

---@param uid AnyId
---@param hd GlobalDataAckHolder
function GlobalDataAckComp:genBroadcastRes(uid, hd)
    ---@type BroadcastRes
    local cache = self.broadcastRes[uid]
    if not cache then
        return string.format("miss broadcast(%s) res", uid)
    end

    local succ, failed = {}, {}
    for cid, res in pairs(cache.succ) do
        hd.finishContainers[cid] = nil
        if not cache.allowed or cache.allowed[cid] then
            -- 返回为json格式，key必须为string
            succ[string.format("%u", cid)] = res
        else
            table.insert(cache.blocked, cid)
        end
    end
    for cid, res in pairs(cache.failed) do
        hd.finishContainers[cid] = nil
        if not cache.allowed or cache.allowed[cid] then
            failed[string.format("%u", cid)] = res
        else
            table.insert(cache.blocked, cid)
        end
    end
    for cid, _ in pairs(hd.finishContainers) do
        table.insert(cache.miss, cid)
    end
    for cid, _ in pairs(hd.pendingContainers) do
        if not cache.allowed or cache.allowed[cid] then
            table.insert(cache.miss, cid)
        else
            table.insert(cache.blocked, cid)
        end
    end
    cache.allowed = nil
    cache.succ = succ
    cache.failed = failed
    return cache
end

-- for emmylua
---@class GlobalDataAckCompEnv
local GlobalDataAckCompEnv = {GlobalDataAckComp=GlobalDataAckComp}
return GlobalDataAckCompEnv
