﻿GMConfigs = {
    {
        tab = "战斗",
        func = "gmEnableBattleTitle",
        name = "开启战斗Title",
        params = {
            {
                name = "是否显示",
                type = "boolean",
                default = true,
            }
        }
    },
    {
        func = "gmAddCompletedHeroesFromTestBattleData",
        name = "读取Debug.TestBattleData添加记忆卡",
        params = {}
    },
    {
        tab = "PVP",
        func = "gmAutoAPFormation",
        isServerFunc = true,
        name = "自动设置防守阵容",
        params = {}
    },
    {
        func = "gmClearAPTargetHistory",
        isServerFunc = true,
        name = "清除刷新历史",
        params = {}
    },
    {
        tab = '指令组二',
        name = 'Lua类型参数测试',
        inputPrompt = '这是输入提示语句',
        func = 'gmTest',
        isServerFunc = true,
        params = {
            {
                name = 'command',
                type = 'lua',
                default = "this is a parameter of type Lua",
            }
        }
    },
    {
        name = 'Lua类型参数测试',
        inputPrompt = 'maxHealth:血量 defense:防御 speed:速度 attack:攻击',
        func = 'gmTest',
        isServerFunc = true,
        params = {
            {
                name = 'command',
                type = 'lua',
                default = "{ maxHealth = 3000,defense = 200 }",
            }
        }
    },
    {
        tab = "玩家",
        func = "gmOfflineNow",
        isServerFunc = true,
        name = "立刻下线",
        params = {
        }
    },
    {
        func = "GmSetTime",
        isServerFunc = true,
        name = "设置时间",
        params = {
            {
                name = "年",
                type = "number",
                default = 2025,
            },
            {
                name = "月",
                type = "number",
                default = 1,
            },
            {
                name = "日",
                type = "number",
                default = 1,
            },
            {
                name = "时",
                type = "number",
                default = 0,
            },
            {
                name = "分",
                type = "number",
                default = 0,
            },
            {
                name = "秒",
                type = "number",
                default = 0,
            }
        }
    },
    {
        tab = "背包",
        func = "gmSetCurrency",
        isServerFunc = true,
        name = "设置货币数量",
        params = {
            {
                name = "货币ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "数量",
                type = "number",
                default = 100000000,
            }
        }
    },
    {
        func = "gmAddItem",
        isServerFunc = true,
        name = "添加道具",
        params = {
            {
                name = "道具ID",
                type = "number",
                default = -1,
            },
            {
                name = "数量",
                type = "number",
                default = 3,
            }
        }
    },
    {
        func = "gmConsumeItem",
        isServerFunc = true,
        name = "消耗道具",
        params = {
            {
                name = "道具ID",
                type = "number",
                default = -1,
            },
            {
                name = "数量",
                type = "number",
                default = 1,
            }
        }
    },
    {
        tab = "养成",
        func = "gmOneClickGrowth",
        isServerFunc = true,
        name = "一键养成",
        params = {}
    },
    {
        func = "gmAddHero",
        isServerFunc = true,
        name = "获取武将",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "数量",
                type = "number",
                default = 1,
            }
        }
    },
    {
        func = "gmSetHeroLevel",
        isServerFunc = true,
        name = "设置武将等级",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级",
                type = "number",
                default = 50,
            },
            {
                name = "经验(-1不修改)",
                type = "number",
                default = -1,
            }
        }
    },
    {
        func = "gmSetHeroStars",
        isServerFunc = true,
        name = "设置武将红度",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "红度",
                type = "number",
                default = 5,
            }
        }
    },
    {
        func = "gmSetHeroStamina",
        isServerFunc = true,
        name = "设置武将体力",
        params = {
            {
                name = "武将ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "体力",
                type = "number",
                default = 1,
            }
        }
    },
    {
        func = "gmAddTactic",
        isServerFunc = true,
        name = "获取战法",
        params = {
            {
                name = "战法ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "数量",
                type = "number",
                default = 1,
            }
        }
    },
    {
        func = "gmSetTacticStars",
        isServerFunc = true,
        name = "设置战法红度",
        params = {
            {
                name = "战法ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "红度",
                type = "number",
                default = 5,
            }
        }
    },
    {
        func = "gmSetTacticLevel",
        isServerFunc = true,
        name = "设置战法等级",
        params = {
            {
                name = "战法ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级",
                type = "number",
                default = 10,
            }
        }
    },
    {
        func = "gmSetTacticResetCnt",
        isServerFunc = true,
        name = "设置战法可用重置次数",
        params = {
            {
                name = "次数",
                type = "number",
                default = 10,
            }
        }
    },
    {
        tab = "城建",
        func = "gmUpgradeHomeBuilding",
        isServerFunc = true,
        name = "升级家园建筑",
        params = {
            {
                name = "建筑ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级（-1为最大等级）",
                type = "number",
                default = -1,
            }
        }
    },
    {
        func = "gmUpgradeHomeTech",
        isServerFunc = true,
        name = "升级家园科技",
        params = {
            {
                name = "科技ID(0为全部)",
                type = "number",
                default = 0,
            },
            {
                name = "等级（-1为最大等级）",
                type = "number",
                default = -1,
            }
        }
    },
    {
        tab = "职业策牌",
        name = "添加职业策牌点数",
        func = "gmAddCareerTalentPoint",
        isServerFunc = true,
        params = {
            {
                name = "点数",
                type = "number",
                default = 13,
            }
        }
    },
    {
        name = "解锁所有天赋树节点",
        func = "gmUnlockAllStrategyTalent",
        isServerFunc = true,
        params = {}
    },
    {
        name = "清理策牌使用次数",
        func = "gmClearStrategyUseCnt",
        isServerFunc = true,
        params = {}
    },
    {
        name = "添加策牌",
        func = "gmAddStrategyCard",
        isServerFunc = true,
        params = {
            {
                name = "策牌ID",
                type = "number",
                default = -1,
            },
            {
                name = "数量",
                type = "number",
                default = 3,
            }
        }
    },
    {
        name = "添加策点",
        func = "gmAddStrategyPoint",
        isServerFunc = true,
        params = {
            {
                name = "策点数量",
                type = "number",
                default = 100,
            }
        }
    },
    {
        tab = "联盟",
        func = "gmCreateAlly",
        isServerFunc = true,
        name = "创建联盟",
        params = {
            {
                name = "联盟名称",
                type = "string",
                default = "allyName",
            }
        }
    },
    {
        func = "gmJoinAlly",
        isServerFunc = true,
        name = "加入联盟",
        params = {
            {
                name = "联盟名称",
                type = "string",
                default = "allyName",
            }
        }
    },
    {
        func = "gmLeaveAlly",
        isServerFunc = true,
        name = "离开联盟",
        params = {}
    },
    {
        tab = '部队基础',
        name = '设置部队移速buff',
        func = 'gmSetArmyMoveBuff',
        isServerFunc = true,
        params = {
            {
                name = "ratio",
                type = "number",
                default = 100,
            },
        }
    },
    {
        name = "设置部队行为等待时间",
        func = "GmSetWaitTime",
        isServerFunc = true,
        params = {
            {
                name = "等待时间",
                type = "number",
                default = 10,
            }
        }
    },
    {
        tab = '系统',
        name = 'ReloadLua',
        func = 'gmReloadLua',
        params = {
        }
    },
    {
        name = '显示帧率',
        func = 'gmShowFps',
        params = {
        }
    },
    {
        name = '修改开关',
        func = 'gmSetSwitch',
        isServerFunc = true,
        params = {
            {
                name = "开关名",
                type = "string",
                default = "",
            },
            {
                name = "开关值",
                type = "boolean",
                default = true,
            }
        }
    },
}

DeclareAndSetGlobal("GMConfigs", GMConfigs)
