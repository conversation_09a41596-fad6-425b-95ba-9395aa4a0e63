local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local CoordUtils = xrequire("Common.Utils.CoordUtils")
local TableEnumUtilsEnv = xrequire("Common.Utils.TableEnumUtils")
local EntityUtilsEnv = xrequire("Common.Utils.EntityUtils")

---@class (partial) WorldBuildingType : WorldActor
---@field props WorldBuildingCompProps|WorldActorProps
---@class WorldBuildingComp: ComponentBase
WorldBuildingComp = DefineClass("WorldBuildingComp", ComponentBaseEnv.ComponentBase)
-- 要求实体实现的方法
-- 依赖其他组件

function WorldBuildingComp:ctor()
    self._processBuildingTimer = nil  -- 建筑建造升级拆除的定时器
    self._occupyAfterBuildingRelease = nil  -- 建筑被摧毁/升级释放时需要设置的归属信息
    -- 建筑的占地和阻挡信息
    self._buildingRange = nil  ---@type table? 建筑的范围
    self._isNaviObstacle = false
    -- 读取建筑配置
    self.worldBuildingConfig = nil  ---@type slg.TableBuilding
    self.nextLevelBuildingConfig = nil  ---@type slg.TableBuilding?
    self:refreshWorldBuildingConfig()
end

function WorldBuildingComp:onEnterSpace()
    self:checkOccupationAndObstacle()
    self:checkProcessBuilding()
end

function WorldBuildingComp:OnAllyChange()
    self:checkOccupationAndObstacle(true)
end

function WorldBuildingComp:OnNameChange()
    self:checkOccupationAndObstacle()
end

function WorldBuildingComp:onLeaveSpace()
    self:clearOccupationAndObstacle()
end

function WorldBuildingComp:IsObstacle()
    return self._isNaviObstacle
end

function WorldBuildingComp:OnActorDestroy()
    self:forceOperatorLeave()
    if self._actorType ~= TableConst.enums.ActorType.MAIN_CITY then
        -- 除了驻城，其他的建筑在销毁时触发调动or集结部队回城
        self:forceArmyInBaseBack()
    end
end

function WorldBuildingComp:IsInBuilding()
    return self.props.buildStatus == TableConst.enums.BuildStatus.Building
end

function WorldBuildingComp:GetBuildingType()
    return self.props.slgTp
end

function WorldBuildingComp:refreshWorldBuildingConfig()
    local configBefore = self.worldBuildingConfig
    self.worldBuildingConfig = ProcessedTableEnv.SLG_BUILDING.allBuilding[self._actorType][self.props.subId]   ---@type slg.TableBuilding
    self.nextLevelBuildingConfig = nil
    if self.worldBuildingConfig and self.worldBuildingConfig.upgrade_next then
        self.nextLevelBuildingConfig = ProcessedTableEnv.SLG_BUILDING.allBuilding[self._actorType][self.worldBuildingConfig.upgrade_next]
    end
    ---@diagnostic disable-next-line: unnecessary-assert
    assert(self.worldBuildingConfig, string.format("No building config for type: %s %s", self._actorType, self.props.subId))
    -- 更新建筑的slgTp
    self.props.slgTp = self.worldBuildingConfig.map_element_id or self.props.staticMapElementId
    if self.space and self.space:IsMapReady() then
        self:checkOccupationAndObstacle()
    end
    if configBefore and not configBefore.is_vehicle and self.worldBuildingConfig.is_vehicle then
        -- 建筑变为载具，需要自动占领地块
        if self.props.gid then
            local x, y = self:GetDoubledPos2D()
            self.space:Occupy(x, y, self:GetRelationProps(), self.props.name, {})
        end
        self:fireEntityEvent("OnOutpostDisabled")
    end
end

---@return string
function WorldBuildingComp:GetBuildingName()
    if self._actorType == TableConst.enums.ActorType.MAIN_CITY then
        return self.props.name
    end
    return self.props.alias or self.worldBuildingConfig.name
end

function WorldBuildingComp:forceArmyInBaseBack()
    if table.isnilorempty(self.props.contents) then
        return
    end
    for _, uid in ipairs(self.props.contents) do
        local actor = self:getActor(uid)
        if actor and actor._actorType == TableConst.enums.ActorType.ARMY and actor.props.inCon[self.props.uid] == TableConst.enums.InConType.InBase then
            local homeType = TableConst.enums.ArmyHomeType.BaseHome
            if actor.props.deployHome and actor.props.siegeHome then
                homeType = TableConst.enums.ArmyHomeType.DeployHome
            end
            actor:BackToHome(homeType)
        end
    end
end

--region 地块占用和阻挡

function WorldBuildingComp:checkOccupationAndObstacle(forceRefreshObstacle)
    local rangeBefore = self._buildingRange
    self._buildingRange = EntityUtilsEnv.GetBuildingRangeByMapElementId(self.props.slgTp, self:GetDoubledPos2DV())
    --[[检查占领地块
    如果需要占领，就直接占领，不需要考虑先释放，只存在新占领、更新归属和无变化三种情况
    如果无法占领，则检查之前是否占领过，若占领过则释放
    --]]
    if not table.isempty(self._buildingRange) then
        self:SetGridOccupyInfo()
    else
        local occupyBefore = self.space:GetBuildingInGrid(self:GetDoubledPos2D())
        if occupyBefore and occupyBefore == self and rangeBefore and not table.isempty(rangeBefore) then
            self:UnsetGridOccupyInfo(rangeBefore)
        end
    end
    --[[检查阻挡
    建筑范围不会变化，因此存在两种情况
    建筑在有无阻挡间变化
    外界确认关系变化，需要刷新建筑的阻挡信息，需要先删除（如果存在）再设置阻挡（如果存在）
    ]]
    if not self.worldBuildingConfig.is_obstacle or table.isempty(self._buildingRange) or forceRefreshObstacle then
        if self._isNaviObstacle then
            self._isNaviObstacle = false
            self:serviceRpc("NaviService", "RemoveObstacle", self.id)
        end
    end
    if (self.worldBuildingConfig.is_obstacle and not table.isempty(self._buildingRange)) or forceRefreshObstacle then
        self._isNaviObstacle = true
        self:serviceRpc("NaviService", "SetObstacle", self._buildingRange, self.id, self.props.gid, self.props.allyId)
    end
end

function WorldBuildingComp:clearOccupationAndObstacle()
    -- 清理占领地块
    local rangeBefore = self._buildingRange
    local occupyBefore = self.space:GetBuildingInGrid(self:GetDoubledPos2D())
    if occupyBefore and occupyBefore == self and rangeBefore and not table.isempty(rangeBefore) then
        self:UnsetGridOccupyInfo(rangeBefore)
    end
    -- 如果标记了建筑销毁信息，就将建筑中心给销毁方占领
    if self._occupyAfterBuildingRelease and self._occupyAfterBuildingRelease.gid ~= 0 then
        local x, y = self:GetDoubledPos2D()
        self.space:Occupy(x, y, self._occupyAfterBuildingRelease, self._occupyAfterBuildingRelease.name, {})
    end
    -- 清理阻挡
    if self._isNaviObstacle then
        self._isNaviObstacle = false
        self:serviceRpc("NaviService", "RemoveObstacle", self.id)
    end
end

function WorldBuildingComp:SetGridOccupyInfo()
    local buildingRange = EntityUtilsEnv.GetBuildingRangeByMapElementId(self.props.slgTp, self:GetDoubledPos2DV())
    local tp = self:GetBuildingType()
    assert(tp)
    self.space:BuildingOccupy(buildingRange, {
        gid = self.props.gid,
        allyId = self.props.allyId,
        clanId = self.props.clanId,
        --- TODO(qun): 归属玩家nickname，还是建筑的导表名字？
        name = self.props.name,
        tp = tp,
        buildingUid = self.props.uid,
    })
end

function WorldBuildingComp:UnsetGridOccupyInfo(rangeInfo)
    self.space:BuildingDemolish(rangeInfo)
end

--endregion

--region 建造&升级

function WorldBuildingComp:checkProcessBuilding()
    -- 检查当前进行中的过程
    if self.props.buildStatus ~= TableConst.enums.BuildStatus.Idle then
        assert(self.props.processTimeout)
        -- 已经在进行中的过程
        if self._processBuildingTimer then
            return
        end
        -- 需要启动定时的过程
        local delay = math.max(0, self.props.processTimeout - ServerTimeEnv.GetServerNow())
        self._processBuildingTimer = self:addTimer(delay, 0, function()
            self:onProcessBuildingTimeout()
        end)
        return
    end
    -- 检查自动开始的过程
    if self.worldBuildingConfig.auto_build then
        local buildStatus = TableConst.enums.BuildStatus.Upgrading
        if self.worldBuildingConfig.direct_build then
            buildStatus = TableConst.enums.BuildStatus.Building
        end
        self:setBuildStatus(buildStatus)
    end
end

function WorldBuildingComp:DestroyBuilding(avatarGid, allyId, canForce)
    if self.worldBuildingConfig.ally_build then
        if allyId ~= self.props.allyId or not canForce then
            self:logError("DestroyBuilding failed, allyId(%s %s) %s", allyId, self.props.allyId, canForce)
            return
        end
    else
        if avatarGid ~= self.props.gid then
            self:logError("DestroyBuilding failed, gid not match %s %s", avatarGid, self.props.gid)
            return
        end
    end
    if self.props.buildStatus ~= TableConst.enums.BuildStatus.Idle then
        self:logError("DestroyBuilding failed, building is in process %s", TableEnumUtilsEnv.GetEnumItemName("BuildStatus", self.props.buildStatus))
        return
    end
    if self.worldBuildingConfig.dismantle_cost_time <= 0 then
        self:logError("DestroyBuilding failed, dismantle_cost_time is zero %s", self.props.uid)
        return
    end
    self:setBuildStatus(TableConst.enums.BuildStatus.Dismantling)
end

function WorldBuildingComp:CancelDestroyBuilding(avatarGid, allyId, canForce)
    if self.worldBuildingConfig.ally_build then
        if allyId ~= self.props.allyId or not canForce then
            self:logError("CancelDestroyBuilding failed, allyId(%s %s) %s", allyId, self.props.allyId, canForce)
            return
        end
    else
        if avatarGid ~= self.props.gid then
            self:logError("CancelDestroyBuilding failed, gid not match %s %s", avatarGid, self.props.gid)
            return
        end
    end
    if self.props.buildStatus ~= TableConst.enums.BuildStatus.Dismantling then
        self:logError("CancelDestroyBuilding failed, building is not in dismantling %s", TableEnumUtilsEnv.GetEnumItemName("BuildStatus", self.props.buildStatus))
        return
    end
    -- 只有拆除过程可以取消
    if self._processBuildingTimer then
        self:delTimer(self._processBuildingTimer)
        self._processBuildingTimer = nil
    end
    self:setBuildStatus(TableConst.enums.BuildStatus.Idle)
end

function WorldBuildingComp:setBuildStatus(buildStatus)
    self:logInfo("set BuildStatus: %s -> %s", TableEnumUtilsEnv.GetEnumItemName("BuildStatus", self.props.buildStatus), TableEnumUtilsEnv.GetEnumItemName("BuildStatus", buildStatus))
    self.props.processTimeout = self:getBuildingProcessTimeout(buildStatus)
    self.props.buildStatus = buildStatus
    self:checkProcessBuilding()
    -- 同步到Ally和Avatar视图
    if self.HasAllyOwningViewComp then
        self:DelaySyncAllyView()
    end
    local syncViews = {
        buildStatus = self.props.buildStatus,
    }
    local syncUnset = {}
    if self.props.processTimeout then
        syncViews["processTimeout"] = self.props.processTimeout
    else
        table.insert(syncUnset, "processTimeout")
    end
    if self.HasAvatarOwningComp then
        self:syncActorView(syncViews, syncUnset)
    end
end

---@return number? 过程结束的时间戳，nil表示没有过程
function WorldBuildingComp:getBuildingProcessTimeout(buildStatus)
    if buildStatus == TableConst.enums.BuildStatus.Idle then
        return nil
    elseif buildStatus == TableConst.enums.BuildStatus.Building or buildStatus == TableConst.enums.BuildStatus.Upgrading then
        assert(self.nextLevelBuildingConfig)
        return ServerTimeEnv.GetServerNow() + self.worldBuildingConfig.build_cost_time
    elseif buildStatus == TableConst.enums.BuildStatus.Dismantling then
        return ServerTimeEnv.GetServerNow() + self.worldBuildingConfig.dismantle_cost_time
    end
end

function WorldBuildingComp:onProcessBuildingTimeout()
    if self._processBuildingTimer then
        self:delTimer(self._processBuildingTimer)
        self._processBuildingTimer = nil
    end
    local processStatus = self.props.buildStatus
    if processStatus == TableConst.enums.BuildStatus.Building or processStatus == TableConst.enums.BuildStatus.Upgrading then
        -- 处理建造完成、升级完成
        assert(self.worldBuildingConfig.upgrade_next)
        self.props.subId = self.worldBuildingConfig.upgrade_next
        self:refreshWorldBuildingConfig()
        self:setBuildStatus(TableConst.enums.BuildStatus.Idle)
        self:fireEntityEvent("OnProcessBuildingEnd")
    elseif processStatus == TableConst.enums.BuildStatus.Dismantling then
        -- 处理拆除完成
        self._occupyAfterBuildingRelease = self:GetRelationProps()
        self._occupyAfterBuildingRelease.name = self.props.name
        self:DestroyBy(self.id, "DestroyBuilding", true)
    end
end

function WorldBuildingComp:OnProcessBuildingEnd()
    self:checkProcessBuilding()
end

function WorldBuildingComp:onSetTime()
    if self._processBuildingTimer then
        self:delTimer(self._processBuildingTimer)
        self._processBuildingTimer = nil
        local delay = math.max(0, self.props.processTimeout - ServerTimeEnv.GetServerNow())
        self._processBuildingTimer = self:addTimer(delay, 0, function()
            self:onProcessBuildingTimeout()
        end)
    end
end

--endregion

--region 载具和建筑管理

function WorldBuildingComp:addOperator(actor)
    if self:getContentByType(TableConst.enums.InConType.InVehicle) then
        self:logInfo("addOperator: vehicle already has operator")
        return false
    end
    if not self:dealActorEnter(actor, TableConst.enums.InConType.InVehicle) then
        self:logInfo("addOperator: dealActorEnter failed")
        return false
    end
    self:logInfo("addOperator: actor %s entered vehicle %s", actor.props.uid, self.props.uid)
    actor:refreshVehicleStatus()
    return true
end

function WorldBuildingComp:onContentEnter(actor, inConType)
    if inConType == TableConst.enums.InConType.InVehicle then
        self.props.operator = actor.id
        actor.props.inVehicle = self.id
        if self.HasAllyOwningViewComp then
            self:DelaySyncAllyView()
        end
    end
end

function WorldBuildingComp:onContentLeave(actor, inConType)
    if inConType == TableConst.enums.InConType.InVehicle and actor.id == self.props.operator then
        self.props.operator = nil
        actor.props.inVehicle = nil
        if self.HasAllyOwningViewComp then
            self:DelaySyncAllyView()
        end
    end
end

function WorldBuildingComp:LetOperatorLeave(avatarGid, allyId, canForce)
    local valid = false
    if avatarGid == self.props.gid then
        valid = true
    elseif self.props.allyId == allyId and canForce then
        valid = true
    end
    if not valid then
        self:logError("LetOperatorLeave failed gid(%s %s) allyId(%s %s) canForce(%s)", avatarGid, self.props.gid, allyId, self.props.allyId, canForce)
        return
    end
    self:forceOperatorLeave()
end

function WorldBuildingComp:forceOperatorLeave()
    if self.props.operator then
        local actor = self:getActorByEid(self.props.operator)
        if actor then
            -- 离开载具
            self:dealActorLeave(actor)
            actor:refreshVehicleStatus()
        end
    end
end

--endregion

function WorldBuildingComp:OnOutpostDurabilityZero(attacker)
    if self.worldBuildingConfig.dura_zero_destroy then
        self._occupyAfterBuildingRelease = attacker:GetRelationProps()
        self._occupyAfterBuildingRelease.name = attacker.props.name
        self:DelayDestroyBy(attacker.id, "OutpostDurabilityZero", true)
        return
    elseif self.worldBuildingConfig.dura_zero_ally_occupy then
        if attacker.props.allyId == 0 then
            self:logError("OnOutpostDurabilityZero failed, attacker allyId is 0")
            self:HandleDurabilityZeroFailed()
            return
        end
        self:OnSyncAlly(attacker.props.allyId)
        return
    end
end

function WorldBuildingComp:GetNaviToArgs(rpcCb)
    if not self._isNaviObstacle then
        self:callbackRpc(rpcCb, self:GetDoubledPos2DV(), {})
        return
    end
    self:callbackRpc(rpcCb, self:GetDoubledPos2DV(), self._buildingRange)
end

function WorldBuildingComp:DelayCheckAllBattle()
    assert(self.space)
    if not self._buildingRange then
        return
    end
    for _, pos in ipairs(self._buildingRange) do
        local packedPos = CoordUtils.PackPos(pos.x, pos.y)
        local gridNode = self.space:getGridNode(packedPos)
        gridNode.matchArmy:DelayCheckAllBattle()
    end
end

function WorldBuildingComp:gmSetDurability(durability)
    self.props.durability = durability
end

function WorldBuildingComp:GmOccupyBuilding(relation)
    if self.worldBuildingConfig.ally_build then
        self:OnSyncAlly(relation.allyId)
    end
end
