{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode", "Field": {"event1": {"Type": "number", "Value": "13"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "1": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "HeroIds"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "True"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "4": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetIds": [], "buffId": {"Type": "number", "Value": "10500201"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "5": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetIds": [], "buffId": {"Type": "number", "Value": "10500202"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "6": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "False"}, "tacticId": {"Type": "number", "Value": "0"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "True"}, "armyTypeHeroId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "armyTypes": [{"Type": "number", "Value": "4"}]}}}, "Links": {"0": {"FormationTacticsArmyTypeNode": ["6.prev"]}, "1": {"next": ["4.prev", "5.prev"]}, "6": {"next": ["1.prev"]}}, "DataFlows": {"1": {"targetIds": ["4.targetIds", "5.targetIds"]}}}