---@type GateEntityEnv
local GateEntity = xrequire(EZFPath .. ".Entities.GateEntity")
---@type NodeMgrEnv
local NodeMgr = xrequire(EZFPath .. ".Utils.NodeMgr")
---@type GlobalEntityMgrEnv
local GlobalEntityMgr = xrequire(EZFPath .. ".Utils.GlobalEntityMgr")
---@type GlobalDataMgrEnv
local GlobalDataMgr = xrequire(EZFPath .. ".Utils.GlobalDataMgr")
---@type DBUtilsEnv
local DBUtils = xrequire(EZFPath .. ".Utils.DBUtils")
---@type ConfigEnv
local Config = xrequire(EZFPath .. ".Utils.Config")
---@type FileEnv
local File = xrequire(EZFPath .. ".Utils.File")
---@type TimeEnv
local Time = xrequire(EZFPath .. ".Utils.Time")
---@type CmdServiceEnv
local CmdService = xrequire(EZFPath .. ".Services.CmdService")
---@type CmdUtilsEnv
local CmdUtils = xrequire(EZFPath .. ".Utils.CmdUtils")
---@type FlagsEnv
local Flags = xrequire(EZFPath .. ".Utils.Flags")


---@class GlobalCallback
GlobalCallback = DefineClass("GlobalCallback")
GlobalCallback.loginEntity = LuaNodeConfig.getConfig("container.login_entity")

function GlobalCallback:clientLogin(clientRoute)
    InfoLog("client login: %s", GlobalCallback.loginEntity)
    local ent = EntityManager.createEntity(GlobalCallback.loginEntity, 0, {})
    ent:connectClient(clientRoute)
    ent:fireEntityEvent("onGetClient")
end

---获得可迁移对象的代理地址
---@param eid EntityId
---@return EntityId
function GlobalCallback:getMigrateProxy(eid)
    local info = GlobalEntityMgr.instance:getGlobalEntityInfo("MigrateService", eid)
    return info and info.mailbox:getEntityId() or 0
end

--region GlobalData回调

-- 更新全局数据
-- 初始化container时的执行时序是onContainerInited->onPushedGlobalData->onContainerReady
-- 在onContainerReady之前调用的都是历史数据，历史数据只保证最终一致性且不保序
---@param key string @数据的key
---@param bin string @打包好的二进制数据
function GlobalCallback:onPushedGlobalData(key, bin)
    DebugLog("[%s] onPushedGlobalData %s", containerType, key)
    GlobalDataMgr.instance:onPushed(key, bin)
end

-- 删除全局数据
---@param key string @数据的key
function GlobalCallback:onPoppedGlobalData(key)
    DebugLog("[%s] onPoppedGlobalData %s", containerType, key)
    GlobalDataMgr.instance:onPopped(key)
end

-- 注册更新回调；已有数据时，可以根据需求选择是否立刻进行一次回调
function GlobalCallback:ensureGlobalDataCallback()
    -- 需要处理的广播等
    -- ...
    GlobalDataMgr.instance:registerCallback("broadcast", "runbroadcast", function (...)
        Functor("broadcast", self)(...)
    end)
end

--endregion GlobalData回调

--region 集群相关回调

function GlobalCallback:onNodeRegistered(nodeId, uid, containers)
    DebugLog("[%s] onNodeRegistered %s %v", containerType, nodeId, containers)
    NodeMgr.instance:onAdd(nodeId, uid, containers)
end

function GlobalCallback:onNodeRemoved(nodeId)
    DebugLog("[%s] onNodeRemoved %s", containerType, nodeId)
    NodeMgr.instance:onDel(nodeId)
end

function GlobalCallback:onOtherContainerReady(cid, conf)
    DebugLog("[%s] onOtherContainerReady %s", containerType, cid)
    NodeMgr.instance:onAddContainer(cid, conf)
end

function GlobalCallback:onGlobalEntityRegistered(name, mailbox_str, ready, instanceData)
    local mailbox = LuaMailbox.createFromString(mailbox_str)
    local instanceData = EZE.msgpackUnpack(instanceData)
    DebugLog("[%s] onGlobalEntityRegistered %s %s %s %v", containerType, name, mailbox:getEntityId(), ready, instanceData)
    GlobalEntityMgr.instance:onRegistered(name, mailbox, ready, instanceData)
end

function GlobalCallback:onGlobalEntityRemoved(name, mailbox_str)
    local mailbox = LuaMailbox.createFromString(mailbox_str)
    DebugLog("[%s] onGlobalEntityRemoved %s %s", containerType, name, mailbox:getEntityId())
    GlobalEntityMgr.instance:onRemoved(name, mailbox)
end

function GlobalCallback:onGlobalEntityReady(name, mailbox_str)
    DebugLog("[%s] onGlobalEntityReady %s", containerType, name)
    local mailbox = LuaMailbox.createFromString(mailbox_str)
    GlobalEntityMgr.instance:onReady(name, mailbox)
end

-- 第一次连接会创建container；后面重连才有回调
function GlobalCallback:onAttachCluster()
    DebugLog("onAttachCluster %s", containerType)
end

function GlobalCallback:onDetachCluster()
    DebugLog("onDetachCluster %s", containerType)
end

--endregion 集群相关回调

--region 开服相关回调

---创建container的回调
---@param containerId ContainerId
---@param containerType string
---@param isBootstrapContainer boolean
---@param recover boolean
function GlobalCallback:onContainerInited(containerId, containerType, isBootstrapContainer, recover)
    -- set global variable
    EZGlobal.BootstrapContainer = isBootstrapContainer
    EZGlobal.Recover = recover
    ---@type SEntity
    EZGlobal.Entity = EntityManager.createEntity("Entity", 0, {})

    -- start telnet server
    local telnetConf = LuaNodeConfig.getContainerConfig(containerType, "telnet_server")
    local TelnetServer = xrequire(EZFPath .. ".Utils.TelnetServer")
    local telnetServer = TelnetServer.TelnetServer:new()
    telnetServer:start(telnetConf.host, telnetConf.port or 0)

    -- set container info to watcher
    EZE.setWatcher({containerType=containerType, telnetPort=telnetServer:port(), node=EZE.containerIdToNodeId(containerId), sidecar=LuaNodeConfig.getConfig("sidecar")}, "container")

    -- register callbacks
    GlobalEntityMgr.instance:registerMeetRequirementsCb("DBMgr", "GlobalCallback", function (info)
        DBUtils.chooseDBMgr()
        GlobalEntityMgr.instance:registerAddCb("DBMgr", "GlobalCallback", function (info)
            DBUtils.chooseDBMgr()
            return false
        end)
        return true
    end)
    GlobalEntityMgr.instance:registerRemovedCb("DBMgr", "GlobalCallback", function (info)
        if info.mailbox:getEntityId() == EntityManager.dbmgr then
            DBUtils.chooseDBMgr()
        end
        return false
    end)
    GlobalEntityMgr.instance:registerMeetRequirementsCb("MigrateService", "GlobalCallback", function (info)
        -- 可迁移对象的统一注册
        EZE.enableMigrateProxy()
        return true
    end)

    self:prepareCreateService()
    local containerTags = LuaNodeConfig.getContainerConfig(containerType, "tags")
    for i, tag in ipairs(containerTags) do
        local svcName = tag:match("svc:([%S]+)")
        if tag == "Gate" then
            local gate = EntityManager.createEntity("GateEntity", 0, {})
            assert(gate)
        elseif tag == "DBMgr" then
            GlobalEntityMgr.instance:create("DBMgr", 0)
        elseif svcName then
            -- service
            if EZE.hasEntityPropFlag(svcName, "", Flags.PERSISTENT) then
                InfoLog("service(%s) create from db", svcName)
                GlobalEntityMgr.instance:create(svcName, 0, {}, nil, svcName)
            else
                GlobalEntityMgr.instance:create(svcName, 0)
            end
        end
    end
end

---处理一些在创建service前需要初始化的内容
function GlobalCallback:prepareCreateService()
end

---初始化container的回调
---@param containerId ContainerId
---@param containerType string
---@param isBootstrapContainer boolean
---@param recover boolean
function GlobalCallback:onContainerReady(containerId, containerType, isBootstrapContainer, recover)
    EZGlobal.ContainerReady = true
    NodeMgr.instance:onAddContainer(containerId, Config.getContainerConfig("global"))
    -- 注册和处理GlobalData回调
    self:ensureGlobalDataCallback()
end

---开服成功
function GlobalCallback:onClusterReady()
    DebugLog("[%s] onClusterReady", containerType)
    EZGlobal.ClusterReady = true

    -- 集群启动完成后再把gate打开
    for _, gate in pairs(GateEntity.GateEntity.instances) do
        local host = LuaNodeConfig.getConfig("gate.host")
        local port = LuaNodeConfig.getConfig("gate.port")
        local threads = LuaNodeConfig.getConfig("gate.threads")
        assert(host and port)
        gate:start(host, port, 100, threads)
        break -- 目前的示例只配置了一个gate
    end

    -- 注册所有Service的容灾恢复rpc
    GlobalEntityMgr.instance:registerRecoverRpc()
end

--endregion 开服状态回调

--region 关服状态回调

-- 开始关服流程
function GlobalCallback:onClusterClosing()
    DebugLog("[%s] onClusterClosing", containerType)
    EZE.continueShutdown()
end

-- 关闭gate，把客户端踢下线
function GlobalCallback:onClusterDisconnectClient()
    DebugLog("[%s] onClusterDisconnectClient", containerType)
    for _, gate in pairs(GateEntity.GateEntity.instances) do
        gate:destroy()
    end
    EZE.continueShutdown()
end

-- 存储前
function GlobalCallback:onClusterPreSave()
    DebugLog("[%s] onClusterPreSave", containerType)
    EZE.continueShutdown()
end

-- 全服存储
function GlobalCallback:onClusterSaving()
    DebugLog("[%s] onClusterSaving", containerType)
    EZE.continueShutdown()
end

-- 存储后
function GlobalCallback:onClusterPostSave()
    DebugLog("[%s] onClusterPostSave", containerType)
    -- 将剩余的持久化Entity全部save&destroy
    local persistentEnts = {}
    for eid, ent in pairs(LuaEntityManager.entities) do
        if ent:getDBID() ~= "" then
            persistentEnts[#persistentEnts + 1] = eid
            ent:destroy()
        end
    end
    EZGlobal.Entity:addTimer(1, 1, function (timerId)
        for i = #persistentEnts, 1, -1 do
            if EntityManager.getEntity(persistentEnts[i]) then
                InfoLog("waiting for destroy persistent entity(%s), left(%s)", persistentEnts[i], #persistentEnts - i + 1)
                return
            else
                table.remove(persistentEnts)
            end
        end
        EZGlobal.Entity:delTimer(timerId)
        EZE.continueShutdown()
    end)
end

-- 关服成功
function GlobalCallback:onClusterClosed()
    DebugLog("[%s] onClusterClosed", containerType)
    if CmdService.CmdService.instance and CmdService.CmdService.instance.locks.global then
        CmdService.CmdService.instance:asyncFeedback({aeId=CmdService.CmdService.instance.locks.global}, CmdUtils.CmdUtils.res("ok"))
    end
    -- 删除所有剩余Entity
    local rest = {}
    for _, ent in pairs(LuaEntityManager.entities) do
        if EZGlobal.Entity ~= ent then
            rest[#rest + 1] = ent
        end
    end
    for _, ent in ipairs(rest) do
        ent:destroy()
    end
    EZGlobal.Entity:addTimer(10, 0, function (timerId)
        EZGlobal.Entity:destroy()
        EZGlobal.Entity = nil
        EZE.continueShutdown()
    end)
end

--endregion 关服状态回调

--region cmdtool相关功能

---@param data string
function GlobalCallback:broadcast(data)
    local raw = EZE.msgpackUnpack(data)
    local mb = LuaMailbox.createFromString(raw.mailboxStr)
    if raw.allowed and not raw.allowed[containerId] then
        EZGlobal.Entity:serverRpc(mb, raw.callback, containerId, raw.uid, true, "blocked")
        return
    end
    xpcall(function ()
        local f = self[raw.method]
        local succ = false
        local r = nil
        if f then
            succ, r = f(self, unpack(raw.args, 1, table.maxn(raw.args)))
        else
            r = string.format("miss method %s", raw.method)
        end
        EZGlobal.Entity:serverRpc(mb, raw.callback, containerId, raw.uid, succ or false, r)
        InfoLog("broadcast(%s) run: %v", raw.method, raw.ignoreLogRes and type(r) or r)
        return r
    end, function (errormsg)
        EZGlobal.Entity:serverRpc(mb, raw.callback, containerId, raw.uid, false, errormsg)
        ErrorLog(EZE.traceback(errormsg, 2))
    end)
end

---@vararg string
function GlobalCallback:reload(...)
    local cnt = select("#", ...)
    if 0 == cnt then
        ReloadAll()
    else
        for i = 1, cnt do
            ReloadModule(select(i, ...))
        end
    end
    return true
end

---@param str string
function GlobalCallback:luaDoString(str)
    local succ, errormsg = lxpcall(function(str) loadstring(str)() end, str)
    return succ, errormsg
end

---@param val any
---@param ... int | string
function GlobalCallback:setWatcher(val, ...)
    return EZE.setWatcher(val, ...)
end

---@param ... int | string
function GlobalCallback:getWatcher(...)
    local val = EZE.getWatcher(...)
    return true, val == nil and "nil" or val
end

--region hotfix

function GlobalCallback:hotfix()
    local hs = self:hotfixServer()
    local hc = self:hotfixClient()
    return hs and hc
end

function GlobalCallback:hotfixServer()
    dofile(EZE.getScriptPath() .. "/Hotfix/Server.lua")
    return true
end

function GlobalCallback:hotfixClient()
    EZGlobal.ClientHotfixStr = File.File.getContent(EZE.getScriptPath() .. "/Hotfix/Client.lua")
    local ver = EZE.md5hexa(EZGlobal.ClientHotfixStr)
    if EZGlobal.ClientHotfixVer == ver then
        WarnLog("client hotfix ver: %s == %s", EZGlobal.ClientHotfixVer, ver)
    else
        WarnLog("client hotfix ver: %s -> %s", EZGlobal.ClientHotfixVer, ver)
        EZGlobal.ClientHotfixVer = ver
        local Account = xrequire("Entities.Account")
        local Avatar = xrequire("Entities.Avatar")
        for _, acc in pairs(Account.Account.instances) do
            acc:notifyClientHotfix()
        end
        for _, avt in pairs(Avatar.Avatar.instances) do
            avt:notifyClientHotfix()
        end
    end
    return true
end

--endregion hotfix

--region TimeProxy

function GlobalCallback:timeproxy(...)
    Time.timeproxy({...})
    EntityManager.dispatchInterface("onSetTime")
    return true, Time.getServerNow()
end

function GlobalCallback:timeorigin()
    Time.timeorigin()
    EntityManager.dispatchInterface("onSetTime")
    return true, Time.getServerNow()
end

--endregion TimeProxy

--endregion cmdtool相关功能

-- set global callback
EZE.setGlobalCallback(GlobalCallback.new())

-- for emmylua
---@class GlobalCallbackEnv
local GlobalCallbackEnv = {GlobalCallback=GlobalCallback}
return GlobalCallbackEnv
