-- Auto generated by Luban XPipeline!

EnumDetails =
{
    Actor = {
        [1] = {
            Name = "Source",
            <PERSON>as = "自身",
            Comment = "自身",
        },
        [2] = {
            Name = "Target",
            <PERSON>as = "目标",
            Comment = "目标",
        },
    },
    ActorType = {
        [1001] = {
            Name = "ARMY",
            <PERSON><PERSON> = "部队",
            Comment = "部队",
        },
        [1002] = {
            Name = "MAIN_CITY",
            Alias = "主堡",
            Comment = "主堡",
        },
        [1003] = {
            Name = "TENT",
            <PERSON><PERSON> = "营帐",
            Comment = "营帐",
        },
        [1004] = {
            Name = "STONE_VEHICLE",
            <PERSON>as = "投石车",
            Comment = "投石车",
        },
        [1005] = {
            Name = "CITY",
            Alias = "城池",
            Comment = "城池",
        },
        [1006] = {
            Name = "CITY_GATE",
            <PERSON>as = "城门",
            Comment = "城门",
        },
        [1007] = {
            Name = "PASS",
            <PERSON>as = "关卡",
            Comment = "关卡",
        },
        [1008] = {
            Name = "FERRY",
            <PERSON><PERSON> = "渡口",
            Comment = "渡口",
        },
        [1009] = {
            Name = "INNER_CITY_GATE",
            Alias = "城主府门",
            Comment = "城主府门",
        },
        [1010] = {
            Name = "SIEGE_CAMP",
            Alias = "攻城大营",
            Comment = "攻城大营",
        },
        [1011] = {
            Name = "CHUNK",
            Alias = "地块",
            Comment = "地块",
        },
        [1012] = {
            Name = "JI_SHEN_JI_DIAN",
            Alias = "稷神祭典",
            Comment = "稷神祭典",
        },
        [1013] = {
            Name = "CITY_BARRACKS",
            Alias = "兵营",
            Comment = "兵营",
        },
    },
    Alignment = {
        [1] = {
            Name = "Left",
            Alias = "居左",
            Comment = "居左",
        },
        [2] = {
            Name = "Right",
            Alias = "居右",
            Comment = "居右",
        },
        [3] = {
            Name = "Center",
            Alias = "居中",
            Comment = "居中",
        },
    },
    AllyBuildingTypeLevel = {
        [1] = {
            Name = "FOUNDATION_OF_RULE",
            Alias = "霸业之基",
            Comment = "霸业之基",
        },
        [2] = {
            Name = "PREFECTURE_CAPITAL",
            Alias = "州府",
            Comment = "州府",
        },
        [3] = {
            Name = "COMMANDERY_CAPITAL",
            Alias = "郡府",
            Comment = "郡府",
        },
        [4] = {
            Name = "FERRY",
            Alias = "码头",
            Comment = "码头",
        },
        [5] = {
            Name = "PASS",
            Alias = "关卡",
            Comment = "关卡",
        },
    },
    ArmyAnimationName = {
        [1] = {
            Name = "idle",
            Alias = "停留",
            Comment = "停留",
        },
        [2] = {
            Name = "battleidle",
            Alias = "驻守",
            Comment = "驻守",
        },
        [3] = {
            Name = "walk",
            Alias = "行走",
            Comment = "walk和run根据移动速度自动切换",
        },
        [4] = {
            Name = "run",
            Alias = "奔跑",
            Comment = "walk和run根据移动速度自动切换",
        },
        [5] = {
            Name = "attack",
            Alias = "攻击",
            Comment = "攻击",
        },
        [6] = {
            Name = "victory",
            Alias = "战斗胜利",
            Comment = "战斗胜利",
        },
        [7] = {
            Name = "retreat",
            Alias = "主动回城",
            Comment = "主动回城",
        },
    },
    ArmyHomeType = {
        [1] = {
            Name = "BaseHome",
            Alias = "主城",
            Comment = "主城",
        },
        [2] = {
            Name = "DeployHome",
            Alias = "调动点",
            Comment = "调动点",
        },
        [3] = {
            Name = "SiegeHome",
            Alias = "集结点",
            Comment = "集结点",
        },
    },
    ArmyStatus = {
        [101] = {
            Name = "Rest",
            Alias = "休息",
            Comment = "城内(主堡城池营帐)休息",
            Tags = {
                main = "main",
            },
        },
        [102] = {
            Name = "Stay",
            Alias = "驻扎",
            Comment = "野外停留",
            Tags = {
                main = "main",
            },
        },
        [103] = {
            Name = "March",
            Alias = "行军",
            Comment = "行军",
            Tags = {
                main = "main",
            },
        },
        [104] = {
            Name = "DefendGrid",
            Alias = "驻守格子",
            Comment = "驻守格子",
            Tags = {
                main = "main",
            },
        },
        [105] = {
            Name = "DefendBuilding",
            Alias = "驻守建筑",
            Comment = "驻守建筑",
            Tags = {
                main = "main",
            },
        },
        [106] = {
            Name = "Occupy",
            Alias = "占领中",
            Comment = "占领地块倒计时中",
            Tags = {
                main = "main",
            },
        },
        [107] = {
            Name = "Retreat",
            Alias = "撤退",
            Comment = "主动回城",
            Tags = {
                main = "main",
            },
        },
        [108] = {
            Name = "Escape",
            Alias = "溃逃",
            Comment = "战败溃逃",
            Tags = {
                main = "main",
            },
        },
        [109] = {
            Name = "Sing",
            Alias = "吟唱",
            Comment = "释放策牌吟唱",
            Tags = {
                main = "main",
            },
        },
        [110] = {
            Name = "WaitSing",
            Alias = "等待吟唱",
            Comment = "释放策牌吟唱被暂停",
            Tags = {
                main = "main",
            },
        },
        [201] = {
            Name = "IdleCombat",
            Alias = "非战斗",
            Comment = "非战斗",
            Tags = {
                combat = "combat",
            },
        },
        [202] = {
            Name = "WaitCombat",
            Alias = "等待战斗",
            Comment = "等待战斗",
            Tags = {
                combat = "combat",
            },
        },
        [203] = {
            Name = "Combat",
            Alias = "战斗",
            Comment = "战斗",
            Tags = {
                combat = "combat",
            },
        },
        [204] = {
            Name = "Injured",
            Alias = "重伤",
            Comment = "重伤",
            Tags = {
                combat = "combat",
            },
        },
        [205] = {
            Name = "BreakDura",
            Alias = "拆除耐久",
            Comment = "拆除耐久",
            Tags = {
                combat = "combat",
            },
        },
        [206] = {
            Name = "Impasse",
            Alias = "僵持",
            Comment = "僵持",
            Tags = {
                combat = "combat",
            },
        },
        [301] = {
            Name = "IdleDeploy",
            Alias = "非调动",
            Comment = "非调动",
            Tags = {
                deploy = "deploy",
            },
        },
        [302] = {
            Name = "GoDeploy",
            Alias = "前往调动",
            Comment = "前往调动",
            Tags = {
                deploy = "deploy",
            },
        },
        [303] = {
            Name = "Deploy",
            Alias = "调动",
            Comment = "调动",
            Tags = {
                deploy = "deploy",
            },
        },
        [304] = {
            Name = "GoSiegeRally",
            Alias = "前往攻城集结",
            Comment = "前往攻城集结",
            Tags = {
                deploy = "deploy",
            },
        },
        [305] = {
            Name = "SiegeRally",
            Alias = "攻城集结",
            Comment = "攻城集结",
            Tags = {
                deploy = "deploy",
            },
        },
        [401] = {
            Name = "IdleVehicle",
            Alias = "非器械中",
            Comment = "非器械中",
            Tags = {
                vehicle = "vehicle",
            },
        },
        [402] = {
            Name = "InStoneVehicle",
            Alias = "在投石车中",
            Comment = "在投石车中",
            Tags = {
                vehicle = "vehicle",
            },
        },
        [403] = {
            Name = "ThrowStone",
            Alias = "投石中",
            Comment = "投石中",
            Tags = {
                vehicle = "vehicle",
            },
        },
    },
    ArmyTypeQualificationTarget = {
        [0] = {
            Name = "Self",
            Alias = "自己",
            Comment = "自己",
        },
    },
    AudioGroup = {
        [0] = {
            Name = "Default",
            Alias = "默认",
            Comment = "默认",
        },
        [1] = {
            Name = "Male",
            Alias = "男",
            Comment = "男",
        },
        [2] = {
            Name = "Female",
            Alias = "女",
            Comment = "女",
        },
    },
    BattleCameraType = {
        [1] = {
            Name = "Full",
            Alias = "全景",
            Comment = "全景",
        },
        [2] = {
            Name = "Follow",
            Alias = "跟随",
            Comment = "跟随",
        },
        [3] = {
            Name = "Focus",
            Alias = "聚焦",
            Comment = "聚焦",
        },
        [4] = {
            Name = "Special",
            Alias = "特殊",
            Comment = "特殊",
        },
    },
    BattleCharacterAnimation = {
        [0] = {
            Name = "skill_01",
            Alias = "",
            Comment = "skill_01",
        },
        [1] = {
            Name = "skill_02",
            Alias = "",
            Comment = "skill_02",
        },
        [2] = {
            Name = "skill_11",
            Alias = "",
            Comment = "skill_11",
        },
        [3] = {
            Name = "skill_12",
            Alias = "",
            Comment = "skill_12",
        },
        [4] = {
            Name = "skill_13",
            Alias = "",
            Comment = "skill_13",
        },
        [5] = {
            Name = "skill_21",
            Alias = "",
            Comment = "skill_21",
        },
        [6] = {
            Name = "skill_22",
            Alias = "",
            Comment = "skill_22",
        },
        [7] = {
            Name = "skill_23",
            Alias = "",
            Comment = "skill_23",
        },
        [8] = {
            Name = "skill_31",
            Alias = "",
            Comment = "skill_31",
        },
        [9] = {
            Name = "skill_32",
            Alias = "",
            Comment = "skill_32",
        },
        [10] = {
            Name = "skill_41",
            Alias = "",
            Comment = "skill_41",
        },
        [11] = {
            Name = "charge",
            Alias = "",
            Comment = "charge",
        },
        [12] = {
            Name = "dodge",
            Alias = "",
            Comment = "dodge",
        },
        [13] = {
            Name = "guard",
            Alias = "",
            Comment = "guard",
        },
        [14] = {
            Name = "hit",
            Alias = "",
            Comment = "hit",
        },
        [15] = {
            Name = "float_up",
            Alias = "",
            Comment = "float_up",
        },
        [16] = {
            Name = "float_mid",
            Alias = "",
            Comment = "float_mid",
        },
        [17] = {
            Name = "float_down",
            Alias = "",
            Comment = "float_down",
        },
        [18] = {
            Name = "special_01",
            Alias = "",
            Comment = "special_01",
        },
        [19] = {
            Name = "special_02",
            Alias = "",
            Comment = "special_02",
        },
    },
    BattleDamageType = {
        [1] = {
            Name = "Attack",
            Alias = "兵刃伤害",
            Comment = "物理伤害",
        },
        [2] = {
            Name = "Intelligence",
            Alias = "谋略伤害",
            Comment = "法术伤害",
        },
        [3] = {
            Name = "Reality",
            Alias = "真实伤害",
            Comment = "真实伤害",
        },
        [4] = {
            Name = "Share",
            Alias = "分摊伤害",
            Comment = "分摊伤害",
        },
    },
    BattleEvent = {
        [0] = {
            Name = "None",
            Alias = "",
            Comment = "",
        },
        [1] = {
            Name = "FormationMorale",
            Alias = "布阵-士气",
            Comment = "布阵-士气",
        },
        [2] = {
            Name = "FormationArmyType",
            Alias = "布阵-兵种",
            Comment = "布阵-兵种",
        },
        [14] = {
            Name = "FormationTechnology",
            Alias = "布阵-科技",
            Comment = "布阵-科技",
        },
        [3] = {
            Name = "FormationEquipment",
            Alias = "布阵-装备",
            Comment = "布阵-装备",
        },
        [15] = {
            Name = "FormationHeroIdentity",
            Alias = "布阵-身份",
            Comment = "布阵-身份",
        },
        [13] = {
            Name = "FormationTacticsArmyType",
            Alias = "布阵-战法-兵种",
            Comment = "布阵-战法-兵种",
        },
        [12] = {
            Name = "FormationTacticsFormation",
            Alias = "布阵-战法-阵法",
            Comment = "布阵-战法-阵法",
        },
        [4] = {
            Name = "FormationTacticsOther",
            Alias = "布阵-战法-其他",
            Comment = "布阵-战法-其他",
        },
        [5] = {
            Name = "RoundPrepare",
            Alias = "回合准备",
            Comment = "回合准备",
        },
        [6] = {
            Name = "HeroTurnStart",
            Alias = "英雄轮次开始",
            Comment = "英雄轮次开始",
        },
        [7] = {
            Name = "HeroTurnAction",
            Alias = "英雄轮次行动",
            Comment = "英雄轮次行动",
        },
        [8] = {
            Name = "HeroTurnEnd",
            Alias = "英雄轮次结束",
            Comment = "英雄轮次结束",
        },
        [9] = {
            Name = "RoundEnd",
            Alias = "回合结束",
            Comment = "回合结束",
        },
        [10] = {
            Name = "BattleResult",
            Alias = "战斗结算",
            Comment = "战斗结算",
        },
        [11] = {
            Name = "Initialize",
            Alias = "初始化",
            Comment = "初始化",
        },
        [100] = {
            Name = "BeforeDamage",
            Alias = "伤害前",
            Comment = "伤害前",
        },
        [101] = {
            Name = "AfterDamage",
            Alias = "伤害后",
            Comment = "伤害后",
        },
        [102] = {
            Name = "BeforeHeal",
            Alias = "治疗前",
            Comment = "治疗前",
        },
        [103] = {
            Name = "AfterHeal",
            Alias = "治疗后",
            Comment = "治疗后",
        },
        [105] = {
            Name = "BeforeUseTactic",
            Alias = "使用战法前",
            Comment = "使用战法前",
        },
        [106] = {
            Name = "AfterUseTactic",
            Alias = "使用战法后",
            Comment = "使用战法后",
        },
        [107] = {
            Name = "TacticProbFailed",
            Alias = "战法发动率判定失败",
            Comment = "战法发动率判定失败",
        },
        [108] = {
            Name = "BeforeBeDamaged",
            Alias = "受到伤害前",
            Comment = "受到伤害前",
        },
        [109] = {
            Name = "AfterBeDamaged",
            Alias = "受到伤害后",
            Comment = "受到伤害后",
        },
        [111] = {
            Name = "AfterPlainAttackProcess",
            Alias = "普攻流程后",
            Comment = "普攻流程后",
        },
        [112] = {
            Name = "AfterPlainAttack",
            Alias = "普攻后",
            Comment = "普攻后",
        },
        [113] = {
            Name = "BeforePlainAttack",
            Alias = "普攻前",
            Comment = "普攻前",
        },
        [114] = {
            Name = "BeforeDodge",
            Alias = "闪避前",
            Comment = "闪避前",
        },
        [115] = {
            Name = "AfterDodgeSuccess",
            Alias = "闪避成功后",
            Comment = "闪避成功后",
        },
        [116] = {
            Name = "AfterDodgeFail",
            Alias = "闪避失败后",
            Comment = "闪避失败后",
        },
        [117] = {
            Name = "BeforeBeHealed",
            Alias = "受到治疗前",
            Comment = "受到治疗前",
        },
        [118] = {
            Name = "AfterBeHealed",
            Alias = "受到治疗后",
            Comment = "受到治疗后",
        },
        [119] = {
            Name = "BeforeFatalDamage",
            Alias = "受致命伤前",
            Comment = "受致命伤前",
        },
        [120] = {
            Name = "BeforeAddBuff",
            Alias = "添加buff前",
            Comment = "添加buff前",
        },
        [121] = {
            Name = "AfterAddBuff",
            Alias = "添加buff后",
            Comment = "添加buff后",
        },
        [122] = {
            Name = "BeforeBeAddBuff",
            Alias = "被添加buff前",
            Comment = "被添加buff前",
        },
        [123] = {
            Name = "AfterBeAddBuff",
            Alias = "被添加buff后",
            Comment = "被添加buff后",
        },
        [124] = {
            Name = "BeforeTryUseTactic",
            Alias = "尝试使用战法前",
            Comment = "尝试使用战法前",
        },
        [125] = {
            Name = "BeforeBePlainAttacked",
            Alias = "被普攻前",
            Comment = "被普攻前",
        },
        [126] = {
            Name = "AfterBePlainAttacked",
            Alias = "被普攻后",
            Comment = "被普攻后",
        },
        [127] = {
            Name = "AfterTacticAwaken",
            Alias = "战法觉醒后",
            Comment = "战法觉醒后",
        },
        [128] = {
            Name = "BeforeBeRemoveBuff",
            Alias = "被移除buff前",
            Comment = "被移除buff前",
        },
        [129] = {
            Name = "AfterBeRemoveBuff",
            Alias = "被移除buff后",
            Comment = "被移除buff后",
        },
    },
    BattleModeType = {
        [0] = {
            Name = "OneOnOne",
            Alias = "1v1",
            Comment = "1v1",
        },
        [1] = {
            Name = "Bo3",
            Alias = "",
            Comment = "",
        },
        [2] = {
            Name = "Kof3",
            Alias = "",
            Comment = "",
        },
    },
    BattlePVEType = {
        [10] = {
            Name = "LandDefender",
            Alias = "",
            Comment = "",
        },
    },
    BattleRecordScope = {
        [1] = {
            Name = "Individual",
            Alias = "个人",
            Comment = "个人",
        },
        [2] = {
            Name = "Ally",
            Alias = "同盟",
            Comment = "同盟",
        },
    },
    BattleTacticGrade = {
        [1] = {
            Name = "White",
            Alias = "白",
            Comment = "白",
        },
        [2] = {
            Name = "Blue",
            Alias = "蓝",
            Comment = "蓝",
        },
        [3] = {
            Name = "Purple",
            Alias = "紫",
            Comment = "紫",
        },
        [4] = {
            Name = "Gold",
            Alias = "橙",
            Comment = "橙",
        },
        [5] = {
            Name = "BasicAttack",
            Alias = "普攻",
            Comment = "普攻",
        },
    },
    BattleType = {
        [1] = {
            Name = "PVP",
            Alias = "PVP交战",
            Comment = "PVP交战",
        },
        [65280] = {
            Name = "PVE_ALL",
            Alias = "PVE所有",
            Comment = "PVE所有",
        },
        [256] = {
            Name = "PVE_LAND",
            Alias = "PVE打地",
            Comment = "PVE打地",
        },
        [512] = {
            Name = "PVE_DRILL",
            Alias = "PVE军演",
            Comment = "PVE军演",
        },
        [1024] = {
            Name = "PVE_OUTPOST_DEF",
            Alias = "PVE驻城守军",
            Comment = "PVE驻城守军",
        },
        [2048] = {
            Name = "PVE_OUTPOST_DURA",
            Alias = "PVE城防守军",
            Comment = "PVE城防守军",
        },
        [0] = {
            Name = "Test",
            Alias = "测试",
            Comment = "测试",
        },
    },
    BehaviorType = {
        [1] = {
            Name = "March",
            Alias = "行军",
            Comment = "A前往行军",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [2] = {
            Name = "OccupyGrid",
            Alias = "攻占",
            Comment = "A前往占领地块",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [3] = {
            Name = "AttackBuilding",
            Alias = "攻城",
            Comment = "攻城",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [4] = {
            Name = "Defend",
            Alias = "驻守",
            Comment = "前往驻守地块或建筑",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [5] = {
            Name = "Deploy",
            Alias = "调动",
            Comment = "前往调动点",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [6] = {
            Name = "Siege",
            Alias = "集结",
            Comment = "前往集结点",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [7] = {
            Name = "Return",
            Alias = "回城",
            Comment = "回城",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [8] = {
            Name = "UseStrategy",
            Alias = "使用策牌",
            Comment = "使用策牌",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [9] = {
            Name = "OperateVehicle",
            Alias = "操控器械",
            Comment = "操控器械",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [10] = {
            Name = "ThrowStone",
            Alias = "投石",
            Comment = "投石",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [11] = {
            Name = "Halt",
            Alias = "中断",
            Comment = "中断任意行为",
            Tags = {
                whitelist = "whitelist",
            },
        },
        [12] = {
            Name = "EnterSpace",
            Alias = "进入场景",
            Comment = "进入场景才可被看见",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [13] = {
            Name = "ModifyArmy",
            Alias = "编队",
            Comment = "编队",
            Tags = {
                blacklist = "blacklist",
            },
        },
        [14] = {
            Name = "Supply",
            Alias = "补给",
            Comment = "恢复部队补给",
            Tags = {
                whitelist = "whitelist",
            },
        },
        [15] = {
            Name = "Interactable",
            Alias = "进入格子交互",
            Comment = "进入格子交互",
            Tags = {
                blacklist = "blacklist",
            },
        },
    },
    BuffAddTiming = {
        [0] = {
            Name = "None",
            Alias = "",
            Comment = "",
        },
        [1] = {
            Name = "Initialize",
            Alias = "初始化",
            Comment = "初始化",
        },
        [2] = {
            Name = "FormationMorale",
            Alias = "布阵-补给",
            Comment = "布阵-补给",
        },
        [3] = {
            Name = "FormationArmyType",
            Alias = "布阵-兵种",
            Comment = "布阵-兵种",
        },
        [4] = {
            Name = "FormationTechnology",
            Alias = "布阵-科技",
            Comment = "布阵-科技",
        },
        [5] = {
            Name = "FormationEquipment",
            Alias = "布阵-装备",
            Comment = "布阵-装备",
        },
        [6] = {
            Name = "FormationHeroIdentity",
            Alias = "布阵-身份",
            Comment = "布阵-身份",
        },
        [7] = {
            Name = "FormationTacticsArmyType",
            Alias = "布阵-战法-兵种",
            Comment = "布阵-战法-兵种",
        },
        [8] = {
            Name = "FormationTacticsFormation",
            Alias = "布阵-战法-阵法",
            Comment = "布阵-战法-阵法",
        },
        [9] = {
            Name = "FormationTacticsOther",
            Alias = "布阵-战法-其他",
            Comment = "布阵-战法-其他",
        },
    },
    BuffAttributeType = {
        [1] = {
            Name = "TotalRound",
            Alias = "总回合数",
            Comment = "总回合数",
        },
    },
    BuffGroupConflictRule = {
        [1] = {
            Name = "Invalid",
            Alias = "失效",
            Comment = "目标存在同类buff组的buff时，新BUFF无法添加",
        },
        [2] = {
            Name = "Cover",
            Alias = "覆盖",
            Comment = "目标存在同类buff组的buff时，新BUFF可以添加，且添加前清除该BUFF组其他BUFF",
        },
        [3] = {
            Name = "Round",
            Alias = "回合",
            Comment = "目标存在同类buff组的buff时,新BUFF如果剩余回合数更长,则执行【覆盖】,否则【失效】",
        },
        [4] = {
            Name = "Attribute",
            Alias = "属性",
            Comment = "目标存在同类buff组的buff时，如果来源【%属性】更高，则执行【覆盖】，否则【失效】",
        },
        [5] = {
            Name = "Coexist",
            Alias = "共存",
            Comment = "目标存在同类buff组的buff时，两个BUFF共存",
        },
    },
    BuffGroupType = {
        [1] = {
            Name = "DamaageDot1",
            Alias = "火攻",
            Comment = "火攻",
        },
        [2] = {
            Name = "DamaageDot2",
            Alias = "水攻",
            Comment = "水攻",
        },
        [3] = {
            Name = "NegativeDot",
            Alias = "雷击",
            Comment = "雷击",
        },
        [4] = {
            Name = "Control",
            Alias = "控制效果",
            Comment = "控制效果",
        },
        [5] = {
            Name = "NegativeCanStacked",
            Alias = "减益可叠加",
            Comment = "减益可叠加",
        },
        [6] = {
            Name = "NegativeNotStacked",
            Alias = "减益不可叠加",
            Comment = "减益不可叠加",
        },
        [7] = {
            Name = "PositiveCanStacked",
            Alias = "增益可叠加",
            Comment = "增益可叠加",
        },
        [8] = {
            Name = "PositiveNotStacked",
            Alias = "增益不可叠加",
            Comment = "增益不可叠加",
        },
    },
    BuffOverlayRule = {
        [1] = {
            Name = "Replace",
            Alias = "顶替",
            Comment = "新buff顶替旧buff",
        },
        [2] = {
            Name = "Refresh",
            Alias = "刷新",
            Comment = "新buff无法添加；但是旧buff的持续时间刷新至最新",
        },
        [3] = {
            Name = "ReplaceMaxRemainRound",
            Alias = "顶替（剩余最大回合）",
            Comment = "根据剩余最大回合来顶替",
        },
    },
    BuffRemoveTiming = {
        [1] = {
            Name = "AfterHeroTurnEnd",
            Alias = "英雄自身轮次结束后",
            Comment = "英雄自身轮次结束后",
        },
        [2] = {
            Name = "AfterRoundEnd",
            Alias = "大回合结束后",
            Comment = "大回合结束后",
        },
    },
    BuffSourceType = {
        [0] = {
            Name = "InBattle",
            Alias = "局内",
            Comment = "局内",
        },
        [1] = {
            Name = "HomeTech",
            Alias = "内城科技",
            Comment = "内城科技",
        },
        [2] = {
            Name = "SandMap",
            Alias = "沙盘地图",
            Comment = "沙盘地图",
        },
        [3] = {
            Name = "Team",
            Alias = "编队",
            Comment = "编队",
        },
    },
    BuffStackRule = {
        [1] = {
            Name = "Mutex",
            Alias = "互斥",
            Comment = "每层独立计算时间",
        },
        [2] = {
            Name = "Refresh",
            Alias = "刷新",
            Comment = "刷新所有层时间",
        },
    },
    BuffType = {
        [1] = {
            Name = "Control",
            Alias = "控制",
            Comment = "控制",
        },
        [2] = {
            Name = "Sustained",
            Alias = "持续",
            Comment = "持续",
        },
        [3] = {
            Name = "Positive",
            Alias = "增益",
            Comment = "增益",
        },
        [4] = {
            Name = "Special",
            Alias = "特殊",
            Comment = "特殊",
        },
    },
    BuildingType = {
        [1000001] = {
            Name = "TaiXue",
            Alias = "太学",
            Comment = "太学",
        },
        [1000002] = {
            Name = "ZhengBingSuo",
            Alias = "征兵所",
            Comment = "征兵所",
        },
        [1000003] = {
            Name = "JunYing",
            Alias = "军营",
            Comment = "军营",
        },
        [1000004] = {
            Name = "JunWangDian",
            Alias = "君王殿",
            Comment = "君王殿",
        },
        [1000005] = {
            Name = "YuMaYuan",
            Alias = "御马苑",
            Comment = "御马苑",
        },
        [1000006] = {
            Name = "TieJiangPu",
            Alias = "铁匠铺",
            Comment = "铁匠铺",
        },
        [1000007] = {
            Name = "FaMu1",
            Alias = "伐木1",
            Comment = "伐木1",
        },
        [1000008] = {
            Name = "FaMu2",
            Alias = "伐木2",
            Comment = "伐木2",
        },
        [1000009] = {
            Name = "TieKuang1",
            Alias = "铁矿1",
            Comment = "铁矿1",
        },
        [1000010] = {
            Name = "TieKuang2",
            Alias = "铁矿2",
            Comment = "铁矿2",
        },
        [1000011] = {
            Name = "CaiShi1",
            Alias = "采石1",
            Comment = "采石1",
        },
        [1000012] = {
            Name = "CaiShi2",
            Alias = "采石2",
            Comment = "采石2",
        },
        [1000013] = {
            Name = "NongChang1",
            Alias = "农场1",
            Comment = "农场1",
        },
        [1000014] = {
            Name = "NongChang2",
            Alias = "农场2",
            Comment = "农场2",
        },
        [1000015] = {
            Name = "CangKu",
            Alias = "仓库",
            Comment = "仓库",
        },
        [1000016] = {
            Name = "JiShi",
            Alias = "集市",
            Comment = "集市",
        },
        [1000017] = {
            Name = "MinJu",
            Alias = "民居",
            Comment = "民居",
        },
        [1000018] = {
            Name = "ZaoBiChang",
            Alias = "造币厂",
            Comment = "造币厂",
        },
    },
    BuildStatus = {
        [1] = {
            Name = "Building",
            Alias = "建造中",
            Comment = "建造中",
        },
        [2] = {
            Name = "Idle",
            Alias = "空闲",
            Comment = "空闲",
        },
        [3] = {
            Name = "Upgrading",
            Alias = "升级中",
            Comment = "升级中",
        },
        [4] = {
            Name = "Dismantling",
            Alias = "拆除中",
            Comment = "拆除中",
        },
    },
    Camp = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无阵营",
        },
        [1] = {
            Name = "A",
            Alias = "进攻方",
            Comment = "左边的阵营",
        },
        [2] = {
            Name = "B",
            Alias = "防守方",
            Comment = "右边的阵营",
        },
    },
    CareerType = {
        [0] = {
            Name = "DEFAULT",
            Alias = "空",
            Comment = "空",
        },
        [101] = {
            Name = "FIELD_MASTER",
            Alias = "田师",
            Comment = "田师",
        },
    },
    ChannelType = {
        [1] = {
            Name = "Sys",
            Alias = "系统",
            Comment = "系统",
        },
        [2] = {
            Name = "World",
            Alias = "世界",
            Comment = "世界",
        },
        [3] = {
            Name = "Clan",
            Alias = "世族",
            Comment = "世族",
        },
        [4] = {
            Name = "Prefecture",
            Alias = "地区",
            Comment = "地区",
        },
    },
    ChatFriendTab = {
        [101] = {
            Name = "Contacts",
            Alias = "联系人",
            Comment = "联系人",
        },
        [102] = {
            Name = "Friend",
            Alias = "好友",
            Comment = "好友",
        },
        [103] = {
            Name = "Blacklist",
            Alias = "黑名单",
            Comment = "黑名单",
        },
    },
    CoinType = {
        [100001] = {
            Name = "Gold",
            Alias = "金币",
            Comment = "金币",
        },
        [100002] = {
            Name = "Copper",
            Alias = "铜币",
            Comment = "铜币",
        },
        [100003] = {
            Name = "Wood",
            Alias = "木头",
            Comment = "木头",
        },
        [100004] = {
            Name = "Iron",
            Alias = "铁块",
            Comment = "铁块",
        },
        [100005] = {
            Name = "Stone",
            Alias = "石头",
            Comment = "石头",
        },
        [100006] = {
            Name = "Food",
            Alias = "粮草",
            Comment = "粮草",
        },
        [100007] = {
            Name = "Soldier",
            Alias = "预备兵",
            Comment = "预备兵",
        },
        [100008] = {
            Name = "Jade",
            Alias = "魂玉",
            Comment = "魂玉",
        },
        [100009] = {
            Name = "BambooLetter",
            Alias = "玉环",
            Comment = "玉环",
        },
        [100010] = {
            Name = "Farmer",
            Alias = "居民",
            Comment = "居民",
        },
        [100011] = {
            Name = "Starstone",
            Alias = "星石",
            Comment = "星石",
        },
    },
    CrowdControlType = {
        [1] = {
            Name = "Silence",
            Alias = "技穷",
            Comment = "技穷",
            Tags = {
                DETAIL = "DETAIL",
            },
        },
        [2] = {
            Name = "Disarm",
            Alias = "缴械",
            Comment = "缴械",
            Tags = {
                DETAIL = "DETAIL",
            },
        },
        [3] = {
            Name = "Stun",
            Alias = "震慑",
            Comment = "震慑",
            Tags = {
                DETAIL = "DETAIL",
            },
        },
        [4] = {
            Name = "Starve",
            Alias = "断粮",
            Comment = "断粮",
        },
        [6] = {
            Name = "AdditionalCaptain",
            Alias = "额外军职-主公",
            Comment = "额外军职-主公",
        },
        [7] = {
            Name = "AdditionalPioneer",
            Alias = "额外军职-先锋",
            Comment = "额外军职-先锋",
        },
        [8] = {
            Name = "AdditionalAdviser",
            Alias = "额外军职-军师",
            Comment = "额外军职-军师",
        },
        [9] = {
            Name = "Taunt",
            Alias = "嘲讽",
            Comment = "普攻锁定目标",
        },
        [10] = {
            Name = "Deterrence",
            Alias = "威慑",
            Comment = "主动、追击战法锁定目标",
        },
        [11] = {
            Name = "Confusion",
            Alias = "混乱",
            Comment = "混乱",
        },
        [12] = {
            Name = "BanPlainAttack",
            Alias = "无法使用普通攻击",
            Comment = "无法使用普通攻击",
            Tags = {
                DETAIL = "DETAIL",
            },
        },
        [13] = {
            Name = "BanActive",
            Alias = "无法使用主动战法",
            Comment = "无法使用主动战法",
            Tags = {
                DETAIL = "DETAIL",
            },
        },
        [14] = {
            Name = "BanPursue",
            Alias = "无法使用追击战法",
            Comment = "无法使用追击战法",
            Tags = {
                DETAIL = "DETAIL",
            },
        },
    },
    DamageFormulaArgs = {
        [1] = {
            Name = "A",
            Alias = "进攻方兵力",
            Comment = "进攻方兵力",
        },
        [2] = {
            Name = "B",
            Alias = "兵力系数",
            Comment = "兵力系数",
        },
        [3] = {
            Name = "C",
            Alias = "属性差值",
            Comment = "属性差值",
        },
        [4] = {
            Name = "C1",
            Alias = "进攻方武力/智力值",
            Comment = "进攻方武力/智力值",
        },
        [5] = {
            Name = "C2",
            Alias = "防守方统帅值",
            Comment = "防守方统帅值",
        },
        [6] = {
            Name = "C3",
            Alias = "防守方智力值",
            Comment = "防守方智力值",
        },
        [7] = {
            Name = "C4",
            Alias = "进攻方破甲/看破百分比",
            Comment = "进攻方破甲/看破百分比",
        },
        [8] = {
            Name = "C5",
            Alias = "常量<C5>",
            Comment = "常量<C5>",
        },
        [9] = {
            Name = "D",
            Alias = "战法伤害系数",
            Comment = "战法伤害系数",
        },
        [10] = {
            Name = "E",
            Alias = "过程类增减伤",
            Comment = "过程类增减伤",
        },
        [11] = {
            Name = "E1",
            Alias = "伤害增加",
            Comment = "伤害增加",
        },
        [12] = {
            Name = "E3",
            Alias = "防守方受伤减少",
            Comment = "防守方受伤减少",
        },
        [13] = {
            Name = "E4",
            Alias = "防守方红度减伤",
            Comment = "防守方红度减伤",
        },
        [14] = {
            Name = "F",
            Alias = "结算类增减伤",
            Comment = "结算类增减伤",
        },
        [15] = {
            Name = "F1",
            Alias = "进攻方施加的受到伤害增加",
            Comment = "进攻方施加的受到伤害增加",
        },
        [16] = {
            Name = "F2",
            Alias = "防守方施加的造成伤害减少",
            Comment = "防守方施加的造成伤害减少",
        },
        [17] = {
            Name = "F3",
            Alias = "会心/奇谋伤害",
            Comment = "会心/奇谋伤害",
        },
        [18] = {
            Name = "G",
            Alias = "独立增减伤",
            Comment = "独立增减伤",
        },
        [19] = {
            Name = "G1",
            Alias = "红度增伤",
            Comment = "红度增伤",
        },
        [20] = {
            Name = "G2",
            Alias = "兵种克制",
            Comment = "兵种克制",
        },
        [21] = {
            Name = "G3",
            Alias = "进攻方独立兵刃/谋略伤害增减伤",
            Comment = "进攻方独立兵刃/谋略伤害增减伤",
        },
        [22] = {
            Name = "G4",
            Alias = "防守方受到兵刃/独立谋略伤害增减伤",
            Comment = "防守方受到兵刃/独立谋略伤害增减伤",
        },
        [26] = {
            Name = "G5",
            Alias = "补给增减伤",
            Comment = "补给增减伤",
        },
        [23] = {
            Name = "W",
            Alias = "兵力保底伤害",
            Comment = "兵力保底伤害",
        },
        [24] = {
            Name = "M",
            Alias = "常量<M>",
            Comment = "常量<M>",
        },
        [25] = {
            Name = "R",
            Alias = "会心/奇谋几率",
            Comment = "会心/奇谋几率",
        },
    },
    DamageRange = {
        [0] = {
            Name = "Single",
            Alias = "单体伤害",
            Comment = "单体伤害",
        },
        [1] = {
            Name = "Aoe",
            Alias = "群体伤害",
            Comment = "群体伤害",
        },
        [2] = {
            Name = "Buff",
            Alias = "Buff伤害",
            Comment = "Buff伤害",
        },
    },
    DetailRecordType = {
        [1] = {
            Name = "SetHero",
            Alias = "",
            Comment = "",
        },
        [101] = {
            Name = "BattleStart",
            Alias = "",
            Comment = "",
        },
        [102] = {
            Name = "BattleStartMorale",
            Alias = "",
            Comment = "",
        },
        [103] = {
            Name = "BattleStartArmyType",
            Alias = "",
            Comment = "",
        },
        [104] = {
            Name = "BattleStartEquipment",
            Alias = "",
            Comment = "",
        },
        [105] = {
            Name = "BattleStartTactics",
            Alias = "",
            Comment = "",
        },
        [106] = {
            Name = "BattleStartTechnology",
            Alias = "",
            Comment = "",
        },
        [107] = {
            Name = "BattleStartHeroIdentity",
            Alias = "",
            Comment = "",
        },
        [151] = {
            Name = "RoundStart",
            Alias = "",
            Comment = "",
        },
        [152] = {
            Name = "HeroTurnStart",
            Alias = "",
            Comment = "",
        },
        [153] = {
            Name = "RoundEnd",
            Alias = "",
            Comment = "",
        },
        [154] = {
            Name = "HeroTurnEnd",
            Alias = "",
            Comment = "",
        },
        [199] = {
            Name = "Result",
            Alias = "",
            Comment = "",
        },
        [201] = {
            Name = "Tactic",
            Alias = "",
            Comment = "",
        },
        [202] = {
            Name = "Damage",
            Alias = "",
            Comment = "",
        },
        [203] = {
            Name = "Heal",
            Alias = "",
            Comment = "",
        },
        [204] = {
            Name = "AddBuff",
            Alias = "",
            Comment = "",
        },
        [205] = {
            Name = "RemoveBuff",
            Alias = "",
            Comment = "",
        },
        [206] = {
            Name = "TriggerBuff",
            Alias = "",
            Comment = "",
        },
        [207] = {
            Name = "DecreaseBuff",
            Alias = "",
            Comment = "",
        },
        [208] = {
            Name = "AddCrowdControl",
            Alias = "",
            Comment = "",
        },
        [209] = {
            Name = "RemoveCrowdControl",
            Alias = "",
            Comment = "",
        },
        [210] = {
            Name = "ModifyAttribute",
            Alias = "",
            Comment = "",
        },
        [211] = {
            Name = "SetVariable",
            Alias = "",
            Comment = "",
        },
        [212] = {
            Name = "TacticFailed",
            Alias = "",
            Comment = "",
        },
        [213] = {
            Name = "TriggerBuffFailed",
            Alias = "",
            Comment = "",
        },
        [214] = {
            Name = "TacticPrepare",
            Alias = "",
            Comment = "",
        },
        [215] = {
            Name = "Combo",
            Alias = "",
            Comment = "",
        },
        [216] = {
            Name = "ComboFailed",
            Alias = "",
            Comment = "",
        },
        [217] = {
            Name = "Dodge",
            Alias = "",
            Comment = "",
        },
        [218] = {
            Name = "RefreshBuff",
            Alias = "",
            Comment = "",
        },
        [219] = {
            Name = "AttackVamp",
            Alias = "",
            Comment = "",
        },
        [220] = {
            Name = "IntelligenceVamp",
            Alias = "",
            Comment = "",
        },
        [221] = {
            Name = "Immune",
            Alias = "",
            Comment = "",
        },
        [222] = {
            Name = "ModifyBuff",
            Alias = "",
            Comment = "",
        },
        [223] = {
            Name = "TacticDisable",
            Alias = "",
            Comment = "",
        },
        [224] = {
            Name = "ModifyTactic",
            Alias = "",
            Comment = "",
        },
        [225] = {
            Name = "PlainAttackDisable",
            Alias = "",
            Comment = "",
        },
        [226] = {
            Name = "TacticAwakening",
            Alias = "",
            Comment = "",
        },
        [227] = {
            Name = "InterruptPrepare",
            Alias = "",
            Comment = "",
        },
        [228] = {
            Name = "Insight",
            Alias = "",
            Comment = "",
        },
        [229] = {
            Name = "CheckCrowdControl",
            Alias = "",
            Comment = "",
        },
        [230] = {
            Name = "ManuTriggerBuff",
            Alias = "",
            Comment = "",
        },
        [998] = {
            Name = "RecoverableToDeath",
            Alias = "",
            Comment = "",
        },
        [999] = {
            Name = "PlainAttackTarget",
            Alias = "",
            Comment = "",
        },
    },
    ECheckOpen = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "占位",
        },
        [1] = {
            Name = "Mail",
            Alias = "邮件",
            Comment = "开启邮件功能",
        },
    },
    ElementalType = {
        [0] = {
            Name = "Wind",
            Alias = "风",
            Comment = "风",
        },
        [1] = {
            Name = "Forest",
            Alias = "林",
            Comment = "林",
        },
        [2] = {
            Name = "Fire",
            Alias = "火",
            Comment = "火",
        },
        [3] = {
            Name = "Mountain",
            Alias = "山",
            Comment = "山",
        },
    },
    EquipEntriesType = {
        [0] = {
            Name = "None",
            Alias = "无词条",
            Comment = "无词条",
        },
        [1] = {
            Name = "Single",
            Alias = "单词条",
            Comment = "单词条",
        },
        [2] = {
            Name = "Double",
            Alias = "双词条",
            Comment = "双词条",
        },
        [3] = {
            Name = "ReverseDouble",
            Alias = "正负词条",
            Comment = "正负词条",
        },
    },
    EquipQuality = {
        [-1] = {
            Name = "None",
            Alias = "无",
            Comment = "无",
        },
        [1] = {
            Name = "Normal",
            Alias = "普通",
            Comment = "普通",
        },
        [2] = {
            Name = "Rare",
            Alias = "稀有",
            Comment = "稀有",
        },
        [3] = {
            Name = "Legend",
            Alias = "传说",
            Comment = "传说",
        },
        [4] = {
            Name = "Special",
            Alias = "专属",
            Comment = "专属",
        },
    },
    EquipType = {
        [1] = {
            Name = "Weapon",
            Alias = "武器",
            Comment = "武器",
        },
        [2] = {
            Name = "Defense",
            Alias = "防具",
            Comment = "防具",
        },
        [3] = {
            Name = "Horse",
            Alias = "坐骑",
            Comment = "坐骑",
        },
    },
    FlyStatusChange = {
        [1] = {
            Name = "ReachTop",
            Alias = "最高点",
            Comment = "最高点",
        },
        [2] = {
            Name = "LandGround",
            Alias = "落地",
            Comment = "落地",
        },
    },
    FuncOpenTrigger = {
        [1] = {
            Name = "MainCity",
            Alias = "城建",
            Comment = "城建",
        },
        [2] = {
            Name = "Test2",
            Alias = "测试2",
            Comment = "测试2",
        },
        [3] = {
            Name = "Max",
            Alias = "最大",
            Comment = "最大",
        },
    },
    GachaCntType = {
        [1] = {
            Name = "Single",
            Alias = "单抽",
            Comment = "单抽",
        },
        [2] = {
            Name = "Five",
            Alias = "五连抽",
            Comment = "五连抽",
        },
        [3] = {
            Name = "Twenty",
            Alias = "二十连抽",
            Comment = "二十连抽",
        },
    },
    GeneralArmyType = {
        [1] = {
            Name = "Shield",
            Alias = "盾",
            Comment = "盾兵",
        },
        [2] = {
            Name = "Cavalry",
            Alias = "骑",
            Comment = "骑兵",
        },
        [3] = {
            Name = "Pikemen",
            Alias = "枪",
            Comment = "枪兵",
        },
        [4] = {
            Name = "Archers",
            Alias = "弓",
            Comment = "弓兵",
        },
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无高级兵种",
        },
        [41] = {
            Name = "TongQueTaiLing",
            Alias = "铜雀台伶",
            Comment = "铜雀台伶",
        },
        [42] = {
            Name = "YuanRongNuBing",
            Alias = "元戎弩兵",
            Comment = "元戎弩兵",
        },
    },
    GeneralAttrType = {
        [1] = {
            Name = "WuLi",
            Alias = "武力",
            Comment = "武力",
        },
        [2] = {
            Name = "ZhiLi",
            Alias = "智力",
            Comment = "智力",
        },
        [4] = {
            Name = "TongShuai",
            Alias = "统率",
            Comment = "统率",
        },
        [5] = {
            Name = "XianGong",
            Alias = "速度",
            Comment = "速度",
        },
    },
    GeneralDynasty = {
        [1] = {
            Name = "Wei",
            Alias = "魏国",
            Comment = "魏国",
        },
        [2] = {
            Name = "Shu",
            Alias = "蜀国",
            Comment = "蜀国",
        },
        [3] = {
            Name = "Wu",
            Alias = "吴国",
            Comment = "吴国",
        },
        [4] = {
            Name = "Qun",
            Alias = "群雄",
            Comment = "群雄",
        },
    },
    GeneralRoleType = {
        [1] = {
            Name = "BingRen",
            Alias = "兵刃",
            Comment = "兵刃",
        },
        [2] = {
            Name = "MouLue",
            Alias = "谋略",
            Comment = "谋略",
        },
        [3] = {
            Name = "WenWu",
            Alias = "文武",
            Comment = "文武",
        },
        [4] = {
            Name = "FuZhu",
            Alias = "辅助",
            Comment = "辅助",
        },
        [5] = {
            Name = "ZhiLiao",
            Alias = "治疗",
            Comment = "治疗",
        },
        [6] = {
            Name = "FangYu",
            Alias = "防御",
            Comment = "防御",
        },
    },
    GMEvent = {
        [1] = {
            Name = "ApplyClan",
            Alias = "申请世族",
            Comment = "申请世族",
        },
        [2] = {
            Name = "LeaveClan",
            Alias = "离开世族",
            Comment = "离开世族",
        },
        [3] = {
            Name = "OccupyLand",
            Alias = "占领地块",
            Comment = "占领地块",
        },
        [4] = {
            Name = "TransferArmy",
            Alias = "转移部队",
            Comment = "转移部队",
        },
        [5] = {
            Name = "SetAnnounceTimeout",
            Alias = "设置宣战倒计时",
            Comment = "设置宣战倒计时",
        },
    },
    HealFormulaArgs = {
        [1] = {
            Name = "A",
            Alias = "施法者智力值",
            Comment = "施法者智力值",
        },
        [2] = {
            Name = "B",
            Alias = "战法恢复系数",
            Comment = "战法恢复系数",
        },
        [3] = {
            Name = "C",
            Alias = "恢复效果提升",
            Comment = "恢复效果提升",
        },
        [4] = {
            Name = "D",
            Alias = "兵力系数",
            Comment = "兵力系数",
        },
        [5] = {
            Name = "D1",
            Alias = "常量<D1>",
            Comment = "常量<D1>",
        },
        [6] = {
            Name = "M",
            Alias = "常量<M>",
            Comment = "常量<M>",
        },
    },
    HudLayer = {
        [0] = {
            Name = "GeographySecondary",
            Alias = "次要地理信息",
            Comment = "次要地理信息",
        },
        [1] = {
            Name = "Geography",
            Alias = "地理信息",
            Comment = "地理信息",
        },
        [2] = {
            Name = "Building",
            Alias = "建筑",
            Comment = "建筑",
        },
        [3] = {
            Name = "Army",
            Alias = "军队",
            Comment = "军队",
        },
        [4] = {
            Name = "SelfArmy",
            Alias = "玩家军队",
            Comment = "玩家军队",
        },
        [5] = {
            Name = "Battle",
            Alias = "战斗",
            Comment = "战斗",
        },
        [6] = {
            Name = "Interactive",
            Alias = "交互界面",
            Comment = "交互界面",
        },
        [7] = {
            Name = "Always",
            Alias = "常显",
            Comment = "常显",
        },
    },
    IdSegType = {
        [1] = {
            Name = "Currency",
            Alias = "货币",
            Comment = "货币",
        },
        [2] = {
            Name = "Item",
            Alias = "道具",
            Comment = "道具",
        },
        [3] = {
            Name = "Equip",
            Alias = "装备",
            Comment = "装备",
        },
        [4] = {
            Name = "Horse",
            Alias = "坐骑",
            Comment = "坐骑",
        },
        [5] = {
            Name = "Token",
            Alias = "信物",
            Comment = "信物",
        },
        [6] = {
            Name = "Material",
            Alias = "材料",
            Comment = "材料",
        },
        [7] = {
            Name = "EffectItem",
            Alias = "立刻生效道具",
            Comment = "立刻生效道具",
        },
        [8] = {
            Name = "Hero",
            Alias = "武将",
            Comment = "武将",
        },
        [9] = {
            Name = "BattleTactic",
            Alias = "战法",
            Comment = "战法",
        },
    },
    InConType = {
        [101] = {
            Name = "InBase",
            Alias = "在基地",
            Comment = "部队在回城点",
        },
        [102] = {
            Name = "InDefend",
            Alias = "在驻守",
            Comment = "驻守容器中",
        },
        [103] = {
            Name = "InVehicle",
            Alias = "操控器械",
            Comment = "部队操控器械中",
        },
        [104] = {
            Name = "Annex",
            Alias = "附属建筑",
            Comment = "城池的附属建筑",
        },
    },
    InstantTacticAttributeType = {
        [1] = {
            Name = "LeftCD",
            Alias = "剩余CD",
            Comment = "剩余CD",
        },
        [2] = {
            Name = "PrepareStep",
            Alias = "准备回合",
            Comment = "准备回合",
        },
        [3] = {
            Name = "ExtraProb",
            Alias = "额外发动率",
            Comment = "额外发动率",
        },
    },
    InteractiveBehavior = {
        [1] = {
            Name = "Attack",
            Alias = "攻击",
            Comment = "A攻击B",
        },
        [2] = {
            Name = "Defend",
            Alias = "驻守",
            Comment = "A驻守B",
        },
        [3] = {
            Name = "Occupy",
            Alias = "攻占",
            Comment = "A攻占B地块",
        },
        [4] = {
            Name = "Raid",
            Alias = "扫荡",
            Comment = "A扫荡B地块",
        },
        [5] = {
            Name = "Exploit",
            Alias = "开发",
            Comment = "A开发B地块",
        },
        [6] = {
            Name = "ThrowStone",
            Alias = "投石",
            Comment = "A向B投石",
        },
    },
    JumpTrigger = {
        [1] = {
            Name = "OpenUpBuildingLevelWindow",
            Alias = "建筑升级界面",
            Comment = "打开建筑升级界面",
        },
        [2] = {
            Name = "OpenTechWindow",
            Alias = "太学界面",
            Comment = "打开太学界面",
        },
        [3] = {
            Name = "JumpToBuild",
            Alias = "定位到指定建筑",
            Comment = "定位到指定建筑",
        },
        [4] = {
            Name = "UISmithyMainMakeNew",
            Alias = "铁匠铺打造界面",
            Comment = "铁匠铺打造界面",
        },
        [5] = {
            Name = "UISmithyMainMakeBetter",
            Alias = "铁匠铺锻造界面",
            Comment = "铁匠铺锻造界面",
        },
        [6] = {
            Name = "UISmithyMainReforge",
            Alias = "铁匠铺重塑界面",
            Comment = "铁匠铺重塑界面",
        },
        [7] = {
            Name = "UITrainingHorse",
            Alias = "御马苑界面",
            Comment = "御马苑界面",
        },
    },
    LandBuffType = {
        [101] = {
            Name = "AddProduct",
            Alias = "增加产量",
            Comment = "增加产量",
        },
    },
    LandMgrSortType = {
        [1] = {
            Name = "ResourceLand",
            Alias = "资源地",
            Comment = "资源地",
        },
        [2] = {
            Name = "RoadLand",
            Alias = "道路地",
            Comment = "道路地",
        },
        [3] = {
            Name = "Building",
            Alias = "建筑地",
            Comment = "建筑地",
        },
        [4] = {
            Name = "Favorite",
            Alias = "收藏地",
            Comment = "收藏地",
        },
    },
    MailType = {
        [1] = {
            Name = "AllyOrder",
            Alias = "法令",
            Comment = "法令",
        },
        [2] = {
            Name = "Ally",
            Alias = "同盟",
            Comment = "同盟",
        },
        [3] = {
            Name = "AllyGroup",
            Alias = "分组",
            Comment = "分组",
        },
        [4] = {
            Name = "System",
            Alias = "系统",
            Comment = "系统",
        },
        [5] = {
            Name = "Announce",
            Alias = "公告",
            Comment = "公告",
        },
    },
    MapViewLevel = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无",
        },
        [1] = {
            Name = "Detail",
            Alias = "详细视图",
            Comment = "最详细的视图层级，显示城池模型、兵模树木等",
        },
        [2] = {
            Name = "Simplified",
            Alias = "缩略视图",
            Comment = "显示大城模型，隐藏树木、兵模等细节，其他建筑以缩略图显示",
        },
        [4] = {
            Name = "Brief",
            Alias = "简化视图",
            Comment = "显示大城与玩家主城的缩略模型，其他信息以更简略的形式(譬如小圆点)展示",
        },
        [8] = {
            Name = "Strategic",
            Alias = "战略视图",
            Comment = "大城模型进一步缩略，同时展示沙盘染色",
        },
        [16] = {
            Name = "Overview",
            Alias = "全景视图",
            Comment = "最简略的视图层级，完全隐藏分块地形，使用平面3d沙盘代替",
        },
    },
    MoveType = {
        [1] = {
            Name = "Default",
            Alias = "默认",
            Comment = "默认",
        },
        [2] = {
            Name = "Return",
            Alias = "返回",
            Comment = "主动回城",
            Tags = {
                IGNORE_OBSTACLE = "IGNORE_OBSTACLE",
                IGNORE_BUFF = "IGNORE_BUFF",
                IGNORE_DEC_SUPPLY = "IGNORE_DEC_SUPPLY",
            },
        },
        [3] = {
            Name = "Escape",
            Alias = "溃逃",
            Comment = "战败溃逃",
            Tags = {
                IGNORE_OBSTACLE = "IGNORE_OBSTACLE",
                IGNORE_BUFF = "IGNORE_BUFF",
                IGNORE_DEC_SUPPLY = "IGNORE_DEC_SUPPLY",
            },
        },
        [4] = {
            Name = "Deploy",
            Alias = "调动",
            Comment = "调动",
            Tags = {
                IGNORE_DEC_SUPPLY = "IGNORE_DEC_SUPPLY",
            },
        },
    },
    MsgType = {
        [1] = {
            Name = "Normal",
            Alias = "默认",
            Comment = "默认",
        },
        [2] = {
            Name = "Invalid",
            Alias = "无效提示",
            Comment = "无效提示",
        },
    },
    NewbeeGuideEventType = {
        [1] = {
            Name = "PV",
            Alias = "PV内容",
            Comment = "PV内容",
        },
        [2] = {
            Name = "Dialogue",
            Alias = "剧情对话",
            Comment = "剧情对话",
        },
        [3] = {
            Name = "CreateId",
            Alias = "创建ID",
            Comment = "创建ID",
        },
        [4] = {
            Name = "ClickOp",
            Alias = "点击操作指引",
            Comment = "点击操作指引",
        },
        [5] = {
            Name = "ClickCheck",
            Alias = "点击查看指引",
            Comment = "点击查看指引",
        },
        [6] = {
            Name = "MoveCheck",
            Alias = "拖动查看指引",
            Comment = "拖动查看指引",
        },
    },
    NodeTaskStatus = {
        [1] = {
            Name = "Default",
            Alias = "默认",
            Comment = "默认",
        },
        [2] = {
            Name = "Running",
            Alias = "运行时",
            Comment = "运行时",
        },
    },
    NPCRandomPool = {
        [1] = {
            Name = "WorldLand",
            Alias = "大世界地块",
            Comment = "第一个按当前日期计算出，后续纯随机",
        },
        [2] = {
            Name = "FullRandom",
            Alias = "完全随机",
            Comment = "完全随机",
        },
    },
    ObstacleType = {
        [0] = {
            Name = "Land",
            Alias = "空地",
            Comment = "空地",
        },
        [1] = {
            Name = "Obstacle",
            Alias = "阻挡",
            Comment = "阻挡",
        },
        [2] = {
            Name = "DynamicObstacle",
            Alias = "动态阻挡",
            Comment = "动态阻挡",
        },
    },
    OwnedStatus = {
        [1] = {
            Name = "Owned",
            Alias = "已拥有",
            Comment = "已拥有",
        },
        [2] = {
            Name = "NotOwned",
            Alias = "未拥有",
            Comment = "未拥有",
        },
    },
    PeriodicType = {
        [1] = {
            Name = "Total",
            Alias = "总共",
            Comment = "总共",
        },
        [2] = {
            Name = "Daily",
            Alias = "每日",
            Comment = "每日",
        },
        [3] = {
            Name = "Weekly",
            Alias = "每周",
            Comment = "每周",
        },
    },
    Position = {
        [1] = {
            Name = "Captain",
            Alias = "主公",
            Comment = "1号位是主公",
        },
        [2] = {
            Name = "Pioneer",
            Alias = "先锋",
            Comment = "2号位是先锋",
        },
        [3] = {
            Name = "Adviser",
            Alias = "军师",
            Comment = "3号位是军师",
        },
    },
    PositiveEffectType = {
        [1] = {
            Name = "Insight",
            Alias = "洞察",
            Comment = "洞察",
        },
        [2] = {
            Name = "Fast",
            Alias = "先机",
            Comment = "先机",
        },
        [3] = {
            Name = "Inevitable",
            Alias = "必中",
            Comment = "必中",
        },
        [4] = {
            Name = "Agile",
            Alias = "机敏",
            Comment = "机敏",
        },
        [5] = {
            Name = "Healer",
            Alias = "神医",
            Comment = "神医",
        },
        [6] = {
            Name = "AvoidDeath",
            Alias = "免死",
            Comment = "免死",
        },
    },
    ProduceSrcType = {
        [1] = {
            Name = "WorldLand",
            Alias = "大世界地块",
            Comment = "大世界地块",
        },
        [2] = {
            Name = "HomeBuilding",
            Alias = "内城建筑",
            Comment = "内城建筑",
        },
        [3] = {
            Name = "CityLand",
            Alias = "城池开垦地",
            Comment = "城池开垦地",
        },
        [4] = {
            Name = "Farmer",
            Alias = "农民",
            Comment = "农民",
        },
        [5] = {
            Name = "Else",
            Alias = "其他",
            Comment = "其他",
        },
        [6] = {
            Name = "ClanBuff",
            Alias = "世族加成",
            Comment = "世族加成",
        },
        [7] = {
            Name = "City",
            Alias = "城池",
            Comment = "城池",
        },
    },
    ProsperitySrc = {
        [1] = {
            Name = "HomeBuilding",
            Alias = "内城建筑",
            Comment = "内城建筑",
        },
        [2] = {
            Name = "HomeTech",
            Alias = "内城科技",
            Comment = "内城科技",
        },
        [3] = {
            Name = "WorldLand",
            Alias = "沙盘地块",
            Comment = "沙盘地块",
        },
        [4] = {
            Name = "WorldBuilding",
            Alias = "沙盘建筑",
            Comment = "沙盘建筑",
        },
    },
    PurseType = {
        [0] = {
            Name = "Normal",
            Alias = "普通",
            Comment = "普通",
        },
        [1] = {
            Name = "Extra",
            Alias = "启动",
            Comment = "启动",
        },
    },
    QualificationRank = {
        [0] = {
            Name = "C",
            Alias = "拙",
            Comment = "拙",
        },
        [1] = {
            Name = "B",
            Alias = "常",
            Comment = "常",
        },
        [2] = {
            Name = "A",
            Alias = "精",
            Comment = "精",
        },
        [3] = {
            Name = "S",
            Alias = "极",
            Comment = "极",
        },
    },
    Quality = {
        [1] = {
            Name = "Blue",
            Alias = "蓝",
            Comment = "蓝",
        },
        [2] = {
            Name = "Violet",
            Alias = "紫",
            Comment = "紫",
        },
        [3] = {
            Name = "Gold",
            Alias = "金",
            Comment = "金",
        },
        [4] = {
            Name = "Red",
            Alias = "红",
            Comment = "红",
        },
    },
    RankType = {
        [1] = {
            Name = "Personal",
            Alias = "个人霸业积分",
            Comment = "个人霸业积分",
        },
        [2] = {
            Name = "Ally",
            Alias = "联盟霸业积分",
            Comment = "联盟霸业积分",
        },
        [3] = {
            Name = "Prosperity",
            Alias = "个人繁荣榜",
            Comment = "个人繁荣榜",
        },
        [4] = {
            Name = "Merit",
            Alias = "个人武勋榜",
            Comment = "个人武勋榜",
        },
        [5] = {
            Name = "MeritWeek",
            Alias = "个人武勋周榜",
            Comment = "个人武勋周榜",
        },
        [6] = {
            Name = "FirstOccupy",
            Alias = "个人首占榜",
            Comment = "个人首占榜",
        },
        [7] = {
            Name = "ClanProsperity",
            Alias = "世族繁荣榜",
            Comment = "世族繁荣榜",
        },
        [8] = {
            Name = "ClanMerit",
            Alias = "世族武勋榜",
            Comment = "世族武勋榜",
        },
        [9] = {
            Name = "ClanMeritWeek",
            Alias = "世族武勋周榜",
            Comment = "世族武勋周榜",
        },
    },
    Reason = {
        [0] = {
            Name = "GM",
            Alias = "管理员",
            Comment = "管理员",
            Tags = {
                overflow = "overflow",
            },
        },
        [101] = {
            Name = "MODIFY_ARMY",
            Alias = "调整部队",
            Comment = "调整部队",
        },
        [102] = {
            Name = "RESET_TACTIC",
            Alias = "重置战法",
            Comment = "重置战法",
        },
        [103] = {
            Name = "UPGRADE_TACTIC",
            Alias = "升级战法",
            Comment = "升级战法",
        },
        [104] = {
            Name = "UPGRADE_HERO_STAR",
            Alias = "武将升星",
            Comment = "武将升星",
        },
        [105] = {
            Name = "UPGRADE_TACTIC_STAR",
            Alias = "战法升星",
            Comment = "战法升星",
        },
        [106] = {
            Name = "CHOOSE_HERO_IDENTITY",
            Alias = "武将身份演化选择",
            Comment = "武将身份演化选择",
        },
        [201] = {
            Name = "DECOMPOSE_TOKEN",
            Alias = "分解信物",
            Comment = "分解信物",
            Tags = {
                notify = "notify",
            },
        },
        [202] = {
            Name = "DECOMPOSE_EQUIP",
            Alias = "分解装备",
            Comment = "分解装备",
            Tags = {
                notify = "notify",
            },
        },
        [203] = {
            Name = "FORGE_EQUIP",
            Alias = "锻造装备",
            Comment = "锻造装备",
        },
        [204] = {
            Name = "REBUILD_EQUIP",
            Alias = "重塑装备",
            Comment = "重塑装备",
        },
        [205] = {
            Name = "BUILD_EQUIP",
            Alias = "打造装备",
            Comment = "打造装备",
        },
        [206] = {
            Name = "TRAIN_HORSE",
            Alias = "驯马",
            Comment = "驯马",
        },
        [207] = {
            Name = "SOLD_HORSE",
            Alias = "出售坐骑",
            Comment = "出售坐骑",
            Tags = {
                notify = "notify",
            },
        },
        [301] = {
            Name = "JI_DIAN_REWARD",
            Alias = "稷神祭典奖励",
            Comment = "稷神祭典奖励",
        },
        [302] = {
            Name = "WO_ZHONG_SHENG_YOU",
            Alias = "无中生有",
            Comment = "无中生有",
        },
        [401] = {
            Name = "USE_ITEM",
            Alias = "使用道具",
            Comment = "使用道具",
            Tags = {
                notify = "notify",
            },
        },
        [402] = {
            Name = "MAIL_REWARD",
            Alias = "领取邮件",
            Comment = "领取邮件",
        },
        [403] = {
            Name = "REFRESH_SHOP",
            Alias = "刷新商店",
            Comment = "刷新商店",
        },
        [404] = {
            Name = "BUY_GOODS",
            Alias = "购买货物",
            Comment = "购买货物",
            Tags = {
                notify = "notify",
            },
        },
        [405] = {
            Name = "RES_TRADE",
            Alias = "资源贸易",
            Comment = "资源贸易",
            Tags = {
                notify = "notify",
            },
        },
        [501] = {
            Name = "TASK_REWARD",
            Alias = "任务奖励",
            Comment = "任务奖励",
            Tags = {
                overflow = "overflow",
                notify = "notify",
            },
        },
        [601] = {
            Name = "HOME_BUILDING",
            Alias = "内城建造",
            Comment = "内城建造",
        },
        [602] = {
            Name = "UPGRADE_HOME_TECH",
            Alias = "升级内城科技",
            Comment = "升级内城科技",
        },
        [603] = {
            Name = "PRODUCE",
            Alias = "产出",
            Comment = "产出",
        },
        [701] = {
            Name = "AMBITIONS_REWARD",
            Alias = "霸业阶段奖励",
            Comment = "霸业阶段奖励",
            Tags = {
                notify = "notify",
            },
        },
        [801] = {
            Name = "DRILL_LEVEL_REWARD",
            Alias = "军演关卡奖励",
            Comment = "军演关卡奖励",
        },
        [802] = {
            Name = "DRILL_CHAPTER_REWARD",
            Alias = "军演章节奖励",
            Comment = "军演章节奖励",
            Tags = {
                notify = "notify",
            },
        },
        [803] = {
            Name = "DRILL_HANGING_REWARD",
            Alias = "军演挂机奖励",
            Comment = "军演挂机奖励",
            Tags = {
                notify = "notify",
            },
        },
        [901] = {
            Name = "GACHA",
            Alias = "抽卡",
            Comment = "抽卡",
        },
        [1001] = {
            Name = "CLAN_DONATE",
            Alias = "世族捐赠",
            Comment = "世族捐赠",
        },
        [1002] = {
            Name = "CLAN_CREATE",
            Alias = "创建世族",
            Comment = "创建世族",
        },
        [1101] = {
            Name = "PAY",
            Alias = "支付",
            Comment = "支付",
            Tags = {
                notify = "notify",
            },
        },
        [1201] = {
            Name = "NEWBEE",
            Alias = "新手",
            Comment = "新手",
        },
        [1300] = {
            Name = "BUILD_WORLD_BUILDING",
            Alias = "建造大世界建筑",
            Comment = "建造大世界建筑",
        },
        [1301] = {
            Name = "UPGRADE_WORLD_BUILDING",
            Alias = "升级大世界建筑",
            Comment = "升级大世界建筑",
        },
        [1302] = {
            Name = "BUILD_ALLY_WORLD_BUILDING",
            Alias = "建造联盟大世界建筑",
            Comment = "建造联盟大世界建筑",
        },
        [1303] = {
            Name = "UPGRADE_ALLY_WORLD_BUILDING",
            Alias = "升级联盟大世界建筑",
            Comment = "升级联盟大世界建筑",
        },
    },
    RelationShip = {
        [0] = {
            Name = "Self",
            Alias = "自己",
            Comment = "自己",
        },
        [1] = {
            Name = "SameAlly",
            Alias = "盟友",
            Comment = "盟友",
        },
        [2] = {
            Name = "FreeMan",
            Alias = "散人",
            Comment = "散人",
        },
        [3] = {
            Name = "DiffAlly",
            Alias = "不同联盟",
            Comment = "不同联盟",
        },
        [4] = {
            Name = "Monster",
            Alias = "野怪",
            Comment = "野怪",
        },
        [5] = {
            Name = "SameClan",
            Alias = "族友",
            Comment = "族友",
        },
    },
    ReportReason = {
        [1] = {
            Name = "spam",
            Alias = "垃圾信息",
            Comment = "垃圾信息",
        },
        [2] = {
            Name = "cheating",
            Alias = "举报外挂",
            Comment = "举报外挂",
        },
        [3] = {
            Name = "inappropriate",
            Alias = "不良发言",
            Comment = "不良发言",
        },
        [4] = {
            Name = "illegal",
            Alias = "不法行为",
            Comment = "不法行为",
        },
        [5] = {
            Name = "nickname",
            Alias = "不雅昵称",
            Comment = "不雅昵称",
        },
        [6] = {
            Name = "advertising",
            Alias = "广告拉人",
            Comment = "广告拉人",
        },
        [7] = {
            Name = "others",
            Alias = "举报其他",
            Comment = "举报其他",
        },
    },
    Role = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无",
        },
        [1] = {
            Name = "Lord",
            Alias = "主公",
            Comment = "主公",
        },
        [2] = {
            Name = "Loyalist",
            Alias = "忠臣",
            Comment = "忠臣",
        },
        [3] = {
            Name = "Rebel",
            Alias = "反贼",
            Comment = "反贼",
        },
        [4] = {
            Name = "Traitor",
            Alias = "内奸",
            Comment = "内奸",
        },
    },
    S1Camp = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无",
        },
        [1] = {
            Name = "Lord",
            Alias = "主公阵营",
            Comment = "主公阵营",
        },
        [2] = {
            Name = "Rebel",
            Alias = "反贼阵营",
            Comment = "反贼阵营",
        },
        [3] = {
            Name = "Traitor",
            Alias = "内奸阵营",
            Comment = "内奸阵营",
        },
    },
    SAvatarEvent = {
        [0] = {
            Name = "DEFAULT",
            Alias = "空",
            Comment = "空",
        },
        [10001] = {
            Name = "PROSPERITY_CHANGE",
            Alias = "繁荣度变化",
            Comment = "繁荣度变化",
        },
        [10002] = {
            Name = "HOME_BUILDING_UPGRADE",
            Alias = "建筑升级建造",
            Comment = "建筑升级建造",
        },
        [10003] = {
            Name = "RES_PRODUCE_SPEED_CHANGE",
            Alias = "资源产量变化",
            Comment = "资源产量变化",
        },
        [10004] = {
            Name = "HOME_TECH_UPGRADE",
            Alias = "内城科技升级解锁",
            Comment = "内城科技升级解锁",
        },
        [10005] = {
            Name = "HERO_LEVE_UPGRADE",
            Alias = "武将升级",
            Comment = "武将升级",
        },
        [10006] = {
            Name = "ARMY_SOLDIER_CHANGE",
            Alias = "部队兵力变化",
            Comment = "部队兵力变化",
        },
        [10007] = {
            Name = "GET_NEW_TACTIC",
            Alias = "获得战法",
            Comment = "获得战法",
        },
        [10008] = {
            Name = "HOME_FARM_UPGRADE",
            Alias = "开垦地升级",
            Comment = "开垦地升级",
        },
        [10009] = {
            Name = "NEW_RES_LAND",
            Alias = "获得资源地",
            Comment = "获得资源地",
        },
        [10010] = {
            Name = "UNLOCK_TALENT",
            Alias = "解锁天赋",
            Comment = "解锁天赋",
        },
        [10011] = {
            Name = "ADD_COIN",
            Alias = "获得货币",
            Comment = "获得货币",
        },
        [10012] = {
            Name = "DEC_COIN",
            Alias = "减少货币",
            Comment = "减少货币",
        },
        [10013] = {
            Name = "TASK_COMPLETE",
            Alias = "任务完成",
            Comment = "任务完成",
        },
        [10014] = {
            Name = "COIN_LIMIT_CHANGE",
            Alias = "货币上限变动",
            Comment = "货币上限变动",
        },
        [10015] = {
            Name = "FARMER_WORK",
            Alias = "分配居民",
            Comment = "分配居民",
        },
        [10016] = {
            Name = "OCCUPY_LAND",
            Alias = "占领城外土地",
            Comment = "占领城外土地",
        },
        [10017] = {
            Name = "GACHA_DRAW",
            Alias = "寻访",
            Comment = "寻访",
        },
        [10018] = {
            Name = "CHALLENGE_DRILL",
            Alias = "军演挑战",
            Comment = "军演挑战",
        },
        [10019] = {
            Name = "HERO_EQUIP_TACTIC",
            Alias = "武将学习战法",
            Comment = "武将学习战法",
        },
        [10020] = {
            Name = "BORN_WITH_TACTIC_LEVEL_UP",
            Alias = "自带战法升级",
            Comment = "自带战法升级",
        },
        [10021] = {
            Name = "SWITCH_ARMY_UNIT",
            Alias = "交换武将",
            Comment = "交换武将",
        },
        [10022] = {
            Name = "DEL_ARMY_UNIT",
            Alias = "下阵武将",
            Comment = "下阵武将",
        },
        [10023] = {
            Name = "ADD_ARMY_UNIT",
            Alias = "上阵武将",
            Comment = "上阵武将",
        },
        [10024] = {
            Name = "ARMY_SOLDIER_MAX_CHANGE",
            Alias = "部队兵力上限变化",
            Comment = "部队兵力上限变化",
        },
    },
    ScheduleType = {
        [1] = {
            Name = "CommonDaily",
            Alias = "日常刷天",
            Comment = "日常刷天",
        },
        [2] = {
            Name = "AmbitionsDaily",
            Alias = "霸业刷天",
            Comment = "霸业刷天",
        },
        [3] = {
            Name = "Nigth",
            Alias = "夜间",
            Comment = "夜间",
        },
        [4] = {
            Name = "CommonWeek",
            Alias = "日常刷周",
            Comment = "日常刷周",
        },
        [5] = {
            Name = "SeasonTest",
            Alias = "测试赛季",
            Comment = "测试赛季",
        },
        [6] = {
            Name = "Newbie",
            Alias = "玩家新手活动示例",
            Comment = "玩家新手活动示例",
        },
    },
    SelectChangeType = {
        [-2] = {
            Name = "SELECT_ALL",
            Alias = "全选",
            Comment = "全选",
        },
        [-1] = {
            Name = "CANCEL_ALL",
            Alias = "重置",
            Comment = "重置",
        },
        [-4] = {
            Name = "NO_CHANGE",
            Alias = "不变",
            Comment = "不变",
        },
    },
    Settings = {
        [1] = {
            Name = "Null",
            Alias = "无",
            Comment = "无",
        },
        [101] = {
            Name = "GlobalVolumn",
            Alias = "总音量",
            Comment = "总音量",
        },
        [102] = {
            Name = "BackgroundVolumn",
            Alias = "背景音量",
            Comment = "背景音量",
        },
        [103] = {
            Name = "SoundFXVolumn",
            Alias = "音效音量",
            Comment = "音效音量",
        },
        [104] = {
            Name = "CharacterVolumn",
            Alias = "角色配音",
            Comment = "角色配音",
        },
        [201] = {
            Name = "ImageQuality",
            Alias = "画面质量",
            Comment = "画面质量",
        },
        [202] = {
            Name = "FrameRate",
            Alias = "帧率",
            Comment = "帧率",
        },
        [203] = {
            Name = "Weather",
            Alias = "天气与昼夜效果",
            Comment = "天气与昼夜效果",
        },
        [211] = {
            Name = "GridOutLine",
            Alias = "地块描边",
            Comment = "地块描边",
        },
        [212] = {
            Name = "ColorAssist",
            Alias = "色彩辅助模式",
            Comment = "色彩辅助模式",
        },
        [213] = {
            Name = "SandboxColorFill",
            Alias = "沙盘染色",
            Comment = "沙盘染色",
        },
        [214] = {
            Name = "ColorAtmosphere",
            Alias = "色彩氛围",
            Comment = "色彩氛围",
        },
        [221] = {
            Name = "Layout",
            Alias = "横竖屏",
            Comment = "横竖屏",
        },
        [301] = {
            Name = "GridVisualization",
            Alias = "地块标识",
            Comment = "地块标识",
        },
        [302] = {
            Name = "ScreenFit",
            Alias = "屏幕自适应",
            Comment = "屏幕自适应",
        },
        [401] = {
            Name = "AddStranger",
            Alias = "允许陌生人加好友",
            Comment = "允许陌生人加好友",
        },
        [411] = {
            Name = "LocationRight",
            Alias = "定位权限",
            Comment = "定位权限",
        },
        [412] = {
            Name = "PushRight",
            Alias = "推送权限",
            Comment = "推送权限",
        },
        [413] = {
            Name = "tempRight",
            Alias = "权限名称六字",
            Comment = "权限名称六字",
        },
    },
    SettingType = {
        [1] = {
            Name = "Toggle",
            Alias = "单个开关",
            Comment = "单个开关",
        },
        [2] = {
            Name = "EnumList",
            Alias = "枚举列表",
            Comment = "枚举列表",
        },
        [3] = {
            Name = "Slider",
            Alias = "滑动条",
            Comment = "滑动条",
        },
        [4] = {
            Name = "ImgEnumList",
            Alias = "图像枚举",
            Comment = "图像枚举",
        },
        [5] = {
            Name = "Button",
            Alias = "按钮",
            Comment = "按钮",
        },
        [12] = {
            Name = "ToggleAndSlider",
            Alias = "开关与滑动条",
            Comment = "开关与滑动条",
        },
    },
    ShopType = {
        [1] = {
            Name = "DailyShop",
            Alias = "日常商店",
            Comment = "日常商店",
        },
        [2] = {
            Name = "MeritShop",
            Alias = "武勋商店",
            Comment = "武勋商店",
        },
    },
    AdministrativeHierarchy = {
        [1] = {
            Name = "County",
            Alias = "县",
            Comment = "县",
        },
        [2] = {
            Name = "Commandery",
            Alias = "郡",
            Comment = "郡",
        },
        [3] = {
            Name = "Prefecture",
            Alias = "州",
            Comment = "州",
        },
    },
    MapElementLevelMask = {
        [1] = {
            Name = "L0",
            Alias = "等级0",
            Comment = "等级0",
        },
        [2] = {
            Name = "L1",
            Alias = "等级1",
            Comment = "等级1",
        },
        [4] = {
            Name = "L2",
            Alias = "等级2",
            Comment = "等级2",
        },
        [8] = {
            Name = "L3",
            Alias = "等级3",
            Comment = "等级3",
        },
        [16] = {
            Name = "L4",
            Alias = "等级4",
            Comment = "等级4",
        },
        [32] = {
            Name = "L5",
            Alias = "等级5",
            Comment = "等级5",
        },
        [64] = {
            Name = "L6",
            Alias = "等级6",
            Comment = "等级6",
        },
        [128] = {
            Name = "L7",
            Alias = "等级7",
            Comment = "等级7",
        },
        [256] = {
            Name = "L8",
            Alias = "等级8",
            Comment = "等级8",
        },
        [512] = {
            Name = "L9",
            Alias = "等级9",
            Comment = "等级9",
        },
        [1024] = {
            Name = "L10",
            Alias = "等级10",
            Comment = "等级10",
        },
        [2048] = {
            Name = "L11",
            Alias = "等级11",
            Comment = "等级11",
        },
        [4096] = {
            Name = "L12",
            Alias = "等级12",
            Comment = "等级12",
        },
    },
    MapElementType = {
        [0] = {
            Name = "Empty",
            Alias = "空地",
            Comment = "空地",
        },
        [1] = {
            Name = "Mountain",
            Alias = "山",
            Comment = "山",
        },
        [2] = {
            Name = "River",
            Alias = "河",
            Comment = "河",
        },
        [3] = {
            Name = "Food",
            Alias = "粮食",
            Comment = "粮食",
        },
        [4] = {
            Name = "Stone",
            Alias = "石料",
            Comment = "石料",
        },
        [5] = {
            Name = "Wood",
            Alias = "木材",
            Comment = "木材",
        },
        [6] = {
            Name = "Iron",
            Alias = "铁矿",
            Comment = "铁矿",
        },
        [7] = {
            Name = "Copper",
            Alias = "铜矿",
            Comment = "铜矿",
        },
        [8] = {
            Name = "DevelopableResourceNode",
            Alias = "开垦地",
            Comment = "开垦地",
        },
        [9] = {
            Name = "FortifiedCity",
            Alias = "主城府",
            Comment = "主城府",
        },
        [26] = {
            Name = "Riverway",
            Alias = "河道",
            Comment = "河道",
        },
        [27] = {
            Name = "Obstacle",
            Alias = "障碍",
            Comment = "障碍",
        },
        [100] = {
            Name = "CityWall",
            Alias = "城墙",
            Comment = "城墙",
        },
        [101] = {
            Name = "InnerRoad",
            Alias = "城内道路",
            Comment = "城内道路",
        },
        [102] = {
            Name = "CityGate",
            Alias = "城门",
            Comment = "城门",
        },
        [103] = {
            Name = "InnerCityGate",
            Alias = "内城门",
            Comment = "内城门",
        },
        [104] = {
            Name = "CityBarracks",
            Alias = "城内兵营",
            Comment = "城内兵营",
        },
        [114] = {
            Name = "SiegeCampFoundation",
            Alias = "攻城地基",
            Comment = "攻城地基",
        },
        [115] = {
            Name = "SiegeCampFoundationCenter",
            Alias = "攻城地基中心",
            Comment = "攻城地基中心",
        },
        [150] = {
            Name = "Ferry",
            Alias = "渡口",
            Comment = "渡口",
        },
        [151] = {
            Name = "FerryyPavilion",
            Alias = "渡亭",
            Comment = "渡亭",
        },
        [200] = {
            Name = "FrontierPass",
            Alias = "关堡",
            Comment = "关堡",
        },
        [201] = {
            Name = "FrontierPassWall",
            Alias = "关墙",
            Comment = "关墙",
        },
        [5000] = {
            Name = "Camp",
            Alias = "营帐",
            Comment = "营帐",
        },
        [5010] = {
            Name = "MainCity",
            Alias = "玩家主城",
            Comment = "玩家主城",
        },
        [5020] = {
            Name = "SiegeCamp",
            Alias = "攻城大营",
            Comment = "攻城大营",
        },
    },
    MapElementTypeMask = {
        [1] = {
            Name = "Mountain",
            Alias = "山",
            Comment = "山",
        },
        [2] = {
            Name = "River",
            Alias = "河",
            Comment = "河",
        },
        [4] = {
            Name = "Food",
            Alias = "粮食",
            Comment = "粮食",
        },
        [8] = {
            Name = "Stone",
            Alias = "石料",
            Comment = "石料",
        },
        [16] = {
            Name = "Wood",
            Alias = "木材",
            Comment = "木材",
        },
        [32] = {
            Name = "Iron",
            Alias = "铁矿",
            Comment = "铁矿",
        },
        [64] = {
            Name = "Copper",
            Alias = "铜矿",
            Comment = "铜矿",
        },
        [128] = {
            Name = "DevelopableResourceNode",
            Alias = "开垦地",
            Comment = "开垦地",
        },
        [256] = {
            Name = "FortifiedCity",
            Alias = "主城府",
            Comment = "主城府",
        },
    },
    StrategyCardType = {
        [0] = {
            Name = "NONE",
            Alias = "空",
            Comment = "空",
        },
        [2000101] = {
            Name = "SHA",
            Alias = "杀",
            Comment = "杀",
        },
        [2000102] = {
            Name = "SHAN",
            Alias = "闪",
            Comment = "闪",
        },
        [2000103] = {
            Name = "WU_ZHONG_SHENG_YOU",
            Alias = "无中生有",
            Comment = "无中生有",
        },
        [2000201] = {
            Name = "WU_GU_FENG_DENG",
            Alias = "五谷丰登",
            Comment = "五谷丰登",
        },
        [2000202] = {
            Name = "KAI_KEN",
            Alias = "开垦",
            Comment = "开垦",
        },
        [2000203] = {
            Name = "JI_SHEN_JI_DIAN",
            Alias = "稷神祭典",
            Comment = "稷神祭典",
        },
    },
    TacticAttributeType = {
        [1] = {
            Name = "TotalCD",
            Alias = "总CD",
            Comment = "总CD",
        },
        [2] = {
            Name = "IsEnabled",
            Alias = "是否可用",
            Comment = "是否可用",
        },
        [3] = {
            Name = "Probability",
            Alias = "成功率",
            Comment = "成功率",
        },
        [4] = {
            Name = "MaxPrepareStep",
            Alias = "最大准备回合",
            Comment = "最大准备回合",
        },
    },
    TacticFeature = {
        [1] = {
            Name = "Assistance",
            Alias = "辅助",
            Comment = "辅助",
        },
        [2] = {
            Name = "CivilAndMilitary",
            Alias = "文武",
            Comment = "文武",
        },
        [3] = {
            Name = "Pursue",
            Alias = "治疗",
            Comment = "治疗",
        },
        [4] = {
            Name = "Defend",
            Alias = "防御",
            Comment = "防御",
        },
        [5] = {
            Name = "Strategy",
            Alias = "谋略",
            Comment = "谋略",
        },
        [6] = {
            Name = "Weapons",
            Alias = "兵刃",
            Comment = "兵刃",
        },
    },
    TacticType = {
        [1] = {
            Name = "Active",
            Alias = "主动技",
            Comment = "主动技",
        },
        [2] = {
            Name = "Passive",
            Alias = "锁定技",
            Comment = "锁定技",
        },
        [3] = {
            Name = "Pursue",
            Alias = "追击技",
            Comment = "追击技",
        },
        [4] = {
            Name = "Awakening",
            Alias = "觉醒技",
            Comment = "觉醒技",
        },
        [5] = {
            Name = "PlainAttack",
            Alias = "普攻",
            Comment = "普攻",
        },
        [6] = {
            Name = "ArmyType",
            Alias = "兵种",
            Comment = "兵种",
        },
        [7] = {
            Name = "Formation",
            Alias = "阵法",
            Comment = "阵法",
        },
    },
    TriggerTiming = {
        [1] = {
            Name = "RoundStart",
            Alias = "回合开始",
            Comment = "回合开始",
        },
        [2] = {
            Name = "ConsumeCard",
            Alias = "消耗卡牌",
            Comment = "消耗卡牌",
        },
    },
    ValueTipsColor = {
        [1] = {
            Name = "Enough",
            Alias = "足够",
            Comment = "绿",
        },
        [2] = {
            Name = "Lack",
            Alias = "不足",
            Comment = "红",
        },
        [3] = {
            Name = "Limit",
            Alias = "数值上限",
            Comment = "白",
        },
        [4] = {
            Name = "Normal",
            Alias = "正常提示",
            Comment = "橙",
        },
        [5] = {
            Name = "Positive",
            Alias = "正面信息",
            Comment = "绿",
        },
        [6] = {
            Name = "Negative",
            Alias = "负面信息",
            Comment = "红",
        },
    },
    WorldBuffType = {
        [101] = {
            Name = "AddMoveSpeed",
            Alias = "增加移速",
            Comment = "增加移速",
        },
        [102] = {
            Name = "AddDefendRange",
            Alias = "增加驻守范围",
            Comment = "增加驻守范围",
        },
    },
    ZoomDisableFlag = {
        [0] = {
            Name = "None",
            Alias = "无",
            Comment = "无",
        },
        [1] = {
            Name = "Paving",
            Alias = "铺路",
            Comment = "铺路时屏蔽缩放功能",
        },
    },
    BattleAttributeType = {
        [1] = {
            Name = "Attack",
            Alias = "",
            Comment = "武力",
        },
        [2] = {
            Name = "Intelligence",
            Alias = "",
            Comment = "智力",
        },
        [3] = {
            Name = "RecoverableHealth",
            Alias = "",
            Comment = "伤兵",
        },
        [4] = {
            Name = "Defense",
            Alias = "",
            Comment = "统率",
        },
        [5] = {
            Name = "Speed",
            Alias = "",
            Comment = "速度",
        },
        [6] = {
            Name = "AttackCritRate",
            Alias = "",
            Comment = "会心几率",
        },
        [7] = {
            Name = "AttackCritDamage",
            Alias = "",
            Comment = "会心伤害率",
        },
        [8] = {
            Name = "AttackDamageAdjustment",
            Alias = "",
            Comment = "造成兵刃伤害",
        },
        [9] = {
            Name = "AttackDamageFinalAdjustment",
            Alias = "",
            Comment = "造成兵刃伤害结果",
        },
        [10] = {
            Name = "HurtAttackAdjustment",
            Alias = "",
            Comment = "受到兵刃伤害",
        },
        [11] = {
            Name = "HurtAttackFinalAdjustment",
            Alias = "",
            Comment = "受到兵刃伤害结果",
        },
        [12] = {
            Name = "IntelligenceCritRate",
            Alias = "",
            Comment = "奇谋几率",
        },
        [13] = {
            Name = "IntelligenceCritDamage",
            Alias = "",
            Comment = "奇谋伤害率",
        },
        [14] = {
            Name = "IntelligenceDamageAdjustment",
            Alias = "",
            Comment = "造成谋略伤害",
        },
        [15] = {
            Name = "IntelligenceDamageFinalAdjustment",
            Alias = "",
            Comment = "造成谋略伤害结果",
        },
        [16] = {
            Name = "HurtIntelligenceAdjustment",
            Alias = "",
            Comment = "受到谋略伤害",
        },
        [17] = {
            Name = "HurtIntelligenceFinalAdjustment",
            Alias = "",
            Comment = "受到谋略伤害结果",
        },
        [18] = {
            Name = "HealAdjustment",
            Alias = "",
            Comment = "造成治疗",
        },
        [19] = {
            Name = "BeHealedAdjustment",
            Alias = "",
            Comment = "受到治疗",
        },
        [20] = {
            Name = "Health",
            Alias = "",
            Comment = "兵力",
        },
        [21] = {
            Name = "ComboRate",
            Alias = "",
            Comment = "连击率",
        },
        [22] = {
            Name = "AttackIgnoreDefense",
            Alias = "",
            Comment = "破甲",
        },
        [23] = {
            Name = "IntelligenceIgnoreDefense",
            Alias = "",
            Comment = "看破",
        },
        [24] = {
            Name = "FinalDamageCoefficient",
            Alias = "",
            Comment = "格挡",
        },
        [25] = {
            Name = "ArmyTypeQualification",
            Alias = "",
            Comment = "兵种适应性",
        },
        [26] = {
            Name = "ActiveTacticProbability",
            Alias = "",
            Comment = "主动战法发动率",
        },
        [27] = {
            Name = "PursueTacticProbability",
            Alias = "",
            Comment = "追击战法发动率",
        },
        [28] = {
            Name = "SelfActiveTacticProbability",
            Alias = "",
            Comment = "自带主动战法发动率",
        },
        [29] = {
            Name = "SelfPursueTacticProbability",
            Alias = "",
            Comment = "自带追击战法发动率",
        },
        [30] = {
            Name = "DodgeRate",
            Alias = "",
            Comment = "闪避率",
        },
        [31] = {
            Name = "ExtraAttackDamageAdjustment",
            Alias = "",
            Comment = "最终造成兵刃伤害",
        },
        [32] = {
            Name = "HurtExtraAttackDamageAdjustment",
            Alias = "",
            Comment = "最终受到兵刃伤害",
        },
        [33] = {
            Name = "ExtraIntelligenceDamageAdjustment",
            Alias = "",
            Comment = "最终造成谋略伤害",
        },
        [34] = {
            Name = "HurtExtraIntelligenceDamageAdjustment",
            Alias = "",
            Comment = "最终受到谋略伤害",
        },
        [35] = {
            Name = "AttackVamp",
            Alias = "",
            Comment = "倒戈",
        },
        [36] = {
            Name = "IntelligenceVamp",
            Alias = "",
            Comment = "攻心",
        },
        [37] = {
            Name = "ArmyTypeCounterAdjustment",
            Alias = "",
            Comment = "对克制兵种造成伤害",
        },
        [38] = {
            Name = "BeArmyTypeCounterAdjustment",
            Alias = "",
            Comment = "受到克制兵种伤害",
        },
        [39] = {
            Name = "ActiveTacticDamageAdjustment",
            Alias = "",
            Comment = "造成主动伤害",
        },
        [40] = {
            Name = "ActiveTacticHurtAdjustment",
            Alias = "",
            Comment = "受到主动伤害",
        },
        [41] = {
            Name = "PursueTacticDamageAdjustment",
            Alias = "",
            Comment = "造成追击伤害",
        },
        [42] = {
            Name = "PursueTacticHurtAdjustment",
            Alias = "",
            Comment = "受到追击伤害",
        },
        [43] = {
            Name = "PlainAttackDamageAdjustment",
            Alias = "",
            Comment = "造成普攻伤害",
        },
        [44] = {
            Name = "PlainAttackHurtAdjustment",
            Alias = "",
            Comment = "受到普攻伤害",
        },
    },
}
