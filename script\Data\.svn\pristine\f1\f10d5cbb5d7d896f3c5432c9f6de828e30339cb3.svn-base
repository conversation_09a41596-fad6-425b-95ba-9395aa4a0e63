{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"event1": {"Type": "number", "Value": "5"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "2": {"Type": "GetAttributeNode", "Field": {"heroIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "20"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": {"Items": []}}}, "4": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "3"}, "statistic": {"Type": "boolean", "Value": "False"}, "sourceId": {"Type": "number", "Value": "0"}, "tacticId": {"Type": "number", "Value": "0"}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgsList": {"Items": [], "Merge": true}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "True"}, "isFixedDamage": {"Type": "boolean", "Value": "True"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "overwriteRecoverableRate": {"Type": "boolean", "Value": "False"}, "recoverableRate": {"Type": "number", "Value": "0"}, "damageValues": {"Items": []}, "damageHeroIds": {"Items": []}, "damagePackageIds": {"Items": []}, "missHeroIds": {"Items": []}}}, "5": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "2"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": "a*b"}, "list": {"Items": []}, "vlist": {"Items": []}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "6": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "2"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "True"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}, "conditions": {}}}, "8": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "1"}, "statistic": {"Type": "boolean", "Value": "False"}, "sourceId": {"Type": "number", "Value": "0"}, "tacticId": {"Type": "number", "Value": "0"}, "damageInputString": {"Type": "string", "Value": "1"}, "damageFactor": {"Type": "number", "Value": "1"}, "overwriteArgsList": {"Items": [], "Merge": true}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedDamage": {"Type": "boolean", "Value": "False"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "overwriteRecoverableRate": {"Type": "boolean", "Value": "False"}, "recoverableRate": {"Type": "number", "Value": "0"}, "damageValues": {"Items": []}, "damageHeroIds": {"Items": []}, "damagePackageIds": {"Items": []}, "missHeroIds": {"Items": []}}}, "9": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "buffId": {"Type": "number", "Value": "11101002"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "10": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "attr1"}, "desc": {"Type": "string", "Value": "崩坏自掉血百分比"}, "result": {"Type": "number", "Value": "0"}}}, "11": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "dam1"}, "desc": {"Type": "string", "Value": "崩坏兵刃伤害"}, "result": {"Type": "number", "Value": "0"}}}, "12": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "True"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}}, "Links": {"0": {"RoundPrepareNode": ["2.prev"]}, "2": {"next": ["10.prev"]}, "4": {"next": ["6.prev"]}, "5": {"next": ["4.prev"]}, "6": {"next": ["12.prev"]}, "8": {"next": ["9.prev"]}, "10": {"next": ["5.prev"]}, "11": {"next": ["8.prev"]}, "12": {"next": ["11.prev"]}}, "DataFlows": {"2": {"attributeValues": ["5.a"]}, "4": {"damageValues": ["12.list"]}, "5": {"result": ["4.fixedDamageValue"]}, "6": {"targetIds": ["8.targetIds", "9.targetIds"]}, "10": {"result": ["5.b"]}, "11": {"result": ["8.damageFactor"]}, "12": {"indexElement": ["11.a"]}}}