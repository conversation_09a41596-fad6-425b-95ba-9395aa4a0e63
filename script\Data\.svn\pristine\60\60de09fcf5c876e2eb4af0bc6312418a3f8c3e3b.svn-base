{"Type": "BuffGraph", "Nodes": {"0": {"Type": "BuffRootNode", "Field": {"consts": {"fromBuff": {"VariableRangeType": 1, "ResetOnExecute": false, "Type": "boolean", "Value": true}}, "event1": {"Type": "number", "Value": "106"}, "eventPriority1": {"Type": "number", "Value": "0"}, "onlySelf1": {"Type": "boolean", "Value": "False"}}}, "1": {"Type": "CheckNode", "Field": {"checkTacticType": {"Type": "boolean", "Value": "True"}, "tacticId": {"Type": "number", "BlackboardValue": "InputTacticId"}, "isBornWith": {"Type": "boolean", "Value": "False"}, "validTypes": [{"Type": "number", "Value": "1"}, {"Type": "number", "Value": "3"}], "checkDamage": {"Type": "boolean", "Value": "False"}, "damagePackageId": {"Type": "number", "Value": "0"}, "checkIsCrit": {"Type": "boolean", "Value": "False"}, "isCrit": {"Type": "boolean", "Value": "False"}, "damageRange": {"Type": "number", "Value": "0"}, "damageRanges": [], "damageType": {"Type": "number", "Value": "0"}, "damageTypes": [], "checkIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "inputIsInevitableDamage": {"Type": "boolean", "Value": "False"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "checkArmyType": {"Type": "boolean", "Value": "False"}, "armyTypeHeroId": {"Type": "number", "Value": "0"}, "armyTypes": []}}, "3": {"Type": "SelectNode", "Field": {"rawTargetIds": [{"Type": "string", "BlackboardValue": "EventSourceId"}], "exceptTargetIds": [], "targetIds": [], "count": {"Type": "number", "Value": "1"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": [], "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}}}, "4": {"Type": "UseTacticNode", "Field": {"casterIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "tacticId": {"Type": "number", "Value": "112003"}, "needProb": {"Type": "boolean", "Value": "True"}, "extraProb": {"Type": "number", "Value": "0"}, "notNeedCarried": {"Type": "boolean", "Value": "True"}, "argNames": [{"Type": "string", "Value": "fromBuff"}], "argsCount": {"Type": "number", "Value": "1"}, "argName0": {"Type": "string", "Value": "TargetId"}, "argValue0": {"Type": "string", "Value": ""}, "argName1": {"Type": "string", "Value": ""}, "argValue1": {"Type": "string", "Value": ""}, "argName2": {"Type": "string", "Value": ""}, "argValue2": {"Type": "string", "Value": ""}, "argName3": {"Type": "string", "Value": ""}, "argValue3": {"Type": "string", "Value": ""}, "argName4": {"Type": "string", "Value": ""}, "argValue4": {"Type": "string", "Value": ""}, "argName5": {"Type": "string", "Value": ""}, "argValue5": {"Type": "string", "Value": ""}, "argName6": {"Type": "string", "Value": ""}, "argValue6": {"Type": "string", "Value": ""}, "argName7": {"Type": "string", "Value": ""}, "argValue7": {"Type": "string", "Value": ""}, "argName8": {"Type": "string", "Value": ""}, "argValue8": {"Type": "string", "Value": ""}, "argName9": {"Type": "string", "Value": ""}, "argValue9": {"Type": "string", "Value": ""}}}, "6": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "string", "Value": ""}, "targetIds": [{"Type": "string", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}], "buffId": {"Type": "number", "Value": "11200302"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}}, "Links": {"0": {"AfterUseTacticNode": ["3.prev"]}, "1": {"next": ["4.prev"]}, "3": {"next": ["1.prev"]}, "4": {"next": ["6.prev"]}}, "DataFlows": {}}