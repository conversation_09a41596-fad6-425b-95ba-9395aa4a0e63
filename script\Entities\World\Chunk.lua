﻿local WorldConst = xrequire("Common.Const.WorldConst")
local CoordUtils = xrequire("Common.Utils.CoordUtils")
local NpcUtils = xrequire("Common.Utils.NpcUtils")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local AnyEntityCallerCompEnv = xrequire("Entities.Components.AnyEntityCallerComp")
local BehaviorJudgeEnv = xrequire("Common.BehaviorJudge")

local EntityEnv = xrequire("Framework.Entities.Entity")
Chunk = DefineEntity("Chunk", {EntityEnv.Entity}, {
    AnyEntityCallerCompEnv.AnyEntityCallerComp,
})

function Chunk:ctor()
    self:createSpaceActor()

    local x = WorldConst.WorldOrigin.x + self.props.xIndex * WorldConst.ChunkSize + WorldConst.HalfChunkSize
    local y = WorldConst.WorldOrigin.y + self.props.yIndex * WorldConst.ChunkSize + WorldConst.HalfChunkSize
    self:setPos2D(x, y)
    self:logInfo("chunk idx (%s, %s) center (%s, %s)", self.props.xIndex, self.props.yIndex, x, y)
end

function Chunk:GetGridType(x, y)
    local packedPos = CoordUtils.PackPos(x, y)
    local building = self.props.gridBuildings[packedPos]
    if building then
        return building.tp
    end

    local tp = self.space.mapData:GetElementType(packedPos)
    local prop = self.props.gridOccupations[packedPos]
    -- 处理地块被升级的情况，需要同时提升地块产量和守军类型
    if prop and prop.levelAddTo then
        local newTp = ProcessedTableEnv.GetMapElementIdByIdAndLevel(tp, prop.levelAddTo)
        if newTp then
            tp = newTp
        end
    end
    return tp
end

function Chunk:GetGridName(col, row)
    local packedPos = CoordUtils.PackPos(col, row)
    local building = self.props.gridBuildings[packedPos]
    if building then
        return building.name
    end

    local tp = self.space.mapData:GetElementType(packedPos)
    return GameCommon.TableDataManager:GetMapElementData(tp).name
end

function Chunk:GetGridDefender(col, row)
    local tp = self:GetGridType(col, row)
    return NpcUtils.GetGridDefenderTeamId(tp, col, row)
end

function Chunk:GetGridOccupation(x, y)
    local packedPos = CoordUtils.PackPos(x, y)
    return self.props.gridOccupations[packedPos]
end

function Chunk:ValidOccupyGrid(x, y, actor)
    -- 是否为可占领且敌对的格子
    local tp = self:GetGridType(x, y)
    local data = GameCommon.TableDataManager:GetMapElementData(tp)
    if data.occupy_time <= 0 then
        return false
    end
    local occupation = self:GetGridOccupation(x, y)
    return occupation == nil or BehaviorJudgeEnv.ValidBehaviorTo(actor.props, occupation, actor._actorType, TableConst.enums.ActorType.CHUNK, TableConst.enums.InteractiveBehavior.Occupy)
end

function Chunk:Occupy(x, y, actor)
    local packedPos = CoordUtils.PackPos(x, y)
    if self:ValidOccupyGrid(x, y, actor) then
        if self.props.gridOccupations[packedPos] and self.props.gridOccupations[packedPos].gid then
            self:onAvatarLostGrid(x, y, self.props.gridOccupations[packedPos].gid)
        end
        self:onAvatarOccupyGrid(x, y, actor.props.gid)
        self.props.gridOccupations[packedPos] = {gid = actor.props.gid, allyId = actor.props.allyId, name = actor.props.name}
        self:logInfo("army %s occupy grid (%s, %s), gid: %s", actor.id, x, y, actor.props.gid)
        return true
    else
        return false
    end
end

function Chunk:onAvatarOccupyGrid(x, y, gid)
    self:callWorldAvatar(gid, "OnOccupyLand", x, y, ServerTimeEnv.GetServerNow())
    local packedPos = CoordUtils.PackPos(x, y)
    local tp = self.space.mapData:GetElementType(packedPos)
    local data = GameCommon.TableDataManager:GetMapElementData(tp)
    self:serviceRpc("AmbitionsService", "OnOccupyLand", data.level)
end

function Chunk:onAvatarLostGrid(x, y, gid)
    self:callWorldAvatar(gid, "OnLostLand", x, y)
end

function Chunk:Build(x, y, building)
    self:logInfo("build %s at (%s, %s), gid: %s, name: %s, allyId: %s", building.tp, x, y, building.gid, building.name, building.allyId)
    local packedPos = CoordUtils.PackPos(x, y)
    self.props.gridOccupations[packedPos] = {gid = building.gid, allyId = building.allyId, name = building.name}
    self.props.gridBuildings[packedPos] = building
end

function Chunk:Demolish(x, y)
    local packedPos = CoordUtils.PackPos(x, y)
    self.props.gridOccupations[packedPos] = nil
    self.props.gridBuildings[packedPos] = nil
end

function Chunk:EnterSpace(space)
    local worldId, spaceId, objectid = self:enterSpace(space)
    self.space = space
end

function Chunk:LeaveSpace()
    self:leaveSpace()
end

function Chunk:onLeaveSpace(worldId, spaceId, objectId)
    self.space = nil
end

function Chunk:OnSyncLandAdd(x, y, levelAddTo, buffs)
    local occupation = self:GetGridOccupation(x, y)
    if occupation then
        occupation.levelAddTo = levelAddTo
        occupation.produceBuffs = buffs
    end
end

-- for emmylua
return {Chunk=Chunk}