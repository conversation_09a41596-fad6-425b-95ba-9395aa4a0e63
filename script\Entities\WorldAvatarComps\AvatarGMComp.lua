local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local GmConfigEnv = xrequire("Common.Const.GMConfig")

---@class GmTask
GmTask = DefineClass("GmTask")

function GmTask:ctor(ent)
    self.ent = ent
    self.co = nil
    self.taskWait = nil
end

function GmTask:listenEvent(event)
    if not self.co then
        return
    end

    self.taskWait = event
end

function GmTask:EventEnd(event, ret)
    if not self.co then
        return
    end

    if self.taskWait ~= event then
        return
    end

    if coroutine.status(self.co) == "suspend" then
        coroutine.resume(self.co, ret)
    else  -- 同步结束的行为, 协程还没有开始yield, 等这一帧协程yield之后, 下一帧再去resume
        self.ent:addTimer(0, 0, function() coroutine.resume(self.co, ret) end)
    end
end


---@class (partial) WorldAvatar
---@class AvatarGMComp : ComponentBase
AvatarGMComp = DefineClass("AvatarGMComp", ComponentBaseEnv.ComponentBase)

function AvatarGMComp:ctor()
    self.props.gmAccess = true
    self.gmTask = nil
end

function AvatarGMComp:checkGmCommand(func)
    if not self.props.gmAccess then
        return Config.Ret.GM_COMMAND_ACCESS_INVALID
    end

    if self.gmTask then
        return Config.Ret.GM_COMMAND_HAS_TASK_PROCESSING
    end

    if not self[func] and (not self.avatar or not self.avatar[func]) then
        return Config.Ret.GM_COMMAND_NOT_EXIST
    end

    return Config.Ret.SUCCESS
end

function AvatarGMComp:GmCommand(func, args, reqIndex, sendMirror)
    self:logInfo("[lb] gm command: %s %v %s %s %s", func, args, reqIndex, sendMirror, self.props.gmAccess)
    local code = self:checkGmCommand(func)
    if code == Config.Ret.SUCCESS then
        if bit.band(sendMirror, GmConfigEnv.GM_USE_TARGET.Self) ~= 0 then
            self:doGmCommand(func, args, reqIndex)
        end
    else
        self:clientRpc("OnGmCommandResult", reqIndex, code)
        if code == Config.Ret.GM_COMMAND_NOT_EXIST then
            return
        end
    end

    if bit.band(sendMirror, GmConfigEnv.GM_USE_TARGET.Mirror) ~= 0 then
        if self.props.gmMirror.index then
            for _, memberDetail in pairs(self.props.gmMirror.members) do
                if memberDetail.confirmed then
                    -- 这里对请求索引取负数，表示是镜像请求，避免和自己的请求冲突
                    self:callWorldAvatar(memberDetail.gid, "mirrorGmCommand", self.props.gid, func, args, -reqIndex)
                end
            end
        end
        if bit.band(sendMirror, GmConfigEnv.GM_USE_TARGET.Self) == 0 then
            -- 这里只发给了镜像，需要给自己一个成功返回
            self:safeClientRpc("OnGmCommandResult", reqIndex, Config.Ret.SUCCESS)
        end
    end
end

function AvatarGMComp:checkMirrorGmCommand(gid, func)
    if gid ~= self.props.gmMirrorBy.gid then
        return Config.Ret.GM_COMMAND_NOT_MIRRORED_BY_GID
    end

    return self:checkGmCommand(func)
end

function AvatarGMComp:mirrorGmCommand(gid, func, args, reqIndex)
    self:logInfo("[qun] mirror gm command: %s from %s %s", func, gid, self.props.gmAccess)
    local code = self:checkMirrorGmCommand(gid, func)
    if code ~= Config.Ret.SUCCESS then
        self:safeClientRpc("OnGmCommandResult", reqIndex, code)
        return
    end

    self:doGmCommand(func, args, reqIndex)
end

function AvatarGMComp:doGmCommand(func, args, reqIndex)
    self:logInfo("doGmCommand. func: %s, args: %v", func, args)
    self.gmTask = GmTask.new(self)
    local executor = self[func] and self or self.avatar
    local handler = self:addTimer(5, 0, function(timer_id)
        -- 先加个5秒钟超时防止卡死
        self:logError("Timeout clear gmTask %s", reqIndex)
        self.gmTask = nil
    end)
    self.gmTask.co = coroutine.create(function()
        local ret = executor[func](executor, unpack(args)) or Config.Ret.SUCCESS
        self:logInfo("doGmCommand End. func: %s, args: %v, ret: %s", func, args, ret)
        self:delTimer(handler)
        self.gmTask = nil
        self:safeClientRpc("OnGmCommandResult", reqIndex, ret)
    end)
    coroutine.resume(self.gmTask.co)
end

function AvatarGMComp:NotifyGmListenEvent(event)
    if self.gmTask then
        self:logInfo("NotifyGmListenEvent. event: %s", event)
        self.gmTask:listenEvent(event)
    end
end

function AvatarGMComp:NotifyGmEventEnd(event, ret)
    if self.gmTask then
        self:logInfo("NotifyGmEventEnd. event: %s, ret: %s", event, ret)
        self.gmTask:EventEnd(event, ret)
    end
end

function AvatarGMComp:gmBindMirrorAccount(accountList)
    local worldAvatarType = LuaNodeConfig.getConfig("fyc.WorldAvatarType")
    local mongoColl = self:getMongoEntityCollection(worldAvatarType)
    mongoColl:find({urs = {["$in"] = accountList, ["$nin"] = {self.props.urs}}}, {urs = 1, gid = 1}, {}, function(succ, docs)
        if not succ or table.isnilorempty(docs) then
            return
        end
        self:gmUnbindMirrorAccount()
        local members = {}
        local index = self:genQueryId()
        local cb = self:makeCallback("onBindMirrorAccountResult")
        for _, doc in ipairs(docs) do
            members[doc.urs] = {gid = doc.gid}
            self:callWorldAvatar(doc.gid, "bindMirrorAccount", index, self.props.gid, cb)
        end
        self.props.gmMirror = {
            index = index,
            members = members,
        }
    end)
end

function AvatarGMComp:bindMirrorAccount(index, gid, cb)
    if self.props.gmMirrorBy then
        self:logWarn("[qun] already bind mirror account: %v", self.props.gmMirrorBy)
        self:callbackRpc(cb, index, self.props.urs, false)
        return
    end
    self:logInfo("bindBy mirror account: %s", gid)
    self.props.gmMirrorBy = {gid = gid, index = index}
    if self.avatar then
        self.avatar:checkDelayDestroy()
        self:callbackRpc(cb, index, self.props.urs, true)
        return
    end
    self.props.gmMirrorBy.waitConfirm = cb
    if not self.creatingAvatar then
        self:createAvatar()
    end
end

function AvatarGMComp:onBindMirrorAccountResult(index, urs, succ)
    if index ~= self.props.gmMirror.index then
        return
    end
    if not succ then
        self:logWarn("[qun] bind mirror account failed: %s", urs)
        self.props.gmMirror.members[urs] = nil
        return
    end
    self:logInfo("[qun] bind mirror account success: %s", urs)
    self.props.gmMirror.members[urs].confirmed = true
end

function AvatarGMComp:gmUnbindMirrorAccount()
    if not self.props.gmMirror.index then
        return
    end
    for _, memberDetail in pairs(self.props.gmMirror.members) do
        self:callWorldAvatar(memberDetail.gid, "unbindMirrorAccount", self.props.gid)
    end
    self.props.gmMirror = {}
end

function AvatarGMComp:unbindMirrorAccount(gid)
    if self.props.gmMirrorBy.gid ~= gid then
        return
    end
    self:logInfo("[qun] unbindBy mirror account: %s", gid)
    self.props.gmMirrorBy = nil
    if self.avatar then
        self.avatar:checkDelayDestroy()
    end
end

function AvatarGMComp:GmCommandCheckAccess(func, args)
    self:logInfo("GmCommandCheckAccess. func: %s, args: %v", func, args)
    if self.props.gmAccess then
        self:clientRpc("GmCommandCheckAccessPass", func, args)
    end
end