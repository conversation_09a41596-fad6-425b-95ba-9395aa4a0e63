--[[ 排行榜
]]

---@class JumpNode
JumpNode = DefineClass("JumpNode")

function JumpNode:ctor(right, down, ele)
    self.right = right
    self.down = down
    self.ele = ele
end

function JumpNode:IsFrontOf(other, downOrder)
    -- removeElement要求，这里value, seq相同时，要返回false
    if self.ele.value == other.ele.value then
        return self.ele.seq < other.ele.seq  -- 保持插入序
    end
    return downOrder and self.ele.value > other.ele.value or self.ele.value < other.ele.value
end

---@class JumpList
--[[跳点实现排行榜
适用于数据量较大的排行，以及需要查找TopN的场景
排序中会保持插入序
]]
JumpList = DefineClass("JumpList")

function JumpList:ctor(downOrder, rank, expectMaxElementNum)
    self.downOrder = downOrder
    self.rank = rank
    self.expectMaxElementNum = expectMaxElementNum
    self.maxLevel = math.ceil(math.log(expectMaxElementNum, 2)) + 1
    self.head = nil
    self:initRank()
end

function JumpList:UpdateElement(ele)
    if self.rank.elements[ele.id] then
        if self.rank.elements[ele.id].value == ele.value then
            return  -- 无需更新
        end
        self:removeElement(self.rank.elements[ele.id])
    end
    ele.seq = self:getSeq()
    self.rank.elements[ele.id] = ele
    self:addElement(ele)
end

function JumpList:GetTopN(n)
    local p = self.head
    for i = 1, self.maxLevel do
        if p.down then
            p = p.down
        end
    end
    local result = {}
    for i = 1, n do
        if p then
            table.insert(result, {p.ele.id, p.ele.value})
            p = p.right
        else
            break
        end
    end
    return result
end

function JumpList:initRank()
    assert(self.head == nil, "JumpList already initialized")
    for _, ele in pairs(self.rank.elements) do
        self:addElement(ele)
    end
end

function JumpList:getSeq()
    self.rank.lastSeq = self.rank.lastSeq + 1
    return self.rank.lastSeq
end

function JumpList:addElement(ele)
    local pathList = {}
    local p = self.head
    for i = 1, self.expectMaxElementNum do
        if not p then
            break
        end
        for j = 1, self.expectMaxElementNum do
            if not p.right or not p.right:IsFrontOf(ele, self.downOrder) then
                break
            end
            p = p.right
        end
        table.insert(pathList, p)
        p = p.down
    end
    local downNode = nil
    local randomLevel = self:getRandomLevel()
    for i = 1, #pathList do
        local node = pathList[#pathList - i + 1]
        node.right = JumpNode(node.right, downNode, ele)
        downNode = node.right
    end
    if randomLevel > #pathList then
        self.head = JumpNode(nil, self.head, ele)
    end
end

function JumpList:removeElement(ele)
    local pathList = {}
    local p = self.head
    for i = 1, self.expectMaxElementNum do
        if not p then
            break
        end
        for j = 1, self.expectMaxElementNum do
            if not p.right or not p.right:IsFrontOf(ele, self.downOrder) then
                break
            end
            p = p.right
        end
        table.insert(pathList, p)
        p = p.down
    end
    for i = 1, #pathList do
        local node = pathList[#pathList - i + 1]
        if node.right and node.right.ele.id == ele.id then
            node.right = node.right.right
        else
            break
        end
    end
    if pathList[1] and pathList[1].ele.id == ele.id then
        self.head = nil
        for i = 1, #pathList do
            local node = pathList[i]
            if node.right then
                self.head = node.right
                break
            end
        end
    end
end

function JumpList:getRandomLevel()
    local level = 1
    while math.random() < 0.5 and level < self.maxLevel do
        level = level + 1
    end
    return level
end
