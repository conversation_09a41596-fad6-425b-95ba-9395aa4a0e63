---
--- <PERSON>a 面向对象类机制实现
--- Created by Administrator.
--- DateTime: 2022/5/9 13:28
---


--[[
-- 关于多继承构造/析构函数的调用顺序
-- 父类深度优先，继承链中出现重复类时，不重复调用构造/析构函数
DefineClass("A")
DefineClass("B", A)
DefineClass("C", A, B)
DefineClass("D", B)
DefineClass("E", D, C)

-- c++ ctor: A -> B -> D -> A -> A -> B -> C -> E
-- lua ctor: A -> B -> D -> C -> E
local e = E.new()
--]]

--- 假装是所有 Lua 对象的基类
---@generic T
---@class LuaClass
---@field __cname string 类名
---@field class T 当前类
LuaClass = {}

-- 作为组件时，不会把这里定义的方法合并到Entity中
---@return T
function LuaClass.new(...) end

---@return T
function LuaClass.instance(...) end

---@param cls T
function LuaClass:onComposed(cls) end

---@protected
---@return void
function LuaClass:ctor(...) end

DeclareAndSetGlobal("LuaClass", LuaClass)

local FUNC_CUSTOM_INDEX = "__customIndex"
local FUNC_CUSTOM_NEW_INDEX = "__customNewIndex"


local function _getCtorList(cls, cache)
    local ctorList = rawget(cls, "__CTOR_LIST__")
    if ctorList then return ctorList end

    ctorList = {}
    if cache then
        rawset(cls, "__CTOR_LIST__", ctorList)
    end

    local ctorMap = {}
    local index = 1

    local function initCtorList(base)
        --保证顺序，先基类再自己
        for _, super in ipairs(base.__supers or {}) do
            initCtorList(super)
        end

        local ctor = rawget(base, "ctor")
        if ctor and not ctorMap[ctor] then
            ctorMap[ctor] = index
            ctorList[index] = ctor
            index = index + 1
        end
    end

    initCtorList(cls)
    ctorMap = nil
    return ctorList
end

--- 自定义oop类，可以完成继承
---@param classname string
---@vararg LuaClass
---@return LuaClass
function DefineClass(classname, ...)
    local env = getfenv(2)  -- caller's env
    assert(env ~= _G, "class %s must be defined in an environment other than _G", classname)
    if env[classname] == nil then
        env[classname] = {__cname = classname, __full_name = env.__module_name .. classname}
    end

    ---@type LuaClass
    local cls = env[classname]

    -- init super
    for _, super in ipairs({...}) do
        local superType = type(super)
        assert(superType == "table",
            string.format('class() - create class "%s" with invalid super class type "%s"', classname, superType)
        )

        cls.__supers = cls.__supers or {}
        cls.__supers[#cls.__supers + 1] = super
    end

    -- init metatable
    local mtCls = {}
    setmetatable(cls, mtCls)

    mtCls.__call = function(_, ...) return cls.new(...) end
    
    if cls.__supers then
        if #cls.__supers == 1 then
            mtCls.__index = cls.__supers[1]
        else
            mtCls.__index = function(_, key)
                    local supers = cls.__supers
                    for i = 1, #supers do
                        local super = supers[i]
                        if super[key] ~= nil then
                            return super[key]
                        end
                    end
                end
        end
    end

    -- init class method
    cls.__index = function(t, k)
        local v = cls[k]
        if v ~= nil then return v end
        local ci = cls[FUNC_CUSTOM_INDEX]
        if ci then
            v = ci(t, k)
            if v ~= nil then return v end
        end
        return nil
    end
    cls.__newindex = function(t, k, v)
        local cn = cls[FUNC_CUSTOM_NEW_INDEX]
        if cn and cn(t, k, v) then return end
        rawset(t, k, v)
    end
    cls.__call_ctor = function(instance, ...)
        -- 调用构造函数列表
        for _, ctor in ipairs(_getCtorList(cls, true)) do ctor(instance, ...) end
    end

    cls.__class = cls
    cls.__TabCompletableMembers = function()
        return cls
    end

    --遵循c++标准，构造函数先构造父类，然后构造子类（注意所有的data数据其实本质都是在instance里面，父类可以认为都是interface）
    cls.new = function(...)
        local instance
        if cls.__create then
            instance = cls.__create(...)
        else
            instance = {}
        end

        setmetatable(instance, cls)

        cls.__call_ctor(instance, ...)

        return instance
    end

    return cls
end
DeclareAndSetGlobal("DefineClass", DefineClass)

function DefineSingletonClass(classname, ...)
    -- correctly set DefineClass's env
    local env = getfenv(2)
    setfenv(1, env)

    local cls = DefineClass(classname, ...)
    cls.__singletonNew = cls.new
    cls.new = nil
    cls.instance = function()
        if cls.__inst == nil then
            cls.__inst = cls.__singletonNew()
        end
        return cls.__inst
    end
    return cls
end
DeclareAndSetGlobal("DefineSingletonClass", DefineSingletonClass)

-- 检查继承链中是否包含className
function CheckClassType(cls, className)
    if cls.__cname == className then
        return true
    end

    if cls.__supers ~= nil then
        for _, super in ipairs(cls.__supers) do
            if CheckClassType(super, className) then
                return true
            end
        end
    end

    return false
end
DeclareAndSetGlobal("CheckClassType", CheckClassType)

-- 定义不受reload影响的类属性
---@param cls table
---@param name string
---@param val any
function ClassAttr(cls, name, val)
    if cls[name] == nil then
        cls[name] = val
    end
end
DeclareAndSetGlobal("ClassAttr", ClassAttr)
