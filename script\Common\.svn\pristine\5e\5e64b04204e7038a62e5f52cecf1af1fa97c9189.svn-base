﻿BattleRecordType = {
    BattleStart = 1,
    RoundPrepare = 2,
    HeroTurnStart = 3,
    HeroTurnAction = 4,
    HeroTurnEnd = 5,
    RoundEnd = 6,
    BattleResult = 7,

    UseAbility = 8,
    TriggerBuff = 9,
}

CompareOperator = {
    Less = 1,
    LessEqual = 2,
    Greater = 3,
    GreaterEqual = 4,
    Equal = 5,
    NotEqual = 6,
}

BuffCountType = {
    StackLayer = 1,
    Category = 2,
    BuffGroupType = 3,
}

RandomType = {
    BranchByProb = 1,
    GenerateNumber = 2,
    Choose = 3,
}

SelectAttributeType = {
    Min = 1,
    Max = 2,
}

BattleHandlerType = {
    --Ability = 1,
    Tactic = 2,
    Buff = 3,
}

EffectType = {
    Hero = 1,
    Stretch = 2,
    Position = 3,
}

CalculateMethod = {
    Formula = 1,
    Sum = 2,
    Average = 3,
}

AttributeModifierType = {
    Fix = 1,
    Rate = 2,
    Overwrite = 3,
}

BattleEventRecordTypeNames = {}
for name, value in pairs(TableConst.enums.BattleEvent) do
    BattleEventRecordTypeNames[value] = name
end

FORMATION_TEAM_NUM = 3
TEAM_SLOT_NUM = 3

DetailTriggerBuffReason = {
    Prob = 1,
    NoTarget = 2,
}

BattleStaticAttributeType = {
    Dynasty = 1,
    ArmyType = 2,
    Stars = 3,
}

BriefDataType = {
    Tactic = 0,
    Buff = 1
}

DebugValueType = {
    AttackDamage = 1,
    IntelligenceDamage = 2,
    Heal = 3,
}

BattleFormulaType = {
    Damage = 0,
    Heal = 1,
}

ChangeTargetRangeType = {
    All = 0,
    ByTacticId = 1,
    ByTacticType = 2,
}
