﻿local EZE = xrequire "EZE"
---@type GlobalEntityMgr
local GlobalEntityMgr = xrequire("Framework.Utils.GlobalEntityMgr")
---@type GlobalDataMgr
local GlobalDataMgr = xrequire("Framework.Utils.GlobalDataMgr").instance
---@type CmdUtils
local CmdUtils = xrequire("Utils.CmdUtils").CmdUtils
---@type GlobalCallback
local GlobalCallback = xrequire("Framework.Bootstrap.GlobalCallback").GlobalCallback
---@class GlobalCallbackApp: GlobalCallback
GlobalCallbackApp = DefineClass("GlobalCallbackApp", GlobalCallback)
local EZScheduleEnv = xrequire(EZFPath .. ".Utils.EZSchedule")
local ScheduleCompEnv = xrequire("Entities.Components.Common.ScheduleComp")
local ServerTimeEnv = xrequire("Utils.ServerTime")

--region 开服相关回调

function GlobalCallbackApp:onContainerInited(containerId, containerType, isBootstrapContainer, recover)
    GlobalCallback.onContainerInited(self, containerId, containerType, isBootstrapContainer, recover)
    -- prepare test
    local test = LuaNodeConfig.getConfig("auto_test")
    if test then
        require("Test.Init")
    end

    EZGlobal.Schedule = EZScheduleEnv.EZSchedule.new()

    local containerTags = LuaNodeConfig.getContainerConfig(containerType, "tags")
    for i, tag in ipairs(containerTags) do
        if tag == "emmy" then
            lxpcall(function()
                dbg = require('emmy_core')
                dbg.tcpConnect('127.0.0.1', 9966)
            end)
        elseif tag == "GidService" then
            GlobalEntityMgr.instance:create("GidService", 0, {}, {})
        elseif tag == "AllyService" then
            GlobalEntityMgr.instance:create("AllyService", 0, {}, {})
        end
    end
end

function GlobalCallbackApp:onContainerReady(containerId, containerType, isBootstrapContainer, recover)
    GlobalCallback.onContainerReady(self, containerId, containerType, isBootstrapContainer, recover)
    -- 注册玩家刷天回调
    EZGlobal.Schedule:addScheduleSlots(TableConst.enums.ScheduleType.CommonDaily, nil, ScheduleCompEnv.ScheduleRecTag.Avatar, EntCb("OnCommonDailySchedule"))
end

function GlobalCallbackApp:onClusterReady()
    GlobalCallback.onClusterReady(self)
    -- run test
    local test = LuaNodeConfig.getConfig("auto_test")
    if test then
        require("Test." .. test)
    end
end

--endregion 开服相关回调

--region 关服状态回调

-- 仅作示例，持久化Avatar等
function GlobalCallbackApp:onClusterSaving()
    DebugLog("[%s] GlobalCallbackApp:onClusterSaving", containerType)
    local Avatar = xrequire("Entities.Avatar")
    for eid, avt in pairs(Avatar.Avatar.instances) do
        avt:destroy() -- 持久化对象会先存储再销毁
    end
    EZGlobal.Entity:addTimer(1, 1, function (timerId)
        if next(Avatar.Avatar.instances) then
            local id, avt = next(Avatar.Avatar.instances)
            InfoLog("waiting for destroy avatar(%s), avatarId(%s)", id, avt:avatarId())
            return
        end
        EZGlobal.Entity:delTimer(timerId)
        EZE.continueShutdown()
    end)
end

function GlobalCallbackApp:onClusterClosed()
    DebugLog("[%s] GlobalCallbackApp:onClusterClosed", containerType)
    GlobalCallback.GlobalCallback.onClusterClosed(self)
end

function GlobalCallbackApp:onClusterClosed()
    DebugLog("[%s] GlobalCallbackApp:onClusterPostSave", containerType)
    ---@type CmdService
    local cmdService = xrequire("Services.CmdService").CmdService.instance
    if cmdService then
        cmdService:asyncFeedback(cmdService.locks.global, CmdUtils.res("ok"))
    end
    ContainerEntity:addTimer(1, 0, function (...)
        EZE.continueShutdown()
    end)
end

--endregion 关服状态回调

function GlobalCallbackApp:timeproxy(...)
    GlobalCallback.timeproxy(self, ...)
    EZGlobal.Schedule:onSetTime()
    EZGlobal.Entity:logInfo("timeproxy %s", os.date("%Y-%m-%d %H:%M:%S", ServerTimeEnv.GetServerNow()))
    return true
end

function GlobalCallbackApp:timeorigin()
    GlobalCallback.timeorigin(self)
    EZGlobal.Schedule:onSetTime()
    EZGlobal.Entity:logInfo("timeorigin %s", os.date("%Y-%m-%d %H:%M:%S", ServerTimeEnv.GetServerNow()))
    return true
end

instance = instance or GlobalCallbackApp.new()

EZE.setGlobalCallback(instance)

