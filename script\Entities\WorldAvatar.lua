local CoordUtils = xrequire("Common.Utils.CoordUtils")

local AvatarEntity = xrequire("Framework.Entities.AvatarEntity")
local ContainerInstancesCompEnv = xrequire("Framework.Entities.Components.ContainerInstancesComp")
local ScheduleCompEnv = xrequire("Framework.Entities.Components.ScheduleComp")
local Schedule = xrequire(EZFPath .. ".Utils.Schedule.Schedule")
local CurrencyCompEnv = xrequire("Entities.WorldAvatarComps.CurrencyComp")
local ArmyMgrCompEnv = xrequire("Entities.WorldAvatarComps.ArmyMgrComp")
local ActorOwnerCompEnv = xrequire("Entities.WorldAvatarComps.ActorOwnerComp")
local AnyEntityCallerCompEnv = xrequire("Entities.CommonComps.AnyEntityCallerComp")
local ResProduceCompEnv = xrequire("Entities.WorldAvatarComps.ResProduceComp")
local ActorViewCompEnv = xrequire("Entities.WorldAvatarComps.ActorViewComp")
local ArmyFormationCompEnv = xrequire("Entities.WorldAvatarComps.ArmyFormationComp")
local AvatarArmyCombatCompEnv = xrequire("Entities.WorldAvatarComps.AvatarArmyCombatComp")
local MainCityCompEnv = xrequire("Entities.WorldAvatarComps.MainCityComp")
local LandMgrCompEnv = xrequire("Entities.WorldAvatarComps.LandMgrComp")
local EventComponentEnv = xrequire("Entities.CommonComps.EventComponent")
local AllyCompEnv = xrequire("Entities.WorldAvatarComps.AllyComp")
local ClanCompEnv = xrequire("Entities.WorldAvatarComps.ClanComp")
local ProsperityCompEnv = xrequire("Entities.WorldAvatarComps.ProsperityComp")
local BuildMgrCompEnv = xrequire("Entities.WorldAvatarComps.BuildMgrComp")
local WorldBattleRecordCompEnv = xrequire("Entities.WorldAvatarComps.WorldBattleRecordComp")
local AvatarConst = xrequire("Common.Const.AvatarConst")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local WorldUtilsEnv = xrequire("Utils.WorldUtils")
local AvatarGMCompEnv = xrequire("Entities.WorldAvatarComps.AvatarGMComp")

---@class (partial) WorldAvatar: SAvatarEntity, LuaEntityClass, ContainerInstancesComp<WorldAvatar>, ScheduleComp, CurrencyComp, ArmyMgrComp, ActorOwnerComp, AnyEntityCallerComp, ResProduceComp, ActorViewComp, ArmyFormationComp, AvatarArmyCombatComp, LandMgrComp, EventComponent, AllyComp, BuildMgrComp, WorldBattleRecordComp
---@field avatar Avatar
---@field props WorldAvatarProps
WorldAvatar = DefineEntity("WorldAvatar", {AvatarEntity.AvatarEntity}, {
    ContainerInstancesCompEnv.ContainerInstancesComp,   ---@type ContainerInstancesComp
    AvatarGMCompEnv.AvatarGMComp,                       ---@type AvatarGMComp
    ScheduleCompEnv.ScheduleComp,                       ---@type ScheduleComp
    CurrencyCompEnv.CurrencyComp,                       ---@type CurrencyComp
    ArmyMgrCompEnv.ArmyMgrComp,                         ---@type ArmyMgrComp
    ActorOwnerCompEnv.ActorOwnerComp,                   ---@type ActorOwnerComp
    AnyEntityCallerCompEnv.AnyEntityCallerComp,         ---@type AnyEntityCallerComp
    ResProduceCompEnv.ResProduceComp,                   ---@type ResProduceComp
    ActorViewCompEnv.ActorViewComp,                     ---@type ActorViewComp
    ArmyFormationCompEnv.ArmyFormationComp,             ---@type ArmyFormationComp
    AvatarArmyCombatCompEnv.AvatarArmyCombatComp,       ---@type AvatarArmyCombatComp
    MainCityCompEnv.MainCityComp,                       ---@type MainCityComp
    LandMgrCompEnv.LandMgrComp,                         ---@type LandMgrComp
    EventComponentEnv.EventComponent,                   ---@type EventComponent
    AllyCompEnv.AllyComp,                               ---@type AllyComp
    ClanCompEnv.ClanComp,                               ---@type ClanComp
    ProsperityCompEnv.ProsperityComp,                   ---@type ProsperityComp
    BuildMgrCompEnv.BuildMgrComp,                       ---@type BuildMgrComp
    WorldBattleRecordCompEnv.WorldBattleRecordComp      ---@type WorldBattleRecordComp
})
--[[ WorldAvatar
1、起服后常驻内存的实体，持有部分Avatar的养成数据，满足Avatar离线期间部队读写养成和背包数据的需求。
2、持有所有归属Avatar的Actor的mailbox，可以做相关rpc的转发和广播。
3、可承载部分Actor与大世界弱相关的功能，例如编队和战斗数据的记录。
4、和Avatar保持在同进程，可以同步获取到。
]]

WorldAvatar.IsWorldAvatar = true

---@diagnostic disable-next-line: duplicate-set-field
function WorldAvatar:ctor()
    self:logDebug("WorldAvatar.ctor urs: %s, notprop: %s", self.props.urs, self.notprop)
    self:startSave()

    self.avatar = nil
    self.sdkInfo = nil
    self.deviceInfo = nil
    self.ipInfo = nil

    self.creatingAvatar = false
    self:syncAvatarInfo()

    -- 放在初始化完成之后
    self:initSchedules()
end

function WorldAvatar:onDestroy()
    self:serviceRpc("RoleService", "avatarDestroyed", self.props.urs, self.props.gid, self:getMailboxStr())
    self:serviceRpc("WorldService", "UnregisterWorldAvatar", self.props.gid, self:getMailboxStr())
end

function WorldAvatar:OnRegisterWorldAvatar(succ)
    if succ then
        self:logInfo("OnRegisterWorldAvatar Succ")
        self:serviceRpc("WorldService", "OnRegisterWorldAvatar", self.props.gid, self:getMailboxStr())
    else
        self:logError("OnRegisterWorldAvatar Fail")
        self:destroy()
    end
end

function WorldAvatar:NotifyTakeover(sdkInfo, deviceInfo, ipInfo)
    self:logInfo("NotifyTakeover. sdkInfo: %v, deviceInfo: %v, ipInfo: %v", sdkInfo, deviceInfo, ipInfo)
    self:UpdateSdkInfo(sdkInfo, deviceInfo, ipInfo)
    return self.avatar and self.avatar:NotifyTakeover()
end

function WorldAvatar:onGetClient()
    self:logInfo("onGetClient")
    if self.avatar then
        self:giveClientTo(self.avatar, true)
    elseif not self.creatingAvatar then
        self:createAvatar()
    end
end

function WorldAvatar:createAvatar()
    self.creatingAvatar = true
    local avatarType = LuaNodeConfig.getConfig("fyc.AvatarType")
    local mongoColl = self:getMongoEntityCollection(avatarType)
    mongoColl:find({ urs = self.props.urs }, { _id = 1, urs = 1 }, {}, function(succ, docs) self:onQueryAvatars(succ, docs) end)
end

function WorldAvatar:onQueryAvatars(succ, docs)
    if succ then
        assert(#docs == 0 or #docs == 1)

        local avatarType = LuaNodeConfig.getConfig("fyc.AvatarType")
        if #docs == 0 then
            self:logInfo("Create Avatar")
            local avatar = EntityManager.createEntity(
                    avatarType, 0, { urs = self.props.urs, gid = self.props.gid, name = self.props.name, createTime = ServerTimeEnv.GetServerNowInt() })
            self:afterCreateAvatar(avatar)
        else
            self:logInfo("Load Avatar")
            EntityManager.createEntityFromDB(avatarType, docs[1]["_id"], function(errcode, eid) self:onCreateAvatarFromDb(eid) end)
        end
    else
        self:logError("Load Avatars Fail. Query From Db Error.")
        self:onLoginFailed()
    end
end

function WorldAvatar:onCreateAvatarFromDb(eid)
    if eid <= 0 then
        self:logError("On Load Avatar Fail")
        self:onLoginFailed()
        return
    end

    self:logInfo("On Load Avatar Succ")
    local avatar = EntityManager.getEntity(eid)
    self:afterCreateAvatar(avatar)
end

function WorldAvatar:afterCreateAvatar(avatar)
    avatar.worldAvatar = self
    self.avatar = avatar
    avatar:OnGetWorldAvatar()
    if self:hasClient() then
        self:giveClientTo(avatar, true)
    end
    self.creatingAvatar = false
    if self.props.gmMirrorBy and self.props.gmMirrorBy.waitConfirm then
        local cb = self.props.gmMirrorBy.waitConfirm
        self.props.gmMirrorBy.waitConfirm = nil
        self:callbackRpc(cb, self.props.gmMirrorBy.index, self.props.urs, true)
    end
end

function WorldAvatar:onLoginFailed()
    self:clientRpc("onLoginFail")
end

function WorldAvatar:UpdateSdkInfo(sdkInfo, deviceInfo, ipInfo)
    self.sdkInfo = sdkInfo
    self.deviceInfo = deviceInfo
    self.ipInfo = ipInfo
end

function WorldAvatar:syncClientIpInfo(ipInfo)
    self:logInfo("syncClientIpInfo. ipInfo: %v", ipInfo)
    if ipInfo then
        self.ipInfo = ipInfo
    end
end

function WorldAvatar:getDisconnectDestroyDelay()
    return nil
end

function WorldAvatar:OnLoseAvatar()
    self.avatar = nil
    self:UpdateSdkInfo()
end

function WorldAvatar:fireAvatarEvent(event, id, ...)
    self:fireEvent(event, id, ...)
    if self.avatar then
        self.avatar:fireEvent(event, id, ...)
    end
end

function WorldAvatar:TriggerWorldLoadDone()
    self:fireEntityEvent("onWorldLoadDone")
end

function WorldAvatar:OnCommonDailySchedule()
    local lastCycle = self:getScheduleRec(TableConst.enums.ScheduleType.CommonDaily)
    local nowCycle = EZGlobal.Schedule:getCurSchedule(TableConst.enums.ScheduleType.CommonDaily)
    self:logInfo("OnCommonDailySchedule cycle %s -> %s", lastCycle, nowCycle)
end

function WorldAvatar:OnCommonWeekSchedule()
    local lastCycle = self:getScheduleRec(TableConst.enums.ScheduleType.CommonWeek)
    local nowCycle = EZGlobal.Schedule:getCurSchedule(TableConst.enums.ScheduleType.CommonWeek)
    self:logInfo("OnCommonWeekSchedule cycle %s -> %s", lastCycle, nowCycle)
end

function WorldAvatar:syncAvatarInfo()
    local info = {}
    for name, key in pairs(AvatarConst.AvatarInfoField) do
        if name == "kingBuildingLv" then  -- 君王殿等级用主堡等级
            info[key] = self.props.mainCityView and self.props.mainCityView.level or 0
        else
            assert(self.props[name])
            info[key] = self.props[name]
        end
    end
    self:serviceRpc("AvatarInfoService", "SyncAvatarInfo", self.props.gid, info)
end

function WorldAvatar:OnModifyBirthPlace(oriBirthCommandery, oriBirthplace, oriBelongState)
    self:logInfo(
            "OnModifyBirthPlace. oriBirthCommandery: %s, oriBirthplace: %s, oriBelongState: %s, birthCommandery: %s, birthplace: %s",
            oriBirthCommandery, oriBirthplace, oriBelongState, self.props.birthCommandery, self.props.birthplace)
    self:OnModifyBirthPlaceNotifyClan(oriBirthCommandery, oriBirthplace, oriBelongState)
end

function WorldAvatar:HttpQueryAvatarInfo(callback)
    self:logInfo("HttpQueryAvatarInfo. gid: %s", self.props.gid)
    if self.avatar then
        self:callbackRpc(callback, true, self:getHttpQueryInfo(self.avatar.props))
        return
    end

    local avatarType = LuaNodeConfig.getConfig("fyc.AvatarType")
    local mongoColl = self:getMongoEntityCollection(avatarType)
    mongoColl:find({ urs = self.props.urs }, {}, {}, function(succ, docs) self:HttpQueryAvatarInfoFindDb(succ, docs, callback) end)
end

function WorldAvatar:HttpQueryAvatarInfoFindDb(succ, docs, callback)
    self:logInfo("HttpQueryAvatarInfoFindDb, gid: %s", self.props.gid)
    if not succ or table.isempty(docs) then
        self:logInfo("HttpQueryAvatarInfoFindDb Fail. succ: %s, docs: %v", succ, docs)
        self:callbackRpc(callback, false, {})
        return
    end

    assert(#docs == 1)
    self:callbackRpc(callback, true, self:getHttpQueryInfo(docs[1]))
end

function WorldAvatar:getHttpQueryInfo(doc)
    return {
        name = doc.name,
        gid = doc.gid,
        urs = doc.urs,
        createTime = doc.createTime,
        loginTime = doc.loginTime,
        logoutTime = doc.logoutTime,
    }
end

--region gm指令

function WorldAvatar:gmSetBirthPlace(birthCommandery, birthplace)
    self:logInfo("gmSetBirthPlace. birthCommandery: %s, birthplace: %s", birthCommandery, birthplace)
    if birthCommandery == 0 or birthplace == 0 then
        return
    end

    if WorldUtilsEnv.GIS.commandery2Prefecture[birthCommandery] ~= birthplace then
        return
    end

    if self.props.clanId > 0 then
        self:QueryMultiClan({self.props.clanId}, function(clanInfoList)
            if #clanInfoList == 0 or clanInfoList[1].birthplace ~= birthplace then
                return
            end

            self:doGmSetBirthPlace(birthCommandery, birthplace)
        end)
    else
        self:doGmSetBirthPlace(birthCommandery, birthplace)
    end

end

function WorldAvatar:doGmSetBirthPlace(birthCommandery, birthplace)
    local oriBirthCommandery = self.props.birthCommandery
    local oriBirthplace = self.props.birthplace
    local oriBelongState = self.props.belongState
    self:logInfo(
            "doGmSetBirthPlace. oriBirthCommandery: %s, oriBirthplace: %s, oriBelongState: %s, birthCommandery: %s, birthplace: %s",
            oriBirthCommandery, oriBirthplace, oriBelongState, birthCommandery, birthplace)
    self.props.birthCommandery = birthCommandery
    self.props.birthplace = birthplace
    self.props.belongState = birthplace
    self:OnModifyBirthPlace(oriBirthCommandery, oriBirthplace, oriBelongState)
end

function WorldAvatar:testGetClientIPInfo()
    self:safeClientRpc("testGetClientIPInfo")
end

--endregion gm指令