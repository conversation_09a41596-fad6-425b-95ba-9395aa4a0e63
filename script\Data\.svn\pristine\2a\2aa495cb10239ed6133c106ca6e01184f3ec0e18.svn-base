{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "True"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}, "conditions": {}}}, "2": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "buffId": {"Type": "number", "Value": "10301301"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "3": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "buffId": {"Type": "number", "Value": "10301302"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}, "5": {"Type": "GetAttributeNode", "Field": {"heroIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "4"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": {"Items": []}}}, "6": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "True"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}, "7": {"Type": "GetAttributeNode", "Field": {"heroIds": {"Items": []}, "isStatic": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "4"}, "staticAttributeType": {"Type": "number", "Value": "1"}, "attributeValues": {"Items": []}}}, "8": {"Type": "ListProcessingNode", "Field": {"list": {"Items": [], "Merge": true}, "exceptList": {"Items": [], "Merge": true}, "listLength": {"Type": "number", "Value": "0"}, "getIndexElement": {"Type": "boolean", "Value": "True"}, "index": {"Type": "number", "Value": "1"}, "indexElement": {"Type": "number", "Value": "0"}, "removeDuplicateElements": {"Type": "boolean", "Value": "False"}, "outputList": {"Items": []}}}, "9": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "4"}, "number2": {"Type": "number", "Value": "100"}}}, "10": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "2"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": "a-b"}, "list": {"Items": []}, "vlist": {"Items": []}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "11": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "False"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "True"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "True"}, "conditions": {}}}, "13": {"Type": "AddBuffNode", "Field": {"sourceId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "buffId": {"Type": "number", "Value": "10009"}, "count": {"Type": "number", "Value": "1"}, "coverTotalRound": {"Type": "boolean", "Value": "False"}, "totalRound": {"Type": "number", "Value": "1"}}}}, "Links": {"0": {"next": ["1.prev"]}, "1": {"next": ["2.prev"]}, "2": {"next": ["3.prev"]}, "3": {"next": ["5.prev"]}, "5": {"next": ["6.prev"]}, "6": {"next": ["7.prev"]}, "7": {"next": ["8.prev"]}, "8": {"next": ["10.prev"]}, "9": {"next": ["11.prev"]}, "10": {"next": ["9.prev"]}, "11": {"next": ["13.prev"]}}, "DataFlows": {"1": {"targetIds": ["2.targetIds", "7.heroIds"]}, "5": {"attributeValues": ["6.list"]}, "6": {"indexElement": ["10.a"]}, "7": {"attributeValues": ["8.list"]}, "8": {"indexElement": ["10.b"]}, "10": {"result": ["9.number1"]}, "11": {"targetIds": ["13.targetIds"]}}}