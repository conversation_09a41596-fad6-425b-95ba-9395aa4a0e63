local ServiceEnv = xrequire "Framework.Services.Service"
local BattleModeEnv = xrequire("Common.Battle.BattleMode")
local DBUtils = xrequire(EZFPath .. ".Utils.DBUtils")

---@class BattleService: Service
local BattleService = DefineEntity("BattleService", {ServiceEnv.Service}, {})

function BattleService:ctor()
    self.collStatistics = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_statistics")
    self.collDetails = self:mongoCollection(DBUtils.dbmgrId, DBUtils.dbName, "battle_record_details")
    self:regist(true)
end

function BattleService:CalcBattleResult(attTeam, defTeam, battleInfo, callback)
    local battleResult, detailRecord, briefRecord = BattleModeEnv.CalcBattleRecord(
        TableConst.enums.BattleModeType.OneOnOne,
        {attTeam},
        {defTeam},
        battleInfo
        )

    if battleResult.success then
        local abstracts = {}
        for _, record in ipairs(detailRecord) do
            self:saveBattleRecord(record)
            table.insert(abstracts, record.battleAbstract)
        end
        self:callbackRpc(callback, battleResult, briefRecord, abstracts)
    else
        self:callbackRpc(callback, battleResult, {}, {})
    end
end

function BattleService:saveBattleRecord(record)
    local uid = EZE.genUUID()
    record.battleAbstract.uid = uid

    local stats = EZE.msgpackPack(record.battleStatistic)
    local details = EZE.msgpackPack(record.battleDetails)

    self.collStatistics:insert_one({_id = uid, data = stats}, {}, function(succ)
        if not succ then
            self:logError("save battle record statistics %s failed", uid)
        end
    end)

    self.collDetails:insert_one({_id = uid, data = details}, {}, function(succ)
        if not succ then
            self:logError("save battle record details %s failed", uid)
        end
    end)
end
