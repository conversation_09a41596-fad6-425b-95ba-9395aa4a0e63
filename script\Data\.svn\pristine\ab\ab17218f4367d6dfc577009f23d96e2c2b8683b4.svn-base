{"Type": "SkillGraph", "Nodes": {"0": {"Type": "SkillRootNode"}, "1": {"Type": "HealNode", "Field": {"targetIds": {"Items": [{"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}]}, "healFactor": {"Type": "string", "Value": ""}, "healFactorNew": {"Type": "number", "Value": "0"}, "overwriteArgsList": {"Items": [], "Merge": true}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "False"}, "isFixedHeal": {"Type": "boolean", "Value": "False"}, "fixedHealValue": {"Type": "number", "Value": "0"}, "healValue": {"Type": "number", "Value": "0"}, "originalHealValue": {"Type": "number", "Value": "0"}, "overHealValue": {"Type": "number", "Value": "0"}, "healHeroIds": {"Items": []}, "healPackageIds": {"Items": []}, "missHeroIds": {"Items": []}}}, "2": {"Type": "FormulaNode", "Field": {"variableCount": {"Type": "number", "Value": "0"}, "a": {"Type": "number", "Value": "0"}, "b": {"Type": "number", "Value": "0"}, "c": {"Type": "number", "Value": "0"}, "d": {"Type": "number", "Value": "0"}, "e": {"Type": "number", "Value": "0"}, "f": {"Type": "number", "Value": "0"}, "formulaKey": {"Type": "string", "Value": "heal1"}, "desc": {"Type": "string", "Value": "挥戈返日治疗系数"}, "result": {"Type": "number", "Value": "0"}}}, "4": {"Type": "CompareNode", "Field": {"number1": {"Type": "number", "Value": "0"}, "op": {"Type": "number", "Value": "3"}, "number2": {"Type": "number", "Value": "0"}}}, "5": {"Type": "CalculateNode", "Field": {"calculateMethod": {"Type": "number", "Value": "1"}, "isVector": {"Type": "boolean", "Value": "False"}, "variableCount": {"Type": "number", "Value": "1"}, "a": {"Type": "number", "Value": "0"}, "formula": {"Type": "string", "Value": "a*2"}, "list": {"Items": []}, "vlist": {"Items": []}, "result": {"Type": "number", "Value": "0"}, "vresult": {"Type": "Vector3", "Value": ["0", "0", "0"]}}}, "6": {"Type": "SelectNode", "Field": {"rawTargetIds": {"Items": [{"Type": "number", "BlackboardValue": "HeroIds"}]}, "exceptTargetIds": {"Items": []}, "targetIds": {"Items": []}, "count": {"Type": "number", "Value": "1"}, "ignoreConfusion": {"Type": "boolean", "Value": "False"}, "selectSelf": {"Type": "boolean", "Value": "False"}, "pursue": {"Type": "boolean", "Value": "True"}, "selectCurTurnHero": {"Type": "boolean", "Value": "False"}, "enemy": {"Type": "boolean", "Value": "False"}, "ally": {"Type": "boolean", "Value": "False"}, "includeSelf": {"Type": "boolean", "Value": "False"}, "alive": {"Type": "boolean", "Value": "True"}, "dead": {"Type": "boolean", "Value": "False"}, "captain": {"Type": "boolean", "Value": "False"}, "pioneer": {"Type": "boolean", "Value": "False"}, "adviser": {"Type": "boolean", "Value": "False"}, "considerAdditionalPosition": {"Type": "boolean", "Value": "False"}, "countryWei": {"Type": "boolean", "Value": "False"}, "countryShu": {"Type": "boolean", "Value": "False"}, "countryWu": {"Type": "boolean", "Value": "False"}, "countryQun": {"Type": "boolean", "Value": "False"}, "male": {"Type": "boolean", "Value": "True"}, "female": {"Type": "boolean", "Value": "True"}, "checkBuff": {"Type": "boolean", "Value": "False"}, "buffIds": {"Items": []}, "checkAttribute": {"Type": "boolean", "Value": "False"}, "attributeType": {"Type": "number", "Value": "1"}, "selectType": {"Type": "number", "Value": "1"}, "random": {"Type": "boolean", "Value": "False"}, "conditions": {}}}, "8": {"Type": "DamageNode", "Field": {"damageCasterId": {"Type": "number", "BlackboardValue": "<PERSON>er<PERSON><PERSON>"}, "targetIds": {"Items": []}, "damageRange": {"Type": "number", "Value": "0"}, "damageType": {"Type": "number", "Value": "3"}, "statistic": {"Type": "boolean", "Value": "False"}, "sourceId": {"Type": "number", "Value": "0"}, "tacticId": {"Type": "number", "Value": "0"}, "damageInputString": {"Type": "string", "Value": ""}, "damageFactor": {"Type": "number", "Value": "0"}, "overwriteArgsList": {"Items": [], "Merge": true}, "enableAdditionalAttributeModification": {"Type": "boolean", "Value": "True"}, "isFixedDamage": {"Type": "boolean", "Value": "True"}, "fixedDamageValue": {"Type": "number", "Value": "0"}, "isInevitableDamage": {"Type": "boolean", "Value": "False"}, "additionalAttackCritRate": {"Type": "number", "Value": "0"}, "additionalAttackCritDamage": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritRate": {"Type": "number", "Value": "0"}, "additionalIntelligenceCritDamage": {"Type": "number", "Value": "0"}, "additionalAttackIgnoreDefense": {"Type": "number", "Value": "0"}, "additionalIntelligenceIgnoreDefense": {"Type": "number", "Value": "0"}, "overwriteRecoverableRate": {"Type": "boolean", "Value": "False"}, "recoverableRate": {"Type": "number", "Value": "0"}, "damageValues": {"Items": []}, "damageHeroIds": {"Items": []}, "damagePackageIds": {"Items": []}, "missHeroIds": {"Items": []}}}}, "Links": {"0": {"next": ["2.prev"]}, "1": {"next": ["4.prev"]}, "2": {"next": ["1.prev"]}, "4": {"next": ["6.prev"]}, "5": {"next": ["8.prev"]}, "6": {"next": ["5.prev"]}}, "DataFlows": {"1": {"overHealValue": ["4.number1", "5.a"]}, "2": {"result": ["1.<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "5": {"result": ["8.fixedDamageValue"]}, "6": {"targetIds": ["8.targetIds"]}}}