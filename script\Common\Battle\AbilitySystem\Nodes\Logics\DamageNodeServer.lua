﻿local NodeBaseServerEnv = xrequire("Common.Battle.AbilitySystem.Nodes.Core.NodeBaseServer")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

---@class DamageNodeServer : NodeBaseServer
local DamageNodeServer = DefineClass("DamageNodeServer", NodeBaseServerEnv.NodeBaseServer)

function DamageNodeServer:ctor()
    self:MarkExecute()
end

function DamageNodeServer:Execute(graphData)
    local battleGame = graphData.caster.battleGame
    local sourceId = graphData:GetProperty(self, "sourceId")
    local caster = battleGame:GetHeroByUid(sourceId) or graphData.caster --支持传入id
    local tacticId = BattleUtilsEnv.GetGraphTacticId(self, graphData)
    local sourceTacticId, sourceTacticHero = BattleUtilsEnv.GetGraphGetSourceTacticIdAndHero(graphData)
    local overwriteArgs = graphData:GetProperty(self, "overwriteArgs")

    local damageCasterId = graphData:GetProperty(self, "damageCasterId")
    local damageCaster
    if string.isnilorempty(damageCasterId) then
        damageCaster = caster
    else
        damageCaster = battleGame:GetHeroByUid(damageCasterId)
    end

    local damagePackageIds = {}
    local damageTargetIds = {}
    local missHeroIds = {}
    local globalFactor = graphData.globalFactor or 1
    local damageType = graphData:GetProperty(self, "damageType")
    local targetIds = graphData:GetProperty(self, "targetIds")
    local damageFactorInputString = graphData:GetProperty(self, "damageInputString")
    local damageFactor = graphData:GetProperty(self, "damageFactor")
    local damageRange = graphData:GetProperty(self, "damageRange")
    local isFixedDamage = graphData:GetProperty(self, "isFixedDamage") or
        damageType == TableConst.enums.BattleDamageType.Reality or
        damageType == TableConst.enums.BattleDamageType.Chain or
        damageType == TableConst.enums.BattleDamageType.Share
    for _, targetId in ipairs(targetIds) do
        local factor = 1
        if isFixedDamage then
            factor = 1
        elseif string.len(damageFactorInputString) > 0 then
            factor = BattleUtilsEnv.ExecuteFormulaForTacticLevel(damageFactorInputString, graphData:GetFromBlackboard("Level"))
        end

        -- new
        if damageFactor and damageFactor ~= 0 then
            factor = damageFactor
        end

        local damagePackage = {
            damageType = damageType,
            damageFactor = factor,
            globalFactor = globalFactor,
            tacticCasterId = caster.uniqueId,
            casterId = damageCaster.uniqueId,
            targetId = targetId,
            tacticId = tacticId,
            damageRange = damageRange,
            recoverable = 0,
            overwriteArgs = {overwriteArgs}
        }

        self:AttachAdditionalModification(graphData, damagePackage)

        local result = battleGame:SubmitDamage(graphData, damagePackage)

        if result.accept then
            table.insert(damageTargetIds, result.targetId)
            if damageType == TableConst.enums.BattleDamageType.Share then -- 分摊伤害统计的来源英雄和来源战法用节点输入端配置的
                battleGame.recorder:TacticStatistic(caster.camp, caster.uniqueId, tacticId, 0, result.damage.damageValue, 0)
            else
                battleGame.recorder:TacticStatistic(sourceTacticHero.camp, sourceTacticHero.uniqueId, sourceTacticId, 0, result.damage.damageValue, 0)
            end
        else
            table.insert(missHeroIds, targetId)
        end
        table.insert(damagePackageIds, result.damagePackageId)
    end
    graphData:SetProperty(self, "damageHeroIds", damageTargetIds)
    graphData:SetProperty(self, "missHeroIds", missHeroIds)
    if graphData.tacticId == graphData.caster.plainAttackId then
        graphData.caster.plainAttackTargetIds = damageTargetIds
    end

    graphData:SetProperty(self, "damagePackageIds", damagePackageIds)

    if next(graphData:GetProperty(self, "damageHeroIds")) then
        graphData:SetProperty(self, "outputName", "next")
    else
        graphData:SetProperty(self, "outputName", "elseNode")
    end
end

function DamageNodeServer:AttachAdditionalModification(graphData, damagePackage)
    local enableAdditionalModification = graphData:GetProperty(self, "enableAdditionalAttributeModification")
    damagePackage.additionalModification = {}
    local additionalModification = damagePackage.additionalModification
    local isInevitableDamage = graphData:GetProperty(self, "isInevitableDamage")
    additionalModification.isInevitableDamage = enableAdditionalModification and isInevitableDamage or false

    additionalModification.isFixedDamage = enableAdditionalModification and (graphData:GetProperty(self, "isFixedDamage") or
        damagePackage.damageType == TableConst.enums.BattleDamageType.Reality or
        damagePackage.damageType == TableConst.enums.BattleDamageType.Chain) or false
    additionalModification.fixedDamageValue = graphData:GetProperty(self, "fixedDamageValue")

    additionalModification.attackCritRate = enableAdditionalModification and graphData:GetProperty(self, "additionalAttackCritRate") or 0
    additionalModification.attackCritDamage = enableAdditionalModification and graphData:GetProperty(self, "additionalAttackCritDamage") or 0
    additionalModification.intelligenceCritRate = enableAdditionalModification and graphData:GetProperty(self, "additionalIntelligenceCritRate") or 0
    additionalModification.intelligenceCritDamage = enableAdditionalModification and graphData:GetProperty(self, "additionalIntelligenceCritDamage") or 0
    additionalModification.attackIgnoreDefense = enableAdditionalModification and graphData:GetProperty(self, "additionalAttackIgnoreDefense") or 0
    additionalModification.intelligenceIgnoreDefense = enableAdditionalModification and graphData:GetProperty(self, "additionalIntelligenceIgnoreDefense") or 0
end



