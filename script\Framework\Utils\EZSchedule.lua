xrequire("luatz.tz")
local Schedule = xrequire("Modules.Schedule.Schedule")
local ScheduleMgr = xrequire("Modules.Schedule.ScheduleMgr")
local Time = xrequire(EZFPath .. ".Utils.Time")
local ScheduleComp = xrequire("Entities.Components.Common.ScheduleComp")



---@class EZSchedule: ScheduleMgr
---@field new fun():EZSchedule
---@field scheduleSlots table<ScheduleId, table<EntityId|int, table<string, EntityCallback>>>
EZSchedule = DefineClass("EZSchedule", ScheduleMgr.ScheduleMgr)

function EZSchedule:ctor()
    self.scheduleSlots = {}
    self:init()
end

function EZSchedule:getServerTZ()
    return EZGlobal.tz:getServerTZ()
end

function EZSchedule:init()
    -- 计时器初始化
    self.timers = {}
    self.nextTimerID = 0
    -- 读表和初始化
    local now = Time.getServerNow()
    for schId, data in pairs(GameCommon.TableDataManager:GetAllScheduleData()) do
        local sch = nil
        if self.isScheduleValid(data) then
            sch = self:createSchedule(
                schId,
                (not data['TimeZone'] or data['TimeZone'] == "") and self:getServerTZ() or data['TimeZone'],
                data['BeginDatetime'],
                data['CycleType'],
                data['CycleSpan'],
                data['CycleLoop'],
                data['Version'] or 0
            )
        else
            assert(false, string.format('schedule init error: %d', schId))
        end
        if data.Stages then
            for stageIdx, stdata in ipairs(data.Stages) do
                local res, err = self:createStage(schId, stdata['StageId'], stageIdx, stdata['BeginDay'], stdata['BeginTime'])
                if not res then
                    ErrorLog(err)
                    self:removeSchedule(schId)
                end
            end
        self:createScheduleFinished(schId, now)
        end
    end
end

function EZSchedule.isScheduleValid(data)
    return data['CycleType'] ~= "SERVERDAY"
end

--region Timer

---@param scheduleId ScheduleId
---@param duration float
---@param func fun(timerId:TimerId):void
function EZSchedule:addTimer(scheduleId, duration, func)
    self:delTimer(scheduleId)
    self.timers[scheduleId] = EZGlobal.Entity:addTimer(duration, 0 , func)
end

---@param scheduleId ScheduleId
function EZSchedule:delTimer(scheduleId)
    local timerId = self.timers[scheduleId]
    if timerId then
        EZGlobal.Entity:delTimer(timerId)
        self.timers[scheduleId] = nil
    end
end

function EZSchedule:clearTimer()
    local all = {}
    for _, timerId in pairs(self.timers) do
        all[#all + 1] = timerId
    end
    for _, timerId in ipairs(all) do
        EZGlobal.Entity:delTimer(timerId)
    end
    self.timers = {}
end

--endregion

function EZSchedule:onSetTime()
    local now = Time.getServerNow()
    local oldSchedule = self.curSchedule
    self.curSchedule = {}
    for schdId, schd in pairs(oldSchedule) do
        self:resetScheduleAndTimer(schdId, now)
    end

    local scheduleIds = {}
    for scheduleId, _ in pairs(self.curSchedule) do
        local oldSchd = oldSchedule[scheduleId]
        local res, _, _, _ = self:needSchedule(scheduleId, now, oldSchd and oldSchd[0], oldSchd and oldSchd[1])
        if res then
            scheduleIds[#scheduleIds + 1] = scheduleId
        end
    end
    self:onScheduleTimeout(0, scheduleIds)
end

-- 添加切阶段计时器
function EZSchedule:addScheduleTimer(timestamp, scheduleId)
    local delay = math.max(timestamp - Time.getServerNow(), 0)
    self:addTimer(scheduleId, delay, function(timerId) self:onScheduleTimeout(timerId, {scheduleId}) end)
end

function EZSchedule:onScheduleTimeout(timerId, scheduleIds)
    for _, schdId in ipairs(scheduleIds) do
        self:delTimer(timerId)
    end
    local now = Time.getServerNow()
    for _, scheduleId in ipairs(scheduleIds) do
        self:resetScheduleAndTimer(scheduleId, now)
    end
    for _, schdId in ipairs(scheduleIds) do
        self:entityScheduleTimeout(schdId)
    end
end

---@param schdId ScheduleId
---@return ScheduleCSV
function EZSchedule:getCurScheduleCSV(schdId)
    local c, s, _ = self:getCurSchedule(schdId)
    if c < 0 then
        return c
    end
    return Schedule.toScheduleCSV(c, self:getSchedule(schdId):getStageIdx(s), GameCommon.TableDataManager:GetScheduleData(schdId).Version)
end

---@param schdId ScheduleId
---@param entityId EntityId | nil @具体哪个entity回调
---@param recTag int | nil @某一类的所有实例回调，recTag和EntityId只有一项有效，另一项为nil
---@param cb EntityCallback
function EZSchedule:addScheduleSlots(schdId, entityId, recTag, cb)
    local slot = self.scheduleSlots[schdId]
    if nil == slot then
        slot = {}
        self.scheduleSlots[schdId] = slot
    end
    local key = nil
    if entityId then
        assert(entityId > ScheduleComp.ScheduleRecTag.MaxValue)
        key = entityId
    else
        assert(recTag < ScheduleComp.ScheduleRecTag.MaxValue)
        key = recTag
        local entCls = ScheduleComp.ScheduleHost[key]
        -- 必须是多实例的类且尚未创建任何实例
        assert(entCls and entCls.instances and not next(entCls.instances))
    end
    assert(key)
    local entityCalls = slot[key]
    if nil == entityCalls then
        entityCalls = {}
        slot[key] = entityCalls
    end
    entityCalls[cb.method] = cb
    -- 立即触发一次
    if entityId then
        local ent = EntityManager.getEntity(entityId)
        assert(ent)
        ent:scheduleOnce(schdId, cb, true, true)
    end
end

-- 复杂度O(n)，如果调用比较频繁，需要考虑优化
---@param schdId ScheduleId
---@param entityId EntityId | nil @具体哪个entity回调
---@param recTag int | nil @某一类的所有实例回调，recTag和EntityId只有一项有效，另一项为nil
---@param cbMethod string
function EZSchedule:delScheduleSlots(schdId, entityId, recTag, cbMethod)
    local slot = self.scheduleSlots[schdId]
    if nil == slot then
        return
    end
    local key = nil
    if entityId then
        assert(entityId >= ScheduleComp.ScheduleRecTag.MaxValue)
        key = entityId
    else
        assert(recTag < ScheduleComp.ScheduleRecTag.MaxValue)
        key = recTag
    end
    assert(key)
    local entityCalls = slot[key]
    if nil == entityCalls then
        return
    end
    entityCalls[cbMethod] = nil
end

---@param dataDict table
---@param getter fun(data:table):ScheduleId
---@param entityId EntityId | nil @具体哪个entity回调
---@param recTag int | nil @某一类的所有实例回调，recTag和EntityId只有一项有效，另一项为nil
---@param cb EntityCallback
function EZSchedule:addScheduleSlotsFromData(dataDict, getter, entityId, recTag, cb)
    for _, data in pairs(dataDict) do
        local schdId = getter(data)
        if schdId then
            self:addScheduleSlots(schdId, entityId, recTag, cb)
        end
    end
end

---@param scheduleId ScheduleId
function EZSchedule:entityScheduleTimeout(scheduleId)
    local slot = self.scheduleSlots[scheduleId]
    if not slot then
        return
    end
    local csv = self:getCurScheduleCSV(scheduleId)
    for key, entityCalls in pairs(slot) do
        if key >= ScheduleComp.ScheduleRecTag.MaxValue then
            local ent = EntityManager.getEntity(key)
            if ent then
                for _, cb in pairs(entityCalls) do
                    lxpcall(ent.scheduleOnce, ent, scheduleId, cb, true, false)
                end
                if not slot[ent.ScheduleRecTag] then  -- 确保只会记录一次且不能提前记录
                    lxpcall(ent.recordSchedule, ent, scheduleId, csv)
                end
            else
                ErrorLog("entityScheduleTimeout - schedule(%s) miss ent(%s)", scheduleId, key)
            end
        else
            local entCls = ScheduleComp.ScheduleHost[key]
            if entCls and entCls.instances then
                for _, ent in pairs(entCls.instances) do
                    for _, cb in pairs(entityCalls) do
                        lxpcall(ent.scheduleOnce, ent, scheduleId, cb, true, false)
                    end
                    lxpcall(ent.recordSchedule, ent, scheduleId, csv)
                end
            else
                ErrorLog("entityScheduleTimeout - schedule(%s) unsupported recTag(%s)", scheduleId, key)
            end
        end
    end
end


-- for emmylua,generate by eze_annotation
return {
    EZSchedule = EZSchedule,
}
