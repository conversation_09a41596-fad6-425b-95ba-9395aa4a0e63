local ComponentBaseEnv = xrequire("Framework.Entities.Components.ComponentBase")
local ProcessedTableEnv = xrequire("Common.ProcessedTable")
local enum_details = xrequire("Table.Defines.enum_details")
local ServerTimeEnv = xrequire("Utils.ServerTime")
local PriorityQueueEnv = xrequire("Common.PriorityQueue")
local ChooseEnv = xrequire("Common.Choose")

---@class BagComp : ComponentBaseEnv.ComponentBase, Avatar
BagComp = DefineClass("BagComp", ComponentBaseEnv.ComponentBase)

--[[
    通用背包组件，提供对背包的基本操作
    支持同时添加和消耗归属不同背包的物品
    BagComp 同时管理
        1. AvatarCurrencyComp 货币背包
        2. 道具背包
        3. 装备背包
        4. 材料背包
        5. 信物背包
]]

function BagComp:ctor()
    self:initAllBagAttr()
end

--region 通用背包接口

---@param itemDict table 物品字典，key为道具ID，value为数量
function BagComp:AddToBag(itemDict, reason)
    local valid, typeToDirectAdd, mailAdd = self:ValidAddAll(itemDict, reason)
    if not valid then
        return false
    end
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    for typeId, directAdd in pairs(typeToDirectAdd) do
        self[BagComp.customDirectAddToBag[typeId]](self, directAdd, reason)
    end
    if not table.isempty(mailAdd) then
        self:logInfo("[qun] mail add items: %v, reason: %s", mailAdd, reasonDetail.Name)
    end
    if reasonDetail.Tags and reasonDetail.Tags.notify then
        -- 通知客户端有新道具获得
        self:NotifyNewItem(typeToDirectAdd, mailAdd)
    end
    return true
end

function BagComp:ValidAddAll(itemDict, reason)
    local succ, typeToItemDict = self:getTypeToItemDict(itemDict)
    if not succ then
        return false
    end
    local typeToDirectAdd, mailAdd = {}, {}
    for typeId, itemDetail in pairs(typeToItemDict) do
        local succ, directAdd = self[BagComp.customValidAddAll[typeId]](self, itemDetail, reason, mailAdd)
        if not succ then
            return false
        end
        typeToDirectAdd[typeId] = directAdd
    end
    return true, typeToDirectAdd, mailAdd
end

function BagComp:ConsumeFromBag(itemDict, reason)
    local valid, typeToItemDict = self:ValidConsumeAll(itemDict)
    if not valid then
        return false
    end
    for typeId, itemDetail in pairs(typeToItemDict) do
        self[BagComp.customDirectConsumeFromBag[typeId]](self, itemDetail, reason)
    end
    return true
end

function BagComp:ValidConsumeAll(itemDict, reason)
    local succ, typeToItemDict = self:getTypeToItemDict(itemDict)
    if not succ then
        return false
    end
    for typeId, itemDetail in pairs(typeToItemDict) do
        if not self[BagComp.customValidConsumeAll[typeId]](self, itemDetail, reason) then
            return false
        end
    end
    return true, typeToItemDict
end

function BagComp:getTypeToItemDict(itemDict)
    local succ = true
    local ret = {}
    for itemId, num in pairs(itemDict) do
        local itemType = ProcessedTableEnv.ID_SEG_TO_TYPE[math.floor(itemId / 100000)]
        if not itemType then
            succ = false
            break
        end
        if not ret[itemType] then
            ret[itemType] = {}
        end
        ret[itemType][itemId] = num
    end
    return succ, ret
end

function BagComp:NotifyNewItem(typeToItemDict, mailAdd)
    self:safeClientRpc("OnNewItem", typeToItemDict, mailAdd)
end

function BagComp:AddReward(rewards, reason)
    if rewards._type_ == "RewardCoin" then
        self:AddToBag(rewards.coin, reason)
    elseif rewards._type_ == "RewardCoinItem" then
        self:AddToBag(rewards.coin, reason)
        self:AddToBag(rewards.item, reason)
    end
end

--endregion

--region 各类背包接口映射

BagComp.customDirectAddToBag = {
    [TableConst.enums.IdSegType.Currency] = "addCurrency",
    [TableConst.enums.IdSegType.Item] = "addItem",
    [TableConst.enums.IdSegType.Material] = "addMaterial",
    [TableConst.enums.IdSegType.Token] = "addToken",
}
BagComp.customValidAddAll = {
    [TableConst.enums.IdSegType.Currency] = "validAddAllCurrency",
    [TableConst.enums.IdSegType.Item] = "validAddAllItem",
    [TableConst.enums.IdSegType.Material] = "validAddAllMaterial",
    [TableConst.enums.IdSegType.Token] = "validAddAllToken",
}
BagComp.customDirectConsumeFromBag = {
    [TableConst.enums.IdSegType.Currency] = "consumeCurrency",
    [TableConst.enums.IdSegType.Item] = "consumeItem",
    [TableConst.enums.IdSegType.Material] = "consumeMaterial",
    [TableConst.enums.IdSegType.Token] = "consumeToken",
}
BagComp.customValidConsumeAll = {
    [TableConst.enums.IdSegType.Currency] = "validConsumeAllCurrency",
    [TableConst.enums.IdSegType.Item] = "validConsumeItem",
    [TableConst.enums.IdSegType.Material] = "validConsumeMaterial",
    [TableConst.enums.IdSegType.Token] = "validConsumeToken",
}

--endregion

--region 主动使用道具

function BagComp:UseItem(useList)
    local cost = {}
    for _, useArg in ipairs(useList) do
        local config = GameCommon.TableDataManager:GetItemData(useArg.id)
        if not config or not config.active_use then
            return
        end
        if useArg.num > GameCommon.TableDataManager:GetItemConst("SINGLE_USE_LIMIT") then
            return
        end
        cost[useArg.id] = (cost[useArg.id] or 0) + useArg.num
        if not self["_useCheck" .. config.use_func._type_](self, useArg.id, useArg.choice) then
            return
        end
    end
    if not self:ConsumeFromBag(cost, TableConst.enums.Reason.USE_ITEM) then
        return
    end
    local items = {}
    for _, useArg in ipairs(useList) do
        local config = GameCommon.TableDataManager:GetItemData(useArg.id)
        for itemId, num in pairs(self["_use" .. config.use_func._type_](self, useArg.id, useArg.choice, useArg.num)) do
            items[itemId] = (items[itemId] or 0) + num
        end
    end
    self:AddToBag(items, TableConst.enums.Reason.USE_ITEM)
end

function BagComp:_useCheckChooseItem(itemId, choice)
    local config = GameCommon.TableDataManager:GetItemData(itemId)
    if not config.use_func.options[choice] then
        return false
    end
    return true
end

function BagComp:_useChooseItem(itemId, choice, num)
    local config = GameCommon.TableDataManager:GetItemData(itemId)
    local num = config.use_func.options[choice] * num
    return {[choice] = num}
end

function BagComp:_useCheckChooseRes(itemId, choice)
    local config = GameCommon.TableDataManager:GetItemData(itemId)
    if not config.use_func.options[choice] then
        return false
    end
    return true
end

function BagComp:_useChooseRes(itemId, choice, num)
    local config = GameCommon.TableDataManager:GetItemData(itemId)
    local num = config.use_func.options[choice] * num
    return {[choice] = num}
end

function BagComp:_useCheckRandomItem(itemId, choice)
    return true
end

function BagComp:_useRandomItem(itemId, choice, num)
    local config = GameCommon.TableDataManager:GetItemData(itemId)
    local items = {}
    local index2Weight = {}
    for idx, opt in ipairs(config.use_func.rand_list) do
        index2Weight[idx] = opt.weight
    end
    for i = 1, num do
        local randIdx = ChooseEnv.RandomChooseByWeight(index2Weight)
        local opt = config.use_func.rand_list[randIdx]
        items[opt.id] = (items[opt.id] or 0) + opt.num
    end
    return items
end

--endregion

--region 分解信物

function BagComp:DecomposeToken(tokenIdList)
    local validToken = {}
    for _, tokenId in ipairs(tokenIdList) do
        local config = GameCommon.TableDataManager:GetTokenData(tokenId)
        local inBag = self.props.tokenBag[tokenId]
        if config and inBag then
            if ProcessedTableEnv.HERO_2_TOKEN_MAP[config.hero] then
                if self:IsHeroMaxStars(config.hero) then
                    validToken[tokenId] = inBag.num
                end
            elseif ProcessedTableEnv.TACTIC_2_TOKEN_MAP[config.tactic] then
                if self:IsTacticMaxStars(config.tactic) then
                    validToken[tokenId] = inBag.num
                end
            end
        end
    end
    for tokenId, num in pairs(validToken) do
        local config = GameCommon.TableDataManager:GetTokenData(tokenId)
        self:ConsumeFromBag({[tokenId] = num}, TableConst.enums.Reason.DECOMPOSE_TOKEN)
        self:AddToBag({[config.decompose_coin] = config.decompose_num * num}, TableConst.enums.Reason.DECOMPOSE_TOKEN)
    end
end

--endregion

--region WorldAvatar接口代理

function BagComp:addCurrency(itemDict, reason)
    self.worldAvatar:addCurrency(itemDict, reason)
end

function BagComp:validAddAllCurrency(itemDict, reason)
    return self.worldAvatar:validAddAllCurrency(itemDict, reason)
end

function BagComp:consumeCurrency(itemDict, reason)
    return self.worldAvatar:consumeCurrency(itemDict, reason)
end

function BagComp:validConsumeAllCurrency(itemDict, reason)
    return self.worldAvatar:validConsumeAllCurrency(itemDict, reason)
end

--endregion

--region CommonBag接口代理

function BagComp:addItem(itemDict, reason)
    return _COMMON_BAG:addItem(self, self.itemBagAttr, self.props.itemBag, itemDict, reason)
end

function BagComp:validAddAllItem(itemDict, reason, retMailAdd)
    return _COMMON_BAG:validAddAllItem(self, self.itemBagAttr, self.props.itemBag, itemDict, reason, retMailAdd)
end

function BagComp:consumeItem(itemDict, reason)
    return _COMMON_BAG:consumeItem(self, self.itemBagAttr, self.props.itemBag, itemDict, reason)
end

function BagComp:validConsumeItem(itemDict, reason)
    return _COMMON_BAG:validConsumeItem(self, self.itemBagAttr, self.props.itemBag, itemDict, reason)
end

function BagComp:addMaterial(itemDict, reason)
    return _COMMON_BAG:addItem(self, self.materialBagAttr, self.props.materialBag, itemDict, reason)
end

function BagComp:validAddAllMaterial(itemDict, reason, retMailAdd)
    return _COMMON_BAG:validAddAllItem(self, self.materialBagAttr, self.props.materialBag, itemDict, reason, retMailAdd)
end

function BagComp:consumeMaterial(itemDict, reason)
    return _COMMON_BAG:consumeItem(self, self.materialBagAttr, self.props.materialBag, itemDict, reason)
end

function BagComp:validConsumeMaterial(itemDict, reason)
    return _COMMON_BAG:validConsumeItem(self, self.materialBagAttr, self.props.materialBag, itemDict, reason)
end

function BagComp:addToken(itemDict, reason)
    return _COMMON_BAG:addItem(self, self.tokenBagAttr, self.props.tokenBag, itemDict, reason)
end

function BagComp:validAddAllToken(itemDict, reason, retMailAdd)
    return _COMMON_BAG:validAddAllItem(self, self.tokenBagAttr, self.props.tokenBag, itemDict, reason, retMailAdd)
end

function BagComp:consumeToken(itemDict, reason)
    return _COMMON_BAG:consumeItem(self, self.tokenBagAttr, self.props.tokenBag, itemDict, reason)
end

function BagComp:validConsumeToken(itemDict, reason)
    return _COMMON_BAG:validConsumeItem(self, self.tokenBagAttr, self.props.tokenBag, itemDict, reason)
end

--endregion

--region 通用背包逻辑

function BagComp:initAllBagAttr()
    self.itemBagAttr = {
        _bagCap = GameCommon.TableDataManager:GetItemConst("ITEM_BAG_CAP")
    }
    _COMMON_BAG:InitAttr(self, self.itemBagAttr, self.props.itemBag)
    self.materialBagAttr = {
        _bagCap = GameCommon.TableDataManager:GetItemConst("MATERIAL_BAG_CAP")
    }
    _COMMON_BAG:InitAttr(self, self.materialBagAttr, self.props.materialBag)
    self.tokenBagAttr = {
        _bagCap = GameCommon.TableDataManager:GetItemConst("TOKEN_BAG_CAP")
    }
    _COMMON_BAG:InitAttr(self, self.tokenBagAttr, self.props.tokenBag)
end

function BagComp:getItemConfig(itemId)
    local itemType = ProcessedTableEnv.ID_SEG_TO_TYPE[math.floor(itemId / 100000)]
    if itemType == TableConst.enums.IdSegType.Item then
        return GameCommon.TableDataManager:GetItemData(itemId)
    elseif itemType == TableConst.enums.IdSegType.Material then
        return GameCommon.TableDataManager:GetMaterialData(itemId)
    elseif itemType == TableConst.enums.IdSegType.Token then
        return GameCommon.TableDataManager:GetTokenData(itemId)
    end
end

function BagComp:onSetTime()
    _COMMON_BAG:restartItemExpireTimer(self, self.itemBagAttr, self.props.itemBag)
    _COMMON_BAG:restartItemExpireTimer(self, self.materialBagAttr, self.props.materialBag)
    _COMMON_BAG:restartItemExpireTimer(self, self.tokenBagAttr, self.props.tokenBag)
end

--endregion

--region 过期提醒

function BagComp:addExpiredItem(itemId, num)
    self.props.expiredItems[itemId] = (self.props.expiredItems[itemId] or 0) + num
end

function BagComp:ConfirmExpiredItems(items)
    local toRemove = {}
    for itemId, num in pairs(items) do
        self.props.expiredItems[itemId] = (self.props.expiredItems[itemId] or 0) - num
        if self.props.expiredItems[itemId] <= 0 then
            table.insert(toRemove, itemId)
        end
    end
    for _, itemId in ipairs(toRemove) do
        self.props.expiredItems[itemId] = nil
    end
end

--endregion

--region GM

function BagComp:gmAddItem(itemId, num)
    local items = {}
    if itemId <= 0 then
        local configList = {
            GameCommon.TableDataManager:GetAllItemData(),
            GameCommon.TableDataManager:GetAllMaterialData(),
            GameCommon.TableDataManager:GetAllTokenData(),
        }
        for _, config in ipairs(configList) do
            for itemId, item in pairs(config) do
                items[itemId] = num
            end
        end
    else
        items[itemId] = num
    end
    self:AddToBag(items, TableConst.enums.Reason.GM)
end

function BagComp:gmConsumeItem(itemId, num)
    local items = {}
    if itemId <= 0 then
        local propList = {
            self.props.itemBag,
            self.props.materialBag,
            self.props.tokenBag,
        }
        for _, prop in ipairs(propList) do
            for id, info in pairs(prop) do
                items[id] = math.min(num, info.num)
            end
        end
    else
        if not self.props.itemBag[itemId] then
            return
        end
        items[itemId] = math.min(cnt, self.props.itemBag[itemId].num)
    end
    self:ConsumeFromBag(items, TableConst.enums.Reason.GM)
end

--endregion

--region 通用背包初始化

---@class CommonBag
CommonBag = DefineClass("CommonBag")

function CommonBag.InitAttr(cls, avatar, attr, bagProp)
    attr._itemGridUsed = 0
    for itemId, item in pairs(bagProp) do
        -- 获取道具占用格子数量，限时道具不同时效的占用多个不同格子
        if table.isempty(item.special) then
            attr._itemGridUsed = attr._itemGridUsed + 1
        else
            attr._itemGridUsed = attr._itemGridUsed + #item.special
        end
    end
    attr._itemExpireQueue = PriorityQueueEnv.PriorityQueue.new(false)
    attr._itemExpireTimer = nil
    cls:initItemExpire(avatar, attr, bagProp)
end

--endregion

--region 增加道具

function CommonBag.addItem(cls, avatar, attr, bagProp, itemDict, reason)
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    avatar:logInfo("addItem success, itemDict: %v, reason: %s", itemDict, reasonDetail.Name)
    for itemId, num in pairs(itemDict) do
        local config = avatar:getItemConfig(itemId)
        if not bagProp[itemId] or (config.expire and config.expire > 0) then
            attr._itemGridUsed = attr._itemGridUsed + 1
        end
        if not bagProp[itemId] then
            bagProp[itemId] = {}
        end
        bagProp[itemId].num = bagProp[itemId].num + num
        if config.expire and config.expire > 0 then
            local expireTime = ServerTimeEnv.GetServerNow() + config.expire
            bagProp[itemId].special:insert({expireTime = expireTime, num = num})
            cls:waitItemExpire(avatar, attr, bagProp, itemId)
        end
    end
end

function CommonBag.validAddAllItem(cls, avatar, attr, bagProp, itemDict, reason, retMailAdd)
    local valid, directAdd = true, {}
    local newUsedGrid = 0
    for itemId, num in pairs(itemDict) do
        if num <= 0 then
            valid = false
            break
        end
        local config = avatar:getItemConfig(itemId)
        if not config then
            valid = false
            break
        end
        local inBag = bagProp[itemId]
        if not inBag or (config.expire and config.expire > 0) then
            newUsedGrid = newUsedGrid + 1
        end
        if newUsedGrid + attr._itemGridUsed <= attr._bagCap then
            local directNum = math.min(math.max(0, config.cap - (inBag and inBag.num or 0)), num)
            local mailNum = num - directNum
            if directNum > 0 then
                directAdd[itemId] = directNum
            end
            if mailNum > 0 then
                retMailAdd[itemId] = mailNum
            end
        else
            retMailAdd[itemId] = num
        end
    end
    return valid, directAdd
end

--endregion

--region 消耗道具

function CommonBag.consumeItem(cls, avatar, attr, bagProp, itemDict, reason)
    local reasonDetail = enum_details.GetEnumItemDetail("Reason", reason)
    avatar:logInfo("consumeItem success, itemDict: %v, reason: %s", itemDict, reasonDetail.Name)
    for itemId, num in pairs(itemDict) do
        local inBag = bagProp[itemId]
        assert(inBag)
        inBag.num = inBag.num - num
        if #inBag.special > 0 then
            local toRemove = {}
            for spIdx, detail in ipairs(inBag.special) do
                if num <= 0 then
                    break
                end
                local spConsume = math.min(num, detail.num)
                num = num - spConsume
                detail.num = detail.num - spConsume
                if detail.num <= 0 then
                    table.insert(toRemove, 1, spIdx)
                end
            end
            for _, spIdx in ipairs(toRemove) do
                inBag.special:remove(spIdx)
            end
            attr._itemGridUsed = attr._itemGridUsed - #toRemove
        elseif inBag.num <= 0 then
            attr._itemGridUsed = attr._itemGridUsed - 1
        end
        if inBag.num <= 0 then
            bagProp[itemId] = nil
        end
    end
end

function CommonBag.validConsumeItem(cls, avatar, attr, bagProp, itemDict)
    if table.isempty(itemDict) then
        return false
    end
    for itemId, num in pairs(itemDict) do
        local config = avatar:getItemConfig(itemId)
        if not config then
            return false
        end
        local inBag = bagProp[itemId]
        if not inBag then
            return false
        end
        if inBag.num < num then
            return false
        end
    end
    return true
end

--endregion

--region 限时道具自动过期

function CommonBag.initItemExpire(cls, avatar, attr, bagProp)
    -- 初始化时遍历所有显示道具，加入到过期等待队列，依次定时过期
    for itemId, item in pairs(bagProp) do
        if #item.special > 0 then
            local expireTime = item.special[1].expireTime
            attr._itemExpireQueue:push({
                itemId = itemId,
                expireTime = expireTime,
            }, expireTime)
        end
    end
    cls:restartItemExpireTimer(avatar, attr, bagProp)
end

function CommonBag.restartItemExpireTimer(cls, avatar, attr, bagProp)
    if attr._itemExpireQueue:isEmpty() then
        return
    end
    if attr._itemExpireTimer then
        avatar:delTimer(attr._itemExpireTimer)
        attr._itemExpireTimer = nil
    end
    local delay = math.max(0, attr._itemExpireQueue:peek().expireTime - ServerTimeEnv.GetServerNow())
    attr._itemExpireTimer = avatar:addTimer(delay, 0, function()
        if attr._itemExpireQueue:isEmpty() then
            return
        end
        local expireTime = attr._itemExpireQueue:peek().expireTime
        if expireTime <= ServerTimeEnv.GetServerNow() then
            local itemId = attr._itemExpireQueue:pop().itemId
            cls:handleItemExpire(avatar, attr, bagProp, itemId)
        end
        cls:restartItemExpireTimer(avatar, attr, bagProp)
    end)
end

function CommonBag.handleItemExpire(cls, avatar, attr, bagProp, itemId)
    -- 新的过期道具被加入到过期等待队列
    local item = bagProp[itemId]
    if not item then
        -- 正常情况，消耗限时道具，不会取消对应的timer
        return
    end
    local mayExpireSp = item.special[1]
    if mayExpireSp.expireTime <= ServerTimeEnv.GetServerNow() then
        avatar:logInfo("[qun] handleItemExpire, itemId: %s, num: %s", itemId, mayExpireSp.num)
        item.num = math.max(0, item.num - mayExpireSp.num)
        item.special:remove(1)
        attr._itemGridUsed = attr._itemGridUsed - 1
        if item.num <= 0 then
            bagProp[itemId] = nil
        end
        avatar:addExpiredItem(itemId, mayExpireSp.num)
    end
    cls:waitItemExpire(avatar, attr, bagProp, itemId)
end

function CommonBag.waitItemExpire(cls, avatar, attr, bagProp, itemId)
    if bagProp[itemId] and #bagProp[itemId].special > 0 then
        attr._itemExpireQueue:push({
            itemId = itemId,
            expireTime = bagProp[itemId].special[1].expireTime,
        }, bagProp[itemId].special[1].expireTime)
        cls:restartItemExpireTimer(avatar, attr, bagProp)
    end
end

--endregion

_COMMON_BAG = CommonBag()
