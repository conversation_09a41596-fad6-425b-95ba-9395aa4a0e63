﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")

---@class BattleBuffLayer
---@field new fun() : BattleBuffLayer
local BattleBuffLayer = DefineClass("BattleBuffLayer")

function BattleBuffLayer:ctor(buff, remainRound)
    self.buff = buff
    self.remainRound = remainRound
    self.attributeModifierKeys = {}
    self.crowdControlKeys = {}
    self.priorSelectTargetKeys = {}
    self.isNeedToCalculateRemainRound = false
end

function BattleBuffLayer:OnRemove()
    if self.attributeModifierKeys then
        self.buff.hero:BatchRemoveAttributeModifier(self.attributeModifierKeys)
        self.attributeModifierKeys = {}
    end
    if self.crowdControlKeys then
        for _, tuple in ipairs(self.crowdControlKeys) do
            local ccType, key = unpack(tuple)
            self.buff.hero:RemoveCrowdControl(ccType, key)
        end
    end
    if self.priorSelectTargetKeys then
        for _, key in ipairs(self.priorSelectTargetKeys) do
            self.buff.hero:RemovePriorSelectTarget(key)
        end
    end
end


---@class BattleBuff
local BattleBuff = DefineClass("BattleBuff")

function BattleBuff:ctor(hero, buffId, sourceId, level, extraArgs)
    self.hero = hero
    self.buffId = buffId
    self.sourceId = sourceId -- 来源英雄id
    self.level = level or 1
    self.buffData = table.deepclone(GameCommon.TableDataManager:GetBuffData(buffId))
    if extraArgs then
        self.sourceTacticId = extraArgs.sourceTacticId -- 来源战法id
        self.sourceTacticHero = extraArgs.sourceTacticHero -- 来源战法所属的英雄
        self.fixedArgs = extraArgs.fixedArgs --每次执行都传入的固定参数
        self.buffData.total_round = extraArgs.coveredTotalRound or self.buffData.total_round
    end
    self.buffLayers = {}
    self.battleEvents = {}
    self.decreaseLayerCount = {}
    self.attributeModifierKeys = {} --初始化的属性修改记录在这里
    self.crowdControlKeys = {} --初始化的特殊属性修改记录在这里
    self.priorSelectTargetKeys = {} --初始化的特殊属性修改记录在这里
    local jsonFile = self.buffData.json_file
    if jsonFile ~= "" then
        local graph = self.hero.battleGame.abilitySystemManager:CreateBuffGraph(jsonFile)
        local i = 1
        while true do
            local eventType = graph.rootNode["event" .. i]
            if not eventType then
                break
            end
            if eventType > 0 then
                local eventTypeName = BattleConstEnv.BattleEventRecordTypeNames[eventType]
                self.battleEvents[eventType] = {}
                self.battleEvents[eventType].name = eventTypeName
                local eventPriorityData = GameCommon.TableDataManager:GetRowByMultiIndices("battle_tbeventpriority", {battle_event = eventType, name = jsonFile})
                self.battleEvents[eventType].priority = eventPriorityData and eventPriorityData.priority or 0
                self.battleEvents[eventType].isSelf = graph.rootNode["onlySelf" .. i]
            end
            i = i + 1
        end
    end
end

function BattleBuff:IncreaseLayerCount(count, nestIds, coveredTotalRound)
    local layerTotalRound = coveredTotalRound or self.buffData.total_round
    local increased = 0
    for i = 1, count do
        increased = increased + self:IncreaseLayer(nestIds[i], layerTotalRound)
    end
    return increased
end

function BattleBuff:IncreaseLayer(nestId, layerTotalRound)
    if self:GetLayerCount() < self.buffData.max_stack_count then --未达到最大堆叠层数 执行叠层规则
        return self:ExecuteStackRule()
    else --达到最大堆叠层数 执行叠加规则
        return self:ExecuteOverlayRule(layerTotalRound)
    end
end

function BattleBuff:OnLayerIncreased()
    local instance = self.hero.battleGame.abilitySystemManager
    local args = {
        Level = self.level,
        Round = self.hero.battleGame.round
    }
    table.update(args, self.fixedArgs)
    local briefData, reason = instance:TriggerBuffGraph(self, self.buffLayers[#self.buffLayers], "onLayerIncreased", args)
    if briefData then
        self.hero.battleGame.recorder:BriefRecord(briefData)
    end

end

function BattleBuff:ExecuteStackRule()
    if self.buffData.can_stack then
        if self.buffData.stack_rule == TableConst.enums.BuffStackRule.Mutex then
        elseif self.buffData.stack_rule == TableConst.enums.BuffStackRule.Refresh then
            local maxRemainRound = 0 --刷新为当前剩余最大回合数
            for i = 1, #self.buffLayers do
                if self.buffLayers[i].remainRound > maxRemainRound then
                    maxRemainRound = self.buffLayers[i].remainRound
                end
            end
            for i = 1, #self.buffLayers do
                self.buffLayers[i].remainRound = maxRemainRound
            end
        end
        table.insert(self.buffLayers, BattleBuffLayer.new(self, self.buffData.total_round))
        self:OnLayerIncreased()
        return 1
    else
        -- 不能叠加的buff不需要考虑填的叠加规则
        if not self.buffLayers[1] then
            self.buffLayers[1] = BattleBuffLayer.new(self, self.buffData.total_round)
            self:OnLayerIncreased()
            return 1
        else
            self.buffLayers[1].remainRound = self.buffData.total_round
            self.hero.battleGame.recorder:DetailRefreshBuff(self.hero.uniqueId, self.buffId)
            return 0
        end
    end
end

function BattleBuff:ExecuteOverlayRule(layerTotalRound)
    if self.buffData.overlay_rule == TableConst.enums.BuffOverlayRule.Replace then
        self:ExecuteOverlayRuleReplace(1)
        return 1
    elseif self.buffData.overlay_rule == TableConst.enums.BuffOverlayRule.ReplaceMaxRemainRound then
        local pos
        local minRemainRound = math.huge
        for i = 1, #self.buffLayers do
            if self.buffLayers[i].remainRound < minRemainRound then
                minRemainRound = self.buffLayers[i].remainRound
                pos = i
            end
        end
        if minRemainRound < layerTotalRound then
            self:ExecuteOverlayRuleReplace(pos)
            return 1
        else
            return 0
        end
    elseif self.buffData.overlay_rule == TableConst.enums.BuffOverlayRule.Refresh then
        for i = 1, #self.buffLayers do
            self.buffLayers[i].remainRound = self.buffData.total_round
        end
        self.hero.battleGame.recorder:DetailRefreshBuff(self.hero.uniqueId, self.buffId)
        return 0
    end
end

function BattleBuff:ExecuteOverlayRuleReplace(pos)
    local buffLayer = table.remove(self.buffLayers, pos)
    buffLayer:OnRemove()
    table.insert(self.buffLayers, BattleBuffLayer.new(self, self.buffData.total_round))
    self:OnLayerIncreased()
end

function BattleBuff:_InternalDecreaseLayerCount(count)
    local layerCount = self:GetLayerCount()
    for i = 1, math.min(count, layerCount) do
        local buffLayer = table.remove(self.buffLayers)
        buffLayer:OnRemove()
    end
end

function BattleBuff:DecreaseLayerCount(count)
    self:_InternalDecreaseLayerCount(count)
    self.hero.battleGame.recorder:DetailDecreaseBuff(self.hero.uniqueId, self.buffId, self:GetLayerCount())
    if self:GetLayerCount() <= 0 then
        self.hero:RemoveBuff(self)
    end
end

function BattleBuff:GetLayerCount()
    return #self.buffLayers
end

function BattleBuff:GetRemainRound()
    local maxRemainRound = 0
    for i = 1, #self.buffLayers do
        if self.buffLayers[i].remainRound > maxRemainRound then
            maxRemainRound = self.buffLayers[i].remainRound
        end
    end
    return maxRemainRound
end

function BattleBuff:CalculateRemainRound()
    local toRemove = {}
    for i = #self.buffLayers, 1, -1 do
        if self.buffLayers[i].isNeedToCalculateRemainRound then
            self.buffLayers[i].remainRound = self.buffLayers[i].remainRound - 1
            if self.buffLayers[i].remainRound <= 0 then
                table.insert(toRemove, i)
            end
        end
    end
    local newLayerCount = self:GetLayerCount() - #toRemove
    if #toRemove > 0 then
        self.hero.battleGame.recorder:DetailDecreaseBuff(self.hero.uniqueId, self.buffId, newLayerCount)
    end
    if newLayerCount > 0 then
        for _, i in ipairs(toRemove) do
            local buffLayer = table.remove(self.buffLayers, i)
            buffLayer:OnRemove()
        end
    else
        if self.buffData.remove_timing == TableConst.enums.BuffRemoveTiming.AfterHeroTurnEnd then
            self.hero:RemoveBuff(self)
        elseif self.buffData.remove_timing == TableConst.enums.BuffRemoveTiming.AfterRoundEnd then
            table.insert(self.hero.battleGame.buffsToRemoveAfterRoundEnd, self)
        end
    end
end

function BattleBuff:OnTrigger(eventArgs)
    local recorder = self.hero.battleGame.recorder
    local detailPointer, currentHeroId = recorder:GetDetailPointerAndHero()
    recorder:DetailTriggerBuff(self.hero.uniqueId, self.buffId, self.sourceTacticId, self.sourceTacticHeroId)
    local portName = self.battleEvents[eventArgs.eventType].name
    if portName then
        eventArgs.Level = self.level
        eventArgs.Round = self.hero.battleGame.round
        table.update(eventArgs, self.fixedArgs)
        local instance = self.hero.battleGame.abilitySystemManager
        local briefData, reason = instance:TriggerBuffGraph(self, self.buffLayers[1], portName .. "Node", eventArgs)
        if briefData then
            if reason then
                local dp = recorder:GetNextDetail(detailPointer, TableConst.enums.DetailRecordType.TriggerBuff)
                recorder:ReplaceDetail({TableConst.enums.DetailRecordType.TriggerBuff, self.buffId, self.sourceTacticId, self.sourceTacticHeroId, reason}, dp)
            end
            recorder:BriefRecord(briefData)
            if self.decreaseLayerCount[portName] then
                self:DecreaseLayerCount(1)
            end
            return true
        else
            recorder:SetDetailPointerAndHero(detailPointer, currentHeroId)
            if reason then
                recorder:DetailTriggerBuffFailed(self.hero.uniqueId, self.buffId, self.sourceTacticId, self.sourceTacticHeroId, reason)
            end
        end
    end
end

function BattleBuff:OnDestroy()
    -- 合并层中的属性修改
    local layerAttributeModifierKeys = {}
    for _, buffLayer in ipairs(self.buffLayers) do
        if buffLayer.attributeModifierKeys then
            table.extend(layerAttributeModifierKeys, buffLayer.attributeModifierKeys)
            buffLayer.attributeModifierKeys = nil
        end
        buffLayer:OnRemove()
    end
    self.hero:BatchRemoveAttributeModifier(layerAttributeModifierKeys)
    self.buffLayers = {}

    if self.attributeModifierKeys then
        self.hero:BatchRemoveAttributeModifier(self.attributeModifierKeys)
        self.attributeModifierKeys = {}
    end
    if self.crowdControlKeys then
        for _, tuple in ipairs(self.crowdControlKeys) do
            local ccType, key = unpack(tuple)
            self.hero:RemoveCrowdControl(ccType, key)
        end
    end
    if self.priorSelectTargetKeys then
        for _, key in ipairs(self.priorSelectTargetKeys) do
            self.hero:RemovePriorSelectTarget(key)
        end
    end

    local sourceHero = self.hero.battleGame:GetHeroByUid(self.sourceId)
    table.remove(sourceHero.appliedBuffs, table.index(sourceHero.buffs, self)) --移除施加者的施加buff列表记录
end

function BattleBuff:Dump()
    return {
        buffId = self.buffId,
        count = self:GetLayerCount(),
    }
end
