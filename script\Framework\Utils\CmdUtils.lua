-- 转发配置
---@class CmdForward
CmdForward = {
    avatarOnlineNum = {service="RoleService", method="avatarOnlineNum"},
}


-- 互斥配置
-- 如果已有同类型的cmd正在执行，则返回失败；global类型与所有cmd互斥
---@class CmdLock
CmdLock = {
    shutdown = {global=1},
}


---@class CmdUtils
CmdUtils = {}

-- pack result
---@param ar? AsyncResult
---@param res? any
---@return table
function CmdUtils.res(ar, res)
    return ar == "ERROR" and {suc = false, err = res or ar} or {suc = true, res = res}
end

---@param num int
---@param endian "'little' | 'big'"
---@param signed? boolean
---@param len? int @数据长度（字节），如果给定的num转换后大于len，会抛出错误
function CmdUtils.int2bytes(num, endian, signed, len)
    if num<0 and not signed then
        error("signed number converting to unsigned")
    end
    local n = math.ceil(select(2,math.frexp(num))/8) -- number of bytes to be used.
    if len then
        if n > len then
            error(string.format("bytes len overflow(%d > %d)", n, len))
        else
            n = len
        end
    end
    local res={}
    if signed and num < 0 then
        num = num + 2^n
    end
    for k=n,1,-1 do -- 256 = 2^8 bits per char.
        local mul=2^(8*(k-1))
        res[k]=math.floor(num/mul)
        num=num-res[k]*mul
    end
    assert(num==0)
    if endian == "big" then
        local t={}
        for k=1,n do
            t[k]=res[n-k+1]
        end
        res=t
    end
    return string.char(unpack(res))
end

function CmdUtils.bytes2int(bytes, endian, signed) -- use length of string to determine 8,16,32,64 bits
    local t={bytes:byte(1,-1)}
    if endian=="big" then --reverse bytes
        local tt={}
        for k=1,#t do
            tt[#t-k+1]=t[k]
        end
        t=tt
    end
    local n=0
    for k=1,#t do
        n=n+t[k]*2^((k-1)*8)
    end
    if signed then
        n = (n > 2^(#t-1) -1) and (n - 2^#t) or n -- if last bit set, negative.
    end
    return n
end

---@param cmdProxy CmdProxy
---@param res table
function CmdUtils.feedback(cmdProxy, res)
    EZGlobal.Entity:serviceRpc("CmdService", "asyncFeedback", cmdProxy, res)
end

-- for emmylua
---@class CmdUtilsEnv
local CmdUtilsEnv = {
    CmdForward=CmdForward,
    CmdLock=CmdLock,
    CmdUtils=CmdUtils,
}
return CmdUtilsEnv
