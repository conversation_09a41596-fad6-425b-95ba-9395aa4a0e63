[{"id": 10001, "name": "灼烧", "desc": "负面状态，大回合结束时受到灼烧层数*50%的伤害（受buff施加者 施加时的智力计算）", "show": true, "record": true, "adjunct": false, "json_file": "buff_10001", "type": 0, "is_debuff": true, "total_round": 3, "can_stack": true, "stack_rule": 1, "max_stack_count": 10, "overlay_rule": 2, "icon_path": "icon-buff4", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 10001, "effect_socket": "Foot", "jump_on_add": "添加灼烧", "jump_on_remove": "移除灼烧", "max_combat": 0}, {"id": 10002, "name": "计穷", "desc": "负面状态，无法使用主动战法", "show": true, "record": true, "adjunct": false, "json_file": "buff_10002", "type": 0, "is_debuff": true, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff2", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加乐", "jump_on_remove": "移除乐", "max_combat": 0}, {"id": 10003, "name": "缴械", "desc": "无法普攻", "show": true, "record": true, "adjunct": false, "json_file": "buff_10003", "type": 0, "is_debuff": true, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff3", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加缴械", "jump_on_remove": "移除缴械", "max_combat": 0}, {"id": 10004, "name": "震慑", "desc": "无法行动", "show": true, "record": true, "adjunct": false, "json_file": "buff_10004", "type": 0, "is_debuff": true, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff1", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 10010, "effect_socket": "HpPoint", "jump_on_add": "添加震慑", "jump_on_remove": "移除震慑", "max_combat": 0}, {"id": 10005, "name": "水攻", "desc": "负面状态，统率-50", "show": true, "record": true, "adjunct": false, "json_file": "buff_10005", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff6", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加水攻", "jump_on_remove": "移除水攻", "max_combat": 0}, {"id": 10006, "name": "虚弱", "desc": "负面状态，造成伤害减少70%", "show": true, "record": true, "adjunct": false, "json_file": "buff_10006", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加粮", "jump_on_remove": "移除粮", "max_combat": 0}, {"id": 10007, "name": "断粮", "desc": "负面状态，受到治疗效果降低70%", "show": true, "record": true, "adjunct": false, "json_file": "buff_10007", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加粮", "jump_on_remove": "移除粮", "max_combat": 0}, {"id": 10008, "name": "嘲讽", "desc": "强制攻击目标", "show": true, "record": true, "adjunct": false, "json_file": "buff_10008", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加嘲讽", "jump_on_remove": "移除嘲讽", "max_combat": 0}, {"id": 10009, "name": "抵御", "desc": "受到的下一次伤害减少70%-100%  可叠加2层", "show": true, "record": true, "adjunct": false, "json_file": "buff_10009", "type": 0, "is_debuff": false, "total_round": 8, "can_stack": true, "stack_rule": 1, "max_stack_count": 2, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加抵御", "jump_on_remove": "移除抵御", "max_combat": 0}, {"id": 10010, "name": "威慑", "desc": "下一次主动或追击战法目标优先选择Buff来源且造成伤害降低35%", "show": true, "record": true, "adjunct": false, "json_file": "buff_10010", "type": 0, "is_debuff": true, "total_round": 1, "can_stack": true, "stack_rule": 1, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "添加威慑", "jump_on_remove": "移除威慑", "max_combat": 0}, {"id": 12333, "name": "额外军职-主公", "desc": "在考虑额外军职时视为主公", "show": true, "record": true, "adjunct": false, "json_file": "buff_12333", "type": 0, "is_debuff": true, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 20001, "name": "不屈", "desc": "伤害分摊", "show": true, "record": true, "adjunct": false, "json_file": "buff_20001", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "icon-buff5", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "免死", "jump_on_remove": "移除粮", "max_combat": 0}, {"id": 10100101, "name": "忠勇[预备]", "desc": "忠勇加属性", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100101", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 2, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100201, "name": "援护[预备]", "desc": "援护减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100201", "type": 0, "is_debuff": false, "total_round": 3, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100301, "name": "亲民减伤", "desc": "亲民减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100301", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": true, "stack_rule": 2, "max_stack_count": 4, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100302, "name": "亲民[预备]", "desc": "亲民减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100302", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100401, "name": "五禽戏[预备]", "desc": "五禽戏[预备]", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100401", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100402, "name": "五禽戏减伤", "desc": "五禽戏减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100402", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100501, "name": "请君入瓮[预备]", "desc": "请君入瓮[预备]", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100501", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10100502, "name": "请君入瓮", "desc": "请君入瓮控制", "show": true, "record": true, "adjunct": false, "json_file": "buff_10100502", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10200301, "name": "散谣[预备]", "desc": "散谣减属性", "show": true, "record": true, "adjunct": false, "json_file": "buff_10200301", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": true, "stack_rule": 2, "max_stack_count": 2, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10200601, "name": "扬威[预备]", "desc": "扬威加暴击", "show": true, "record": true, "adjunct": false, "json_file": "buff_10200601", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10200801, "name": "自愈", "desc": "自愈回血", "show": true, "record": true, "adjunct": false, "json_file": "buff_10200801", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10200901, "name": "洛水惊鸿​", "desc": "洛水惊鸿​", "show": true, "record": true, "adjunct": false, "json_file": "buff_10200901", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10300101, "name": "短兵[预备]", "desc": "短兵减统率", "show": true, "record": true, "adjunct": false, "json_file": "buff_10300101", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10400101, "name": "血战[预备]", "desc": "血战加连击", "show": true, "record": true, "adjunct": false, "json_file": "buff_10400101", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10400102, "name": "血战衰减[预备]", "desc": "血战每回合减连击", "show": true, "record": true, "adjunct": false, "json_file": "buff_10400102", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": true, "stack_rule": 2, "max_stack_count": 10, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10500101, "name": "元戎弩兵[预备]", "desc": "检测buff", "show": true, "record": true, "adjunct": false, "json_file": "buff_10500101", "type": 0, "is_debuff": false, "total_round": 4, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10500102, "name": "元戎弩兵-暴击[预备]", "desc": "加暴击", "show": true, "record": true, "adjunct": false, "json_file": "buff_10500102", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10600101, "name": "虎翼阵[预备]", "desc": "虎翼阵先锋增伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_10600101", "type": 0, "is_debuff": false, "total_round": 3, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10600102, "name": "虎翼阵[预备]", "desc": "虎翼阵主将减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_10600102", "type": 0, "is_debuff": false, "total_round": 3, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100101, "name": "仁德[预备]", "desc": "仁德加属性", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100101", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": true, "stack_rule": 2, "max_stack_count": 2, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100102, "name": "激将[预备]", "desc": "邀请普攻", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100102", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100201, "name": "破军[预备]", "desc": "破军预备", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100201", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100401, "name": "鬼才[预备]", "desc": "鬼才监听", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100401", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100402, "name": "鬼才[属性]", "desc": "鬼才加属性（非军）", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100402", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": true, "stack_rule": 2, "max_stack_count": 8, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100403, "name": "鬼才[属性]", "desc": "鬼才加属性（军）", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100403", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": true, "stack_rule": 2, "max_stack_count": 12, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100501, "name": "黄天[预备]", "desc": "黄天[预备]", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100501", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100502, "name": "雷击[预备]", "desc": "雷击[预备]", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100502", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100503, "name": "黄天[预备]", "desc": "黄天[预备]", "show": false, "record": false, "adjunct": false, "json_file": "buff_11100503", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": true, "stack_rule": 2, "max_stack_count": 2, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11300101, "name": "奇袭", "desc": "奇袭", "show": false, "record": false, "adjunct": false, "json_file": "buff_11300101", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": true, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11300102, "name": "奇袭-统率", "desc": "奇袭", "show": false, "record": false, "adjunct": false, "json_file": "buff_11300102", "type": 0, "is_debuff": true, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11200101, "name": "洛神[预备]", "desc": "概率降低", "show": false, "record": false, "adjunct": false, "json_file": "buff_11200101", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": true, "stack_rule": 2, "max_stack_count": 5, "overlay_rule": 2, "icon_path": "", "remove_timing": 2, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11200501, "name": "离间", "desc": "追击战法发动率提升", "show": false, "record": true, "adjunct": false, "json_file": "buff_11200501", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400101, "name": "咆哮[预备]", "desc": "自己回合普攻追击", "show": false, "record": true, "adjunct": false, "json_file": "buff_11400101", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400102, "name": "咆哮[预备]", "desc": "兵刃伤害提升", "show": false, "record": false, "adjunct": false, "json_file": "buff_11400102", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400103, "name": "咆哮[预备]", "desc": "兵刃伤害提升", "show": false, "record": false, "adjunct": false, "json_file": "buff_11400103", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": true, "stack_rule": 2, "max_stack_count": 9, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400201, "name": "武圣[破甲]", "desc": "破甲提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_11400201", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": true, "stack_rule": 2, "max_stack_count": 4, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400202, "name": "武魂[预备]", "desc": "武圣-武力 主动发动率提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_11400202", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": true, "stack_rule": 2, "max_stack_count": 4, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400203, "name": "武圣[水攻]", "desc": "水攻buff", "show": true, "record": true, "adjunct": false, "json_file": "buff_11400203", "type": 0, "is_debuff": true, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400204, "name": "武圣[预备]", "desc": "武圣预备", "show": false, "record": true, "adjunct": false, "json_file": "buff_11400204", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400301, "name": "不屈[预备]", "desc": "不屈预备", "show": false, "record": true, "adjunct": false, "json_file": "buff_11400301", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": true, "stack_rule": 2, "max_stack_count": 13, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 999001, "name": "兵种适应性", "desc": "兵种适应性属性加成", "show": true, "record": false, "adjunct": false, "json_file": "buff_999001", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "兵种适应性", "jump_on_remove": "", "max_combat": 0}, {"id": 9990021, "name": "城建科技盾兵十级特殊效果", "desc": "第2回合，我方盾兵受到伤害降低7%", "show": true, "record": false, "adjunct": false, "json_file": "buff_9990021", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100302, "name": "奸雄", "desc": "奸雄减属性", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100302", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": true, "stack_rule": 2, "max_stack_count": 2, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11100303, "name": "奸雄", "desc": "奸雄加属性", "show": false, "record": true, "adjunct": false, "json_file": "buff_11100303", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": true, "stack_rule": 2, "max_stack_count": 2, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": true, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100000, "name": "城建科技增加武力", "desc": "城建科技增加武力", "show": true, "record": true, "adjunct": false, "json_file": "buff_100000", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100001, "name": "城建科技增加智力", "desc": "城建科技增加智力", "show": true, "record": true, "adjunct": false, "json_file": "buff_100001", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100002, "name": "城建科技增加统帅", "desc": "城建科技增加统帅", "show": true, "record": true, "adjunct": false, "json_file": "buff_100002", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100003, "name": "城建科技增加速度", "desc": "城建科技增加速度", "show": true, "record": true, "adjunct": false, "json_file": "buff_100003", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100004, "name": "城建科技增加盾兵增减伤", "desc": "城建科技增加盾兵增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100004", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100005, "name": "城建科技增加弓兵增减伤", "desc": "城建科技增加弓兵增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100005", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100006, "name": "城建科技增加骑兵增减伤", "desc": "城建科技增加骑兵增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100006", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100007, "name": "城建科技增加枪兵增减伤", "desc": "城建科技增加枪兵增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100007", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100008, "name": "魏国武将增减伤", "desc": "魏国武将增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100008", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100009, "name": "蜀国武将增减伤", "desc": "蜀国武将增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100009", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100010, "name": "吴国武将增减伤", "desc": "吴国武将增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100010", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100011, "name": "群国武将增减伤", "desc": "群国武将增减伤", "show": true, "record": true, "adjunct": false, "json_file": "buff_100011", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100012, "name": "第2回合，我方盾兵受到伤害降低7%", "desc": "城建科技盾兵十级特殊效果", "show": true, "record": true, "adjunct": false, "json_file": "buff_100012", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 2, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100013, "name": "第3回合，我方弓兵会心和奇谋几率提升10%", "desc": "城建科技弓兵十级特殊效果", "show": true, "record": true, "adjunct": false, "json_file": "buff_100013", "type": 0, "is_debuff": false, "total_round": 3, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 2, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100014, "name": "首回合，我方骑兵规避率提升5%", "desc": "城建科技骑兵十级特殊效果", "show": true, "record": true, "adjunct": false, "json_file": "buff_100014", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 2, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 100015, "name": "第4回合，我方枪兵破甲和看破提升8%", "desc": "城建科技枪兵十级特殊效果", "show": true, "record": true, "adjunct": false, "json_file": "buff_100015", "type": 0, "is_debuff": false, "total_round": 4, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 2, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 200001, "name": "策牌-杀", "desc": "策牌-杀", "show": true, "record": true, "adjunct": false, "json_file": "", "type": 0, "is_debuff": false, "total_round": 4, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 1}, {"id": 200002, "name": "策牌-闪", "desc": "策牌-闪", "show": true, "record": true, "adjunct": false, "json_file": "", "type": 0, "is_debuff": false, "total_round": 4, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 1}, {"id": 11200201, "name": "流离加闪避", "desc": "闪避提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_11200201", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11200301, "name": "集智", "desc": "奇才BUFF", "show": true, "record": true, "adjunct": false, "json_file": "buff_11200301", "type": 0, "is_debuff": false, "total_round": 2, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10500201, "name": "闪避增加[预备]", "desc": "闪避提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_10500201", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10500202, "name": "前三回合闪避增加[预备]", "desc": "闪避提升（前3回合）", "show": true, "record": true, "adjunct": false, "json_file": "buff_10500202", "type": 0, "is_debuff": false, "total_round": 3, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10600201, "name": "胭脂阵发动率增加[预备]", "desc": "发动率提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_10600201", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10600202, "name": "胭脂阵回血[预备]", "desc": "胭脂阵治疗", "show": true, "record": true, "adjunct": false, "json_file": "buff_10600202", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11400501, "name": "受治疗提升", "desc": "闪避提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_11400501", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11300201, "name": "弓腰姬", "desc": "闪避提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_11300201", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 10, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 11200302, "name": "奇才概率降低", "desc": "闪避提升", "show": true, "record": true, "adjunct": false, "json_file": "buff_11200302", "type": 0, "is_debuff": false, "total_round": 1, "can_stack": false, "stack_rule": 2, "max_stack_count": 99, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10600203, "name": "胭脂阵先锋回血[预备]", "desc": "胭脂阵先锋回血", "show": true, "record": true, "adjunct": false, "json_file": "buff_10600203", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 10600204, "name": "胭脂阵属性增加[预备]", "desc": "胭脂阵属性增加", "show": true, "record": true, "adjunct": false, "json_file": "buff_10600204", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": false, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 7980001, "name": "装备词条加武力", "desc": "加10点武力", "show": true, "record": true, "adjunct": false, "json_file": "buff_7980001", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}, {"id": 7980002, "name": "装备词条武圣", "desc": "仅关羽发动率增加", "show": true, "record": true, "adjunct": false, "json_file": "buff_7980002", "type": 0, "is_debuff": false, "total_round": 9, "can_stack": false, "stack_rule": 2, "max_stack_count": 1, "overlay_rule": 2, "icon_path": "", "remove_timing": 1, "dispellable": false, "groupTypeList": [], "sourceDeadClear": true, "effect_id": 0, "effect_socket": "", "jump_on_add": "", "jump_on_remove": "", "max_combat": 0}]