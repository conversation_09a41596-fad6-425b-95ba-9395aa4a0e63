﻿local BattleConstEnv = xrequire("Common.Battle.BattleConst")
local BattleUtilsEnv = xrequire("Common.Battle.BattleUtils")

---@class BattleGameEvent
local BattleGameEvent = DefineClass("BattleGameEvent")

function BattleGameEvent:ctor(battleGame)
    self.battleGame = battleGame
    self.battleEvents = {}
    self.eventTypePriorityRecord = {} --记录每个事件类型中存在的优先级 在触发某个事件时直接查找对应优先级的事件table
    self.eventHeroRecord = {} --记录每个英雄注册了哪些事件 用来获取某个英雄的所有事件
end

function BattleGameEvent:ensureTableExists(table, ...)
    local keys = { ... }
    local currentTable = table

    for i = 1, #keys - 1 do
        local key = keys[i]
        if not currentTable[key] then
            currentTable[key] = {}
        end
        currentTable = currentTable[key]
    end

    local lastKey = keys[#keys]
    if not currentTable[lastKey] then
        currentTable[lastKey] = {}
    end
end

function BattleGameEvent:checkTableExists(table, ...)
    local keys = { ... }
    local currentTable = table

    for i = 1, #keys do
        local key = keys[i]
        if not currentTable[key] then
            return false
        end
        currentTable = currentTable[key]
    end

    return true
end

function BattleGameEvent:RegisterBattleEvent(eventType, priority, heroUid, handlerType, handlerId, isSelf)
    priority = priority or 0
    self:ensureTableExists(self.battleEvents, eventType, priority, heroUid, handlerType)
    local handlerStruct = { handlerId = handlerId, isSelf = isSelf }
    table.insert(self.battleEvents[eventType][priority][heroUid][handlerType], handlerStruct)

    self:EventStructRecord(self.eventTypePriorityRecord, eventType, priority)
    table.sort(self.eventTypePriorityRecord[eventType], function(a, b) return b.priority > a.priority end)

    self:ensureTableExists(self.eventHeroRecord, heroUid)
    self:EventStructRecord(self.eventHeroRecord[heroUid], eventType, priority)
end

function BattleGameEvent:EventStructRecord(t, eventType, priority)
    self:ensureTableExists(t, eventType)
    local index
    for i, eventRecordStruct in ipairs(t[eventType]) do
        if eventRecordStruct.priority == priority then
            index = i
        end
    end
    if not index then
        local eventRecordStruct = { priority = priority, count = 1 }
        table.insert(t[eventType], eventRecordStruct)
    else
        t[eventType][index].count = t[eventType][index].count + 1
    end
end

function BattleGameEvent:UnregisterBattleEvent(eventType, priority, heroUid, handlerType, handlerId)
    priority = priority or 0
    
    local targetBattleEventsTable = BattleUtilsEnv.Safe_access(self.battleEvents, eventType, priority, heroUid, handlerType)
    for i, handlerStruct in ipairs(targetBattleEventsTable) do
        if handlerStruct.handlerId == handlerId then
            if #targetBattleEventsTable == 1 then
                self.battleEvents[eventType][priority][heroUid][handlerType] = nil
            else
                table.remove(targetBattleEventsTable, i)
            end
        end
    end

    self:EventStructRemove(self.eventTypePriorityRecord[eventType], priority)

    self:EventStructRemove(self.eventHeroRecord[heroUid][eventType], priority)
end

function BattleGameEvent:EventStructRemove(t, priority)
    local idx --idx应该一定存在 否则不应该进入UnregisterBattleEvent流程
    for i, eventStruct in ipairs(t) do
        if eventStruct.priority == priority then
            idx = i
        end
    end
    t[idx].count = t[idx].count - 1
    if t[idx].count == 0 then
        table.remove(t, idx)
    end
end

function BattleGameEvent:UnregisterHeroBattleEvents(heroUid)
    local targetHeroRecordTable = self.eventHeroRecord[heroUid]
    for eventType, _ in pairs(targetHeroRecordTable or {}) do
        for _, eventStruct in pairs(targetHeroRecordTable[eventType]) do
            local priority = eventStruct.priority
            self.battleEvents[eventType][priority][heroUid] = nil
            break
        end
    end
end

function BattleGameEvent:RecordHeroOrder(t)
    local orderedHeroes = {}
    for order, hero in pairs(t) do
        orderedHeroes[order] = hero.uniqueId
    end
    return orderedHeroes
end

function BattleGameEvent:FireBattleEvent(eventType, eventArgs, curHeroUid)
    if self.battleGame:CheckBattleEnd() then
        return false
    end
    if not self.eventTypePriorityRecord[eventType] then
        return false
    end
    
    self.battleGame:CalculateHeroSpeed() --每个事件触发时计算一次角色速度顺序
    local orderedHeroes = BattleGameEvent:RecordHeroOrder(self.battleGame.orderedHeroes)

    local handled
    for _, eventRecordStruct in ipairs(self.eventTypePriorityRecord[eventType]) do
        for _, heroUid in ipairs(orderedHeroes) do
            local priority = eventRecordStruct.priority
            if self.battleEvents[eventType][priority][heroUid] then
                local hero = self.battleGame.heroUniqueIdMap[heroUid]
                if hero:IsAlive() then
                    for handlerType, handlerStructs in pairs(self.battleEvents[eventType][priority][heroUid]) do
                        for _, handlerStruct in pairs(handlerStructs) do
                            if not (handlerStruct.isSelf and heroUid ~= curHeroUid) then --自身事件检测
                                eventArgs.eventType = eventType
                                handled = self:FireHeroBattleEventByHandler(hero, handlerType, handlerStruct.handlerId, eventArgs) or handled
                            end
                        end
                    end

                    if self.battleGame:CheckBattleEnd() then
                        return handled
                    end
                end
            end
        end
    end

    return handled
end

function BattleGameEvent:FireHeroBattleEvent(hero, eventType, eventArgs, curHeroUid)
    if self.battleGame:CheckBattleEnd() then
        return false
    end
    if not self.eventTypePriorityRecord[eventType] then
        return false
    end
    if not hero:IsAlive() then
        return false
    end
    local handled
    local heroUid = hero.uniqueId
    for _, eventRecordStruct in ipairs(self.eventTypePriorityRecord[eventType]) do
        local priority = eventRecordStruct.priority
        if self.battleEvents[eventType][priority][heroUid] then
            for handlerType, handlerStructs in pairs(self.battleEvents[eventType][priority][heroUid]) do
                for _, handlerStruct in pairs(handlerStructs) do
                    if not (handlerStruct.isSelf and heroUid ~= curHeroUid) then --自身事件检测
                        eventArgs.eventType = eventType
                        handled = self:FireHeroBattleEventByHandler(hero, handlerType, handlerStruct.handlerId, eventArgs) or handled
                    end
                end
            end

            if self.battleGame:CheckBattleEnd() then
                return handled
            end
        end
    end

    return handled
end

function BattleGameEvent:FireHeroBattleEventByHandler(hero, handlerType, handlerId, eventArgs)
    local inputTacticId = eventArgs.InputTacticId
    local inputBuffId = eventArgs.InputBuffId
    local eventType = eventArgs.eventType

    local handled = false
    if handlerType == BattleConstEnv.BattleHandlerType.Tactic then
        -- 战法
        local tactic = self.battleGame:GetTacticByUid(handlerId)
        if inputTacticId ~= tactic.tacticId and tactic.battleEvents[eventType] ~= nil and tactic:CanUse() then
            handled = hero:UseTactic(tactic, eventArgs) or handled
            if not hero:IsAliveAndNotBattleEnd() then
                return handled
            end
        end
    end
    if handlerType == BattleConstEnv.BattleHandlerType.Buff then
        -- buff
        local buff = hero:GetBuff(handlerId)
        if not buff then
            local w = 1
        end
        if inputBuffId ~= buff.buffId then
            handled = buff:OnTrigger(eventArgs) or handled
            if not hero:IsAliveAndNotBattleEnd() then
                return handled
            end
        end
    end

    return handled
end
